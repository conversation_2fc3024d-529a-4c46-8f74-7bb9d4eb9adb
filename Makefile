# Membership SAAS Backend Makefile

# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod
BINARY_NAME=membership-saas-backend
BINARY_PATH=./tmp/$(BINARY_NAME)
MAIN_PATH=./cmd/api/main.go

# Database parameters - FIXED to match Docker Compose
DB_URL=postgres://user:password@localhost:5432/membership_db?sslmode=disable
MIGRATE_PATH=db/migrations

# Test parameters
TEST_TIMEOUT=5m
COVERAGE_OUT=coverage.out
TEST_PARALLEL_WORKERS=4
COVERAGE_TARGET=85

.PHONY: all build clean test coverage deps run dev docker-build docker-run \
        db-migrate db-migrate-up db-migrate-down db-reset sqlc-generate \
        mocks-generate lint format swagger help test-parallel test-watch \
        test-service test-stability test-stability-integration test-stability-password \
        coverage-html coverage-check coverage-gate ci-test ci-coverage test-contracts \
        tech-debt-status tech-debt-waiver load-test metrics-collect \
        test-unit test-integration test-security test-e2e test-load \
        test-fast test-slow test-all test-ci test-local test-agents \
        test-agents-auth test-agents-file test-agents-rbac \
        validate-schema setup-hooks compliance-check compliance-report \
        compliance-ci security-scan seed seed-run seed-export seed-import

# Default target
all: clean deps test build

# Build the application
build:
	@echo "Building $(BINARY_NAME)..."
	$(GOBUILD) -o $(BINARY_PATH) $(MAIN_PATH)

# Clean build artifacts
clean:
	@echo "Cleaning..."
	$(GOCLEAN)
	rm -f $(BINARY_PATH)
	rm -f $(COVERAGE_OUT)

# Run all tests (unit + integration + security)
test:
	@echo "🧪 Running all tests..."
	@echo "This will run unit tests, integration tests, and security tests"
	@$(MAKE) test-unit
	@$(MAKE) test-integration
	@$(MAKE) test-security

# Run tests in parallel (faster execution)
test-parallel:
	@echo "Running tests in parallel..."
	./scripts/run_parallel_tests.sh -p $(TEST_PARALLEL_WORKERS)

# Run tests with coverage
coverage:
	@echo "Running tests with coverage..."
	$(GOTEST) -v -timeout $(TEST_TIMEOUT) -coverprofile=$(COVERAGE_OUT) ./...
	./scripts/coverage_report.sh -f $(COVERAGE_OUT)

# Generate HTML coverage report
coverage-html:
	@echo "Generating HTML coverage report..."
	$(GOTEST) -timeout $(TEST_TIMEOUT) -coverprofile=$(COVERAGE_OUT) ./... > /dev/null 2>&1 || true
	./scripts/coverage_report.sh -f $(COVERAGE_OUT) --html

# Check coverage against threshold
coverage-check:
	@echo "Checking coverage against threshold..."
	$(GOTEST) -timeout $(TEST_TIMEOUT) -coverprofile=$(COVERAGE_OUT) ./... > /dev/null 2>&1 || true
	./scripts/coverage_report.sh -f $(COVERAGE_OUT) --compare $(COVERAGE_TARGET)

# Quality Gates - Tollbooth Enforcement Pattern
coverage-gate:
	@echo "🚦 Running coverage quality gate..."
	./scripts/check-coverage.sh

# Tech Debt Management
tech-debt-status:
	@echo "📋 Checking tech debt waiver status..."
	./scripts/check-waivers.sh

tech-debt-waiver:
	@echo "📝 Creating tech debt waiver..."
	@read -p "Waiver type (coverage/complexity/lint/dependency): " TYPE; \
	read -p "Reason: " REASON; \
	./scripts/create-tech-debt-waiver.sh "$$TYPE" "$$REASON"

# Test organization with build tags for proper separation
.PHONY: test-unit test-integration test-security test-e2e test-load

test-unit: ## Run unit tests only (no build tags, <30s)
	@echo "🧪 Running unit tests..."
	@$(GOTEST) -short -timeout 30s ./internal/... ./cmd/...

test-integration: ## Run integration tests only (requires database, <3m)
	@echo "🔗 Running integration tests..."
	@$(GOTEST) -tags=integration -timeout 3m ./internal/... ./tests/integration/...

test-e2e: ## Run end-to-end tests (full application, <10m)
	@echo "🚀 Running end-to-end tests..."
	@$(GOTEST) -tags=e2e -timeout 10m ./tests/e2e/...

test-security: ## Run security tests (OWASP, vulnerabilities, <2m)
	@echo "🔒 Running security tests..."
	@$(GOTEST) -tags=security -timeout 2m ./tests/security/...


test-load: ## Run load and stress tests
	@echo "💪 Running load tests..."
	@$(GOTEST) -tags=load -timeout 15m ./tests/load/...

# Combined test commands
test-fast: ## Run only fast tests (unit tests)
	@echo "⚡ Running fast tests only..."
	@$(MAKE) test-unit

test-slow: ## Run slow tests (integration + e2e)
	@echo "🐌 Running slow tests..."
	@$(MAKE) test-integration
	@$(MAKE) test-e2e

test-all: ## Run all test types including load tests
	@echo "🎯 Running all test types..."
	@$(MAKE) test-unit
	@$(MAKE) test-integration
	@$(MAKE) test-security
	@$(MAKE) test-e2e

test-ci: ## Run CI test suite (unit + integration + security)
	@echo "🤖 Running CI test suite..."
	@$(MAKE) test-unit
	@$(MAKE) test-integration
	@$(MAKE) test-security

test-local: ## Run local development tests (unit + integration)
	@echo "🏠 Running local development tests..."
	@$(MAKE) test-unit
	@$(MAKE) test-integration

# OWASP Top 10 Security Tests
test-owasp: ## Run all OWASP Top 10 security tests
	@echo "Running OWASP Top 10 security tests..."
	@$(GOTEST) -v -timeout 30m ./tests/security/owasp/...

test-owasp-critical: ## Run critical OWASP tests only (faster for CI)
	@echo "Running critical OWASP security tests..."
	@$(GOTEST) -v -timeout 10m -run TestOWASPCritical ./tests/security/owasp/...

test-owasp-a01: ## Run OWASP A01: Broken Access Control tests
	@echo "Running OWASP A01: Broken Access Control tests..."
	@$(GOTEST) -v -timeout 5m -run TestA01 ./tests/security/owasp/...

test-owasp-a02: ## Run OWASP A02: Cryptographic Failures tests
	@echo "Running OWASP A02: Cryptographic Failures tests..."
	@$(GOTEST) -v -timeout 5m -run TestA02 ./tests/security/owasp/...

test-owasp-a03: ## Run OWASP A03: Injection tests
	@echo "Running OWASP A03: Injection tests..."
	@$(GOTEST) -v -timeout 5m -run TestA03 ./tests/security/owasp/...

test-owasp-a04: ## Run OWASP A04: Insecure Design tests
	@echo "Running OWASP A04: Insecure Design tests..."
	@$(GOTEST) -v -timeout 5m -run TestA04 ./tests/security/owasp/...

test-owasp-a05: ## Run OWASP A05: Security Misconfiguration tests
	@echo "Running OWASP A05: Security Misconfiguration tests..."
	@$(GOTEST) -v -timeout 5m -run TestA05 ./tests/security/owasp/...

test-owasp-a07: ## Run OWASP A07: Identification and Authentication tests
	@echo "Running OWASP A07: Identification and Authentication tests..."
	@$(GOTEST) -v -timeout 5m -run TestA07 ./tests/security/owasp/...

test-owasp-a08: ## Run OWASP A08: Software and Data Integrity tests
	@echo "Running OWASP A08: Software and Data Integrity tests..."
	@$(GOTEST) -v -timeout 5m -run TestA08 ./tests/security/owasp/...

test-owasp-a09: ## Run OWASP A09: Security Logging and Monitoring tests
	@echo "Running OWASP A09: Security Logging and Monitoring tests..."
	@$(GOTEST) -v -timeout 5m -run TestA09 ./tests/security/owasp/...

test-owasp-a10: ## Run OWASP A10: Server-Side Request Forgery tests
	@echo "Running OWASP A10: Server-Side Request Forgery tests..."
	@$(GOTEST) -v -timeout 5m -run TestA10 ./tests/security/owasp/...

# Run sub-agent tests
test-agents:
	@echo "Running sub-agent tests..."
	@go run cmd/test-agents/main.go -verbose

# Run authentication agent tests only
test-agents-auth:
	@echo "Running authentication agent tests..."
	@go run cmd/test-agents/main.go -agent auth -verbose

# Run file security agent tests only
test-agents-file:
	@echo "Running file security agent tests..."
	@go run cmd/test-agents/main.go -agent file -verbose

# Run RBAC agent tests only
test-agents-rbac:
	@echo "Running RBAC agent tests..."
	@go run cmd/test-agents/main.go -agent rbac -verbose

# Run tests for a specific service
test-service:
	@if [ -z "$(SERVICE)" ]; then \
		echo "Usage: make test-service SERVICE=user"; \
		exit 1; \
	fi
	@echo "Running tests for service: $(SERVICE)..."
	$(GOTEST) -v -timeout $(TEST_TIMEOUT) ./internal/services/$(SERVICE)/...

# Run tests in watch mode (requires fswatch)
test-watch:
	@echo "Running tests in watch mode..."
	@if ! command -v fswatch >/dev/null 2>&1; then \
		echo "Error: fswatch is not installed. Install it with: brew install fswatch (macOS) or apt-get install fswatch (Linux)"; \
		exit 1; \
	fi
	@echo "Watching for changes..."
	@fswatch -o . -e ".*" -i "\\.go$$" | xargs -n1 -I{} $(GOTEST) -v ./...

# Run tests multiple times to detect flaky behavior
test-stability:
	@echo "Running stability tests (50 iterations) - detecting flaky tests..."
	@echo "This may take several minutes. Press Ctrl+C to stop early."
	$(GOTEST) -count=50 -timeout $(TEST_TIMEOUT) ./...

# Run integration tests multiple times for stability validation
test-stability-integration:
	@echo "Running integration tests stability check (20 iterations)..."
	@echo "Testing integration test stability - this will take 10-20 minutes."
	$(GOTEST) -count=20 -timeout $(TEST_TIMEOUT) -run "TestIntegration" ./...

# Run password change tests specifically for 100 iterations (critical stability test)
test-stability-password:
	@echo "Running password change tests 100 times for critical stability validation..."
	@echo "This is the key test for P84 completion - detecting state pollution in password tests."
	$(GOTEST) -count=100 -timeout $(TEST_TIMEOUT) -run "TestChangePassword" ./internal/handlers/user/...

# Run OpenAPI contract tests
test-contracts:
	@echo "Running OpenAPI contract tests..."
	@echo "Validating API implementation against OpenAPI specification..."
	$(GOTEST) -v -timeout $(TEST_TIMEOUT) ./tests/contract/...


# Run load tests
load-test:
	@echo "Running load tests..."
	@cd tests/load && go run . -duration=5m -users=100 -rps=50


# Collect performance metrics
metrics-collect:
	@echo "Collecting performance metrics..."
	@./scripts/collect_metrics.sh "manual" "0" "0" "0" "0"

# Download dependencies
deps:
	@echo "Downloading dependencies..."
	$(GOMOD) download
	$(GOMOD) tidy

# Run the application
run: build
	@echo "Starting $(BINARY_NAME)..."
	$(BINARY_PATH)

# Run the application directly (no build)
dev:
	@echo "Running in development mode..."
	$(GOCMD) run $(MAIN_PATH)

# Run with live reload using air
dev-watch:
	@echo "Running with live reload..."
	air -c .air.toml

# Docker commands
docker-build:
	@echo "Building Docker image..."
	docker build -t $(BINARY_NAME) .

docker-run:
	@echo "Running Docker container..."
	docker-compose up --build

docker-down:
	@echo "Stopping Docker containers..."
	docker-compose down

# Database migrations
db-migrate-up:
	@echo "Running database migrations up..."
	migrate -path $(MIGRATE_PATH) -database "$(DB_URL)" up

db-migrate-down:
	@echo "Running database migrations down..."
	migrate -path $(MIGRATE_PATH) -database "$(DB_URL)" down

db-migrate-force:
	@echo "Forcing database migration version..."
	@read -p "Enter migration version: " version; \
	migrate -path $(MIGRATE_PATH) -database "$(DB_URL)" force $$version

db-reset: db-migrate-down db-migrate-up
	@echo "Database reset complete"

# Initialize application (superadmin, default org)
init:
	@echo "Initializing application..."
	@go run ./cmd/init/main.go

# Data seeding (events, posts, tags, etc.)
seed-export:
	@echo "Exporting database data..."
	@go run ./cmd/seed/main.go export -output seed_export.json

seed-import:
	@echo "Importing seed data from file..."
	@go run ./cmd/seed/main.go import -file seed_data.json

# Legacy alias for backward compatibility
seed-run: init
	@echo "Application initialization complete"

seed: init
	@echo "Application initialization complete"

# Generate SQLC code
sqlc-generate:
	@echo "Generating SQLC code..."
	cd db && sqlc generate

# Generate mocks
mocks-generate:
	@echo "Generating mocks..."
	@echo "Using unified mock generation strategy..."
	mockery

# Clean all mocks (removes all mock files)
mocks-clean:
	@echo "Cleaning all mocks..."
	@find internal/mocks -name "*_mock.go" -type f -delete 2>/dev/null || true
	@find internal -name "*_mock.go" -type f ! -path "*/mocks/*" ! -path "*/testing/*" -delete 2>/dev/null || true
	@find internal -name "mock_*.go" -type f ! -path "*/mocks/*" ! -path "*/testing/*" -delete 2>/dev/null || true
	@echo "Mock cleanup complete"

# Regenerate all mocks (clean then generate)
mocks-regenerate: mocks-clean mocks-generate
	@echo "Mock regeneration complete"

# Check mock consistency
mocks-check:
	@echo "Checking mock consistency..."
	@echo "Checking for orphaned mocks..."
	@find internal/mocks -name "*_mock.go" -type f | while read mock; do \
		interface=$$(basename $$mock _mock.go); \
		if ! grep -q "type $$interface interface" internal --include="*.go" -r 2>/dev/null; then \
			echo "Warning: Orphaned mock $$mock (no interface found)"; \
		fi; \
	done
	@echo "Checking for missing mocks..."
	@grep -r "type.*interface {" internal --include="*.go" | grep -v mock | grep -v test | while read line; do \
		interface=$$(echo $$line | sed 's/.*type \([^ ]*\) interface.*/\1/'); \
		if ! find internal/mocks -name "$${interface}_mock.go" | grep -q .; then \
			echo "Warning: Missing mock for interface $$interface"; \
		fi; \
	done

# Update specific domain mocks
mocks-update-domain:
	@read -p "Enter domain (event/user/organization/post/resource/verification/volunteer/scheduler/notification/registration): " domain; \
	echo "Updating mocks for $$domain domain..."; \
	find internal/mocks/services/$$domain -name "*_mock.go" -type f -delete 2>/dev/null || true; \
	mockery --dir internal/services/$$domain --output internal/mocks/services/$$domain --all

# List all interfaces that should have mocks
mocks-list-interfaces:
	@echo "Interfaces that should have mocks:"
	@grep -r "type.*interface {" internal --include="*.go" | grep -v mock | grep -v test | \
		awk -F: '{print $$1 ": " $$2}' | sort | uniq

# Code generation (all)
generate: sqlc-generate mocks-generate
	@echo "Code generation complete"

# Linting
lint:
	@echo "Running linter..."
	golangci-lint run

# Format code
format:
	@echo "Formatting code..."
	$(GOCMD) fmt ./...
	goimports -w .

# Generate Swagger documentation
swagger:
	@echo "Generating Swagger documentation..."
	swag init -g cmd/api/main.go -o docs

# Validate Swagger documentation (Plan 94)
validate-swagger:
	@echo "Validating Swagger documentation..."
	./scripts/validate_swagger.sh

# Install development tools
install-tools:
	@echo "Installing development tools..."
	$(GOGET) github.com/swaggo/swag/cmd/swag@latest
	$(GOGET) github.com/air-verse/air@latest
	$(GOGET) github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	$(GOGET) golang.org/x/tools/cmd/goimports@latest
	$(GOGET) github.com/vektra/mockery/v3@latest
	$(GOGET) github.com/sonatype-nexus-community/nancy@latest
	$(GOGET) github.com/securego/gosec/v2/cmd/gosec@latest

# Security scan
security:
	@echo "Running security scan..."
	gosec ./...

# Vulnerability scanning with Nancy
.PHONY: vuln-scan vuln-scan-ci vuln-scan-report vuln-scan-update

# Run vulnerability scan with Nancy
vuln-scan:
	@echo "🔍 Running dependency vulnerability scan with Nancy..."
	@go list -json -deps ./... | nancy sleuth

# Run vulnerability scan for CI (exits with error on vulnerabilities)
vuln-scan-ci:
	@echo "🔍 Running CI vulnerability scan..."
	@go list -json -deps ./... | nancy sleuth --exit-with-error

# Generate vulnerability report in multiple formats
vuln-scan-report:
	@echo "📊 Generating vulnerability reports..."
	@mkdir -p security-reports
	@go list -json -deps ./... | nancy sleuth --output json > security-reports/nancy-report.json
	@go list -json -deps ./... | nancy sleuth --output csv > security-reports/nancy-report.csv
	@go list -json -deps ./... | nancy sleuth > security-reports/nancy-report.txt
	@echo "Reports generated in security-reports/"

# Update Nancy database
vuln-scan-update:
	@echo "🔄 Updating Nancy vulnerability database..."
	@nancy update

# Generate SARIF reports for GitHub Security tab
security-sarif:
	@echo "📊 Generating SARIF security reports..."
	@./scripts/security-scan-sarif.sh

# Run all security scans
security-all: security vuln-scan security-sarif
	@echo "✅ All security scans completed"

# Vulnerability Management Commands
.PHONY: vuln-check vuln-baseline vuln-diff vuln-monitor vuln-report-html

# Check vulnerabilities against security policy
vuln-check:
	@echo "🔍 Checking vulnerabilities against security policy..."
	@./scripts/vulnerability-management.sh check --severity $(SEVERITY)

# Create vulnerability baseline
vuln-baseline:
	@echo "📸 Creating vulnerability baseline..."
	@./scripts/vulnerability-management.sh baseline

# Compare current state with baseline
vuln-diff:
	@echo "📊 Comparing with vulnerability baseline..."
	@./scripts/vulnerability-management.sh diff

# Compliance Management Commands
.PHONY: compliance-check compliance-report compliance-ci compliance-dashboard

# Run compliance checks
compliance-check:
	@echo "🔍 Running compliance checks..."
	@./scripts/compliance-check.sh

# Generate comprehensive compliance report
compliance-report:
	@echo "📊 Generating compliance report..."
	@mkdir -p compliance-reports
	@go run ./cmd/compliance-report/main.go -output compliance-reports

# Run compliance checks for CI/CD (fails on non-compliance)
compliance-ci:
	@echo "🔍 Running CI compliance checks..."
	@./scripts/compliance-check.sh || exit 1

# Launch compliance dashboard (requires server running)
compliance-dashboard:
	@echo "📊 Launching compliance dashboard..."
	@echo "Dashboard will be available at http://localhost:8081/compliance"
	@go run ./cmd/compliance-dashboard/main.go

# Security scan with gosec
security-scan:
	@echo "🔒 Running security scan with gosec..."
	@mkdir -p security-reports
	@gosec -fmt json -out security-reports/gosec-report.json ./... || true
	@gosec -fmt sarif -out security-reports/gosec-report.sarif ./... || true
	@gosec ./...

# Run SOC2 compliance verification
soc2-check:
	@echo "🔍 Running SOC2 compliance checks..."
	@go test -v ./internal/compliance/soc2/... -run TestSOC2Compliance

# Run GDPR compliance verification
gdpr-check:
	@echo "🔍 Running GDPR compliance checks..."
	@go test -v ./internal/compliance/gdpr/... -run TestGDPRCompliance

# Run data retention policy
data-retention:
	@echo "🗑️ Running data retention policy..."
	@go run ./cmd/data-retention/main.go

# Generate audit report
audit-report:
	@echo "📊 Generating audit report..."
	@mkdir -p compliance-reports
	@go run ./cmd/audit-report/main.go -days 30 -output compliance-reports/audit-report.json

# Monitor vulnerabilities continuously
vuln-monitor:
	@echo "👀 Starting vulnerability monitor..."
	@./scripts/vulnerability-management.sh monitor --severity $(SEVERITY)

# Generate HTML vulnerability report
vuln-report-html:
	@echo "📄 Generating HTML vulnerability report..."
	@./scripts/vulnerability-management.sh report --format html --output vulnerability-report.html
	@echo "Report generated: vulnerability-report.html"

# Default severity for vulnerability checks
SEVERITY ?= HIGH

# Benchmark tests
bench:
	@echo "Running benchmarks..."
	$(GOTEST) -bench=. -benchmem ./...

# Get admin token for testing
admin-token:
	@echo "Getting admin token..."
	./get_admin_token.sh

# Setup development environment
setup: install-tools deps db-migrate-up generate
	@echo "Development environment setup complete"

# CI/CD test target
ci-test:
	@echo "Running CI tests..."
	./scripts/run_parallel_tests.sh -p $(TEST_PARALLEL_WORKERS) --coverage
	./scripts/coverage_report.sh -f coverage.out --compare $(COVERAGE_TARGET)

# CI/CD coverage target
ci-coverage:
	@echo "Running CI coverage checks..."
	$(GOTEST) -timeout $(TEST_TIMEOUT) -coverprofile=$(COVERAGE_OUT) ./... > /dev/null 2>&1
	./scripts/coverage_report.sh -f $(COVERAGE_OUT) --html --badge --compare $(COVERAGE_TARGET)

# CI pipeline
ci: deps lint ci-test
	@echo "CI pipeline complete"

# Release build (optimized)
release:
	@echo "Building release version..."
	CGO_ENABLED=0 GOOS=linux $(GOBUILD) -a -installsuffix cgo -ldflags '-extldflags "-static"' -o $(BINARY_PATH) $(MAIN_PATH)

# Help
help:
	@echo "Available targets:"
	@echo "  build         - Build the application"
	@echo "  clean         - Clean build artifacts"
	@echo "  test          - Run standard test suite (unit + integration + security)"
	@echo "  test-parallel - Run tests in parallel for faster execution"
	@echo ""
	@echo "Test Types (by build tags):"
	@echo "  test-unit     - Run unit tests only (no build tags, <30s)"
	@echo "  test-integration - Run integration tests (requires database, <3m)"
	@echo "  test-security - Run security tests (OWASP, vulnerabilities, <2m)"
	@echo "  test-e2e      - Run end-to-end tests (full application, <10m)"
	@echo "  test-load     - Run load and stress tests"
	@echo ""
	@echo "Combined Test Commands:"
	@echo "  test-fast     - Run only fast tests (unit tests)"
	@echo "  test-slow     - Run slow tests (integration + e2e)"
	@echo "  test-all      - Run all test types including performance"
	@echo "  test-ci       - Run CI test suite (unit + integration + security)"
	@echo "  test-local    - Run local development tests (unit + integration)"
	@echo "  test-service  - Run tests for specific service (use SERVICE=name)"
	@echo "  test-watch    - Run tests in watch mode"
	@echo "  test-stability - Run tests 50x to detect flaky behavior"
	@echo "  test-stability-integration - Run integration tests 20x for stability"
	@echo "  test-stability-password - Run password tests 100x (P84 validation)"
	@echo "  test-agents   - Run all sub-agent tests (auth, file, rbac)"
	@echo "  test-agents-auth - Run authentication agent tests"
	@echo "  test-agents-file - Run file security agent tests"
	@echo "  test-agents-rbac - Run RBAC agent tests"
	@echo "  coverage      - Run tests with coverage report"
	@echo "  coverage-html - Generate HTML coverage report"
	@echo "  coverage-check - Check coverage against threshold ($(COVERAGE_TARGET)%)"
	@echo "  deps          - Download dependencies"
	@echo "  run           - Build and run the application"
	@echo "  dev           - Run the application in development mode"
	@echo "  dev-watch     - Run with live reload (air)"
	@echo "  docker-build  - Build Docker image"
	@echo "  docker-run    - Run with Docker Compose"
	@echo "  docker-down   - Stop Docker containers"
	@echo "  db-migrate-up - Run database migrations up"
	@echo "  db-migrate-down - Run database migrations down"
	@echo "  db-reset      - Reset database (down then up)"
	@echo "  init          - Initialize application (superadmin, default org)"
	@echo "  seed          - Initialize application (alias for init)"
	@echo "  seed-run      - Initialize application (legacy alias)"
	@echo "  seed-export   - Export database data to JSON file"
	@echo "  seed-import   - Import seed data from JSON file"
	@echo "  sqlc-generate - Generate SQLC code"
	@echo "  mocks-generate - Generate mocks using unified strategy"
	@echo "  mocks-clean   - Clean all mock files"
	@echo "  mocks-regenerate - Clean and regenerate all mocks"
	@echo "  mocks-check   - Check mock consistency and find issues"
	@echo "  mocks-update-domain - Update mocks for specific domain"
	@echo "  mocks-list-interfaces - List all interfaces that need mocks"
	@echo "  generate      - Run all code generation"
	@echo "  lint          - Run linter"
	@echo "  format        - Format code"
	@echo "  swagger       - Generate Swagger documentation"
	@echo "  validate-swagger - Validate Swagger documentation (Plan 94)"
	@echo "  install-tools - Install development tools"
	@echo "  security      - Run security scan with gosec"
	@echo "  security-all  - Run all security scans (gosec, nancy, SARIF)"
	@echo "  security-sarif - Generate SARIF reports for GitHub Security"
	@echo "  vuln-scan     - Run Nancy dependency vulnerability scan"
	@echo "  vuln-scan-ci  - Run CI vulnerability scan (exits on errors)"
	@echo "  vuln-scan-report - Generate vulnerability reports (JSON/CSV/TXT)"
	@echo "  vuln-scan-update - Update vulnerability databases"
	@echo "  vuln-check    - Check against security policy"
	@echo "  vuln-baseline - Create vulnerability baseline"
	@echo "  vuln-diff     - Compare with baseline"
	@echo "  vuln-monitor  - Continuous vulnerability monitoring"
	@echo "  vuln-report-html - Generate HTML vulnerability report"
	@echo "  bench         - Run benchmark tests"
	@echo "  admin-token   - Get admin token for testing"
	@echo "  setup         - Setup development environment"
	@echo "  ci-test       - Run tests for CI/CD"
	@echo "  ci-coverage   - Generate coverage for CI/CD"
	@echo "  ci            - Run full CI pipeline"
	@echo "  release       - Build optimized release version"
	@echo "  validate-schema - Validate schema-code synchronization"
	@echo "  setup-hooks   - Setup Git hooks for the project"
	@echo "  help          - Show this help message"
# ============================================================================
# Agent C Coverage Automation Targets
# ============================================================================

# Enhanced Coverage Collection
.PHONY: coverage-collect coverage-collect-full coverage-packages coverage-trends
coverage-collect: ## Collect basic coverage data
	@echo "📊 Collecting coverage data..."
	@./scripts/coverage_collect.sh

coverage-collect-full: ## Collect full coverage data with packages and trends
	@echo "📊 Collecting comprehensive coverage data..."
	@./scripts/coverage_collect.sh --packages --trends --json --html

coverage-packages: ## Analyze package-level coverage
	@echo "📦 Analyzing package coverage..."
	@./scripts/coverage_collect.sh --packages --json

coverage-trends: ## Generate coverage trends
	@echo "📈 Generating coverage trends..."
	@./scripts/coverage_collect.sh --trends

# Coverage Quality Gates
.PHONY: coverage-gates coverage-gates-all coverage-gates-strict coverage-regression-check
coverage-gates: ## Run basic coverage gates
	@echo "🚦 Running coverage gates..."
	@./scripts/coverage_gates.sh --gate overall

coverage-gates-all: ## Run all coverage gates
	@echo "🚦 Running all coverage gates..."
	@./scripts/coverage_gates.sh --gate all

coverage-gates-strict: ## Run coverage gates in strict mode
	@echo "🚦 Running coverage gates (strict mode)..."
	@./scripts/coverage_gates.sh --gate all --strict

coverage-regression-check: ## Check for coverage regressions
	@echo "📉 Checking for coverage regressions..."
	@./scripts/coverage_gates.sh --gate regression

# Coverage Reporting
.PHONY: coverage-dashboard coverage-reports coverage-serve coverage-watch
coverage-dashboard: ## Generate interactive coverage dashboard
	@echo "🌐 Generating coverage dashboard..."
	@./scripts/coverage_reporting.sh --type dashboard --interactive

coverage-reports: ## Generate all coverage reports
	@echo "📊 Generating all coverage reports..."
	@./scripts/coverage_reporting.sh --type all --interactive

coverage-serve: ## Start coverage report server
	@echo "🚀 Starting coverage report server..."
	@./scripts/coverage_reporting.sh --serve --watch --port 8080

coverage-watch: ## Watch for coverage changes and auto-update reports
	@echo "👀 Watching for coverage changes..."
	@./scripts/coverage_reporting.sh --watch

# Coverage Quality Management
.PHONY: coverage-quality coverage-quality-check coverage-waivers coverage-thresholds
coverage-quality: ## Run quality checks
	@echo "🎯 Running coverage quality checks..."
	@./scripts/coverage_quality.sh --check

coverage-quality-check: ## Run quality checks with strict enforcement
	@echo "🎯 Running coverage quality checks (strict)..."
	@./scripts/coverage_quality.sh --check --strict

coverage-waivers: ## List active coverage waivers
	@echo "📋 Listing active coverage waivers..."
	@./scripts/coverage_quality.sh --list-waivers

coverage-thresholds: ## List coverage thresholds
	@echo "🎯 Listing coverage thresholds..."
	@./scripts/coverage_quality.sh --list-thresholds

# Coverage Automation
.PHONY: coverage-pr-diff coverage-badges coverage-hooks coverage-ci
coverage-pr-diff: ## Generate PR coverage diff (requires PR_NUMBER)
	@if [ -z "$(PR_NUMBER)" ]; then \
		echo "Usage: make coverage-pr-diff PR_NUMBER=123"; \
		exit 1; \
	fi
	@echo "🔍 Generating PR coverage diff..."
	@./scripts/coverage_automation.sh --pr-number $(PR_NUMBER) --pr-comment

coverage-badges: ## Generate coverage badges
	@echo "🏷️ Generating coverage badges..."
	@./scripts/coverage_automation.sh --integrate badges

coverage-hooks: ## Set up Git hooks for coverage
	@echo "🪝 Setting up Git hooks..."
	@./scripts/coverage_automation.sh --integrate hooks

coverage-ci: ## Generate CI/CD integration files
	@echo "🔄 Generating CI/CD integration..."
	@./scripts/coverage_automation.sh --integrate ci

# Comprehensive Coverage Workflow
.PHONY: coverage-full coverage-ci-check coverage-pr-check
coverage-full: ## Full coverage workflow (collect, check, report)
	@echo "🚀 Running full coverage workflow..."
	@$(MAKE) coverage-collect-full
	@$(MAKE) coverage-gates-all
	@$(MAKE) coverage-quality-check
	@$(MAKE) coverage-dashboard

coverage-ci-check: ## CI/CD coverage check (for automated builds)
	@echo "🤖 Running CI coverage checks..."
	@./scripts/coverage_collect.sh --packages --json
	@./scripts/coverage_gates.sh --gate all --output-format json
	@./scripts/coverage_quality.sh --check --output-format json

coverage-pr-check: ## PR coverage check (requires PR_NUMBER)
	@if [ -z "$(PR_NUMBER)" ]; then \
		echo "Usage: make coverage-pr-check PR_NUMBER=123"; \
		exit 1; \
	fi
	@echo "🔍 Running PR coverage check..."
	@./scripts/coverage_automation.sh --pr-number $(PR_NUMBER) --pr-comment --threshold-check

# Coverage Maintenance
.PHONY: coverage-clean coverage-reset coverage-backup
coverage-clean: ## Clean coverage data and reports
	@echo "🧹 Cleaning coverage data..."
	@rm -rf coverage-data/reports/*
	@rm -f coverage.out coverage.html

coverage-reset: ## Reset all coverage data and configuration
	@echo "🔄 Resetting coverage data..."
	@rm -rf coverage-data/
	@rm -f coverage.out coverage.html coverage-config.yaml

coverage-backup: ## Backup coverage data
	@echo "💾 Backing up coverage data..."
	@tar -czf coverage-backup-$(shell date +%Y%m%d-%H%M%S).tar.gz coverage-data/ coverage.out coverage-config.yaml 2>/dev/null || true

# Help for coverage targets
coverage-help: ## Show coverage automation help
	@echo "Coverage Automation Targets:"
	@echo "============================"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | grep coverage | awk 'BEGIN {FS = ":.*?## "}; {printf "  %-25s %s\n", $$1, $$2}'

# Schema validation and synchronization
validate-schema: ## Validate schema-code synchronization
	@echo "🔍 Validating schema-code synchronization..."
	@./scripts/validate-schema-sync.sh

# Setup Git hooks
setup-hooks: ## Setup Git hooks for the project
	@echo "🔧 Setting up Git hooks..."
	@./scripts/setup-git-hooks.sh

