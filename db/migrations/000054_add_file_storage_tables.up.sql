-- Create file status enum
CREATE TYPE file_status AS ENUM (
    'pending_upload',
    'uploading',
    'upload_failed',
    'pending_scanning',
    'scanning',
    'quarantined',
    'available',
    'deleted'
);

-- Create file type enum
CREATE TYPE file_type AS ENUM (
    'image',
    'document',
    'video'
);

-- Main files table
CREATE TABLE files (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    owner_id UUID NOT NULL REFERENCES users(id),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    original_filename TEXT NOT NULL,
    storage_provider TEXT NOT NULL, -- 'local' or 's3'
    storage_key TEXT NOT NULL, -- Path or S3 key
    mime_type TEXT NOT NULL,
    file_type file_type NOT NULL,
    size_bytes BIGINT NOT NULL,
    hash_sha256 TEXT,
    status file_status NOT NULL DEFAULT 'pending_upload',
    virus_scan_result TEXT, -- JSON result from virus scanner
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ,
    
    -- Indexes
    CONSTRAINT unique_storage_key UNIQUE (storage_provider, storage_key),
    CONSTRAINT positive_size CHECK (size_bytes > 0)
);

-- Indexes for common queries
CREATE INDEX idx_files_owner_org ON files(owner_id, organization_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_files_org_hash ON files(organization_id, hash_sha256) WHERE deleted_at IS NULL;
CREATE INDEX idx_files_status ON files(status) WHERE deleted_at IS NULL;
CREATE INDEX idx_files_created_at ON files(created_at DESC);

-- File permissions table
CREATE TABLE file_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    file_id UUID NOT NULL REFERENCES files(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    can_read BOOLEAN NOT NULL DEFAULT false,
    can_delete BOOLEAN NOT NULL DEFAULT false,
    granted_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    granted_by UUID NOT NULL REFERENCES users(id),
    revoked_at TIMESTAMPTZ
);

CREATE INDEX idx_file_permissions_user ON file_permissions(user_id, file_id) WHERE revoked_at IS NULL;
CREATE INDEX idx_file_permissions_file ON file_permissions(file_id) WHERE revoked_at IS NULL;
-- Create partial unique index to ensure only one active permission per user/file
CREATE UNIQUE INDEX unique_active_file_permission ON file_permissions(file_id, user_id) WHERE revoked_at IS NULL;

-- File access audit log
CREATE TABLE file_access_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    file_id UUID NOT NULL REFERENCES files(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    action TEXT NOT NULL CHECK (action IN ('upload', 'download', 'delete', 'scan', 'share')),
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Indexes for audit queries
CREATE INDEX idx_file_access_logs_file ON file_access_logs(file_id, created_at DESC);
CREATE INDEX idx_file_access_logs_user ON file_access_logs(user_id, created_at DESC);
CREATE INDEX idx_file_access_logs_created_at ON file_access_logs(created_at DESC);

-- Trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_files_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER files_updated_at_trigger
BEFORE UPDATE ON files
FOR EACH ROW
EXECUTE FUNCTION update_files_updated_at();

-- Comments for documentation
COMMENT ON TABLE files IS 'Stores metadata for all uploaded files';
COMMENT ON COLUMN files.storage_provider IS 'Storage backend: local or s3';
COMMENT ON COLUMN files.storage_key IS 'Unique identifier for file in storage backend';
COMMENT ON COLUMN files.hash_sha256 IS 'SHA256 hash of file content for deduplication';
COMMENT ON COLUMN files.virus_scan_result IS 'JSON result from virus scanner including threat details';

COMMENT ON TABLE file_permissions IS 'Tracks file access permissions for users';
COMMENT ON TABLE file_access_logs IS 'Audit log of all file operations for security and compliance';