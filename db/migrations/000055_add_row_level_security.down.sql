-- Disable Row Level Security and remove policies

-- Drop all policies first
DROP POLICY IF EXISTS tenant_isolation_events ON events;
DROP POLICY IF EXISTS tenant_isolation_posts ON posts;
DROP POLICY IF EXISTS tenant_isolation_resources ON resources;
DROP POLICY IF EXISTS tenant_isolation_event_registrations ON event_registrations;
DROP POLICY IF EXISTS tenant_isolation_event_volunteer_applications ON event_volunteer_applications;
DROP POLICY IF EXISTS tenant_isolation_user_org_memberships ON user_organization_memberships;
DROP POLICY IF EXISTS tenant_isolation_user_volunteer_applications ON user_volunteer_applications;
DROP POLICY IF EXISTS tenant_isolation_post_media ON post_media_items;
DROP POLICY IF EXISTS tenant_isolation_event_media ON event_media_items;
DROP POLICY IF EXISTS tenant_isolation_resource_files ON resource_files;

-- Disable RLS on all tables
ALTER TABLE events DISABLE ROW LEVEL SECURITY;
ALTER TABLE posts DISABLE ROW LEVEL SECURITY;
ALTER TABLE resources DISABLE ROW LEVEL SECURITY;
ALTER TABLE event_registrations DISABLE ROW LEVEL SECURITY;
ALTER TABLE event_volunteer_applications DISABLE ROW LEVEL SECURITY;
ALTER TABLE user_organization_memberships DISABLE ROW LEVEL SECURITY;
ALTER TABLE user_volunteer_applications DISABLE ROW LEVEL SECURITY;
ALTER TABLE post_media_items DISABLE ROW LEVEL SECURITY;
ALTER TABLE event_media_items DISABLE ROW LEVEL SECURITY;
ALTER TABLE resource_files DISABLE ROW LEVEL SECURITY;

-- Drop helper functions
DROP FUNCTION IF EXISTS current_tenant_id();
DROP FUNCTION IF EXISTS is_superadmin();

-- Note: We're not dropping the indexes as they may still be useful for performance