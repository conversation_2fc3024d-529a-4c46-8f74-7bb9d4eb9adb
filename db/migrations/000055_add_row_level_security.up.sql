-- Enable Row Level Security on tenant-scoped tables
-- This migration sets up RLS policies to ensure data isolation between organizations

-- First, we need to ensure the session parameter is available
-- This allows us to set the current tenant context per transaction/session
-- The parameter is used by RLS policies to determine which rows are visible

-- Enable RLS on core tables that contain organization-specific data
ALTER TABLE events ENABLE ROW LEVEL SECURITY;
<PERSON>TER TABLE posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE resources ENABLE ROW LEVEL SECURITY;
ALTER TABLE event_registrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE event_volunteer_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_organization_memberships ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_volunteer_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE post_media_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE event_media_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE resource_files ENABLE ROW LEVEL SECURITY;

-- Create a function to get the current tenant ID from session
-- Returns NULL if not set (which will cause policies to reject access)
CREATE OR REPLACE FUNCTION current_tenant_id() RETURNS UUID AS $$
BEGIN
    -- Return the tenant ID from session, or NULL if not set
    -- The 'true' parameter makes it return NULL instead of erroring if not set
    RETURN current_setting('app.tenant_id', true)::UUID;
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$$ LANGUAGE plpgsql STABLE;

-- Create a function to check if current session is superadmin
CREATE OR REPLACE FUNCTION is_superadmin() RETURNS BOOLEAN AS $$
BEGIN
    -- Check if superadmin flag is set in session
    RETURN current_setting('app.is_superadmin', true) = 'true';
EXCEPTION
    WHEN OTHERS THEN
        RETURN FALSE;
END;
$$ LANGUAGE plpgsql STABLE;

-- Policy for events table
CREATE POLICY tenant_isolation_events ON events
    FOR ALL
    USING (
        -- Allow access if:
        -- 1. User is superadmin, OR
        -- 2. Event belongs to current tenant, OR
        -- 3. Event is published (for public access - controlled at app level)
        is_superadmin() OR 
        organization_id = current_tenant_id() OR
        (status = 'published' AND current_setting('app.allow_public_access', true) = 'true')
    )
    WITH CHECK (
        -- For INSERT/UPDATE, ensure the organization_id matches current tenant
        -- unless user is superadmin
        is_superadmin() OR organization_id = current_tenant_id()
    );

-- Policy for posts table
CREATE POLICY tenant_isolation_posts ON posts
    FOR ALL
    USING (
        is_superadmin() OR 
        organization_id = current_tenant_id() OR
        (status = 'published' AND current_setting('app.allow_public_access', true) = 'true')
    )
    WITH CHECK (
        is_superadmin() OR organization_id = current_tenant_id()
    );

-- Policy for resources table
CREATE POLICY tenant_isolation_resources ON resources
    FOR ALL
    USING (
        is_superadmin() OR 
        organization_id = current_tenant_id() OR
        (status = 'published' AND current_setting('app.allow_public_access', true) = 'true')
    )
    WITH CHECK (
        is_superadmin() OR organization_id = current_tenant_id()
    );

-- Policy for event_registrations table
-- Registrations are visible to both the user and the organization
CREATE POLICY tenant_isolation_event_registrations ON event_registrations
    FOR ALL
    USING (
        is_superadmin() OR
        EXISTS (
            SELECT 1 FROM events e 
            WHERE e.id = event_registrations.event_id 
            AND e.organization_id = current_tenant_id()
        ) OR
        user_id = current_setting('app.user_id', true)::UUID
    )
    WITH CHECK (
        is_superadmin() OR
        EXISTS (
            SELECT 1 FROM events e 
            WHERE e.id = event_registrations.event_id 
            AND e.organization_id = current_tenant_id()
        )
    );

-- Policy for event_volunteer_applications table
CREATE POLICY tenant_isolation_event_volunteer_applications ON event_volunteer_applications
    FOR ALL
    USING (
        is_superadmin() OR
        organization_id = current_tenant_id() OR
        user_id = current_setting('app.user_id', true)::UUID
    )
    WITH CHECK (
        is_superadmin() OR organization_id = current_tenant_id()
    );

-- Policy for user_organization_memberships table
-- Users can see their own memberships, orgs can see their members
CREATE POLICY tenant_isolation_user_org_memberships ON user_organization_memberships
    FOR ALL
    USING (
        is_superadmin() OR
        organization_id = current_tenant_id() OR
        user_id = current_setting('app.user_id', true)::UUID
    )
    WITH CHECK (
        is_superadmin() OR organization_id = current_tenant_id()
    );

-- Policy for user_volunteer_applications table
CREATE POLICY tenant_isolation_user_volunteer_applications ON user_volunteer_applications
    FOR ALL
    USING (
        is_superadmin() OR
        organization_id = current_tenant_id() OR
        user_id = current_setting('app.user_id', true)::UUID
    )
    WITH CHECK (
        is_superadmin() OR organization_id = current_tenant_id()
    );

-- Policy for post_media_items table
CREATE POLICY tenant_isolation_post_media ON post_media_items
    FOR ALL
    USING (
        is_superadmin() OR
        EXISTS (
            SELECT 1 FROM posts p 
            WHERE p.id = post_media_items.post_id 
            AND p.organization_id = current_tenant_id()
        )
    )
    WITH CHECK (
        is_superadmin() OR
        EXISTS (
            SELECT 1 FROM posts p 
            WHERE p.id = post_media_items.post_id 
            AND p.organization_id = current_tenant_id()
        )
    );

-- Policy for event_media_items table
CREATE POLICY tenant_isolation_event_media ON event_media_items
    FOR ALL
    USING (
        is_superadmin() OR
        EXISTS (
            SELECT 1 FROM events e 
            WHERE e.id = event_media_items.event_id 
            AND e.organization_id = current_tenant_id()
        )
    )
    WITH CHECK (
        is_superadmin() OR
        EXISTS (
            SELECT 1 FROM events e 
            WHERE e.id = event_media_items.event_id 
            AND e.organization_id = current_tenant_id()
        )
    );

-- Policy for resource_files table
CREATE POLICY tenant_isolation_resource_files ON resource_files
    FOR ALL
    USING (
        is_superadmin() OR
        EXISTS (
            SELECT 1 FROM resources r 
            WHERE r.id = resource_files.resource_id 
            AND r.organization_id = current_tenant_id()
        )
    )
    WITH CHECK (
        is_superadmin() OR
        EXISTS (
            SELECT 1 FROM resources r 
            WHERE r.id = resource_files.resource_id 
            AND r.organization_id = current_tenant_id()
        )
    );

-- Create indexes to optimize RLS policy checks
CREATE INDEX IF NOT EXISTS idx_events_org_id_status ON events(organization_id, status);
CREATE INDEX IF NOT EXISTS idx_posts_org_id_status ON posts(organization_id, status);
CREATE INDEX IF NOT EXISTS idx_resources_org_id_status ON resources(organization_id, status);
CREATE INDEX IF NOT EXISTS idx_event_registrations_event_id ON event_registrations(event_id);
CREATE INDEX IF NOT EXISTS idx_event_registrations_user_id ON event_registrations(user_id);
CREATE INDEX IF NOT EXISTS idx_event_volunteer_applications_org_id ON event_volunteer_applications(organization_id);
CREATE INDEX IF NOT EXISTS idx_event_volunteer_applications_user_id ON event_volunteer_applications(user_id);

-- Add comment explaining the RLS setup
COMMENT ON SCHEMA public IS 'Row Level Security is enabled on tenant-scoped tables. Set app.tenant_id in the session to filter data by organization.';