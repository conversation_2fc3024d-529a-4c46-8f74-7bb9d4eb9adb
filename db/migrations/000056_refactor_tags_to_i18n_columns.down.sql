-- Revert the refactor of tags from i18n columns back to separate language records
-- This migration reverses the changes made in the up migration

-- Since the up migration is a no-op placeholder, the down migration is also a no-op
-- The actual tag system changes are handled by migration 070 and its corresponding down migration

-- Note: This migration serves as a placeholder to maintain migration sequence integrity.
