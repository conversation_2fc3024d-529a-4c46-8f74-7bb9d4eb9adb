-- Refactor tags to use i18n columns instead of separate language records
-- This migration transforms the old language-based tag structure to i18n columns
-- Note: This is an intermediate migration that will be superseded by the unified tags system in migration 070

-- This migration is intentionally kept minimal as it will be superseded by migration 070
-- which implements the complete unified tags system. We just ensure the basic structure
-- exists for migration 070 to work with.

-- Note: The actual tag refactoring is handled by migration 070 (refactor_tags_unified_system)
-- This migration serves as a placeholder to maintain migration sequence integrity.
