-- Create volunteer_status table
CREATE TABLE volunteer_status (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    is_active BOOLEAN NOT NULL DEFAULT true,
    skills TEXT[] DEFAULT '{}',
    availability JSONB DEFAULT '{}',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, organization_id)
);

-- Add indexes
CREATE INDEX idx_volunteer_status_user_id ON volunteer_status(user_id);
CREATE INDEX idx_volunteer_status_organization_id ON volunteer_status(organization_id);
CREATE INDEX idx_volunteer_status_active ON volunteer_status(organization_id, is_active);

-- Add trigger to update updated_at
CREATE OR REPLACE FUNCTION update_volunteer_status_updated_at()
<PERSON><PERSON><PERSON>NS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_volunteer_status_updated_at
    BEFORE UPDATE ON volunteer_status
    FOR EACH ROW
    EXECUTE FUNCTION update_volunteer_status_updated_at();