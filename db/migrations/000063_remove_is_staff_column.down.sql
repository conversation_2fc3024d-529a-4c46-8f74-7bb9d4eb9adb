-- Re-add is_staff column
ALTER TABLE users ADD COLUMN is_staff BOOLEAN DEFAULT FALSE;

-- Populate based on platform_role
UPDATE users SET is_staff = true WHERE platform_role IN ('staff', 'super_admin');

-- Remove the NOT NULL constraint
ALTER TABLE users ALTER COLUMN platform_role DROP NOT NULL;

-- Remove the default
ALTER TABLE users ALTER COLUMN platform_role DROP DEFAULT;

-- Drop the index
DROP INDEX IF EXISTS idx_users_platform_role;