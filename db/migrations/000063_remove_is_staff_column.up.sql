-- First populate platform_role from existing is_staff values
UPDATE users 
SET platform_role = CASE 
    WHEN email = '<EMAIL>' THEN 'superadmin'
    WHEN is_staff = true THEN 'admin'
    ELSE 'member'
END
WHERE platform_role IS NULL;

-- Add index for performance
CREATE INDEX IF NOT EXISTS idx_users_platform_role ON users(platform_role);

-- Ensure all users have a platform_role
ALTER TABLE users ALTER COLUMN platform_role SET NOT NULL;
ALTER TABLE users ALTER COLUMN platform_role SET DEFAULT 'member';

-- BREAKING: Remove is_staff column completely
ALTER TABLE users DROP COLUMN IF EXISTS is_staff;