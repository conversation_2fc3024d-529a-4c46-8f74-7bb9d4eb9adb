-- This migration maintains the current hyphen format consistently
-- Since the system now standardizes on hyphen format (en-US, zh-HK, zh-CN)
-- this down migration should not revert to underscore format

-- Note: This is intentionally a no-op migration as reverting would break
-- the established hyphen-format standard. If a true rollback is needed,
-- consider the impact on the entire system's language code consistency.