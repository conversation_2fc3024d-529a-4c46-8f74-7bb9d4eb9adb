-- Standardize language codes to BCP 47 format
-- en → en-US, zh_HK → zh-HK, zh_CN → zh-CN

-- Update users table
UPDATE users SET interface_language = 'en-US' WHERE interface_language = 'en';
UPDATE users SET communication_language = 'en-US' WHERE communication_language = 'en';

-- Note: zh_HK and zh_CN with underscores don't exist in current data
-- as the system already uses zh-HK format in practice

-- Note: Tag-related tables (post_tag_translations, event_tag_translations) were removed
-- in migration 070 (refactor_tags_unified_system) and replaced with a unified tags system
-- that already uses the correct BCP 47 format (name_en_us, name_zh_hk, name_zh_cn)

-- Note: The verification system uses enums (verification_type_enum) and specific tables
-- for each verification type, but there's no general verification_types table with
-- language codes that needs updating.