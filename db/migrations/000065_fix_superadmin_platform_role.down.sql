-- Revert the platform_role constraint to the old values
ALTER TABLE users DROP CONSTRAINT IF EXISTS check_platform_role;

ALTER TABLE users ADD CONSTRAINT check_platform_role 
CHECK (platform_role IN ('member', 'admin', 'superadmin'));

-- Revert the platform_role values
UPDATE users SET platform_role = 'member' WHERE platform_role = 'user';
UPDATE users SET platform_role = 'admin' WHERE platform_role = 'staff';
UPDATE users SET platform_role = 'superadmin' WHERE platform_role = 'super_admin';