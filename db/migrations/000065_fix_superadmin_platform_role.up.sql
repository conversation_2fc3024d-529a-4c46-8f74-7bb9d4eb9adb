-- Fix the platform_role constraint to use consistent naming with underscores
-- This migration needs to handle the constraint change carefully

-- First, drop the existing constraint
ALTER TABLE users DROP CONSTRAINT IF EXISTS check_platform_role;

-- Update any existing 'member' values to 'user' for consistency
UPDATE users SET platform_role = 'user' WHERE platform_role = 'member';

-- Update any existing 'admin' values to 'staff' for consistency
UPDATE users SET platform_role = 'staff' WHERE platform_role = 'admin';

-- Update any existing 'superadmin' values to 'super_admin' for consistency
UPDATE users SET platform_role = 'super_admin' WHERE platform_role = 'superadmin';

-- Specifically <NAME_EMAIL> has the correct role
UPDATE users SET platform_role = 'super_admin' WHERE email = '<EMAIL>';

-- Add the new constraint with consistent naming (using underscores)
ALTER TABLE users ADD CONSTRAINT check_platform_role 
CHECK (platform_role IN ('user', 'staff', 'super_admin'));