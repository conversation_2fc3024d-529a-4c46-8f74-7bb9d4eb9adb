-- Revert platform_role back to <PERSON><PERSON><PERSON>R type

-- Step 1: Add a temporary VARCHAR column
ALTER TABLE users ADD COLUMN platform_role_temp VARCHAR(50);

-- Step 2: Copy data from enum to varchar
UPDATE users 
SET platform_role_temp = platform_role::text;

-- Step 3: Drop the enum column
ALTER TABLE users DROP COLUMN platform_role;

-- Step 4: Rename temp column
ALTER TABLE users RENAME COLUMN platform_role_temp TO platform_role;

-- Step 5: Re-add constraints
ALTER TABLE users ALTER COLUMN platform_role SET NOT NULL;
ALTER TABLE users ALTER COLUMN platform_role SET DEFAULT 'user';

-- Step 6: Re-add check constraint
ALTER TABLE users ADD CONSTRAINT check_platform_role 
CHECK (platform_role IN ('user', 'staff', 'super_admin'));

-- Step 7: Re-create index
CREATE INDEX IF NOT EXISTS idx_users_platform_role ON users(platform_role);

-- Step 8: Drop the enum type
DROP TYPE platform_role_enum;