-- Convert platform_role to a proper enum type for better type safety and performance
-- This migration ensures consistency between database schema and application code

-- Step 1: Create the enum type
CREATE TYPE platform_role_enum AS ENUM ('user', 'staff', 'super_admin');

-- Step 2: Add a temporary column with the enum type
ALTER TABLE users ADD COLUMN platform_role_new platform_role_enum;

-- Step 3: Copy data from the old column to the new column, with default handling
UPDATE users 
SET platform_role_new = 
    CASE 
        WHEN platform_role = 'user' THEN 'user'::platform_role_enum
        WHEN platform_role = 'staff' THEN 'staff'::platform_role_enum
        WHEN platform_role = 'super_admin' THEN 'super_admin'::platform_role_enum
        -- Handle any legacy values that might exist
        WHEN platform_role = 'member' THEN 'user'::platform_role_enum
        WHEN platform_role = 'admin' THEN 'staff'::platform_role_enum
        WHEN platform_role = 'superadmin' THEN 'super_admin'::platform_role_enum
        ELSE 'user'::platform_role_enum -- Default to user for any unknown values
    END;

-- Step 4: Drop the old column and constraint
ALTER TABLE users DROP CONSTRAINT IF EXISTS check_platform_role;
ALTER TABLE users DROP COLUMN platform_role;

-- Step 5: Rename the new column to the original name
ALTER TABLE users RENAME COLUMN platform_role_new TO platform_role;

-- Step 6: Set NOT NULL constraint and default value
ALTER TABLE users ALTER COLUMN platform_role SET NOT NULL;
ALTER TABLE users ALTER COLUMN platform_role SET DEFAULT 'user'::platform_role_enum;

-- Step 7: Re-create the index
CREATE INDEX IF NOT EXISTS idx_users_platform_role ON users(platform_role);

-- Step 8: Add comment for documentation
COMMENT ON TYPE platform_role_enum IS 'Platform-level roles: user (regular user), staff (platform administrator), super_admin (super administrator)';
COMMENT ON COLUMN users.platform_role IS 'User platform-level role determining system-wide permissions';