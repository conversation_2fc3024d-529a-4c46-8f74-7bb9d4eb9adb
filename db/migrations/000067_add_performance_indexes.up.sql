-- Add performance optimization indexes
-- These indexes are designed to improve query performance based on analysis of slow queries

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Event performance indexes
-- Note: Using CREATE INDEX without CONCURRENTLY for test environments
-- Production deployments should run these with <PERSON><PERSON><PERSON>RENTLY manually if needed
CREATE INDEX IF NOT EXISTS idx_events_org_status_start 
ON events(organization_id, status, start_time DESC);

CREATE INDEX IF NOT EXISTS idx_event_registrations_event_status 
ON event_registrations(event_id, status);

CREATE INDEX IF NOT EXISTS idx_event_event_tags_tag 
ON event_event_tags(event_tag_id, event_id);

-- Text search optimization
CREATE INDEX IF NOT EXISTS idx_events_title_trgm 
ON events USING gin(title gin_trgm_ops);

CREATE INDEX IF NOT EXISTS idx_events_location_trgm 
ON events USING gin(location_full_address gin_trgm_ops);

-- Post performance indexes
CREATE INDEX IF NOT EXISTS idx_posts_org_status_published 
ON posts(organization_id, status, published_at DESC);

CREATE INDEX IF NOT EXISTS idx_posts_title_trgm 
ON posts USING gin(title gin_trgm_ops);

CREATE INDEX IF NOT EXISTS idx_post_post_tags_tag 
ON post_post_tags(tag_id, post_id);

-- User organization membership optimization
CREATE INDEX IF NOT EXISTS idx_user_org_memberships_user_active 
ON user_organization_memberships(user_id, is_active)
WHERE is_active = TRUE;

-- Add comments explaining the indexes
COMMENT ON INDEX idx_events_org_status_start IS 'Optimizes event listing queries filtered by organization, status, and date';
COMMENT ON INDEX idx_event_registrations_event_status IS 'Optimizes event registration count queries';
COMMENT ON INDEX idx_events_title_trgm IS 'Optimizes text search on event titles';
COMMENT ON INDEX idx_posts_org_status_published IS 'Optimizes post listing queries';
COMMENT ON INDEX idx_user_org_memberships_user_active IS 'Optimizes active organization lookups for users';