-- Production version of performance indexes with CO<PERSON><PERSON>RENTLY
-- Run this script manually in production to create indexes without blocking table access
-- 
-- Usage: psql -U user -d database -f 000065_add_performance_indexes_production.sql

-- Enable required extensions (if not already enabled)
CREATE EXTENSION IF NOT EXISTS pg_trgm;

-- Drop and recreate indexes with CONC<PERSON>RENTLY for zero-downtime deployment
-- Event performance indexes
DROP INDEX IF EXISTS idx_events_org_status_start;
CREATE INDEX CONCURRENTLY idx_events_org_status_start 
ON events(organization_id, status, start_time DESC);

DROP INDEX IF EXISTS idx_event_registrations_event_status;
CREATE INDEX CONCURRENTLY idx_event_registrations_event_status 
ON event_registrations(event_id, status);

DROP INDEX IF EXISTS idx_event_event_tags_tag;
CREATE INDEX CONCURRENTLY idx_event_event_tags_tag 
ON event_event_tags(event_tag_id, event_id);

-- Text search optimization
DROP INDEX IF EXISTS idx_events_title_trgm;
CREATE INDEX CONCURRENTLY idx_events_title_trgm 
ON events USING gin(title gin_trgm_ops);

DROP INDEX IF EXISTS idx_events_location_trgm;
CREATE INDEX CONCURRENTLY idx_events_location_trgm 
ON events USING gin(location_full_address gin_trgm_ops);

-- Post performance indexes
DROP INDEX IF EXISTS idx_posts_org_status_published;
CREATE INDEX CONCURRENTLY idx_posts_org_status_published 
ON posts(organization_id, status, published_at DESC);

DROP INDEX IF EXISTS idx_posts_title_trgm;
CREATE INDEX CONCURRENTLY idx_posts_title_trgm 
ON posts USING gin(title gin_trgm_ops);

DROP INDEX IF EXISTS idx_post_post_tags_tag;
CREATE INDEX CONCURRENTLY idx_post_post_tags_tag 
ON post_post_tags(tag_id, post_id);

-- User organization membership optimization
DROP INDEX IF EXISTS idx_user_org_memberships_user_active;
CREATE INDEX CONCURRENTLY idx_user_org_memberships_user_active 
ON user_organization_memberships(user_id, is_active)
WHERE is_active = TRUE;

-- Add comments explaining the indexes
COMMENT ON INDEX idx_events_org_status_start IS 'Optimizes event listing queries filtered by organization, status, and date';
COMMENT ON INDEX idx_event_registrations_event_status IS 'Optimizes event registration count queries';
COMMENT ON INDEX idx_events_title_trgm IS 'Optimizes text search on event titles';
COMMENT ON INDEX idx_posts_org_status_published IS 'Optimizes post listing queries';
COMMENT ON INDEX idx_user_org_memberships_user_active IS 'Optimizes active organization lookups for users';