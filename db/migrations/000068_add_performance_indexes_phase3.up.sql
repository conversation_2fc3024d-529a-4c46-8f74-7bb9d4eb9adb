-- Phase 3 Performance Indexes from Query Optimization Report
-- These indexes address the performance issues identified in the optimization analysis

-- 1. Composite index for event filtering queries
CREATE INDEX IF NOT EXISTS idx_events_org_status_start 
    ON events(organization_id, status, start_time DESC);

-- 2. Index for event registrations counts (optimized for GetEventRegistrationCounts)
CREATE INDEX IF NOT EXISTS idx_event_registrations_event_status 
    ON event_registrations(event_id, status);

-- 3. Index for tag filtering 
CREATE INDEX IF NOT EXISTS idx_event_event_tags_tag 
    ON event_event_tags(event_tag_id, event_id);

-- 4. Index for posts search
CREATE INDEX IF NOT EXISTS idx_posts_org_status_published 
    ON posts(organization_id, status, published_at DESC);

-- 5. Text search indexes for ILIKE queries (requires pg_trgm extension)
CREATE EXTENSION IF NOT EXISTS pg_trgm;

CREATE INDEX IF NOT EXISTS idx_events_title_trgm 
    ON events USING gin(title gin_trgm_ops);

CREATE INDEX IF NOT EXISTS idx_events_location_trgm 
    ON events USING gin(location_full_address gin_trgm_ops);

CREATE INDEX IF NOT EXISTS idx_posts_title_trgm 
    ON posts USING gin(title gin_trgm_ops);

-- 6. Index for user organization memberships (active lookups)
CREATE INDEX IF NOT EXISTS idx_user_org_memberships_user_active
    ON user_organization_memberships(user_id, is_active)
    WHERE is_active = true;

-- 7. Composite index for event statistics queries
CREATE INDEX IF NOT EXISTS idx_events_status_dates
    ON events(status, start_time, end_time)
    WHERE status = 'published';

-- 8. Index for volunteer applications by event and status
CREATE INDEX IF NOT EXISTS idx_volunteer_apps_event_status
    ON event_volunteer_applications(event_id, status);

-- 9. Index for post tags relationship
CREATE INDEX IF NOT EXISTS idx_post_post_tags_post
    ON post_post_tags(post_id, tag_id);

-- 10. Index for refresh tokens cleanup
CREATE INDEX IF NOT EXISTS idx_refresh_tokens_expires
    ON refresh_tokens(expires_at)
    WHERE is_revoked = false;