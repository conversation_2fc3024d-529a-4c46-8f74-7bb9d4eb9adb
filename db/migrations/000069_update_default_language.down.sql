-- Revert default language back to 'en' 
ALTER TABLE users 
    ALTER COLUMN interface_language SET DEFAULT 'en',
    ALTER COLUMN communication_language SET DEFAULT 'en';

-- Revert existing users who were changed from 'en' to 'zh-HK'
-- Note: This is a best-effort revert and may not be 100% accurate
-- if users have changed their language preferences after the migration
UPDATE users 
SET interface_language = 'en' 
WHERE interface_language = 'zh-HK';

UPDATE users 
SET communication_language = 'en' 
WHERE communication_language = 'zh-HK';