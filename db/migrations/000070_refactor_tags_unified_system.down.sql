-- Revert unified tag system back to separate post_tags and event_tags

-- Drop the new junction tables
DROP TABLE IF EXISTS "event_tags";
DROP TABLE IF EXISTS "post_tags";

-- Drop the unified tags table
DROP TABLE IF EXISTS "tags";

-- Drop the approval status enum
DROP TYPE IF EXISTS tag_approval_status;

-- Recreate the old post_tags table structure
CREATE TABLE "post_tags" (
  "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  "tag_name" VARCHAR(255) NOT NULL,
  "language_code" VARCHAR(10) NOT NULL,
  "description" TEXT,
  "created_at" TIMESTAMPTZ NOT NULL DEFAULT (now()),
  "updated_at" TIMESTAMPTZ NOT NULL DEFAULT (now()),
  UNIQUE ("tag_name", "language_code")
);

-- Recreate the old post_post_tags junction table
CREATE TABLE "post_post_tags" (
  "post_id" uuid NOT NULL,
  "tag_id" uuid NOT NULL,
  "created_at" TIMESTAMPTZ NOT NULL DEFAULT (now()),
  PRIMARY KEY ("post_id", "tag_id")
);

ALTER TABLE "post_post_tags" ADD FOREIGN KEY ("post_id") REFERENCES "posts" ("id") ON DELETE CASCADE;
ALTER TABLE "post_post_tags" ADD FOREIGN KEY ("tag_id") REFERENCES "post_tags" ("id") ON DELETE CASCADE;

-- Recreate the old event_tags table structure
CREATE TABLE "event_tags" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "tag_name" VARCHAR(100) NOT NULL,
    "language_code" VARCHAR(10) NOT NULL,
    "description" TEXT,
    "created_by_user_id" UUID REFERENCES "users"("id"),
    "is_globally_approved" BOOLEAN NOT NULL DEFAULT TRUE,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE ("tag_name", "language_code")
);

CREATE INDEX ON "event_tags" ("language_code");
CREATE INDEX ON "event_tags" ("created_by_user_id");

-- Recreate the old event_event_tags junction table
CREATE TABLE "event_event_tags" (
    "event_id" UUID NOT NULL REFERENCES "events"("id") ON DELETE CASCADE,
    "event_tag_id" UUID NOT NULL REFERENCES "event_tags"("id") ON DELETE CASCADE,
    PRIMARY KEY ("event_id", "event_tag_id")
);

-- Apply triggers to old tables
CREATE TRIGGER set_timestamp_post_tags
BEFORE UPDATE ON "post_tags"
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

CREATE TRIGGER set_timestamp_event_tags
BEFORE UPDATE ON "event_tags"
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- Migrate data back from unified tags to separate tables
-- Migrate post tags
INSERT INTO post_tags (id, tag_name, language_code, description, created_at, updated_at)
SELECT 
    gen_random_uuid(),
    t.name_en_us,
    'en',
    t.description_en_us,
    t.created_at,
    t.updated_at
FROM tags t
WHERE EXISTS (
    SELECT 1 FROM post_tags pt WHERE pt.tag_id = t.id
)
UNION ALL
SELECT 
    gen_random_uuid(),
    t.name_zh_hk,
    'zh_HK',
    t.description_zh_hk,
    t.created_at,
    t.updated_at
FROM tags t
WHERE EXISTS (
    SELECT 1 FROM post_tags pt WHERE pt.tag_id = t.id
)
UNION ALL
SELECT 
    gen_random_uuid(),
    t.name_zh_cn,
    'zh_CN',
    t.description_zh_cn,
    t.created_at,
    t.updated_at
FROM tags t
WHERE EXISTS (
    SELECT 1 FROM post_tags pt WHERE pt.tag_id = t.id
);

-- Migrate event tags
INSERT INTO event_tags (id, tag_name, language_code, description, created_by_user_id, is_globally_approved, created_at, updated_at)
SELECT 
    gen_random_uuid(),
    t.name_en_us,
    'en',
    t.description_en_us,
    t.created_by_user_id,
    CASE WHEN t.approval_status = 'approved' THEN TRUE ELSE FALSE END,
    t.created_at,
    t.updated_at
FROM tags t
WHERE EXISTS (
    SELECT 1 FROM event_tags et WHERE et.tag_id = t.id
)
UNION ALL
SELECT 
    gen_random_uuid(),
    t.name_zh_hk,
    'zh_HK',
    t.description_zh_hk,
    t.created_by_user_id,
    CASE WHEN t.approval_status = 'approved' THEN TRUE ELSE FALSE END,
    t.created_at,
    t.updated_at
FROM tags t
WHERE EXISTS (
    SELECT 1 FROM event_tags et WHERE et.tag_id = t.id
)
UNION ALL
SELECT 
    gen_random_uuid(),
    t.name_zh_cn,
    'zh_CN',
    t.description_zh_cn,
    t.created_by_user_id,
    CASE WHEN t.approval_status = 'approved' THEN TRUE ELSE FALSE END,
    t.created_at,
    t.updated_at
FROM tags t
WHERE EXISTS (
    SELECT 1 FROM event_tags et WHERE et.tag_id = t.id
);

-- Note: Junction table relationships cannot be perfectly restored as we lose the language-specific mappings
-- This is a best-effort restoration

-- Drop the unified tag system
DROP TABLE IF EXISTS "event_tags";
DROP TABLE IF EXISTS "post_tags";
DROP TABLE IF EXISTS "tags";
DROP TYPE IF EXISTS tag_approval_status;