-- Refactor tags to unified system with approval workflow
-- This migration creates a new unified tag system with:
-- 1. Tags supporting 3 locales (en-US, zh-HK, zh-CN) - all required
-- 2. Approval workflow for org-created tags
-- 3. System-wide and org-specific tags

-- Create approval status enum if it doesn't exist
DO $$ BEGIN
    CREATE TYPE tag_approval_status AS ENUM ('pending', 'approved', 'rejected');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create the new unified tags table
CREATE TABLE IF NOT EXISTS "tags" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Localized names (all required)
    "name_en_us" VARCHAR(255) NOT NULL,
    "name_zh_hk" VARCHAR(255) NOT NULL,
    "name_zh_cn" VARCHAR(255) NOT NULL,
    
    -- Localized descriptions (optional)
    "description_en_us" TEXT,
    "description_zh_hk" TEXT,
    "description_zh_cn" TEXT,
    
    -- Organization scope (NULL for system-wide tags)
    "organization_id" UUID REFERENCES "organizations"("id") ON DELETE CASCADE,
    
    -- Approval workflow
    "approval_status" tag_approval_status NOT NULL DEFAULT 'approved',
    
    -- Tracking fields
    "created_by_user_id" UUID NOT NULL REFERENCES "users"("id"),
    "approved_by_user_id" UUID REFERENCES "users"("id"),
    "approved_at" TIMESTAMPTZ,
    "rejection_reason" TEXT,
    
    -- Timestamps
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Unique constraint: tag names must be unique within their scope
    -- System-wide tags (org_id NULL) must be globally unique
    -- Org-specific tags must be unique within the organization
    CONSTRAINT "unique_tag_names_per_scope" UNIQUE (
        "organization_id",
        "name_en_us",
        "name_zh_hk",
        "name_zh_cn"
    )
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS "idx_tags_organization_id" ON "tags" ("organization_id");
CREATE INDEX IF NOT EXISTS "idx_tags_approval_status" ON "tags" ("approval_status");
CREATE INDEX IF NOT EXISTS "idx_tags_created_by_user_id" ON "tags" ("created_by_user_id");
CREATE INDEX IF NOT EXISTS "idx_tags_approved_by_user_id" ON "tags" ("approved_by_user_id");

-- Add trigger to update 'updated_at' column
DROP TRIGGER IF EXISTS set_timestamp_tags ON tags;
CREATE TRIGGER set_timestamp_tags
BEFORE UPDATE ON "tags"
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- Migrate existing post tags to new system
-- Check if post_tags table exists and has data
DO $$
DECLARE
    superadmin_id UUID;
BEGIN
    -- Get a super admin user ID for created_by_user_id
    SELECT id INTO superadmin_id FROM users WHERE platform_role = 'super_admin' LIMIT 1;
    
    -- If no super admin exists, create a system user
    IF superadmin_id IS NULL THEN
        INSERT INTO users (phone, display_name, platform_role, enable_email_notifications, enable_sms_notifications)
        VALUES ('+00000000000', 'System Migration User', 'super_admin', false, false)
        RETURNING id INTO superadmin_id;
    END IF;
    
    -- Check if post_tags exists and which structure it has
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'post_tags' AND table_schema = 'public') THEN
        -- Check if it has language_code column (migration 037 structure)
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'post_tags' AND column_name = 'language_code' AND table_schema = 'public') THEN
            -- Migrate from language_code structure
            WITH grouped_tags AS (
                SELECT 
                    MIN(CASE WHEN language_code = 'en' THEN tag_name END) as name_en,
                    MIN(CASE WHEN language_code = 'zh_HK' THEN tag_name END) as name_zh_hk,
                    MIN(CASE WHEN language_code = 'zh_CN' THEN tag_name END) as name_zh_cn,
                    MIN(CASE WHEN language_code = 'en' THEN description END) as desc_en,
                    MIN(CASE WHEN language_code = 'zh_HK' THEN description END) as desc_zh_hk,
                    MIN(CASE WHEN language_code = 'zh_CN' THEN description END) as desc_zh_cn
                FROM post_tags
                GROUP BY 
                    -- Group by English tag name to consolidate translations
                    (SELECT tag_name FROM post_tags pt2 
                     WHERE pt2.language_code = 'en' 
                     AND pt2.tag_name IN (SELECT tag_name FROM post_tags pt3 WHERE pt3.id = post_tags.id)
                     LIMIT 1)
                HAVING 
                    MIN(CASE WHEN language_code = 'en' THEN tag_name END) IS NOT NULL
            )
            INSERT INTO tags (
                name_en_us,
                name_zh_hk,
                name_zh_cn,
                description_en_us,
                description_zh_hk,
                description_zh_cn,
                organization_id,
                approval_status,
                created_by_user_id,
                created_at,
                updated_at
            )
            SELECT
                COALESCE(name_en, 'Translation Missing'),
                COALESCE(name_zh_hk, name_en, 'Translation Missing'),
                COALESCE(name_zh_cn, name_en, 'Translation Missing'),
                desc_en,
                desc_zh_hk,
                desc_zh_cn,
                NULL, -- System-wide tags
                'approved'::tag_approval_status,
                superadmin_id,
                CURRENT_TIMESTAMP,
                CURRENT_TIMESTAMP
            FROM grouped_tags;
            
        -- Check if it has name_en/name_zh columns (migration 026 structure)
        ELSIF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'post_tags' AND column_name = 'name_en' AND table_schema = 'public') THEN
            -- Migrate from name_en/name_zh structure
            INSERT INTO tags (
                name_en_us,
                name_zh_hk,
                name_zh_cn,
                organization_id,
                approval_status,
                created_by_user_id,
                created_at,
                updated_at
            )
            SELECT
                pt.name_en,
                pt.name_zh,
                pt.name_zh,
                NULL, -- System-wide tag
                'approved'::tag_approval_status,
                superadmin_id,
                pt.created_at,
                pt.updated_at
            FROM post_tags pt
            WHERE NOT EXISTS (
                SELECT 1 FROM tags t WHERE t.name_en_us = pt.name_en
            );
        END IF;
    END IF;
END $$;

-- Migrate existing event tags to new system (only if event_tags exists)
DO $$
DECLARE
    superadmin_id UUID;
BEGIN
    -- Get super admin ID
    SELECT id INTO superadmin_id FROM users WHERE platform_role = 'super_admin' LIMIT 1;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'event_tags' AND table_schema = 'public') THEN
        WITH grouped_event_tags AS (
            SELECT 
                MIN(CASE WHEN language_code = 'en' THEN tag_name END) as name_en,
                MIN(CASE WHEN language_code = 'zh_HK' THEN tag_name END) as name_zh_hk,
                MIN(CASE WHEN language_code = 'zh_CN' THEN tag_name END) as name_zh_cn,
                MIN(CASE WHEN language_code = 'en' THEN description END) as desc_en,
                MIN(CASE WHEN language_code = 'zh_HK' THEN description END) as desc_zh_hk,
                MIN(CASE WHEN language_code = 'zh_CN' THEN description END) as desc_zh_cn,
                BOOL_OR(is_globally_approved) as is_approved
            FROM event_tags
            GROUP BY 
                -- Group by English tag name to consolidate translations
                (SELECT tag_name FROM event_tags et2 
                 WHERE et2.language_code = 'en' 
                 AND et2.tag_name IN (SELECT tag_name FROM event_tags et3 WHERE et3.id = event_tags.id)
                 LIMIT 1)
            HAVING 
                MIN(CASE WHEN language_code = 'en' THEN tag_name END) IS NOT NULL
        )
        INSERT INTO tags (
            name_en_us,
            name_zh_hk,
            name_zh_cn,
            description_en_us,
            description_zh_hk,
            description_zh_cn,
            organization_id,
            approval_status,
            created_by_user_id,
            created_at,
            updated_at
        )
        SELECT
            COALESCE(name_en, 'Translation Missing'),
            COALESCE(name_zh_hk, name_en, 'Translation Missing'),
            COALESCE(name_zh_cn, name_en, 'Translation Missing'),
            desc_en,
            desc_zh_hk,
            desc_zh_cn,
            NULL, -- System-wide tags
            CASE WHEN is_approved THEN 'approved'::tag_approval_status ELSE 'pending'::tag_approval_status END,
            superadmin_id,
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        FROM grouped_event_tags
        WHERE NOT EXISTS (
            SELECT 1 FROM tags t 
            WHERE t.name_en_us = COALESCE(grouped_event_tags.name_en, 'Translation Missing')
        );
    END IF;
END $$;

-- Create new junction tables with temporary names to avoid conflicts
CREATE TABLE "post_tags_new" (
    "post_id" UUID NOT NULL REFERENCES "posts"("id") ON DELETE CASCADE,
    "tag_id" UUID NOT NULL REFERENCES "tags"("id") ON DELETE CASCADE,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY ("post_id", "tag_id")
);

CREATE INDEX "idx_post_tags_new_post_id" ON "post_tags_new" ("post_id");
CREATE INDEX "idx_post_tags_new_tag_id" ON "post_tags_new" ("tag_id");

CREATE TABLE "event_tags_new" (
    "event_id" UUID NOT NULL REFERENCES "events"("id") ON DELETE CASCADE,
    "tag_id" UUID NOT NULL REFERENCES "tags"("id") ON DELETE CASCADE,
    "created_at" TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY ("event_id", "tag_id")
);

CREATE INDEX "idx_event_tags_new_event_id" ON "event_tags_new" ("event_id");
CREATE INDEX "idx_event_tags_new_tag_id" ON "event_tags_new" ("tag_id");

-- Migrate post-tag relationships
-- Need to handle both possible structures
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'post_post_tags' AND table_schema = 'public') THEN
        -- Check which structure we have for post_tags
        IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'post_tags' AND column_name = 'language_code' AND table_schema = 'public') THEN
            -- language_code structure - need to join on tag_name/language_code
            INSERT INTO post_tags_new (post_id, tag_id, created_at)
            SELECT DISTINCT
                ppt.post_id,
                t.id,
                ppt.created_at
            FROM post_post_tags ppt
            JOIN post_tags pt_old ON pt_old.id = ppt.tag_id
            JOIN tags t ON (
                (pt_old.language_code = 'en' AND t.name_en_us = pt_old.tag_name) OR
                (pt_old.language_code = 'zh_HK' AND t.name_zh_hk = pt_old.tag_name) OR
                (pt_old.language_code = 'zh_CN' AND t.name_zh_cn = pt_old.tag_name)
            )
            ON CONFLICT (post_id, tag_id) DO NOTHING;
        ELSIF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'post_tags' AND column_name = 'name_en' AND table_schema = 'public') THEN
            -- name_en/name_zh structure
            INSERT INTO post_tags_new (post_id, tag_id, created_at)
            SELECT DISTINCT
                ppt.post_id,
                t.id,
                ppt.created_at
            FROM post_post_tags ppt
            JOIN post_tags pt_old ON ppt.tag_id = pt_old.id
            JOIN tags t ON t.name_en_us = pt_old.name_en
            ON CONFLICT (post_id, tag_id) DO NOTHING;
        END IF;
    END IF;
END $$;

-- Migrate event-tag relationships
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'event_event_tags' AND table_schema = 'public') THEN
        INSERT INTO event_tags_new (event_id, tag_id, created_at)
        SELECT DISTINCT
            eet.event_id,
            t.id,
            CURRENT_TIMESTAMP
        FROM event_event_tags eet
        JOIN event_tags et_old ON et_old.id = eet.event_tag_id
        JOIN tags t ON (
            (et_old.language_code = 'en' AND t.name_en_us = et_old.tag_name) OR
            (et_old.language_code = 'zh_HK' AND t.name_zh_hk = et_old.tag_name) OR
            (et_old.language_code = 'zh_CN' AND t.name_zh_cn = et_old.tag_name)
        )
        ON CONFLICT (event_id, tag_id) DO NOTHING;
    END IF;
END $$;

-- Drop old tables
DROP TABLE IF EXISTS "post_post_tags";
DROP TABLE IF EXISTS "post_tags";
DROP TABLE IF EXISTS "event_event_tags";
DROP TABLE IF EXISTS "event_tags";

-- Rename new tables to final names
ALTER TABLE "post_tags_new" RENAME TO "post_tags";
ALTER TABLE "event_tags_new" RENAME TO "event_tags";

-- Rename indexes
ALTER INDEX "idx_post_tags_new_post_id" RENAME TO "idx_post_tags_post_id";
ALTER INDEX "idx_post_tags_new_tag_id" RENAME TO "idx_post_tags_tag_id";
ALTER INDEX "idx_event_tags_new_event_id" RENAME TO "idx_event_tags_event_id";
ALTER INDEX "idx_event_tags_new_tag_id" RENAME TO "idx_event_tags_tag_id";