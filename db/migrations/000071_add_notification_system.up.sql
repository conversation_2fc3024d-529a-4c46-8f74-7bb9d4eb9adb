-- Add notification system tables
-- This migration creates tables for:
-- 1. Notifications - core notification data
-- 2. Notification preferences - user settings per channel
-- 3. Push tokens - device tokens for push notifications
-- 4. Notification templates - reusable message templates
-- 5. Notification logs - delivery tracking and audit trail

-- Create notification channel enum
CREATE TYPE notification_channel AS ENUM (
    'push',        -- Push notifications via Expo
    'websocket',   -- Real-time in-app notifications
    'whatsapp',    -- WhatsApp messages via Twi<PERSON>
    'sms'          -- SMS messages via <PERSON><PERSON><PERSON>
);

-- Create notification type enum
CREATE TYPE notification_type AS ENUM (
    'event_reminder',       -- Event reminders
    'event_registration',   -- Event registration confirmations
    'event_update',         -- Event changes/cancellations
    'post_published',       -- New posts published
    'announcement',         -- Important announcements
    'volunteer_update',     -- Volunteer application updates
    'verification_update',  -- Verification status changes
    'system'               -- System notifications
);

-- Create notification status enum
CREATE TYPE notification_status AS ENUM (
    'pending',     -- Awaiting delivery
    'sent',        -- Successfully sent
    'delivered',   -- Confirmed delivered
    'failed',      -- Delivery failed
    'read'         -- User has read the notification
);

-- Create notifications table
CREATE TABLE "notifications" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "user_id" UUID NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
    "organization_id" UUID REFERENCES "organizations"("id") ON DELETE CASCADE,
    "type" notification_type NOT NULL,
    "channel" notification_channel NOT NULL,
    "title" VARCHAR(255) NOT NULL,
    "body" TEXT NOT NULL,
    "data" JSONB DEFAULT '{}',  -- Additional payload data
    "priority" VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    "read_at" TIMESTAMP,
    "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "scheduled_for" TIMESTAMP,  -- For scheduled notifications
    "expires_at" TIMESTAMP      -- Auto-delete after this time
);

-- Create notification preferences table
CREATE TABLE "notification_preferences" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "user_id" UUID NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
    "channel" notification_channel NOT NULL,
    "enabled" BOOLEAN NOT NULL DEFAULT true,
    "types" notification_type[] NOT NULL DEFAULT ARRAY[]::notification_type[],  -- Which types to receive on this channel
    "quiet_hours_start" TIME,  -- Do not disturb start time
    "quiet_hours_end" TIME,    -- Do not disturb end time
    "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, channel)
);

-- Create push tokens table
CREATE TABLE "push_tokens" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "user_id" UUID NOT NULL REFERENCES "users"("id") ON DELETE CASCADE,
    "token" TEXT NOT NULL UNIQUE,
    "device_type" VARCHAR(20) NOT NULL CHECK (device_type IN ('ios', 'android', 'web')),
    "device_id" VARCHAR(255),  -- Optional device identifier
    "device_name" VARCHAR(255), -- Optional device name
    "app_version" VARCHAR(50),
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "last_used_at" TIMESTAMP,
    "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create notification templates table
CREATE TABLE "notification_templates" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "organization_id" UUID REFERENCES "organizations"("id") ON DELETE CASCADE,  -- NULL for system templates
    "type" notification_type NOT NULL,
    "channel" notification_channel NOT NULL,
    "name" VARCHAR(255) NOT NULL,
    "title_template" VARCHAR(500) NOT NULL,  -- With placeholders like {{user_name}}
    "body_template" TEXT NOT NULL,
    "data_template" JSONB DEFAULT '{}',
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_by" UUID REFERENCES "users"("id"),
    "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(organization_id, type, channel, name)
);

-- Create notification logs table for delivery tracking
CREATE TABLE "notification_logs" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "notification_id" UUID REFERENCES "notifications"("id") ON DELETE CASCADE,
    "channel" notification_channel NOT NULL,
    "status" notification_status NOT NULL,
    "attempt_number" INTEGER NOT NULL DEFAULT 1,
    "sent_at" TIMESTAMP,
    "delivered_at" TIMESTAMP,
    "failed_at" TIMESTAMP,
    "error_message" TEXT,
    "provider_message_id" VARCHAR(255),  -- External provider's message ID
    "metadata" JSONB DEFAULT '{}',       -- Provider-specific metadata
    "created_at" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_org_id ON notifications(organization_id);
CREATE INDEX idx_notifications_created_at ON notifications(created_at DESC);
CREATE INDEX idx_notifications_scheduled_for ON notifications(scheduled_for) WHERE scheduled_for IS NOT NULL;
CREATE INDEX idx_notifications_unread ON notifications(user_id, created_at DESC) WHERE read_at IS NULL;

CREATE INDEX idx_push_tokens_user_id ON push_tokens(user_id);
CREATE INDEX idx_push_tokens_active ON push_tokens(token) WHERE is_active = true;

CREATE INDEX idx_notification_logs_notification_id ON notification_logs(notification_id);
CREATE INDEX idx_notification_logs_status ON notification_logs(status);

-- Add triggers for updated_at
CREATE TRIGGER update_notification_preferences_updated_at
    BEFORE UPDATE ON notification_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_push_tokens_updated_at
    BEFORE UPDATE ON push_tokens
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notification_templates_updated_at
    BEFORE UPDATE ON notification_templates
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add default notification preferences for existing users
INSERT INTO notification_preferences (user_id, channel, enabled, types)
SELECT 
    u.id,
    'push',
    true,
    ARRAY['event_reminder', 'event_registration', 'event_update', 'announcement']::notification_type[]
FROM users u
ON CONFLICT (user_id, channel) DO NOTHING;

INSERT INTO notification_preferences (user_id, channel, enabled, types)
SELECT 
    u.id,
    'websocket',
    true,
    ARRAY['event_reminder', 'event_registration', 'event_update', 'post_published', 'announcement', 'volunteer_update', 'verification_update', 'system']::notification_type[]
FROM users u
ON CONFLICT (user_id, channel) DO NOTHING;

-- Notification template seed data has been removed from migrations.
-- Use the seed CLI tool to populate notification templates.
-- Templates can be managed through the admin interface or API.