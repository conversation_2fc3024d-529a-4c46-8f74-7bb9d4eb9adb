-- Restore original active_organization_members view
DROP VIEW IF EXISTS active_organization_members;
CREATE VIEW active_organization_members AS
SELECT * FROM user_organization_memberships;

-- Drop audit table
DROP TABLE IF EXISTS account_deletion_audit;

-- Drop scheduled deletions table
DROP TABLE IF EXISTS scheduled_deletions;

-- Drop deletion requests table
DROP TABLE IF EXISTS deletion_requests;

-- Remove soft delete column from users
ALTER TABLE users DROP COLUMN IF EXISTS deleted_at;