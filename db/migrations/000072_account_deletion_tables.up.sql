-- Add soft delete support to users table
ALTER TABLE users ADD COLUMN deleted_at TIMESTAMP;

-- Create index for deleted_at to efficiently find active users
CREATE INDEX idx_users_deleted_at ON users(deleted_at);

-- Create table for deletion confirmation requests
CREATE TABLE deletion_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    code VARCHAR(6) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    confirmed_at TIMESTAMP,
    CONSTRAINT unique_active_deletion_request UNIQUE (user_id, confirmed_at)
);

-- Index for finding active deletion requests
CREATE INDEX idx_deletion_requests_user_expires ON deletion_requests(user_id, expires_at) WHERE confirmed_at IS NULL;

-- Create table for scheduled deletions (30-day grace period)
CREATE TABLE scheduled_deletions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    scheduled_for TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    cancelled_at TIMESTAMP,
    processed_at TIMESTAMP,
    CONSTRAINT unique_active_scheduled_deletion UNIQUE (user_id)
);

-- Index for finding deletions to process
CREATE INDEX idx_scheduled_deletions_scheduled_for ON scheduled_deletions(scheduled_for) 
WHERE cancelled_at IS NULL AND processed_at IS NULL;

-- Index for finding active scheduled deletions by user
CREATE INDEX idx_scheduled_deletions_user_active ON scheduled_deletions(user_id) 
WHERE cancelled_at IS NULL AND processed_at IS NULL;

-- Create audit table for account deletion events
CREATE TABLE account_deletion_audit (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    action VARCHAR(50) NOT NULL, -- 'initiated', 'confirmed', 'cancelled', 'processed', 'recovered'
    metadata JSONB,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Index for audit queries
CREATE INDEX idx_account_deletion_audit_user ON account_deletion_audit(user_id);
CREATE INDEX idx_account_deletion_audit_created ON account_deletion_audit(created_at);

-- Update existing views to filter out deleted users
-- Update active_organization_members view
DROP VIEW IF EXISTS active_organization_members;
CREATE VIEW active_organization_members AS
SELECT om.* 
FROM user_organization_memberships om
JOIN users u ON om.user_id = u.id
WHERE u.deleted_at IS NULL;

-- Add comment
COMMENT ON TABLE deletion_requests IS 'Stores temporary deletion confirmation codes for account deletion';
COMMENT ON TABLE scheduled_deletions IS 'Tracks scheduled account deletions with 30-day grace period';
COMMENT ON TABLE account_deletion_audit IS 'Audit trail for all account deletion related events';
COMMENT ON COLUMN users.deleted_at IS 'Soft delete timestamp for GDPR compliance';