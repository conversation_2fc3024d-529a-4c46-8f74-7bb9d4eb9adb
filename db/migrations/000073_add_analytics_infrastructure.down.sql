-- Drop analytics infrastructure tables and types

-- Drop triggers first
DROP TRIGGER IF EXISTS trigger_update_user_analytics ON analytics_events;
DROP TRIGGER IF EXISTS update_organizations_analytics_updated_at ON organization_analytics;
DROP TRIGGER IF EXISTS update_user_analytics_updated_at ON user_analytics;
DROP TRIGGER IF EXISTS set_analytics_events_created_date ON analytics_events;

-- Drop functions
DROP FUNCTION IF EXISTS update_user_analytics();
DROP FUNCTION IF EXISTS create_monthly_partitions();
DROP FUNCTION IF EXISTS set_created_date();

-- Drop indexes
DROP INDEX IF EXISTS idx_feature_usage_feature_last_used;
DROP INDEX IF EXISTS idx_org_analytics_updated;
DROP INDEX IF EXISTS idx_user_analytics_engagement;
DROP INDEX IF EXISTS idx_user_analytics_org_activity;
DROP INDEX IF EXISTS idx_analytics_events_session;
DROP INDEX IF EXISTS idx_analytics_events_entity;
DROP INDEX IF EXISTS idx_analytics_events_org_timestamp;
DROP INDEX IF EXISTS idx_analytics_events_user_timestamp;
DROP INDEX IF EXISTS idx_analytics_events_event_type_timestamp;

-- Drop tables (partitioned tables need special handling)
DROP TABLE IF EXISTS feature_usage CASCADE;
DROP TABLE IF EXISTS analytics_daily_snapshots CASCADE;
DROP TABLE IF EXISTS organization_analytics CASCADE;
DROP TABLE IF EXISTS user_analytics CASCADE;
DROP TABLE IF EXISTS analytics_events CASCADE;

-- Drop types
DROP TYPE IF EXISTS analytics_event_type CASCADE;