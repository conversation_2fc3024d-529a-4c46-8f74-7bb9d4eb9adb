-- 000071_add_analytics_infrastructure.up.sql
-- Analytics infrastructure tables for event tracking and metrics
-- Extracted from new migration 000006 to work with existing schema

-- Create event type enum for analytics
CREATE TYPE analytics_event_type AS ENUM (
    -- User lifecycle events
    'user_registered',
    'user_login',
    'user_logout',
    'user_profile_updated',
    'user_deleted',
    
    -- Organization events
    'org_created',
    'org_updated',
    'org_member_joined',
    'org_member_left',
    'org_member_role_changed',
    
    -- Event management
    'event_created',
    'event_updated',
    'event_registered',
    'event_unregistered',
    'event_checked_in',
    'event_completed',
    'event_cancelled',
    
    -- Content interactions
    'post_created',
    'post_viewed',
    'post_liked',
    'post_shared',
    'post_commented',
    
    -- Resource access
    'resource_created',
    'resource_viewed',
    'resource_downloaded',
    
    -- Communication events
    'notification_sent',
    'notification_delivered',
    'notification_read',
    'notification_clicked',
    
    -- System events
    'api_error',
    'performance_metric',
    'feature_used'
);

-- Create analytics events table (time-series optimized)
CREATE TABLE IF NOT EXISTS analytics_events (
    id UUID DEFAULT gen_random_uuid(),
    
    -- Event details
    event_type analytics_event_type NOT NULL,
    event_name VARCHAR(255) NOT NULL,
    event_timestamp TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- User and organization context
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    organization_id UUID REFERENCES organizations(id) ON DELETE SET NULL,
    
    -- Related entities
    entity_type VARCHAR(50), -- 'event', 'post', 'resource', etc.
    entity_id UUID,
    
    -- Event metadata
    properties JSONB DEFAULT '{}',
    
    -- Request context
    ip_address INET,
    user_agent TEXT,
    referer TEXT,
    
    -- Session tracking
    session_id UUID,
    
    -- Partition key for time-series optimization
    created_date DATE NOT NULL,
    
    PRIMARY KEY (id, created_date)
) PARTITION BY RANGE (created_date);

-- Create initial partitions for the next 12 months
DO $$
DECLARE
    start_date DATE := DATE_TRUNC('month', CURRENT_DATE);
    end_date DATE;
    partition_name TEXT;
BEGIN
    FOR i IN 0..11 LOOP
        end_date := start_date + INTERVAL '1 month';
        partition_name := 'analytics_events_' || TO_CHAR(start_date, 'YYYY_MM');
        
        EXECUTE format('
            CREATE TABLE IF NOT EXISTS %I PARTITION OF analytics_events
            FOR VALUES FROM (%L) TO (%L)',
            partition_name, start_date, end_date
        );
        
        start_date := end_date;
    END LOOP;
END $$;

-- Create function to set created_date
CREATE OR REPLACE FUNCTION set_created_date()
RETURNS TRIGGER AS $$
BEGIN
    NEW.created_date := DATE(COALESCE(NEW.event_timestamp, CURRENT_TIMESTAMP));
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to set created_date
CREATE TRIGGER set_analytics_events_created_date
BEFORE INSERT ON analytics_events
FOR EACH ROW
EXECUTE FUNCTION set_created_date();

-- Create user analytics aggregates table
CREATE TABLE IF NOT EXISTS user_analytics (
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    
    -- Login metrics
    total_logins INT NOT NULL DEFAULT 0,
    last_login_at TIMESTAMPTZ,
    
    -- Activity metrics
    total_events_attended INT NOT NULL DEFAULT 0,
    total_events_registered INT NOT NULL DEFAULT 0,
    total_posts_created INT NOT NULL DEFAULT 0,
    total_posts_viewed INT NOT NULL DEFAULT 0,
    total_resources_accessed INT NOT NULL DEFAULT 0,
    
    -- Engagement metrics
    engagement_score DECIMAL(5,2) NOT NULL DEFAULT 0,
    activity_streak_days INT NOT NULL DEFAULT 0,
    last_activity_at TIMESTAMPTZ,
    
    -- Time-based metrics
    first_activity_at TIMESTAMPTZ,
    total_time_spent_minutes INT NOT NULL DEFAULT 0,
    
    -- Updated timestamp
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (user_id, organization_id)
);

-- Create organization analytics aggregates table
CREATE TABLE IF NOT EXISTS organization_analytics (
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    
    -- Member metrics
    total_members INT NOT NULL DEFAULT 0,
    active_members_30d INT NOT NULL DEFAULT 0,
    active_members_7d INT NOT NULL DEFAULT 0,
    active_members_1d INT NOT NULL DEFAULT 0,
    
    -- Event metrics
    total_events_created INT NOT NULL DEFAULT 0,
    total_event_registrations INT NOT NULL DEFAULT 0,
    total_event_check_ins INT NOT NULL DEFAULT 0,
    avg_event_attendance_rate DECIMAL(5,2),
    
    -- Content metrics
    total_posts_created INT NOT NULL DEFAULT 0,
    total_post_views INT NOT NULL DEFAULT 0,
    total_resources_created INT NOT NULL DEFAULT 0,
    total_resource_downloads INT NOT NULL DEFAULT 0,
    
    -- Engagement metrics
    avg_member_engagement_score DECIMAL(5,2) NOT NULL DEFAULT 0,
    member_retention_rate DECIMAL(5,2),
    
    -- Growth metrics
    new_members_30d INT NOT NULL DEFAULT 0,
    new_members_7d INT NOT NULL DEFAULT 0,
    
    -- Updated timestamp
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (organization_id)
);

-- Create daily analytics snapshots table
CREATE TABLE IF NOT EXISTS analytics_daily_snapshots (
    id UUID DEFAULT gen_random_uuid(),
    snapshot_date DATE NOT NULL,
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    
    -- User metrics
    daily_active_users INT NOT NULL DEFAULT 0,
    new_users INT NOT NULL DEFAULT 0,
    returning_users INT NOT NULL DEFAULT 0,
    
    -- Event metrics
    events_created INT NOT NULL DEFAULT 0,
    event_registrations INT NOT NULL DEFAULT 0,
    event_check_ins INT NOT NULL DEFAULT 0,
    
    -- Content metrics
    posts_created INT NOT NULL DEFAULT 0,
    post_views INT NOT NULL DEFAULT 0,
    resources_created INT NOT NULL DEFAULT 0,
    resource_downloads INT NOT NULL DEFAULT 0,
    
    -- Engagement metrics
    avg_session_duration_minutes DECIMAL(10,2),
    total_sessions INT NOT NULL DEFAULT 0,
    
    -- Created timestamp
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (id, snapshot_date),
    UNIQUE (snapshot_date, organization_id)
) PARTITION BY RANGE (snapshot_date);

-- Create initial partitions for daily snapshots
DO $$
DECLARE
    start_date DATE := DATE_TRUNC('month', CURRENT_DATE);
    end_date DATE;
    partition_name TEXT;
BEGIN
    FOR i IN 0..11 LOOP
        end_date := start_date + INTERVAL '1 month';
        partition_name := 'analytics_daily_snapshots_' || TO_CHAR(start_date, 'YYYY_MM');
        
        EXECUTE format('
            CREATE TABLE IF NOT EXISTS %I PARTITION OF analytics_daily_snapshots
            FOR VALUES FROM (%L) TO (%L)',
            partition_name, start_date, end_date
        );
        
        start_date := end_date;
    END LOOP;
END $$;

-- Create feature usage tracking table
CREATE TABLE IF NOT EXISTS feature_usage (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    feature_name VARCHAR(255) NOT NULL,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    organization_id UUID REFERENCES organizations(id) ON DELETE SET NULL,
    
    -- Usage details
    usage_count INT NOT NULL DEFAULT 1,
    first_used_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_used_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Metadata
    context JSONB DEFAULT '{}',
    
    -- Unique constraint for user-feature tracking
    UNIQUE (user_id, feature_name, organization_id)
);

-- Create indexes for efficient querying
CREATE INDEX idx_analytics_events_event_type_timestamp 
    ON analytics_events (event_type, event_timestamp DESC);

CREATE INDEX idx_analytics_events_user_timestamp 
    ON analytics_events (user_id, event_timestamp DESC) 
    WHERE user_id IS NOT NULL;

CREATE INDEX idx_analytics_events_org_timestamp 
    ON analytics_events (organization_id, event_timestamp DESC) 
    WHERE organization_id IS NOT NULL;

CREATE INDEX idx_analytics_events_entity 
    ON analytics_events (entity_type, entity_id) 
    WHERE entity_type IS NOT NULL;

CREATE INDEX idx_analytics_events_session 
    ON analytics_events (session_id, event_timestamp) 
    WHERE session_id IS NOT NULL;

-- Indexes for aggregate tables
CREATE INDEX idx_user_analytics_org_activity 
    ON user_analytics (organization_id, last_activity_at DESC);

CREATE INDEX idx_user_analytics_engagement 
    ON user_analytics (engagement_score DESC);

CREATE INDEX idx_org_analytics_updated 
    ON organization_analytics (updated_at DESC);

CREATE INDEX idx_feature_usage_feature_last_used 
    ON feature_usage (feature_name, last_used_at DESC);

-- Create function to update user analytics
CREATE OR REPLACE FUNCTION update_user_analytics()
RETURNS TRIGGER AS $$
BEGIN
    -- Update user analytics based on the event
    INSERT INTO user_analytics (user_id, organization_id, last_activity_at)
    VALUES (NEW.user_id, NEW.organization_id, NEW.event_timestamp)
    ON CONFLICT (user_id, organization_id)
    DO UPDATE SET
        last_activity_at = EXCLUDED.last_activity_at,
        updated_at = CURRENT_TIMESTAMP;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updating user analytics
CREATE TRIGGER trigger_update_user_analytics
AFTER INSERT ON analytics_events
FOR EACH ROW
WHEN (NEW.user_id IS NOT NULL)
EXECUTE FUNCTION update_user_analytics();

-- Create function to automatically create monthly partitions
CREATE OR REPLACE FUNCTION create_monthly_partitions()
RETURNS void AS $$
DECLARE
    start_date DATE;
    end_date DATE;
    partition_name TEXT;
BEGIN
    -- Create partitions for the next 3 months
    start_date := DATE_TRUNC('month', CURRENT_DATE + INTERVAL '12 months');
    
    FOR i IN 0..2 LOOP
        end_date := start_date + INTERVAL '1 month';
        
        -- Create partition for analytics_events
        partition_name := 'analytics_events_' || TO_CHAR(start_date, 'YYYY_MM');
        EXECUTE format('
            CREATE TABLE IF NOT EXISTS %I PARTITION OF analytics_events
            FOR VALUES FROM (%L) TO (%L)',
            partition_name, start_date, end_date
        );
        
        -- Create partition for analytics_daily_snapshots
        partition_name := 'analytics_daily_snapshots_' || TO_CHAR(start_date, 'YYYY_MM');
        EXECUTE format('
            CREATE TABLE IF NOT EXISTS %I PARTITION OF analytics_daily_snapshots
            FOR VALUES FROM (%L) TO (%L)',
            partition_name, start_date, end_date
        );
        
        start_date := end_date;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Add update trigger for organizations
CREATE TRIGGER update_organizations_analytics_updated_at
BEFORE UPDATE ON organization_analytics
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Add update trigger for user analytics
CREATE TRIGGER update_user_analytics_updated_at
BEFORE UPDATE ON user_analytics
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();