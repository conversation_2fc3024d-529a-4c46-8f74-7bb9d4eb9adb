-- Drop social features tables and types

-- Drop materialized view first
DROP MATERIALIZED VIEW IF EXISTS social_engagement_stats CASCADE;

-- Drop triggers
DROP TRIGGER IF EXISTS comment_reply_count_trigger ON comments;
DROP TRIGGER IF EXISTS reaction_like_count_trigger ON reactions;
DROP TRIGGER IF EXISTS comment_thread_id_trigger ON comments;
DROP TRIGGER IF EXISTS update_comments_updated_at ON comments;
DROP TRIGGER IF EXISTS update_bookmarks_updated_at ON bookmarks;
DROP TRIGGER IF EXISTS update_content_reports_updated_at ON content_reports;
DROP TRIGGER IF EXISTS update_social_notification_preferences_updated_at ON social_notification_preferences;

-- Drop functions
DROP FUNCTION IF EXISTS update_comment_reply_count();
DROP FUNCTION IF EXISTS update_comment_like_count();
DROP FUNCTION IF EXISTS set_comment_thread_id();
DROP FUNCTION IF EXISTS cleanup_expired_rate_limits();
DROP FUNCTION IF EXISTS refresh_social_engagement_stats();

-- Drop indexes
DROP INDEX IF EXISTS idx_social_notification_prefs_org_id;
DROP INDEX IF EXISTS idx_social_notification_prefs_user_id;
DROP INDEX IF EXISTS idx_social_rate_limits_last_action;
DROP INDEX IF EXISTS idx_social_rate_limits_window_start;
DROP INDEX IF EXISTS idx_social_rate_limits_action_type;
DROP INDEX IF EXISTS idx_social_rate_limits_user_id;
DROP INDEX IF EXISTS idx_content_reports_created_at;
DROP INDEX IF EXISTS idx_content_reports_status;
DROP INDEX IF EXISTS idx_content_reports_reported_by;
DROP INDEX IF EXISTS idx_content_reports_content_type_id;
DROP INDEX IF EXISTS idx_content_reports_organization_id;
DROP INDEX IF EXISTS idx_social_activity_logs_created_at;
DROP INDEX IF EXISTS idx_social_activity_logs_content_type_id;
DROP INDEX IF EXISTS idx_social_activity_logs_activity_type;
DROP INDEX IF EXISTS idx_social_activity_logs_user_id;
DROP INDEX IF EXISTS idx_social_activity_logs_organization_id;
DROP INDEX IF EXISTS idx_bookmarks_created_at;
DROP INDEX IF EXISTS idx_bookmarks_folder;
DROP INDEX IF EXISTS idx_bookmarks_user_id;
DROP INDEX IF EXISTS idx_bookmarks_content_type_id;
DROP INDEX IF EXISTS idx_bookmarks_organization_id;
DROP INDEX IF EXISTS idx_reactions_created_at;
DROP INDEX IF EXISTS idx_reactions_type;
DROP INDEX IF EXISTS idx_reactions_user_id;
DROP INDEX IF EXISTS idx_reactions_content_type_id;
DROP INDEX IF EXISTS idx_reactions_organization_id;
DROP INDEX IF EXISTS idx_comments_created_at;
DROP INDEX IF EXISTS idx_comments_moderation_status;
DROP INDEX IF EXISTS idx_comments_status;
DROP INDEX IF EXISTS idx_comments_thread_id;
DROP INDEX IF EXISTS idx_comments_parent_id;
DROP INDEX IF EXISTS idx_comments_user_id;
DROP INDEX IF EXISTS idx_comments_content_type_id;
DROP INDEX IF EXISTS idx_comments_organization_id;

-- Drop tables
DROP TABLE IF EXISTS social_notification_preferences CASCADE;
DROP TABLE IF EXISTS social_rate_limits CASCADE;
DROP TABLE IF EXISTS content_reports CASCADE;
DROP TABLE IF EXISTS social_activity_logs CASCADE;
DROP TABLE IF EXISTS bookmarks CASCADE;
DROP TABLE IF EXISTS reactions CASCADE;
DROP TABLE IF EXISTS comments CASCADE;

-- Drop types
DROP TYPE IF EXISTS comment_status CASCADE;
DROP TYPE IF EXISTS moderation_status CASCADE;
DROP TYPE IF EXISTS content_type CASCADE;
DROP TYPE IF EXISTS reaction_type CASCADE;