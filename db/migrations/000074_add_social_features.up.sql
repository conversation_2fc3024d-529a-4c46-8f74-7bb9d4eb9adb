-- 000072_add_social_features.up.sql
-- Social features: comments, reactions, bookmarks, and moderation
-- Extracted from new migration 000008 to work with existing schema

-- Create reaction type enum (check if not exists)
DO $$ BEGIN
    CREATE TYPE reaction_type AS ENUM ('like', 'heart', 'laugh', 'angry', 'sad', 'thumbs_up', 'thumbs_down');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create content type enum for social interactions (check if not exists)
DO $$ BEGIN
    CREATE TYPE content_type AS ENUM ('post', 'event', 'comment');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create moderation status enum (check if not exists)
DO $$ BEGIN
    CREATE TYPE moderation_status AS ENUM ('pending', 'approved', 'rejected', 'flagged');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create comment status enum (check if not exists)
DO $$ BEGIN
    CREATE TYPE comment_status AS ENUM ('active', 'hidden', 'deleted', 'pending_moderation');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create comments table (hierarchical with parent/child support)
CREATE TABLE IF NOT EXISTS comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    
    -- Content reference (polymorphic)
    content_type content_type NOT NULL,
    content_id UUID NOT NULL,
    
    -- Hierarchical structure
    parent_comment_id UUID REFERENCES comments(id) ON DELETE CASCADE,
    thread_id UUID, -- Root comment ID for threading
    depth INTEGER NOT NULL DEFAULT 0,
    
    -- Comment content
    content TEXT NOT NULL,
    content_html TEXT, -- Rendered HTML version
    
    -- Status and moderation
    status comment_status NOT NULL DEFAULT 'active',
    moderation_status moderation_status NOT NULL DEFAULT 'approved',
    moderation_reason TEXT,
    moderated_by UUID REFERENCES users(id),
    moderated_at TIMESTAMPTZ,
    
    -- User info
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    user_name VARCHAR(255), -- Cached for performance
    user_avatar_url TEXT, -- Cached for performance
    
    -- Engagement metrics (updated via triggers)
    reply_count INTEGER NOT NULL DEFAULT 0,
    like_count INTEGER NOT NULL DEFAULT 0,
    
    -- Tracking
    edited_at TIMESTAMPTZ,
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT chk_comment_depth CHECK (depth >= 0 AND depth <= 10), -- Limit nesting
    CONSTRAINT chk_comment_content_length CHECK (char_length(content) > 0 AND char_length(content) <= 10000),
    CONSTRAINT chk_comment_self_parent CHECK (id != parent_comment_id)
);

-- Create reactions table (likes, hearts, etc.)
CREATE TABLE IF NOT EXISTS reactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    
    -- Content reference (polymorphic)
    content_type content_type NOT NULL,
    content_id UUID NOT NULL,
    
    -- Reaction details
    reaction_type reaction_type NOT NULL,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT unique_user_content_reaction UNIQUE (user_id, content_type, content_id, reaction_type)
);

-- Create bookmarks table
CREATE TABLE IF NOT EXISTS bookmarks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    
    -- Content reference (polymorphic)
    content_type content_type NOT NULL,
    content_id UUID NOT NULL,
    
    -- User info
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Optional bookmark organization
    folder_name VARCHAR(255),
    notes TEXT,
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT unique_user_content_bookmark UNIQUE (user_id, content_type, content_id)
);

-- Create social_activity_logs table for analytics and moderation
CREATE TABLE IF NOT EXISTS social_activity_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    
    -- Activity details
    activity_type VARCHAR(50) NOT NULL, -- 'comment_created', 'reaction_added', 'bookmark_added', etc.
    content_type content_type NOT NULL,
    content_id UUID NOT NULL,
    
    -- User info
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Activity metadata
    metadata JSONB,
    
    -- IP and user agent for security
    ip_address INET,
    user_agent TEXT,
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT chk_activity_type CHECK (activity_type IN (
        'comment_created', 'comment_updated', 'comment_deleted', 
        'reaction_added', 'reaction_removed', 
        'bookmark_added', 'bookmark_removed',
        'content_reported', 'content_moderated'
    ))
);

-- Create content reports table for moderation
CREATE TABLE IF NOT EXISTS content_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    
    -- Content being reported
    content_type content_type NOT NULL,
    content_id UUID NOT NULL,
    
    -- Reporter info
    reported_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Report details
    reason VARCHAR(100) NOT NULL,
    description TEXT,
    
    -- Moderation status
    status moderation_status NOT NULL DEFAULT 'pending',
    reviewed_by UUID REFERENCES users(id),
    reviewed_at TIMESTAMPTZ,
    moderator_notes TEXT,
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT chk_report_reason CHECK (reason IN (
        'spam', 'harassment', 'hate_speech', 'inappropriate_content', 
        'misinformation', 'violence', 'adult_content', 'copyright', 'other'
    ))
);

-- Create rate limiting tables
CREATE TABLE IF NOT EXISTS social_rate_limits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- User and action identification
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    action_type VARCHAR(50) NOT NULL,
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    
    -- Rate limiting windows
    window_start TIMESTAMPTZ NOT NULL,
    window_duration INTERVAL NOT NULL,
    action_count INTEGER NOT NULL DEFAULT 1,
    max_actions INTEGER NOT NULL,
    
    -- Tracking
    last_action_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT chk_social_rate_limit_action CHECK (action_type IN (
        'comment_create', 'reaction_add', 'bookmark_add', 'report_content'
    )),
    CONSTRAINT unique_user_action_window UNIQUE (user_id, action_type, organization_id, window_start)
);

-- Create notification preferences for social features
CREATE TABLE IF NOT EXISTS social_notification_preferences (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    
    -- Preference settings
    notify_on_comment_reply BOOLEAN DEFAULT TRUE,
    notify_on_comment_reaction BOOLEAN DEFAULT TRUE,
    notify_on_post_comment BOOLEAN DEFAULT TRUE,
    notify_on_event_comment BOOLEAN DEFAULT TRUE,
    
    -- Notification channels
    email_enabled BOOLEAN DEFAULT TRUE,
    push_enabled BOOLEAN DEFAULT TRUE,
    websocket_enabled BOOLEAN DEFAULT TRUE,
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    CONSTRAINT unique_user_org_social_prefs UNIQUE (user_id, organization_id)
);

-- Create indexes for performance
CREATE INDEX idx_comments_organization_id ON comments(organization_id);
CREATE INDEX idx_comments_content_type_id ON comments(content_type, content_id);
CREATE INDEX idx_comments_user_id ON comments(user_id);
CREATE INDEX idx_comments_parent_id ON comments(parent_comment_id);
CREATE INDEX idx_comments_thread_id ON comments(thread_id);
CREATE INDEX idx_comments_status ON comments(status);
CREATE INDEX idx_comments_moderation_status ON comments(moderation_status);
CREATE INDEX idx_comments_created_at ON comments(created_at);

CREATE INDEX idx_reactions_organization_id ON reactions(organization_id);
CREATE INDEX idx_reactions_content_type_id ON reactions(content_type, content_id);
CREATE INDEX idx_reactions_user_id ON reactions(user_id);
CREATE INDEX idx_reactions_type ON reactions(reaction_type);
CREATE INDEX idx_reactions_created_at ON reactions(created_at);

CREATE INDEX idx_bookmarks_organization_id ON bookmarks(organization_id);
CREATE INDEX idx_bookmarks_content_type_id ON bookmarks(content_type, content_id);
CREATE INDEX idx_bookmarks_user_id ON bookmarks(user_id);
CREATE INDEX idx_bookmarks_folder ON bookmarks(folder_name);
CREATE INDEX idx_bookmarks_created_at ON bookmarks(created_at);

CREATE INDEX idx_social_activity_logs_organization_id ON social_activity_logs(organization_id);
CREATE INDEX idx_social_activity_logs_user_id ON social_activity_logs(user_id);
CREATE INDEX idx_social_activity_logs_activity_type ON social_activity_logs(activity_type);
CREATE INDEX idx_social_activity_logs_content_type_id ON social_activity_logs(content_type, content_id);
CREATE INDEX idx_social_activity_logs_created_at ON social_activity_logs(created_at);

CREATE INDEX idx_content_reports_organization_id ON content_reports(organization_id);
CREATE INDEX idx_content_reports_content_type_id ON content_reports(content_type, content_id);
CREATE INDEX idx_content_reports_reported_by ON content_reports(reported_by);
CREATE INDEX idx_content_reports_status ON content_reports(status);
CREATE INDEX idx_content_reports_created_at ON content_reports(created_at);

CREATE INDEX idx_social_rate_limits_user_id ON social_rate_limits(user_id);
CREATE INDEX idx_social_rate_limits_action_type ON social_rate_limits(action_type);
CREATE INDEX idx_social_rate_limits_window_start ON social_rate_limits(window_start);
CREATE INDEX idx_social_rate_limits_last_action ON social_rate_limits(last_action_at);

CREATE INDEX idx_social_notification_prefs_user_id ON social_notification_preferences(user_id);
CREATE INDEX idx_social_notification_prefs_org_id ON social_notification_preferences(organization_id);

-- Apply updated_at triggers
CREATE TRIGGER update_comments_updated_at BEFORE UPDATE ON comments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_bookmarks_updated_at BEFORE UPDATE ON bookmarks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_content_reports_updated_at BEFORE UPDATE ON content_reports FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_social_notification_preferences_updated_at BEFORE UPDATE ON social_notification_preferences FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to update reply counts on comments
CREATE OR REPLACE FUNCTION update_comment_reply_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Increment reply count on parent comment
        IF NEW.parent_comment_id IS NOT NULL THEN
            UPDATE comments 
            SET reply_count = reply_count + 1 
            WHERE id = NEW.parent_comment_id;
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        -- Decrement reply count on parent comment
        IF OLD.parent_comment_id IS NOT NULL THEN
            UPDATE comments 
            SET reply_count = reply_count - 1 
            WHERE id = OLD.parent_comment_id AND reply_count > 0;
        END IF;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to update like counts on comments
CREATE OR REPLACE FUNCTION update_comment_like_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Increment like count if it's a like reaction on a comment
        IF NEW.content_type = 'comment' AND NEW.reaction_type = 'like' THEN
            UPDATE comments 
            SET like_count = like_count + 1 
            WHERE id = NEW.content_id;
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        -- Decrement like count if it's a like reaction on a comment
        IF OLD.content_type = 'comment' AND OLD.reaction_type = 'like' THEN
            UPDATE comments 
            SET like_count = like_count - 1 
            WHERE id = OLD.content_id AND like_count > 0;
        END IF;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to set thread_id for comments
CREATE OR REPLACE FUNCTION set_comment_thread_id()
RETURNS TRIGGER AS $$
BEGIN
    -- If this is a root comment, set thread_id to its own id
    IF NEW.parent_comment_id IS NULL THEN
        NEW.thread_id = NEW.id;
    ELSE
        -- For replies, inherit thread_id from parent
        SELECT thread_id INTO NEW.thread_id 
        FROM comments 
        WHERE id = NEW.parent_comment_id;
        
        -- Set depth based on parent
        SELECT depth + 1 INTO NEW.depth 
        FROM comments 
        WHERE id = NEW.parent_comment_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
CREATE TRIGGER comment_reply_count_trigger
    AFTER INSERT OR DELETE ON comments
    FOR EACH ROW EXECUTE FUNCTION update_comment_reply_count();

CREATE TRIGGER reaction_like_count_trigger
    AFTER INSERT OR DELETE ON reactions
    FOR EACH ROW EXECUTE FUNCTION update_comment_like_count();

CREATE TRIGGER comment_thread_id_trigger
    BEFORE INSERT ON comments
    FOR EACH ROW EXECUTE FUNCTION set_comment_thread_id();

-- Function to clean up old rate limit records
CREATE OR REPLACE FUNCTION cleanup_expired_rate_limits()
RETURNS void AS $$
BEGIN
    DELETE FROM social_rate_limits 
    WHERE window_start + window_duration < NOW() - INTERVAL '1 hour';
END;
$$ LANGUAGE plpgsql;

-- Create materialized view for social engagement statistics
CREATE MATERIALIZED VIEW IF NOT EXISTS social_engagement_stats AS
SELECT 
    content_type,
    content_id,
    organization_id,
    COUNT(CASE WHEN activity_type LIKE '%comment%' THEN 1 END) as comment_count,
    COUNT(CASE WHEN activity_type LIKE '%reaction%' THEN 1 END) as reaction_count,
    COUNT(CASE WHEN activity_type LIKE '%bookmark%' THEN 1 END) as bookmark_count,
    COUNT(DISTINCT user_id) as unique_users,
    MAX(created_at) as last_activity_at
FROM social_activity_logs
GROUP BY content_type, content_id, organization_id;

-- Create index on materialized view
CREATE INDEX idx_social_engagement_stats_content ON social_engagement_stats(content_type, content_id);
CREATE INDEX idx_social_engagement_stats_org ON social_engagement_stats(organization_id);

-- Function to refresh social engagement stats
CREATE OR REPLACE FUNCTION refresh_social_engagement_stats()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY social_engagement_stats;
END;
$$ LANGUAGE plpgsql;