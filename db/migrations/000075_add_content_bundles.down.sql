-- Drop content bundles tables and types

-- Drop RLS policies
DROP POLICY IF EXISTS bundle_content_items_access ON bundle_content_items;
DROP POLICY IF EXISTS bundle_generation_logs_access ON bundle_generation_logs;
DROP POLICY IF EXISTS content_bundles_organization_access ON content_bundles;

-- Drop triggers
DROP TRIGGER IF EXISTS content_bundles_compression_ratio ON content_bundles;
DROP TRIGGER IF EXISTS content_bundles_updated_at ON content_bundles;

-- Drop functions
DROP FUNCTION IF EXISTS calculate_compression_ratio();
DROP FUNCTION IF EXISTS update_content_bundles_updated_at();

-- Drop indexes
DROP INDEX IF EXISTS idx_bundle_content_items_content;
DROP INDEX IF EXISTS idx_bundle_content_items_bundle_type;
DROP INDEX IF EXISTS idx_bundle_generation_logs_created_at;
DROP INDEX IF EXISTS idx_bundle_generation_logs_bundle_level;
DROP INDEX IF EXISTS idx_content_bundles_created_at;
DROP INDEX IF EXISTS idx_content_bundles_base_bundle;
DROP INDEX IF EXISTS idx_content_bundles_status_expires;
DROP INDEX IF EXISTS idx_content_bundles_org_type_status;
DROP INDEX IF EXISTS idx_content_bundles_org_status;

-- Drop tables
DROP TABLE IF EXISTS bundle_content_items CASCADE;
DROP TABLE IF EXISTS bundle_generation_logs CASCADE;
DROP TABLE IF EXISTS content_bundles CASCADE;

-- Drop types
DROP TYPE IF EXISTS bundle_format CASCADE;
DROP TYPE IF EXISTS bundle_type CASCADE;
DROP TYPE IF EXISTS bundle_status CASCADE;