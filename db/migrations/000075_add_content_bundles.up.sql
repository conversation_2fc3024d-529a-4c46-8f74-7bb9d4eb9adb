-- 000073_add_content_bundles.up.sql
-- Offline bundles tables for content bundling system
-- Extracted from new migration 000009 to work with existing schema

-- Content bundle generation status enum
CREATE TYPE bundle_status AS ENUM ('pending', 'generating', 'completed', 'failed', 'expired');

-- Bundle type enum for different bundling strategies
CREATE TYPE bundle_type AS ENUM ('full', 'delta', 'selective');

-- Bundle format enum for compression/serialization
CREATE TYPE bundle_format AS ENUM ('json', 'msgpack', 'sqlite');

-- Main content bundles table
CREATE TABLE content_bundles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    bundle_type bundle_type NOT NULL DEFAULT 'full',
    bundle_format bundle_format NOT NULL DEFAULT 'json',
    status bundle_status NOT NULL DEFAULT 'pending',
    
    -- Bundle metadata
    name VARCHAR(255),
    description TEXT,
    
    -- Size and compression info
    uncompressed_size_bytes BIGINT DEFAULT 0,
    compressed_size_bytes BIGINT DEFAULT 0,
    compression_ratio DECIMAL(5,3),
    
    -- Version and delta tracking
    version_number INTEGER NOT NULL DEFAULT 1,
    base_bundle_id UUID REFERENCES content_bundles(id) ON DELETE SET NULL,
    last_updated_content_at TIMESTAMPTZ,
    
    -- Bundle configuration
    include_posts BOOLEAN DEFAULT true,
    include_events BOOLEAN DEFAULT true,
    include_resources BOOLEAN DEFAULT true,
    include_media BOOLEAN DEFAULT true,
    
    -- Filter configuration (JSONB for flexible filtering)
    filter_criteria JSONB DEFAULT '{}',
    
    -- Generation tracking
    requested_by UUID REFERENCES users(id) ON DELETE SET NULL,
    requested_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    generation_started_at TIMESTAMPTZ,
    generation_completed_at TIMESTAMPTZ,
    generation_failed_at TIMESTAMPTZ,
    generation_duration_ms INTEGER,
    
    -- File storage
    file_path TEXT,
    file_url TEXT,
    checksum_sha256 VARCHAR(64),
    
    -- Expiration and cleanup
    expires_at TIMESTAMPTZ,
    
    -- Audit fields
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Bundle generation logs for debugging and monitoring
CREATE TABLE bundle_generation_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    bundle_id UUID NOT NULL REFERENCES content_bundles(id) ON DELETE CASCADE,
    log_level VARCHAR(10) NOT NULL, -- 'info', 'warn', 'error', 'debug'
    message TEXT NOT NULL,
    details JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Content inclusion tracking for delta bundles
CREATE TABLE bundle_content_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    bundle_id UUID NOT NULL REFERENCES content_bundles(id) ON DELETE CASCADE,
    content_type VARCHAR(50) NOT NULL, -- 'post', 'event', 'resource', 'media'
    content_id UUID NOT NULL,
    content_updated_at TIMESTAMPTZ NOT NULL,
    inclusion_reason VARCHAR(100), -- 'new', 'updated', 'dependency'
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Ensure uniqueness within a bundle
    UNIQUE(bundle_id, content_type, content_id)
);

-- Performance indexes
CREATE INDEX idx_content_bundles_org_status ON content_bundles(organization_id, status);
CREATE INDEX idx_content_bundles_org_type_status ON content_bundles(organization_id, bundle_type, status);
CREATE INDEX idx_content_bundles_status_expires ON content_bundles(status, expires_at);
CREATE INDEX idx_content_bundles_base_bundle ON content_bundles(base_bundle_id) WHERE base_bundle_id IS NOT NULL;
CREATE INDEX idx_content_bundles_created_at ON content_bundles(created_at DESC);

CREATE INDEX idx_bundle_generation_logs_bundle_level ON bundle_generation_logs(bundle_id, log_level);
CREATE INDEX idx_bundle_generation_logs_created_at ON bundle_generation_logs(created_at DESC);

CREATE INDEX idx_bundle_content_items_bundle_type ON bundle_content_items(bundle_id, content_type);
CREATE INDEX idx_bundle_content_items_content ON bundle_content_items(content_type, content_id);

-- RLS policies for content bundles
ALTER TABLE content_bundles ENABLE ROW LEVEL SECURITY;
ALTER TABLE bundle_generation_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE bundle_content_items ENABLE ROW LEVEL SECURITY;

-- Users can only access bundles for their organization(s)
CREATE POLICY content_bundles_organization_access ON content_bundles
    FOR ALL
    USING (
        organization_id IN (
            SELECT uom.organization_id 
            FROM user_organization_memberships uom 
            WHERE uom.user_id = current_setting('app.current_user_id', true)::uuid
        )
    );

CREATE POLICY bundle_generation_logs_access ON bundle_generation_logs
    FOR ALL
    USING (
        bundle_id IN (
            SELECT cb.id 
            FROM content_bundles cb 
            WHERE cb.organization_id IN (
                SELECT uom.organization_id 
                FROM user_organization_memberships uom 
                WHERE uom.user_id = current_setting('app.current_user_id', true)::uuid
            )
        )
    );

CREATE POLICY bundle_content_items_access ON bundle_content_items
    FOR ALL
    USING (
        bundle_id IN (
            SELECT cb.id 
            FROM content_bundles cb 
            WHERE cb.organization_id IN (
                SELECT uom.organization_id 
                FROM user_organization_memberships uom 
                WHERE uom.user_id = current_setting('app.current_user_id', true)::uuid
            )
        )
    );

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_content_bundles_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for updated_at
CREATE TRIGGER content_bundles_updated_at
    BEFORE UPDATE ON content_bundles
    FOR EACH ROW
    EXECUTE FUNCTION update_content_bundles_updated_at();

-- Function to automatically set compression ratio when size fields are updated
CREATE OR REPLACE FUNCTION calculate_compression_ratio()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.uncompressed_size_bytes > 0 AND NEW.compressed_size_bytes > 0 THEN
        NEW.compression_ratio = ROUND((NEW.compressed_size_bytes::decimal / NEW.uncompressed_size_bytes::decimal), 3);
    ELSE
        NEW.compression_ratio = NULL;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for compression ratio calculation
CREATE TRIGGER content_bundles_compression_ratio
    BEFORE INSERT OR UPDATE ON content_bundles
    FOR EACH ROW
    EXECUTE FUNCTION calculate_compression_ratio();

-- Comments for documentation
COMMENT ON TABLE content_bundles IS 'Stores metadata for offline content bundles with compression and delta sync support';
COMMENT ON COLUMN content_bundles.bundle_type IS 'Type of bundle: full (complete snapshot), delta (changes since base), selective (filtered content)';
COMMENT ON COLUMN content_bundles.bundle_format IS 'Serialization format: json (human readable), msgpack (binary efficient), sqlite (queryable)';
COMMENT ON COLUMN content_bundles.filter_criteria IS 'JSONB configuration for selective bundles (tag filters, date ranges, etc.)';
COMMENT ON COLUMN content_bundles.compression_ratio IS 'Ratio of compressed to uncompressed size (0.1 = 90% compression)';
COMMENT ON COLUMN content_bundles.generation_duration_ms IS 'Time taken to generate bundle in milliseconds';

COMMENT ON TABLE bundle_generation_logs IS 'Logs for bundle generation process to aid debugging and monitoring';
COMMENT ON TABLE bundle_content_items IS 'Tracks which content items are included in each bundle for delta calculations';