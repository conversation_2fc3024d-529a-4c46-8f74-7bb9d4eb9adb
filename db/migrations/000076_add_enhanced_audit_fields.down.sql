-- Drop enhanced audit fields and tables

-- Drop triggers
DROP TRIGGER IF EXISTS set_audit_log_retention_trigger ON audit_logs;

-- Drop functions
DROP FUNCTION IF EXISTS set_audit_log_retention();
DROP FUNCTION IF EXISTS anonymize_old_audit_logs();
DROP FUNCTION IF EXISTS delete_expired_audit_logs();

-- Drop indexes on new tables
DROP INDEX IF EXISTS idx_audit_log_exports_expires_at;
DROP INDEX IF EXISTS idx_audit_log_exports_status;
DROP INDEX IF EXISTS idx_audit_log_exports_requested_by;
DROP INDEX IF EXISTS idx_security_alerts_resolved;
DROP INDEX IF EXISTS idx_security_alerts_user_id;
DROP INDEX IF EXISTS idx_security_alerts_severity;
DROP INDEX IF EXISTS idx_security_alerts_created_at;
DROP INDEX IF EXISTS idx_user_consents_expires_at;
DROP INDEX IF EXISTS idx_user_consents_purpose;
DROP INDEX IF EXISTS idx_user_consents_user_id;
DROP INDEX IF EXISTS unique_active_consent;
DROP INDEX IF EXISTS idx_data_subject_requests_requested_at;
DROP INDEX IF EXISTS idx_data_subject_requests_status;
DROP INDEX IF EXISTS idx_data_subject_requests_user_id;

-- Drop new indexes on audit_logs
DROP INDEX IF EXISTS idx_audit_logs_retention;
DROP INDEX IF EXISTS idx_audit_logs_correlation_id;
DROP INDEX IF EXISTS idx_audit_logs_severity_high;
DROP INDEX IF EXISTS idx_audit_logs_category_action;
DROP INDEX IF EXISTS idx_audit_logs_organization_id_notnull;
DROP INDEX IF EXISTS idx_audit_logs_user_id_notnull;
DROP INDEX IF EXISTS idx_audit_logs_created_at_desc;

-- Drop new tables
DROP TABLE IF EXISTS audit_log_exports CASCADE;
DROP TABLE IF EXISTS security_alerts CASCADE;
DROP TABLE IF EXISTS user_consents CASCADE;
DROP TABLE IF EXISTS data_subject_requests CASCADE;

-- Remove columns from audit_logs
ALTER TABLE audit_logs DROP COLUMN IF EXISTS retention_expires_at;
ALTER TABLE audit_logs DROP COLUMN IF EXISTS anonymized_at;
ALTER TABLE audit_logs DROP COLUMN IF EXISTS contains_pii;
ALTER TABLE audit_logs DROP COLUMN IF EXISTS schema_version;
ALTER TABLE audit_logs DROP COLUMN IF EXISTS trace_id;
ALTER TABLE audit_logs DROP COLUMN IF EXISTS correlation_id;
ALTER TABLE audit_logs DROP COLUMN IF EXISTS severity;
ALTER TABLE audit_logs DROP COLUMN IF EXISTS category;

-- Recreate original indexes
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_organization_id ON audit_logs(organization_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at DESC);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);

-- Drop types
DROP TYPE IF EXISTS audit_severity CASCADE;
DROP TYPE IF EXISTS audit_category CASCADE;