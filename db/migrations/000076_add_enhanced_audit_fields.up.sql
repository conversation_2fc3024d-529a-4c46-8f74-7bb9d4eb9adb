-- 000074_add_enhanced_audit_fields.up.sql
-- Add missing fields to audit_logs table and create enhanced audit tables
-- Extracted from new migration 000007 to work with existing schema

-- Create audit category enum
CREATE TYPE audit_category AS ENUM (
    'authentication',
    'authorization', 
    'data_access',
    'configuration',
    'user_management',
    'organization_management',
    'content_management',
    'system_administration',
    'suspicious_activity'
);

-- Create severity levels for audit logs
CREATE TYPE audit_severity AS ENUM (
    'critical',
    'high',
    'medium',
    'low',
    'info'
);

-- Add new columns to existing audit_logs table
ALTER TABLE audit_logs ADD COLUMN IF NOT EXISTS category audit_category;
ALTER TABLE audit_logs ADD COLUMN IF NOT EXISTS severity audit_severity DEFAULT 'info';
ALTER TABLE audit_logs ADD COLUMN IF NOT EXISTS correlation_id UUID;
ALTER TABLE audit_logs ADD COLUMN IF NOT EXISTS trace_id VARCHAR(64);
ALTER TABLE audit_logs ADD COLUMN IF NOT EXISTS schema_version INTEGER DEFAULT 1;
ALTER TABLE audit_logs ADD COLUMN IF NOT EXISTS contains_pii BOOLEAN DEFAULT false;
ALTER TABLE audit_logs ADD COLUMN IF NOT EXISTS anonymized_at TIMESTAMPTZ;
ALTER TABLE audit_logs ADD COLUMN IF NOT EXISTS retention_expires_at TIMESTAMPTZ;

-- Update existing records to have a default category based on action
UPDATE audit_logs SET category = 
    CASE 
        WHEN action LIKE '%login%' OR action LIKE '%logout%' OR action LIKE '%auth%' THEN 'authentication'::audit_category
        WHEN action LIKE '%create%' OR action LIKE '%update%' OR action LIKE '%delete%' THEN 'content_management'::audit_category
        WHEN action LIKE '%user%' THEN 'user_management'::audit_category
        WHEN action LIKE '%org%' THEN 'organization_management'::audit_category
        ELSE 'data_access'::audit_category
    END
WHERE category IS NULL;

-- Make category NOT NULL after update
ALTER TABLE audit_logs ALTER COLUMN category SET NOT NULL;

-- Rename existing indexes to avoid conflicts
DROP INDEX IF EXISTS idx_audit_logs_created_at;
DROP INDEX IF EXISTS idx_audit_logs_user_id;
DROP INDEX IF EXISTS idx_audit_logs_organization_id;
DROP INDEX IF EXISTS idx_audit_logs_action;

-- Create new indexes for enhanced querying
CREATE INDEX idx_audit_logs_created_at_desc ON audit_logs(created_at DESC);
CREATE INDEX idx_audit_logs_user_id_notnull ON audit_logs(user_id) WHERE user_id IS NOT NULL;
CREATE INDEX idx_audit_logs_organization_id_notnull ON audit_logs(organization_id) WHERE organization_id IS NOT NULL;
CREATE INDEX idx_audit_logs_category_action ON audit_logs(category, action);
CREATE INDEX idx_audit_logs_severity_high ON audit_logs(severity) WHERE severity IN ('critical', 'high');
CREATE INDEX idx_audit_logs_correlation_id ON audit_logs(correlation_id) WHERE correlation_id IS NOT NULL;
CREATE INDEX idx_audit_logs_retention ON audit_logs(retention_expires_at) WHERE retention_expires_at IS NOT NULL;

-- Create table for GDPR data subject requests
CREATE TABLE data_subject_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    request_id UUID NOT NULL UNIQUE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Request details
    request_type VARCHAR(50) NOT NULL, -- access, erasure, portability, rectification
    requested_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Processing information
    status VARCHAR(50) NOT NULL DEFAULT 'pending', -- pending, processing, completed, rejected
    processed_at TIMESTAMPTZ,
    processed_by UUID REFERENCES users(id),
    
    -- Response details
    response_url TEXT,
    response_format VARCHAR(20), -- json, csv, pdf
    
    -- Additional information
    reason TEXT,
    notes TEXT,
    metadata JSONB,
    
    -- Constraints
    CONSTRAINT chk_request_type CHECK (request_type IN ('access', 'erasure', 'portability', 'rectification')),
    CONSTRAINT chk_status CHECK (status IN ('pending', 'processing', 'completed', 'rejected'))
);

-- Create table for consent management
CREATE TABLE user_consents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    
    -- Consent details
    consent_id UUID NOT NULL,
    purpose VARCHAR(255) NOT NULL,
    data_types TEXT[] NOT NULL,
    
    -- Consent lifecycle
    granted_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMPTZ NOT NULL,
    withdrawn_at TIMESTAMPTZ,
    
    -- Consent context
    ip_address INET,
    user_agent TEXT,
    consent_text TEXT NOT NULL,
    version VARCHAR(20) NOT NULL
);

-- Create partial unique index for active consents
CREATE UNIQUE INDEX unique_active_consent ON user_consents (user_id, purpose, version) 
    WHERE withdrawn_at IS NULL;

-- Create table for security alerts
CREATE TABLE security_alerts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Alert details
    alert_type VARCHAR(100) NOT NULL,
    severity audit_severity NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    
    -- Context
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    organization_id UUID REFERENCES organizations(id) ON DELETE SET NULL,
    ip_address INET,
    
    -- Alert metadata
    details JSONB,
    indicators TEXT[],
    risk_score INTEGER CHECK (risk_score >= 0 AND risk_score <= 100),
    
    -- Response
    action_taken VARCHAR(100),
    resolved_at TIMESTAMPTZ,
    resolved_by UUID REFERENCES users(id),
    
    -- Timing
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Correlation
    correlation_id UUID,
    related_audit_logs UUID[]
);

-- Create table for audit log exports (for compliance)
CREATE TABLE audit_log_exports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    export_id UUID NOT NULL UNIQUE,
    
    -- Export details
    requested_by UUID NOT NULL REFERENCES users(id),
    approved_by UUID REFERENCES users(id),
    
    -- Export parameters
    start_date TIMESTAMPTZ NOT NULL,
    end_date TIMESTAMPTZ NOT NULL,
    filters JSONB,
    format VARCHAR(20) NOT NULL, -- csv, json, pdf
    
    -- Export status
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ,
    
    -- Export location
    file_path TEXT,
    file_size BIGINT,
    
    -- Compliance
    reason TEXT NOT NULL,
    include_pii BOOLEAN NOT NULL DEFAULT false,
    
    -- Constraints
    CONSTRAINT chk_export_format CHECK (format IN ('csv', 'json', 'pdf')),
    CONSTRAINT chk_export_status CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'expired'))
);

-- Create indexes for other tables
CREATE INDEX idx_data_subject_requests_user_id ON data_subject_requests(user_id);
CREATE INDEX idx_data_subject_requests_status ON data_subject_requests(status) WHERE status != 'completed';
CREATE INDEX idx_data_subject_requests_requested_at ON data_subject_requests(requested_at);

CREATE INDEX idx_user_consents_user_id ON user_consents(user_id);
CREATE INDEX idx_user_consents_purpose ON user_consents(purpose);
CREATE INDEX idx_user_consents_expires_at ON user_consents(expires_at) WHERE withdrawn_at IS NULL;

CREATE INDEX idx_security_alerts_created_at ON security_alerts(created_at DESC);
CREATE INDEX idx_security_alerts_severity ON security_alerts(severity) WHERE severity IN ('critical', 'high');
CREATE INDEX idx_security_alerts_user_id ON security_alerts(user_id) WHERE user_id IS NOT NULL;
CREATE INDEX idx_security_alerts_resolved ON security_alerts(resolved_at) WHERE resolved_at IS NULL;

CREATE INDEX idx_audit_log_exports_requested_by ON audit_log_exports(requested_by);
CREATE INDEX idx_audit_log_exports_status ON audit_log_exports(status) WHERE status != 'completed';
CREATE INDEX idx_audit_log_exports_expires_at ON audit_log_exports(expires_at) WHERE expires_at IS NOT NULL;

-- Create function to automatically set retention expiry
CREATE OR REPLACE FUNCTION set_audit_log_retention()
RETURNS TRIGGER AS $$
BEGIN
    -- Set retention based on category and severity
    IF NEW.severity IN ('critical', 'high') THEN
        NEW.retention_expires_at := NEW.created_at + INTERVAL '180 days';
    ELSIF NEW.category = 'authentication' THEN
        NEW.retention_expires_at := NEW.created_at + INTERVAL '90 days';
    ELSE
        NEW.retention_expires_at := NEW.created_at + INTERVAL '30 days';
    END IF;
    
    -- Mark if contains PII based on category
    IF NEW.category IN ('authentication', 'user_management', 'data_access') THEN
        NEW.contains_pii := true;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for retention
CREATE TRIGGER set_audit_log_retention_trigger
    BEFORE INSERT ON audit_logs
    FOR EACH ROW
    EXECUTE FUNCTION set_audit_log_retention();

-- Create function to anonymize old audit logs
CREATE OR REPLACE FUNCTION anonymize_old_audit_logs()
RETURNS INTEGER AS $$
DECLARE
    rows_updated INTEGER;
BEGIN
    UPDATE audit_logs
    SET 
        ip_address = '0.0.0.0'::inet,
        user_agent = '[ANONYMIZED]',
        details = details - ARRAY['email', 'phone', 'address', 'name'],
        anonymized_at = CURRENT_TIMESTAMP
    WHERE 
        contains_pii = true 
        AND anonymized_at IS NULL
        AND created_at < CURRENT_TIMESTAMP - INTERVAL '30 days';
    
    GET DIAGNOSTICS rows_updated = ROW_COUNT;
    RETURN rows_updated;
END;
$$ LANGUAGE plpgsql;

-- Create function to delete expired audit logs
CREATE OR REPLACE FUNCTION delete_expired_audit_logs()
RETURNS INTEGER AS $$
DECLARE
    rows_deleted INTEGER;
BEGIN
    DELETE FROM audit_logs
    WHERE retention_expires_at < CURRENT_TIMESTAMP;
    
    GET DIAGNOSTICS rows_deleted = ROW_COUNT;
    RETURN rows_deleted;
END;
$$ LANGUAGE plpgsql;