-- Add WhatsApp message queue table for reliable message delivery
CREATE TABLE IF NOT EXISTS whatsapp_message_queue (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    notification_id UUID REFERENCES notifications(id) ON DELETE SET NULL,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    phone_number TEXT NOT NULL,
    template_name TEXT NOT NULL,
    template_params JSONB NOT NULL DEFAULT '{}',
    priority INTEGER NOT NULL DEFAULT 5, -- 1 = highest, 10 = lowest
    attempts INTEGER NOT NULL DEFAULT 0,
    max_attempts INTEGER NOT NULL DEFAULT 3,
    status TEXT NOT NULL DEFAULT 'pending',
    scheduled_for TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    next_retry_at TIMESTAMP WITH TIME ZONE,
    last_attempt_at TIMESTAMP WITH TIME ZONE,
    last_error TEXT,
    provider_message_id TEXT,
    delivered_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for efficient queue processing
CREATE INDEX idx_whatsapp_queue_status ON whatsapp_message_queue(status);
CREATE INDEX idx_whatsapp_queue_scheduled ON whatsapp_message_queue(scheduled_for) WHERE status = 'pending';
CREATE INDEX idx_whatsapp_queue_retry ON whatsapp_message_queue(next_retry_at) WHERE status = 'pending' AND next_retry_at IS NOT NULL;
CREATE INDEX idx_whatsapp_queue_user ON whatsapp_message_queue(user_id);
CREATE INDEX idx_whatsapp_queue_notification ON whatsapp_message_queue(notification_id) WHERE notification_id IS NOT NULL;
CREATE INDEX idx_whatsapp_queue_expires ON whatsapp_message_queue(expires_at) WHERE expires_at IS NOT NULL;

-- Add trigger to update updated_at
CREATE TRIGGER update_whatsapp_message_queue_updated_at BEFORE UPDATE ON whatsapp_message_queue
    FOR EACH ROW EXECUTE FUNCTION trigger_set_timestamp();

-- Add check constraint for valid status values
ALTER TABLE whatsapp_message_queue
ADD CONSTRAINT chk_whatsapp_queue_status CHECK (status IN ('pending', 'processing', 'sent', 'failed', 'expired'));

-- Add check constraint for priority range
ALTER TABLE whatsapp_message_queue
ADD CONSTRAINT chk_whatsapp_queue_priority CHECK (priority >= 1 AND priority <= 10);