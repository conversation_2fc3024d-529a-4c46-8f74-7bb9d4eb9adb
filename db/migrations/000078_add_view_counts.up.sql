-- 000076_add_view_counts.up.sql
-- Add view counting system for posts and resources

-- Create view_counts table
CREATE TABLE view_counts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    post_id UUID REFERENCES posts(id) ON DELETE CASCADE,
    resource_id UUID REFERENCES resources(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    viewed_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Ensure each user can only have one view per post or resource
    CONSTRAINT unique_post_user_view UNIQUE(post_id, user_id),
    CONSTRAINT unique_resource_user_view UNIQUE(resource_id, user_id),
    
    -- Ensure that either post_id or resource_id is set, but not both
    CONSTRAINT check_view_type CHECK (
        (post_id IS NOT NULL AND resource_id IS NULL) OR 
        (post_id IS NULL AND resource_id IS NOT NULL)
    )
);

-- Create indexes for efficient querying
CREATE INDEX idx_view_counts_post_id ON view_counts(post_id) WHERE post_id IS NOT NULL;
CREATE INDEX idx_view_counts_resource_id ON view_counts(resource_id) WHERE resource_id IS NOT NULL;
CREATE INDEX idx_view_counts_user_id ON view_counts(user_id);
CREATE INDEX idx_view_counts_viewed_at ON view_counts(viewed_at DESC);

-- Create a composite index for getting user's recent views
CREATE INDEX idx_view_counts_user_viewed_at ON view_counts(user_id, viewed_at DESC);

-- Create indexes for counting views efficiently
CREATE INDEX idx_view_counts_post_count ON view_counts(post_id) WHERE post_id IS NOT NULL;
CREATE INDEX idx_view_counts_resource_count ON view_counts(resource_id) WHERE resource_id IS NOT NULL;

-- Create function to update viewed_at timestamp
CREATE OR REPLACE FUNCTION update_view_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.viewed_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update viewed_at on updates
CREATE TRIGGER update_view_counts_viewed_at
    BEFORE UPDATE ON view_counts
    FOR EACH ROW
    EXECUTE FUNCTION update_view_timestamp();