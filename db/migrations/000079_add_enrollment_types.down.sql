-- Remove the constraint
ALTER TABLE event_registrations 
DROP CONSTRAINT IF EXISTS unique_waitlist_position_per_event;

-- Remove the indexes
DROP INDEX IF EXISTS idx_event_registrations_waitlist_position;
DROP INDEX IF EXISTS idx_event_registrations_admin_selected;

-- Remove the columns
ALTER TABLE event_registrations 
DROP COLUMN IF EXISTS selected_by_admin;

ALTER TABLE event_registrations 
DROP COLUMN IF EXISTS waitlist_position;

ALTER TABLE events 
DROP COLUMN IF EXISTS enrollment_type;

-- Remove the enum type
DROP TYPE IF EXISTS enrollment_type_enum;