-- Add organization user comments table for admin notes about users
CREATE TABLE org_user_comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    org_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    admin_id UUID NOT NULL REFERENCES users(id) ON DELETE RESTRICT,
    comment TEXT NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    -- Ensure only one comment per user per organization
    CONSTRAINT unique_org_user_comment UNIQUE(org_id, user_id)
);

-- Add indexes for efficient querying
CREATE INDEX idx_org_user_comments_org_id ON org_user_comments(org_id);
CREATE INDEX idx_org_user_comments_user_id ON org_user_comments(user_id);
CREATE INDEX idx_org_user_comments_admin_id ON org_user_comments(admin_id);
CREATE INDEX idx_org_user_comments_created_at ON org_user_comments(created_at);

-- Add updated_at trigger
CREATE OR REPLACE TRIGGER update_org_user_comments_updated_at
BEFORE UPDATE ON org_user_comments
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add comment to track purpose
COMMENT ON TABLE org_user_comments IS 'Administrative comments about users within organizations - for internal staff notes only';
COMMENT ON COLUMN org_user_comments.admin_id IS 'The admin user who created or last updated this comment';
COMMENT ON COLUMN org_user_comments.comment IS 'Administrative note about the user (internal use only)';