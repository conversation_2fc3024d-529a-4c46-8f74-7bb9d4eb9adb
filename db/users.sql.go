// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: users.sql

package db

import (
	"context"
	"time"

	"github.com/google/uuid"
)

const countUsers = `-- name: CountUsers :one
SELECT COUNT(*) FROM users
WHERE ($1::text IS NULL OR platform_role = $1::platform_role_enum)
`

func (q *Queries) CountUsers(ctx context.Context, platformRoleFilter *string) (int64, error) {
	row := q.db.QueryRow(ctx, countUsers, platformRoleFilter)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createStaffUserWithEmailPassword = `-- name: CreateStaffUserWithEmailPassword :one
INSERT INTO users (
    display_name,
    email,
    email_verified_at, -- Assume verified for testing endpoint
    hashed_password,
    interface_language,
    communication_language,
    enable_app_notifications,
    enable_whatsapp_notifications,
    enable_sms_notifications,
    enable_email_notifications,
    phone_otp_channel, -- Can be default, not primary for this user type
    platform_role
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, 'staff' -- Explicitly set platform_role to 'staff'
)
RETURNING id, display_name, hashed_password, profile_picture_url, phone, phone_verified_at, email, email_verified_at, phone_otp_channel, interface_language, communication_language, enable_app_notifications, enable_whatsapp_notifications, enable_sms_notifications, enable_email_notifications, created_at, updated_at, platform_role, deleted_at
`

type CreateStaffUserWithEmailPasswordParams struct {
	DisplayName                 string     `db:"display_name" json:"display_name"`
	Email                       *string    `db:"email" json:"email"`
	EmailVerifiedAt             *time.Time `db:"email_verified_at" json:"email_verified_at"`
	HashedPassword              *string    `db:"hashed_password" json:"hashed_password"`
	InterfaceLanguage           string     `db:"interface_language" json:"interface_language"`
	CommunicationLanguage       string     `db:"communication_language" json:"communication_language"`
	EnableAppNotifications      bool       `db:"enable_app_notifications" json:"enable_app_notifications"`
	EnableWhatsappNotifications bool       `db:"enable_whatsapp_notifications" json:"enable_whatsapp_notifications"`
	EnableSmsNotifications      bool       `db:"enable_sms_notifications" json:"enable_sms_notifications"`
	EnableEmailNotifications    bool       `db:"enable_email_notifications" json:"enable_email_notifications"`
	PhoneOtpChannel             string     `db:"phone_otp_channel" json:"phone_otp_channel"`
	Phone             			string     `db:"phone" json:"phone"`
}

func (q *Queries) CreateStaffUserWithEmailPassword(ctx context.Context, arg CreateStaffUserWithEmailPasswordParams) (User, error) {
	row := q.db.QueryRow(ctx, createStaffUserWithEmailPassword,
		arg.DisplayName,
		arg.Email,
		arg.EmailVerifiedAt,
		arg.HashedPassword,
		arg.InterfaceLanguage,
		arg.CommunicationLanguage,
		arg.EnableAppNotifications,
		arg.EnableWhatsappNotifications,
		arg.EnableSmsNotifications,
		arg.EnableEmailNotifications,
		arg.PhoneOtpChannel,
		arg.Phone,
	)
	var i User
	err := row.Scan(
		&i.ID,
		&i.DisplayName,
		&i.HashedPassword,
		&i.ProfilePictureUrl,
		&i.Phone,
		&i.PhoneVerifiedAt,
		&i.Email,
		&i.EmailVerifiedAt,
		&i.PhoneOtpChannel,
		&i.InterfaceLanguage,
		&i.CommunicationLanguage,
		&i.EnableAppNotifications,
		&i.EnableWhatsappNotifications,
		&i.EnableSmsNotifications,
		&i.EnableEmailNotifications,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.PlatformRole,
		&i.DeletedAt,
	)
	return i, err
}

const createUserWithPhone = `-- name: CreateUserWithPhone :one
INSERT INTO users (
    display_name,
    phone,
    phone_verified_at,
    -- email, -- Can be added later via profile update
    -- hashed_password, -- Not set during phone OTP registration
    interface_language,
    communication_language,
    enable_app_notifications,
    enable_whatsapp_notifications,
    enable_sms_notifications,
    enable_email_notifications,
    phone_otp_channel,
    platform_role
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, 'user' -- Default platform_role to 'user'
)
RETURNING id, display_name, hashed_password, profile_picture_url, phone, phone_verified_at, email, email_verified_at, phone_otp_channel, interface_language, communication_language, enable_app_notifications, enable_whatsapp_notifications, enable_sms_notifications, enable_email_notifications, created_at, updated_at, platform_role, deleted_at
`

type CreateUserWithPhoneParams struct {
	DisplayName                 string     `db:"display_name" json:"display_name"`
	Phone                       *string    `db:"phone" json:"phone"`
	PhoneVerifiedAt             *time.Time `db:"phone_verified_at" json:"phone_verified_at"`
	InterfaceLanguage           string     `db:"interface_language" json:"interface_language"`
	CommunicationLanguage       string     `db:"communication_language" json:"communication_language"`
	EnableAppNotifications      bool       `db:"enable_app_notifications" json:"enable_app_notifications"`
	EnableWhatsappNotifications bool       `db:"enable_whatsapp_notifications" json:"enable_whatsapp_notifications"`
	EnableSmsNotifications      bool       `db:"enable_sms_notifications" json:"enable_sms_notifications"`
	EnableEmailNotifications    bool       `db:"enable_email_notifications" json:"enable_email_notifications"`
	PhoneOtpChannel             string     `db:"phone_otp_channel" json:"phone_otp_channel"`
}

func (q *Queries) CreateUserWithPhone(ctx context.Context, arg CreateUserWithPhoneParams) (User, error) {
	row := q.db.QueryRow(ctx, createUserWithPhone,
		arg.DisplayName,
		arg.Phone,
		arg.PhoneVerifiedAt,
		arg.InterfaceLanguage,
		arg.CommunicationLanguage,
		arg.EnableAppNotifications,
		arg.EnableWhatsappNotifications,
		arg.EnableSmsNotifications,
		arg.EnableEmailNotifications,
		arg.PhoneOtpChannel,
	)
	var i User
	err := row.Scan(
		&i.ID,
		&i.DisplayName,
		&i.HashedPassword,
		&i.ProfilePictureUrl,
		&i.Phone,
		&i.PhoneVerifiedAt,
		&i.Email,
		&i.EmailVerifiedAt,
		&i.PhoneOtpChannel,
		&i.InterfaceLanguage,
		&i.CommunicationLanguage,
		&i.EnableAppNotifications,
		&i.EnableWhatsappNotifications,
		&i.EnableSmsNotifications,
		&i.EnableEmailNotifications,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.PlatformRole,
		&i.DeletedAt,
	)
	return i, err
}

const getMonthlyAttendedEvents = `-- name: GetMonthlyAttendedEvents :many

SELECT
    TO_CHAR(e.start_time, 'YYYY-MM') AS month,
    COUNT(er.id) AS count
FROM event_registrations er
JOIN events e ON er.event_id = e.id
WHERE er.user_id = $1
AND er.status = 'attended'
AND e.start_time >= NOW() - INTERVAL '6 months'
GROUP BY month
ORDER BY month DESC
`

type GetMonthlyAttendedEventsRow struct {
	Month string `db:"month" json:"month"`
	Count int64  `db:"count" json:"count"`
}

// or whatever status indicates confirmed volunteering
func (q *Queries) GetMonthlyAttendedEvents(ctx context.Context, userID uuid.UUID) ([]GetMonthlyAttendedEventsRow, error) {
	rows, err := q.db.Query(ctx, getMonthlyAttendedEvents, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetMonthlyAttendedEventsRow{}
	for rows.Next() {
		var i GetMonthlyAttendedEventsRow
		if err := rows.Scan(&i.Month, &i.Count); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getTopAttendedEventTags = `-- name: GetTopAttendedEventTags :many
SELECT
    t.name_en_us AS name,
    COUNT(er.id) AS count
FROM event_registrations er
JOIN events e ON er.event_id = e.id
JOIN event_tags et ON e.id = et.event_id
JOIN tags t ON et.tag_id = t.id
WHERE er.user_id = $1
AND er.status = 'attended'
AND e.start_time >= NOW() - INTERVAL '6 months'
GROUP BY t.name_en_us
ORDER BY count DESC
LIMIT 5
`

type GetTopAttendedEventTagsRow struct {
	Name  string `db:"name" json:"name"`
	Count int64  `db:"count" json:"count"`
}

func (q *Queries) GetTopAttendedEventTags(ctx context.Context, userID uuid.UUID) ([]GetTopAttendedEventTagsRow, error) {
	rows, err := q.db.Query(ctx, getTopAttendedEventTags, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetTopAttendedEventTagsRow{}
	for rows.Next() {
		var i GetTopAttendedEventTagsRow
		if err := rows.Scan(&i.Name, &i.Count); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserByEmail = `-- name: GetUserByEmail :one
SELECT id, display_name, hashed_password, profile_picture_url, phone, phone_verified_at, email, email_verified_at, phone_otp_channel, interface_language, communication_language, enable_app_notifications, enable_whatsapp_notifications, enable_sms_notifications, enable_email_notifications, created_at, updated_at, platform_role, deleted_at
FROM users
WHERE email = $1 LIMIT 1
`

func (q *Queries) GetUserByEmail(ctx context.Context, email *string) (User, error) {
	row := q.db.QueryRow(ctx, getUserByEmail, email)
	var i User
	err := row.Scan(
		&i.ID,
		&i.DisplayName,
		&i.HashedPassword,
		&i.ProfilePictureUrl,
		&i.Phone,
		&i.PhoneVerifiedAt,
		&i.Email,
		&i.EmailVerifiedAt,
		&i.PhoneOtpChannel,
		&i.InterfaceLanguage,
		&i.CommunicationLanguage,
		&i.EnableAppNotifications,
		&i.EnableWhatsappNotifications,
		&i.EnableSmsNotifications,
		&i.EnableEmailNotifications,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.PlatformRole,
		&i.DeletedAt,
	)
	return i, err
}

const getUserByID = `-- name: GetUserByID :one
SELECT id, display_name, hashed_password, profile_picture_url, phone, phone_verified_at, email, email_verified_at, phone_otp_channel, interface_language, communication_language, enable_app_notifications, enable_whatsapp_notifications, enable_sms_notifications, enable_email_notifications, created_at, updated_at, platform_role, deleted_at
FROM users
WHERE id = $1 LIMIT 1
`

func (q *Queries) GetUserByID(ctx context.Context, id uuid.UUID) (User, error) {
	row := q.db.QueryRow(ctx, getUserByID, id)
	var i User
	err := row.Scan(
		&i.ID,
		&i.DisplayName,
		&i.HashedPassword,
		&i.ProfilePictureUrl,
		&i.Phone,
		&i.PhoneVerifiedAt,
		&i.Email,
		&i.EmailVerifiedAt,
		&i.PhoneOtpChannel,
		&i.InterfaceLanguage,
		&i.CommunicationLanguage,
		&i.EnableAppNotifications,
		&i.EnableWhatsappNotifications,
		&i.EnableSmsNotifications,
		&i.EnableEmailNotifications,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.PlatformRole,
		&i.DeletedAt,
	)
	return i, err
}

const getUserByPhone = `-- name: GetUserByPhone :one
SELECT id, display_name, hashed_password, profile_picture_url, phone, phone_verified_at, email, email_verified_at, phone_otp_channel, interface_language, communication_language, enable_app_notifications, enable_whatsapp_notifications, enable_sms_notifications, enable_email_notifications, created_at, updated_at, platform_role, deleted_at
FROM users
WHERE phone = $1 LIMIT 1
`

func (q *Queries) GetUserByPhone(ctx context.Context, phone *string) (User, error) {
	row := q.db.QueryRow(ctx, getUserByPhone, phone)
	var i User
	err := row.Scan(
		&i.ID,
		&i.DisplayName,
		&i.HashedPassword,
		&i.ProfilePictureUrl,
		&i.Phone,
		&i.PhoneVerifiedAt,
		&i.Email,
		&i.EmailVerifiedAt,
		&i.PhoneOtpChannel,
		&i.InterfaceLanguage,
		&i.CommunicationLanguage,
		&i.EnableAppNotifications,
		&i.EnableWhatsappNotifications,
		&i.EnableSmsNotifications,
		&i.EnableEmailNotifications,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.PlatformRole,
		&i.DeletedAt,
	)
	return i, err
}

const getUserRegistrationDate = `-- name: GetUserRegistrationDate :one
SELECT created_at AS registration_date FROM users
WHERE id = $1
`

func (q *Queries) GetUserRegistrationDate(ctx context.Context, id uuid.UUID) (time.Time, error) {
	row := q.db.QueryRow(ctx, getUserRegistrationDate, id)
	var registration_date time.Time
	err := row.Scan(&registration_date)
	return registration_date, err
}

const getUserTotalAttendedEvents = `-- name: GetUserTotalAttendedEvents :one
SELECT
    COUNT(er.id) AS total_attended_events
FROM event_registrations er
JOIN events e ON er.event_id = e.id
WHERE er.user_id = $1
AND er.status = 'attended'
`

func (q *Queries) GetUserTotalAttendedEvents(ctx context.Context, userID uuid.UUID) (int64, error) {
	row := q.db.QueryRow(ctx, getUserTotalAttendedEvents, userID)
	var total_attended_events int64
	err := row.Scan(&total_attended_events)
	return total_attended_events, err
}

const getUserTotalVolunteerEvents = `-- name: GetUserTotalVolunteerEvents :one
SELECT COUNT(*) AS total_volunteer_events
FROM event_volunteer_applications eva
WHERE eva.user_id = $1
AND eva.status = 'approved'
`

func (q *Queries) GetUserTotalVolunteerEvents(ctx context.Context, userID uuid.UUID) (int64, error) {
	row := q.db.QueryRow(ctx, getUserTotalVolunteerEvents, userID)
	var total_volunteer_events int64
	err := row.Scan(&total_volunteer_events)
	return total_volunteer_events, err
}

const listUsers = `-- name: ListUsers :many
SELECT id, display_name, hashed_password, profile_picture_url, phone, phone_verified_at, email, email_verified_at, phone_otp_channel, interface_language, communication_language, enable_app_notifications, enable_whatsapp_notifications, enable_sms_notifications, enable_email_notifications, created_at, updated_at, platform_role, deleted_at FROM users
WHERE ($3::text IS NULL OR platform_role = $3::platform_role_enum)
ORDER BY created_at DESC
LIMIT $1 OFFSET $2
`

type ListUsersParams struct {
	Limit              int32   `db:"limit" json:"limit"`
	Offset             int32   `db:"offset" json:"offset"`
	PlatformRoleFilter *string `db:"platform_role_filter" json:"platform_role_filter"`
}

func (q *Queries) ListUsers(ctx context.Context, arg ListUsersParams) ([]User, error) {
	rows, err := q.db.Query(ctx, listUsers, arg.Limit, arg.Offset, arg.PlatformRoleFilter)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []User{}
	for rows.Next() {
		var i User
		if err := rows.Scan(
			&i.ID,
			&i.DisplayName,
			&i.HashedPassword,
			&i.ProfilePictureUrl,
			&i.Phone,
			&i.PhoneVerifiedAt,
			&i.Email,
			&i.EmailVerifiedAt,
			&i.PhoneOtpChannel,
			&i.InterfaceLanguage,
			&i.CommunicationLanguage,
			&i.EnableAppNotifications,
			&i.EnableWhatsappNotifications,
			&i.EnableSmsNotifications,
			&i.EnableEmailNotifications,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.PlatformRole,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const setUserPhoneVerified = `-- name: SetUserPhoneVerified :one
UPDATE users
SET phone_verified_at = $2, updated_at = NOW()
WHERE id = $1
RETURNING id, display_name, hashed_password, profile_picture_url, phone, phone_verified_at, email, email_verified_at, phone_otp_channel, interface_language, communication_language, enable_app_notifications, enable_whatsapp_notifications, enable_sms_notifications, enable_email_notifications, created_at, updated_at, platform_role, deleted_at
`

type SetUserPhoneVerifiedParams struct {
	ID              uuid.UUID  `db:"id" json:"id"`
	PhoneVerifiedAt *time.Time `db:"phone_verified_at" json:"phone_verified_at"`
}

func (q *Queries) SetUserPhoneVerified(ctx context.Context, arg SetUserPhoneVerifiedParams) (User, error) {
	row := q.db.QueryRow(ctx, setUserPhoneVerified, arg.ID, arg.PhoneVerifiedAt)
	var i User
	err := row.Scan(
		&i.ID,
		&i.DisplayName,
		&i.HashedPassword,
		&i.ProfilePictureUrl,
		&i.Phone,
		&i.PhoneVerifiedAt,
		&i.Email,
		&i.EmailVerifiedAt,
		&i.PhoneOtpChannel,
		&i.InterfaceLanguage,
		&i.CommunicationLanguage,
		&i.EnableAppNotifications,
		&i.EnableWhatsappNotifications,
		&i.EnableSmsNotifications,
		&i.EnableEmailNotifications,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.PlatformRole,
		&i.DeletedAt,
	)
	return i, err
}

const updateUserDisplayName = `-- name: UpdateUserDisplayName :one
UPDATE users
SET display_name = $2, updated_at = NOW()
WHERE id = $1
RETURNING id, display_name, hashed_password, profile_picture_url, phone, phone_verified_at, email, email_verified_at, phone_otp_channel, interface_language, communication_language, enable_app_notifications, enable_whatsapp_notifications, enable_sms_notifications, enable_email_notifications, created_at, updated_at, platform_role, deleted_at
`

type UpdateUserDisplayNameParams struct {
	ID          uuid.UUID `db:"id" json:"id"`
	DisplayName string    `db:"display_name" json:"display_name"`
}

func (q *Queries) UpdateUserDisplayName(ctx context.Context, arg UpdateUserDisplayNameParams) (User, error) {
	row := q.db.QueryRow(ctx, updateUserDisplayName, arg.ID, arg.DisplayName)
	var i User
	err := row.Scan(
		&i.ID,
		&i.DisplayName,
		&i.HashedPassword,
		&i.ProfilePictureUrl,
		&i.Phone,
		&i.PhoneVerifiedAt,
		&i.Email,
		&i.EmailVerifiedAt,
		&i.PhoneOtpChannel,
		&i.InterfaceLanguage,
		&i.CommunicationLanguage,
		&i.EnableAppNotifications,
		&i.EnableWhatsappNotifications,
		&i.EnableSmsNotifications,
		&i.EnableEmailNotifications,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.PlatformRole,
		&i.DeletedAt,
	)
	return i, err
}

const updateUserEmailAndVerificationStatus = `-- name: UpdateUserEmailAndVerificationStatus :one
UPDATE users
SET
    email = $2,
    email_verified_at = $3, -- Assume verified for testing endpoint
    updated_at = NOW()
WHERE id = $1
RETURNING id, display_name, hashed_password, profile_picture_url, phone, phone_verified_at, email, email_verified_at, phone_otp_channel, interface_language, communication_language, enable_app_notifications, enable_whatsapp_notifications, enable_sms_notifications, enable_email_notifications, created_at, updated_at, platform_role, deleted_at
`

type UpdateUserEmailAndVerificationStatusParams struct {
	ID              uuid.UUID  `db:"id" json:"id"`
	Email           *string    `db:"email" json:"email"`
	EmailVerifiedAt *time.Time `db:"email_verified_at" json:"email_verified_at"`
}

func (q *Queries) UpdateUserEmailAndVerificationStatus(ctx context.Context, arg UpdateUserEmailAndVerificationStatusParams) (User, error) {
	row := q.db.QueryRow(ctx, updateUserEmailAndVerificationStatus, arg.ID, arg.Email, arg.EmailVerifiedAt)
	var i User
	err := row.Scan(
		&i.ID,
		&i.DisplayName,
		&i.HashedPassword,
		&i.ProfilePictureUrl,
		&i.Phone,
		&i.PhoneVerifiedAt,
		&i.Email,
		&i.EmailVerifiedAt,
		&i.PhoneOtpChannel,
		&i.InterfaceLanguage,
		&i.CommunicationLanguage,
		&i.EnableAppNotifications,
		&i.EnableWhatsappNotifications,
		&i.EnableSmsNotifications,
		&i.EnableEmailNotifications,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.PlatformRole,
		&i.DeletedAt,
	)
	return i, err
}

const updateUserLanguagePreferences = `-- name: UpdateUserLanguagePreferences :one
UPDATE users
SET 
    interface_language = $2,
    communication_language = $3,
    updated_at = NOW()
WHERE id = $1
RETURNING id, display_name, hashed_password, profile_picture_url, phone, phone_verified_at, email, email_verified_at, phone_otp_channel, interface_language, communication_language, enable_app_notifications, enable_whatsapp_notifications, enable_sms_notifications, enable_email_notifications, created_at, updated_at, platform_role, deleted_at
`

type UpdateUserLanguagePreferencesParams struct {
	ID                    uuid.UUID `db:"id" json:"id"`
	InterfaceLanguage     string    `db:"interface_language" json:"interface_language"`
	CommunicationLanguage string    `db:"communication_language" json:"communication_language"`
}

func (q *Queries) UpdateUserLanguagePreferences(ctx context.Context, arg UpdateUserLanguagePreferencesParams) (User, error) {
	row := q.db.QueryRow(ctx, updateUserLanguagePreferences, arg.ID, arg.InterfaceLanguage, arg.CommunicationLanguage)
	var i User
	err := row.Scan(
		&i.ID,
		&i.DisplayName,
		&i.HashedPassword,
		&i.ProfilePictureUrl,
		&i.Phone,
		&i.PhoneVerifiedAt,
		&i.Email,
		&i.EmailVerifiedAt,
		&i.PhoneOtpChannel,
		&i.InterfaceLanguage,
		&i.CommunicationLanguage,
		&i.EnableAppNotifications,
		&i.EnableWhatsappNotifications,
		&i.EnableSmsNotifications,
		&i.EnableEmailNotifications,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.PlatformRole,
		&i.DeletedAt,
	)
	return i, err
}

const updateUserNotificationSettings = `-- name: UpdateUserNotificationSettings :one
UPDATE users
SET 
    enable_app_notifications = $2,
    enable_whatsapp_notifications = $3,
    enable_sms_notifications = $4,
    enable_email_notifications = $5,
    updated_at = NOW()
WHERE id = $1
RETURNING id, display_name, hashed_password, profile_picture_url, phone, phone_verified_at, email, email_verified_at, phone_otp_channel, interface_language, communication_language, enable_app_notifications, enable_whatsapp_notifications, enable_sms_notifications, enable_email_notifications, created_at, updated_at, platform_role, deleted_at
`

type UpdateUserNotificationSettingsParams struct {
	ID                          uuid.UUID `db:"id" json:"id"`
	EnableAppNotifications      bool      `db:"enable_app_notifications" json:"enable_app_notifications"`
	EnableWhatsappNotifications bool      `db:"enable_whatsapp_notifications" json:"enable_whatsapp_notifications"`
	EnableSmsNotifications      bool      `db:"enable_sms_notifications" json:"enable_sms_notifications"`
	EnableEmailNotifications    bool      `db:"enable_email_notifications" json:"enable_email_notifications"`
}

func (q *Queries) UpdateUserNotificationSettings(ctx context.Context, arg UpdateUserNotificationSettingsParams) (User, error) {
	row := q.db.QueryRow(ctx, updateUserNotificationSettings,
		arg.ID,
		arg.EnableAppNotifications,
		arg.EnableWhatsappNotifications,
		arg.EnableSmsNotifications,
		arg.EnableEmailNotifications,
	)
	var i User
	err := row.Scan(
		&i.ID,
		&i.DisplayName,
		&i.HashedPassword,
		&i.ProfilePictureUrl,
		&i.Phone,
		&i.PhoneVerifiedAt,
		&i.Email,
		&i.EmailVerifiedAt,
		&i.PhoneOtpChannel,
		&i.InterfaceLanguage,
		&i.CommunicationLanguage,
		&i.EnableAppNotifications,
		&i.EnableWhatsappNotifications,
		&i.EnableSmsNotifications,
		&i.EnableEmailNotifications,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.PlatformRole,
		&i.DeletedAt,
	)
	return i, err
}

const updateUserPassword = `-- name: UpdateUserPassword :exec
UPDATE users
SET 
    hashed_password = $2,
    updated_at = NOW()
WHERE id = $1
`

type UpdateUserPasswordParams struct {
	ID             uuid.UUID `db:"id" json:"id"`
	HashedPassword *string   `db:"hashed_password" json:"hashed_password"`
}

func (q *Queries) UpdateUserPassword(ctx context.Context, arg UpdateUserPasswordParams) error {
	_, err := q.db.Exec(ctx, updateUserPassword, arg.ID, arg.HashedPassword)
	return err
}

const updateUserPhoneAndMarkVerified = `-- name: UpdateUserPhoneAndMarkVerified :one
UPDATE users
SET 
    phone = $2,
    phone_verified_at = NOW(), -- Mark as verified
    updated_at = NOW()
WHERE id = $1
RETURNING id, display_name, hashed_password, profile_picture_url, phone, phone_verified_at, email, email_verified_at, phone_otp_channel, interface_language, communication_language, enable_app_notifications, enable_whatsapp_notifications, enable_sms_notifications, enable_email_notifications, created_at, updated_at, platform_role, deleted_at
`

type UpdateUserPhoneAndMarkVerifiedParams struct {
	ID    uuid.UUID `db:"id" json:"id"`
	Phone *string   `db:"phone" json:"phone"`
}

func (q *Queries) UpdateUserPhoneAndMarkVerified(ctx context.Context, arg UpdateUserPhoneAndMarkVerifiedParams) (User, error) {
	row := q.db.QueryRow(ctx, updateUserPhoneAndMarkVerified, arg.ID, arg.Phone)
	var i User
	err := row.Scan(
		&i.ID,
		&i.DisplayName,
		&i.HashedPassword,
		&i.ProfilePictureUrl,
		&i.Phone,
		&i.PhoneVerifiedAt,
		&i.Email,
		&i.EmailVerifiedAt,
		&i.PhoneOtpChannel,
		&i.InterfaceLanguage,
		&i.CommunicationLanguage,
		&i.EnableAppNotifications,
		&i.EnableWhatsappNotifications,
		&i.EnableSmsNotifications,
		&i.EnableEmailNotifications,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.PlatformRole,
		&i.DeletedAt,
	)
	return i, err
}

const updateUserPhoneAndVerificationStatus = `-- name: UpdateUserPhoneAndVerificationStatus :one
UPDATE users
SET 
    phone = $2,
    phone_verified_at = NULL, -- Needs re-verification
    updated_at = NOW()
WHERE id = $1
RETURNING id, display_name, hashed_password, profile_picture_url, phone, phone_verified_at, email, email_verified_at, phone_otp_channel, interface_language, communication_language, enable_app_notifications, enable_whatsapp_notifications, enable_sms_notifications, enable_email_notifications, created_at, updated_at, platform_role, deleted_at
`

type UpdateUserPhoneAndVerificationStatusParams struct {
	ID    uuid.UUID `db:"id" json:"id"`
	Phone *string   `db:"phone" json:"phone"`
}

func (q *Queries) UpdateUserPhoneAndVerificationStatus(ctx context.Context, arg UpdateUserPhoneAndVerificationStatusParams) (User, error) {
	row := q.db.QueryRow(ctx, updateUserPhoneAndVerificationStatus, arg.ID, arg.Phone)
	var i User
	err := row.Scan(
		&i.ID,
		&i.DisplayName,
		&i.HashedPassword,
		&i.ProfilePictureUrl,
		&i.Phone,
		&i.PhoneVerifiedAt,
		&i.Email,
		&i.EmailVerifiedAt,
		&i.PhoneOtpChannel,
		&i.InterfaceLanguage,
		&i.CommunicationLanguage,
		&i.EnableAppNotifications,
		&i.EnableWhatsappNotifications,
		&i.EnableSmsNotifications,
		&i.EnableEmailNotifications,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.PlatformRole,
		&i.DeletedAt,
	)
	return i, err
}
