package authn

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"os"
	"time"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/apierror"
	"Membership-SAAS-System-Backend/internal/constants"
	"Membership-SAAS-System-Backend/internal/locales"
	"Membership-SAAS-System-Backend/internal/token"
	"Membership-SAAS-System-Backend/internal/utils"

	httputil "Membership-SAAS-System-Backend/internal/utils/http"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/labstack/echo/v4"
	"github.com/rs/zerolog/log"
)

const (
	// Language constants
	languageEN = "en"
)

// InitiatePhoneRegistrationRequest defines the structure for initiating a new user registration via phone OTP.
// It's very similar to InitiatePhoneOTPRequest as both start a PKCE flow.
type InitiatePhoneRegistrationRequest struct {
	Phone               string `json:"phone" validate:"required"`
	ClientID            string `json:"client_id" validate:"required"`
	RedirectURI         string `json:"redirect_uri,omitempty"` // Optional, but good for consistency
	CodeChallenge       string `json:"code_challenge" validate:"required"`
	CodeChallengeMethod string `json:"code_challenge_method" validate:"required,eq=S256"`                   // e.g., "S256"
	State               string `json:"state" validate:"required"`                                           // Opaque value for CSRF protection
	PhoneOTPChannel     string `json:"phone_otp_channel,omitempty" validate:"omitempty,oneof=whatsapp sms"` // e.g., 'whatsapp', 'sms'
}

// InitiatePhoneRegistrationResponse defines the response after successfully initiating registration.
type InitiatePhoneRegistrationResponse struct {
	State   string `json:"state"`             // Echo back the state for client verification
	Message string `json:"message,omitempty"` // e.g., "Registration OTP initiated successfully"
	FlowID  string `json:"flow_id,omitempty"` // The ID of the created auth_flow, useful for client tracking
}

// VerifyPhoneRegistrationRequest defines the request for verifying phone OTP and completing registration.
// It includes user profile data that will be used to create the user.
type VerifyPhoneRegistrationRequest struct {
	State                 string `json:"state" validate:"required"`         // The state value from the initiation step
	OTP                   string `json:"otp" validate:"required"`           // The OTP received by the user
	CodeVerifier          string `json:"code_verifier" validate:"required"` // PKCE code verifier
	DisplayName           string `json:"display_name" validate:"required"`  // User's chosen display name
	InterfaceLanguage     string `json:"interface_language,omitempty"`
	CommunicationLanguage string `json:"communication_language,omitempty"`
	PhoneOTPChannel       string `json:"phone_otp_channel,omitempty" validate:"omitempty,oneof=whatsapp sms"` // e.g., 'whatsapp', 'sms'
}

// VerifyPhoneRegistrationResponse defines the response after successful registration and verification.
type VerifyPhoneRegistrationResponse struct {
	Message      string    `json:"message"`
	UserID       uuid.UUID `json:"user_id"`
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
}

// InitiatePhoneRegistrationHandler godoc
// @Summary Initiate OTP for new user registration
// @Description Starts the registration process for a new user with phone OTP and PKCE.
// @Tags Authentication
// @Accept json
// @Produce json
// @Param body body InitiatePhoneRegistrationRequest true "Phone number, client details, PKCE parameters, and OTP channel preference"
// @Success 200 {object} InitiatePhoneRegistrationResponse "State, message and FlowID"
// @Failure 400 {object} map[string]string "Invalid request payload or missing required fields"
// @Failure 500 {object} map[string]string "Failed to initiate registration flow or Failed to send OTP"
// @Router /authn/register/phone/initiate [post]
func (as *AuthnService) InitiatePhoneRegistrationHandler(c echo.Context) error {
	ctx := c.Request().Context()
	var req InitiatePhoneRegistrationRequest
	if err := c.Bind(&req); err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("InitiatePhoneRegistrationHandler: Failed to bind request")
		return httputil.HandleError(c, http.StatusBadRequest, "Invalid request payload", err)
	}

	if err := c.Validate(&req); err != nil {
		return err // Will be handled by CustomHTTPErrorHandler
	}

	// Validations (similar to InitiatePhoneOTPHandler)
	if req.Phone == "" {
		return httputil.HandleError(c, http.StatusBadRequest, "Phone number is required", apierror.ErrMissingFields)
	}
	if req.ClientID == "" {
		return httputil.HandleError(c, http.StatusBadRequest, "Client ID is required", apierror.ErrMissingFields)
	}
	if req.CodeChallenge == "" {
		return httputil.HandleError(c, http.StatusBadRequest, "Code challenge is required", apierror.ErrMissingFields)
	}
	if req.State == "" {
		return httputil.HandleError(c, http.StatusBadRequest, "State is required", apierror.ErrMissingFields)
	}
	if req.CodeChallengeMethod != constants.CodeChallengeMethodS256 { // Enforce S256 for now
		return httputil.HandleError(c, http.StatusBadRequest, "Code challenge method must be S256", apierror.ErrValidation)
	}

	// Check if phone number already exists to prevent duplicate registrations
	phoneParam := &req.Phone
	existingUser, err := as.Store.GetUserByPhone(ctx, phoneParam)
	if err != nil && !errors.Is(err, pgx.ErrNoRows) {
		log.Ctx(ctx).Error().Err(err).Str("phone", req.Phone).Msg("Error checking for existing phone during registration initiation")
		return httputil.HandleError(c, http.StatusInternalServerError, "Failed to verify phone availability", err)
	}
	if err == nil {
		// User found with this phone number
		log.Ctx(ctx).Info().Str("phone", req.Phone).Str("existing_user_id", existingUser.ID.String()).Msg("Attempted registration with existing phone number")
		return httputil.HandleError(c, http.StatusConflict, "Phone number already registered", apierror.ErrDuplicatePhone)
	}

	flowExpiresAt := time.Now().Add(10 * time.Minute) // OTP/flow valid for 10 minutes

	params := db.CreateAuthFlowParams{
		FlowType:            constants.AuthFlowTypePhoneRegistration, // Explicitly "phone_registration"
		CodeChallenge:       req.CodeChallenge,
		CodeChallengeMethod: req.CodeChallengeMethod,
		State:               req.State,
		ClientID:            req.ClientID,
		RedirectUri:         req.RedirectURI,
		Phone:               &req.Phone, // Phone is present
		Email:               nil,        // Email is absent for phone registration
		ExpiresAt:           flowExpiresAt,
		CodeVerifier:        nil, // Will be nil until token exchange
	}

	createdFlow, err := as.Store.CreateAuthFlow(ctx, params)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("phone", req.Phone).Msg("Error creating auth flow for registration")
		return httputil.HandleError(c, http.StatusInternalServerError, "Failed to initiate registration flow", err)
	}
	log.Ctx(ctx).Info().Str("phone", req.Phone).Str("flow_id", createdFlow.ID.String()).Msg("Created auth flow for registration. Attempting to send OTP.")

	// Determine OTP channel
	otpChannel := req.PhoneOTPChannel
	if otpChannel == "" {
		otpChannel = constants.OTPChannelWhatsApp // Default to 'whatsapp' if not specified by client for registration
		// For registration, there's no existing user preference yet.
	}
	if otpChannel != constants.OTPChannelSMS && otpChannel != constants.OTPChannelWhatsApp { // Validate channel
		log.Ctx(ctx).Warn().Str("phone", req.Phone).Str("flow_id", createdFlow.ID.String()).Str("channel", otpChannel).Msg("Invalid OTP channel specified, defaulting to whatsapp")
		otpChannel = constants.OTPChannelWhatsApp // Default to a valid channel
	}

	// Check if Twilio service is available
	if as.Twilio == nil {
		// Check if we're in production environment
		if as.Environment == "" || isProductionEnvironment(as.Environment) {
			log.Ctx(ctx).Error().Msg("Twilio service not initialized in production environment")
			return httputil.HandleError(c, http.StatusServiceUnavailable, "OTP service unavailable", fmt.Errorf("twilio not initialized"))
		}

		log.Ctx(ctx).Warn().
			Str("phone", req.Phone).
			Str("flow_id", createdFlow.ID.String()).
			Str("environment", as.Environment).
			Msg("SECURITY WARNING: Using mock OTP for registration in non-production environment")

		// Use a mock OTP SID when Twilio is not available
		otpSid := "mock_otp_sid_" + utils.GenerateRandomState(16)

		// Generate a secure mock OTP
		mockOTP, err := generateSecureOTP()
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to generate mock OTP")
			return httputil.HandleError(c, http.StatusInternalServerError, "Failed to generate OTP", err)
		}

		// Store the mock OTP (thread-safe)
		as.mockOTPsMu.Lock()
		as.mockOTPs[otpSid] = mockOTP
		as.mockOTPsMu.Unlock()

		// Update the auth_flow with mock otp_sid
		_, errUpdate := as.Store.UpdateAuthFlowOTPSID(ctx, db.UpdateAuthFlowOTPSIDParams{
			ID:     createdFlow.ID,
			OtpSid: &otpSid,
		})
		if errUpdate != nil {
			log.Ctx(ctx).Error().Err(errUpdate).Msg("Failed to update auth flow with mock OTP SID")
			return httputil.HandleError(c, http.StatusInternalServerError, "Failed to update registration flow", errUpdate)
		}

		log.Ctx(ctx).Info().
			Str("phone", req.Phone).
			Str("flow_id", createdFlow.ID.String()).
			Str("otp_sid", otpSid).
			Str("mock_otp", mockOTP). // Log OTP only in development
			Msg("Mock OTP flow created for registration")

		return c.JSON(http.StatusOK, InitiatePhoneRegistrationResponse{
			Message: fmt.Sprintf("Registration initiated. Mock OTP flow created in %s environment. Check logs for OTP.", as.Environment),
			State:   createdFlow.State,
			FlowID:  createdFlow.ID.String(),
		})
	}

	otpSid, err := as.Twilio.SendOTP(req.Phone, otpChannel)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("phone", req.Phone).Str("flow_id", createdFlow.ID.String()).Msg("Error sending OTP via Twilio for registration")
		// Consider deleting the createdFlow or marking it as failed if OTP send fails critically
		return httputil.HandleError(c, http.StatusInternalServerError, "Failed to send OTP. Please try again.", err)
	}
	log.Ctx(ctx).Info().Str("phone", req.Phone).Str("flow_id", createdFlow.ID.String()).Str("otp_sid", otpSid).Str("channel", otpChannel).Msg("OTP sent successfully for registration")

	// Update the auth_flow with otp_sid
	_, errUpdate := as.Store.UpdateAuthFlowOTPSID(ctx, db.UpdateAuthFlowOTPSIDParams{
		ID:     createdFlow.ID,
		OtpSid: &otpSid,
	})
	if errUpdate != nil {
		// Log the error but don't necessarily fail the whole operation if OTP was sent.
		// The Verify step will rely on the user entering the code.
		// However, if otp_sid is crucial for some other logic (e.g. resends without re-creating flow), this could be an issue.
		if errors.Is(errUpdate, pgx.ErrNoRows) {
			log.Ctx(ctx).Warn().Str("flow_id", createdFlow.ID.String()).Msg("Auth flow (registration) not updated with otp_sid, likely expired/consumed before update.")
		} else {
			log.Ctx(ctx).Error().Err(errUpdate).Str("flow_id", createdFlow.ID.String()).Str("otp_sid", otpSid).Msg("Error updating auth flow (registration) with otp_sid")
		}
	} else {
		log.Ctx(ctx).Info().Str("flow_id", createdFlow.ID.String()).Msg("Successfully updated auth flow (registration) with otp_sid.")
	}

	return c.JSON(http.StatusOK, InitiatePhoneRegistrationResponse{
		State:   req.State,
		Message: "Registration OTP initiated successfully. Please check your " + otpChannel + ".",
		FlowID:  createdFlow.ID.String(),
	})
}

// VerifyPhoneRegistrationHandler godoc
// @Summary Verify OTP for registration
// @Description Verifies OTP and PKCE, creates the user, and issues tokens.
// @Tags Authentication
// @Accept json
// @Produce json
// @Param body body VerifyPhoneRegistrationRequest true "State, OTP, code verifier and user details"
// @Success 201 {object} VerifyPhoneRegistrationResponse "Message, UserID, AccessToken, RefreshToken"
// @Failure 400 {object} map[string]string "Invalid request payload or missing required fields or Invalid PKCE code verifier or Invalid OTP"
// @Failure 403 {object} map[string]string "Account locked due to too many failed OTP attempts or Too many failed OTP attempts. Account locked for a short period."
// @Failure 404 {object} map[string]string "Invalid or expired registration state"
// @Failure 500 {object} map[string]string "Failed to retrieve registration flow or Registration flow integrity error or Failed to process OTP attempts or Unsupported code challenge method or OTP verification failed or Failed to create user account or Failed to generate access token or Failed to generate refresh token"
// validateVerifyPhoneRegistrationRequest validates the incoming registration verification request
func (as *AuthnService) validateVerifyPhoneRegistrationRequest(req *VerifyPhoneRegistrationRequest) error {
	if req.State == "" {
		return fmt.Errorf("state is required")
	}
	if req.OTP == "" {
		return fmt.Errorf("OTP is required")
	}
	if req.CodeVerifier == "" {
		return fmt.Errorf("code verifier is required")
	}
	if req.DisplayName == "" {
		return fmt.Errorf("display name is required")
	}
	return nil
}

// checkOTPAttemptLimits checks if the phone number is locked due to too many failed attempts
func (as *AuthnService) checkOTPAttemptLimits(ctx context.Context, phone string) error {
	attempt, err := as.Store.GetOTPAttempt(ctx, phone)
	if err != nil && !errors.Is(err, pgx.ErrNoRows) {
		log.Ctx(ctx).Error().Err(err).Str("phone", phone).Msg("Error getting OTP attempt")
		return fmt.Errorf("failed to process OTP attempts")
	}

	if err == nil && attempt.LockedUntil != nil && attempt.LockedUntil.After(time.Now()) {
		return fmt.Errorf("account locked due to too many failed OTP attempts. Try again after %s",
			time.Until(*attempt.LockedUntil).Round(time.Second).String())
	}

	return nil
}

// isTestModeOTPBypassAllowed checks if test mode OTP bypass is allowed with security controls
func (as *AuthnService) isTestModeOTPBypassAllowed(ctx context.Context, otpSid, otp string) bool {
	// CRITICAL SECURITY CHECK: Never allow test bypass in production
	// Check both service environment and global environment detection
	isServiceProduction := isProductionEnvironment(as.Environment)
	isGlobalProduction := isProductionEnvironmentSecure()

	if as.Environment == "" || isServiceProduction || isGlobalProduction {
		// In production, log any attempt to use test OTP as a security incident
		if otp == "123456" {
			log.Ctx(ctx).Error().
				Str("otp_sid", otpSid).
				Str("environment", as.Environment).
				Msg("SECURITY INCIDENT: Attempt to use hardcoded test OTP in production environment")

			// Audit log the security incident
			if as.AuditService != nil {
				_ = as.AuditService.LogAction(ctx, AuditLogActionParams{
					UserID:       uuid.Nil, // No user context yet
					Action:       "security.test_otp_production_attempt",
					ResourceType: "authentication",
					Details: map[string]interface{}{
						"otp_sid":     otpSid,
						"environment": as.Environment,
						"alert":       "hardcoded_test_otp_in_production",
					},
				})
			}
		}
		return false
	}

	// Only allow in explicitly configured test modes
	testModeEnabled := os.Getenv("TEST_MODE") == "true"
	if !testModeEnabled {
		return false
	}

	// Check for hardcoded test OTP
	if otp == "123456" {
		log.Ctx(ctx).Warn().
			Str("otp_sid", otpSid).
			Str("environment", as.Environment).
			Msg("SECURITY WARNING: Using hardcoded test OTP - only allowed in test environment")

		// Audit log the test bypass usage
		if as.AuditService != nil {
			_ = as.AuditService.LogAction(ctx, AuditLogActionParams{
				UserID:       uuid.Nil, // No user context yet
				Action:       "security.test_otp_bypass",
				ResourceType: "authentication",
				Details: map[string]interface{}{
					"otp_sid":     otpSid,
					"environment": as.Environment,
					"bypass_type": "hardcoded_test_otp",
				},
			})
		}
		return true
	}

	return false
}

// verifyOTPCode verifies the OTP code using Twilio or mock verification
func (as *AuthnService) verifyOTPCode(ctx context.Context, otpSid, otp string) (bool, error) {
	// SECURITY FIX: Environment-aware test OTP bypass with audit logging
	if as.isTestModeOTPBypassAllowed(ctx, otpSid, otp) {
		return true, nil
	}

	if as.Twilio == nil {
		// Check if we're in production environment
		if as.Environment == "" || isProductionEnvironment(as.Environment) {
			log.Ctx(ctx).Error().Msg("Twilio service not initialized in production environment")
			return false, fmt.Errorf("OTP verification service unavailable")
		}

		log.Ctx(ctx).Warn().
			Str("otp_sid", otpSid).
			Str("environment", as.Environment).
			Msg("SECURITY WARNING: Using mock OTP verification in non-production environment")

		// In development mode, check against the stored mock OTP
		as.mockOTPsMu.RLock()
		storedOTP, exists := as.mockOTPs[otpSid]
		as.mockOTPsMu.RUnlock()

		if !exists {
			log.Ctx(ctx).Error().Str("otp_sid", otpSid).Msg("Mock OTP not found for SID")
			return false, nil
		}

		otpValid := otp == storedOTP
		// Clean up used OTP
		if otpValid {
			as.mockOTPsMu.Lock()
			delete(as.mockOTPs, otpSid)
			as.mockOTPsMu.Unlock()
		}
		return otpValid, nil
	}

	otpValid, err := as.Twilio.VerifyOTPWithSID(otpSid, otp)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("otp_sid", otpSid).Msg("Error verifying OTP with Twilio")
		return false, fmt.Errorf("OTP verification failed. Please try again")
	}

	return otpValid, nil
}

// handleFailedOTPAttempt handles a failed OTP attempt, updating attempt count and locking if necessary
func (as *AuthnService) handleFailedOTPAttempt(ctx context.Context, phone string) error {
	updatedAttempt, upsertErr := as.Store.UpsertOTPAttempt(ctx, phone)
	if upsertErr != nil {
		log.Ctx(ctx).Error().Err(upsertErr).Str("phone", phone).Msg("Error upserting OTP attempt")
		return nil // Don't fail the request, just log the error
	}

	if updatedAttempt.AttemptCount >= MaxOTPAttempts {
		lockUntilTime := time.Now().Add(OTPLockoutDuration)
		_, lockErr := as.Store.LockOTPAttempts(ctx, db.LockOTPAttemptsParams{
			Phone:       phone,
			LockedUntil: &lockUntilTime,
		})
		if lockErr != nil {
			log.Ctx(ctx).Error().Err(lockErr).Str("phone", phone).Msg("Error locking OTP attempts")
		} else {
			log.Ctx(ctx).Info().Str("phone", phone).Time("lock_until", lockUntilTime).Msg("OTP attempts locked")
		}
		return fmt.Errorf("too many failed OTP attempts. Account locked for a short period")
	}

	return nil
}

// createUserFromRegistration creates a new user from registration request
func (as *AuthnService) createUserFromRegistration(ctx context.Context, req *VerifyPhoneRegistrationRequest, phone *string) (*db.User, error) {
	now := time.Now()

	// Set defaults for optional fields
	interfaceLang := req.InterfaceLanguage
	if interfaceLang == "" {
		interfaceLang = locales.LangZHHK
	}
	commLang := req.CommunicationLanguage
	if commLang == "" {
		commLang = locales.LangZHHK
	}
	phoneOTPChan := req.PhoneOTPChannel
	if phoneOTPChan == "" {
		phoneOTPChan = constants.OTPChannelWhatsApp
	}

	createUserParams := db.CreateUserWithPhoneParams{
		DisplayName:                 req.DisplayName,
		Phone:                       phone,
		PhoneVerifiedAt:             &now,
		InterfaceLanguage:           interfaceLang,
		CommunicationLanguage:       commLang,
		EnableAppNotifications:      true,
		EnableWhatsappNotifications: true,
		EnableSmsNotifications:      false,
		EnableEmailNotifications:    false,
		PhoneOtpChannel:             phoneOTPChan,
	}

	newUser, err := as.Store.CreateUserWithPhone(ctx, createUserParams)
	if err != nil {
		return nil, err
	}
	return &newUser, nil
}

// addUserToDefaultOrganization adds the user to the default organization
func (as *AuthnService) addUserToDefaultOrganization(ctx context.Context, userID uuid.UUID) {
	var err error

	switch {
	case as.OrgMembershipService != nil:
		err = as.OrgMembershipService.AddUserToDefaultOrganizationAndGrantRole(ctx, userID, "member")
	case as.OrgManager != nil:
		err = as.OrgManager.AddUserToDefaultOrganizationAndGrantRole(ctx, userID, "member")
	default:
		log.Ctx(ctx).Error().Str("user_id", userID.String()).Msg("Neither OrgMembershipService nor OrgManager is initialized in AuthnService. Cannot add user to default org.")
		return
	}

	if err != nil {
		// Log the error but don't fail the entire registration if this step fails
		log.Ctx(ctx).Error().Err(err).Str("user_id", userID.String()).Msg("Failed to add user to default organization or grant role during registration")
	}
}

// @Router /authn/register/phone/verify [post]
func (as *AuthnService) VerifyPhoneRegistrationHandler(c echo.Context) error {
	ctx := c.Request().Context()
	log.Ctx(ctx).Info().Msg("VerifyPhoneRegistrationHandler: Entered handler")

	var req VerifyPhoneRegistrationRequest
	if err := c.Bind(&req); err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("VerifyPhoneRegistrationHandler: Failed to bind request")
		return httputil.HandleError(c, http.StatusBadRequest, "Invalid request payload", err)
	}

	// Validate request
	if err := as.validateVerifyPhoneRegistrationRequest(&req); err != nil {
		return httputil.HandleError(c, http.StatusBadRequest, err.Error(), apierror.ErrMissingFields)
	}

	// 1. Fetch Auth Flow by State
	activeFlow, err := as.Store.GetAuthFlowByStateAndType(ctx, db.GetAuthFlowByStateAndTypeParams{
		State:    req.State,
		FlowType: constants.AuthFlowTypePhoneRegistration,
	})
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return httputil.HandleError(c, http.StatusNotFound, "Invalid or expired registration state", apierror.ErrNotFound)
		}
		log.Ctx(ctx).Error().Err(err).Str("state", req.State).Msg("Error fetching auth flow by state")
		return httputil.HandleError(c, http.StatusInternalServerError, "Failed to retrieve registration flow", err)
	}

	if activeFlow.Phone == nil || *activeFlow.Phone == "" {
		log.Ctx(ctx).Error().Str("flow_id", activeFlow.ID.String()).Str("state", req.State).Msg("Auth flow is missing phone number.")
		return httputil.HandleError(c, http.StatusInternalServerError, "Registration flow integrity error", nil)
	}
	currentPhone := *activeFlow.Phone

	// Check OTP attempt limits
	if err := as.checkOTPAttemptLimits(ctx, currentPhone); err != nil {
		return httputil.HandleError(c, http.StatusForbidden, err.Error(), apierror.ErrForbidden)
	}

	// 2. Verify PKCE Challenge
	if activeFlow.CodeChallengeMethod != constants.CodeChallengeMethodS256 {
		log.Ctx(ctx).Warn().Str("method", activeFlow.CodeChallengeMethod).Str("flow_id", activeFlow.ID.String()).Msg("Unsupported code challenge method")
		return httputil.HandleError(c, http.StatusBadRequest, "Unsupported code challenge method", apierror.ErrValidation)
	}
	log.Ctx(ctx).Info().Str("active_flow_code_challenge_from_db", activeFlow.CodeChallenge).Str("code_verifier_from_request", req.CodeVerifier).Msg("Before calling verifyPKCEChallengeWithAudit in VerifyPhoneRegistrationHandler")
	// Use the audit-aware PKCE verification (pass nil userID since user isn't created yet)
	if !as.verifyPKCEChallengeWithAudit(ctx, c, req.CodeVerifier, activeFlow.CodeChallenge, nil) {
		return httputil.HandleError(c, http.StatusBadRequest, "Invalid PKCE code verifier", apierror.ErrValidation)
	}

	// 3. Verify OTP
	if activeFlow.OtpSid == nil || *activeFlow.OtpSid == "" {
		log.Ctx(ctx).Error().Str("flow_id", activeFlow.ID.String()).Str("state", req.State).Msg("OTP SID missing in auth flow. Cannot verify OTP.")
		return httputil.HandleError(c, http.StatusInternalServerError, "OTP verification failed due to missing flow details. Please try initiating again.", nil)
	}

	otpValid, err := as.verifyOTPCode(ctx, *activeFlow.OtpSid, req.OTP)
	if err != nil {
		return httputil.HandleError(c, http.StatusInternalServerError, err.Error(), err)
	}

	if !otpValid {
		log.Ctx(ctx).Warn().Str("otp_sid", *activeFlow.OtpSid).Str("phone", currentPhone).Msg("Invalid OTP provided")

		// Handle failed OTP attempt
		if err := as.handleFailedOTPAttempt(ctx, currentPhone); err != nil {
			return httputil.HandleError(c, http.StatusForbidden, err.Error(), apierror.ErrForbidden)
		}
		return httputil.HandleError(c, http.StatusBadRequest, "Invalid OTP", apierror.ErrValidation)
	}

	log.Ctx(ctx).Info().Str("flow_id", activeFlow.ID.String()).Str("phone", currentPhone).Str("otp_sid", *activeFlow.OtpSid).Msg("OTP verified successfully")

	// Reset OTP attempts on success
	if resetErr := as.Store.ResetOTPAttempts(ctx, currentPhone); resetErr != nil {
		log.Ctx(ctx).Error().Err(resetErr).Str("phone", currentPhone).Msg("Error resetting OTP attempts after successful verification")
		// Log and continue, user is verified
	}

	// 4. Create User
	newUser, err := as.createUserFromRegistration(ctx, &req, activeFlow.Phone)
	if err != nil {
		if activeFlow.Phone != nil {
			log.Ctx(ctx).Error().Err(err).Str("phone", *activeFlow.Phone).Msg("Error creating user")
		} else {
			log.Ctx(ctx).Error().Err(err).Msg("Error creating user, activeFlow.Phone was nil")
		}
		return httputil.HandleError(c, http.StatusInternalServerError, "Failed to create user account", err)
	}

	// 5. Mark Auth Flow as consumed/completed
	_, errUpdate := as.Store.UpdateAuthFlowConsumed(ctx, db.UpdateAuthFlowConsumedParams{
		ID:           activeFlow.ID,
		CodeVerifier: &req.CodeVerifier,
		OtpSid:       activeFlow.OtpSid,
	})
	if errUpdate != nil {
		log.Ctx(ctx).Error().Err(errUpdate).Str("flow_id", activeFlow.ID.String()).Msg("Error updating auth flow as consumed")
		// Non-critical for user, but log it. User is already created.
	}

	if newUser.Phone != nil {
		log.Ctx(ctx).Info().Str("user_id", newUser.ID.String()).Str("phone", *newUser.Phone).Msg("User registered successfully for phone")
	} else {
		log.Ctx(ctx).Info().Str("user_id", newUser.ID.String()).Msg("User registered successfully for phone (phone is nil)")
	}

	// Add user to the Default Organization with "member" role
	as.addUserToDefaultOrganization(ctx, newUser.ID)

	// 6. Generate JWT (Access & Refresh Tokens)
	accessToken, err := token.GenerateEnhancedAccessToken(ctx, as.Store, newUser.ID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", newUser.ID.String()).Msg("Error generating access token for user")
		return httputil.HandleError(c, http.StatusInternalServerError, "Failed to generate access token", err)
	}

	refreshToken, err := token.GenerateRefreshToken(ctx, as.Store, newUser.ID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", newUser.ID.String()).Msg("Error generating refresh token for user")
		return httputil.HandleError(c, http.StatusInternalServerError, "Failed to generate refresh token", err)
	}

	return c.JSON(http.StatusCreated, VerifyPhoneRegistrationResponse{
		Message:      "User registered and phone verified successfully.",
		UserID:       newUser.ID,
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
	})
}
