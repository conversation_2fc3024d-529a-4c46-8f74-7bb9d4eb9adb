package authn

import (
	"context"

	"Membership-SAAS-System-Backend/internal/token"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
)

// SetUserIDInContext is a test helper that sets up a minimal JWT context
// for testing handlers that require authentication
func SetUserIDInContext(ctx context.Context, userID uuid.UUID) context.Context {
	// This is a simple helper that returns the context as-is
	// The actual JWT setup needs to be done at the Echo context level
	return ctx
}

// SetupTestJWTContext sets up a mock JWT token in the Echo context for testing
func SetupTestJWTContext(c echo.Context, userID uuid.UUID) {
	// Create mock enhanced claims (which is what the handlers expect)
	claims := &token.EnhancedClaims{
		UserID:       userID,
		PlatformRole: "user",
	}

	// Create a mock JWT token
	jwtToken := &jwt.Token{
		Claims: claims,
		Valid:  true,
	}

	// Set the token in the Echo context (this is what the JWT middleware would do)
	c.Set("user", jwtToken)

	// Also set user_claims and user_id for backward compatibility
	c.Set("user_claims", claims)
	c.Set("user_id", userID)
}
