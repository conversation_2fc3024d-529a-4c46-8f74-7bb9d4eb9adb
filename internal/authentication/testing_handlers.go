package authn

import (
	"errors"
	"net/http"
	"time"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/apierror"
	"Membership-SAAS-System-Backend/internal/locales"
	"Membership-SAAS-System-Backend/internal/token"
	"Membership-SAAS-System-Backend/internal/utils/auth"

	httputil "Membership-SAAS-System-Backend/internal/utils/http"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/labstack/echo/v4"
	"github.com/rs/zerolog/log"
)

// CreateStaffUserForTestingRequest defines the request for creating a staff user for testing.
// Note: This endpoint should ONLY be enabled in non-production environments.
type CreateStaffUserForTestingRequest struct {
	Email       string `json:"email" validate:"required,email"`
	Password    string `json:"password" validate:"required,min=8"`
	DisplayName string `json:"display_name" validate:"required"`
}

// CreateStaffUserForTestingHandler creates a new staff user with the given details.
// This handler is intended for testing purposes ONLY and should not be exposed in production.
func (as *AuthnService) CreateStaffUserForTestingHandler(c echo.Context) error {
	ctx := c.Request().Context()
	log.Ctx(ctx).Warn().Msg("CreateStaffUserForTestingHandler: Attempting to create a staff user via testing endpoint. THIS SHOULD NOT BE ENABLED IN PRODUCTION.")

	var req CreateStaffUserForTestingRequest
	if err := c.Bind(&req); err != nil {
		return httputil.HandleError(c, http.StatusBadRequest, "Invalid request payload", err)
	}
	if err := c.Validate(req); err != nil { // Assuming validator is set up on Echo instance
		return httputil.HandleError(c, http.StatusBadRequest, "Validation failed", err)
	}

	hashedPassword, err := auth.HashPassword(req.Password)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to hash password for test staff user")
		return httputil.HandleError(c, http.StatusInternalServerError, "Failed to process password", err)
	}

	now := time.Now()
	params := db.CreateStaffUserWithEmailPasswordParams{
		DisplayName:                 req.DisplayName,
		Email:                       &req.Email,
		EmailVerifiedAt:             &now, // Assume email is verified for testing
		HashedPassword:              &hashedPassword,
		InterfaceLanguage:           locales.LangZHHK,       // Default
		CommunicationLanguage:       locales.LangZHHK,       // Default
		EnableAppNotifications:      true,       // Default
		EnableWhatsappNotifications: true,       // Default
		EnableSmsNotifications:      false,      // Default
		EnableEmailNotifications:    true,       // Default for email user
		PhoneOtpChannel:             "whatsapp", // Default, though not primary for staff
		// IsStaff is handled by the query (TRUE)
	}

	user, err := as.Store.CreateStaffUserWithEmailPassword(ctx, params)
	if err != nil {
		// Check for unique constraint violation (e.g., email already exists)
		var pgErr *pgconn.PgError
		if errors.As(err, &pgErr) && pgErr.Code == "23505" { // Unique violation
			log.Ctx(ctx).Warn().Err(err).Str("email", req.Email).Msg("Attempt to create staff user with existing email via testing endpoint.")
			return httputil.HandleError(c, http.StatusConflict, "Email already exists", apierror.ErrDuplicateEmail)
		}
		log.Ctx(ctx).Error().Err(err).Msg("Failed to create staff user via testing endpoint")
		return httputil.HandleError(c, http.StatusInternalServerError, "Failed to create staff user", err)
	}

	log.Ctx(ctx).Info().Str("email", req.Email).Str("userID", user.ID.String()).Msg("Successfully created staff user via testing endpoint.")
	return c.JSON(http.StatusCreated, user) // Return the created user object (or a simplified DTO)
}

// CreateRegularUserForTestingRequest defines the request for creating a regular user for testing.
// Note: This endpoint should ONLY be enabled in non-production environments.
type CreateRegularUserForTestingRequest struct {
	Phone       string `json:"phone" validate:"required"` // Add phone validation if possible (e.g. E.164)
	DisplayName string `json:"display_name" validate:"required"`
}

// CreateRegularUserForTestingHandler creates a new regular (non-staff) user with the given details.
// This handler is intended for testing purposes ONLY and should not be exposed in production.
func (as *AuthnService) CreateRegularUserForTestingHandler(c echo.Context) error {
	ctx := c.Request().Context()
	log.Ctx(ctx).Warn().Msg("CreateRegularUserForTestingHandler: Attempting to create a regular user via testing endpoint. THIS SHOULD NOT BE ENABLED IN PRODUCTION.")

	var req CreateRegularUserForTestingRequest
	if err := c.Bind(&req); err != nil {
		return httputil.HandleError(c, http.StatusBadRequest, "Invalid request payload", err)
	}
	if err := c.Validate(req); err != nil { // Assuming validator is set up
		return httputil.HandleError(c, http.StatusBadRequest, "Validation failed", err)
	}

	now := time.Now()
	params := db.CreateUserWithPhoneParams{
		DisplayName:                 req.DisplayName,
		Phone:                       &req.Phone,
		PhoneVerifiedAt:             &now,       // Assume phone is verified for testing
		InterfaceLanguage:           locales.LangZHHK,       // Default
		CommunicationLanguage:       locales.LangZHHK,       // Default
		EnableAppNotifications:      true,       // Default
		EnableWhatsappNotifications: true,       // Default
		EnableSmsNotifications:      false,      // Default
		EnableEmailNotifications:    false,      // Default for phone user initially
		PhoneOtpChannel:             "whatsapp", // Default
		// IsStaff is handled by the query (FALSE)
	}

	user, err := as.Store.CreateUserWithPhone(ctx, params)
	if err != nil {
		var pgErr *pgconn.PgError
		if errors.As(err, &pgErr) && pgErr.Code == "23505" { // Unique violation for phone
			log.Ctx(ctx).Warn().Err(err).Str("phone", req.Phone).Msg("Attempt to create regular user with existing phone via testing endpoint.")
			return httputil.HandleError(c, http.StatusConflict, "Phone number already exists", apierror.ErrDuplicatePhone)
		}
		log.Ctx(ctx).Error().Err(err).Msg("Failed to create regular user via testing endpoint")
		return httputil.HandleError(c, http.StatusInternalServerError, "Failed to create regular user", err)
	}

	log.Ctx(ctx).Info().Str("phone", req.Phone).Str("userID", user.ID.String()).Msg("Successfully created regular user via testing endpoint.")
	return c.JSON(http.StatusCreated, user) // Return the created user object (or a DTO)
}

// GenerateShortLivedTokenForTestingRequest defines the request for generating a short-lived token.
type GenerateShortLivedTokenForTestingRequest struct {
	UserID        string `json:"user_id" validate:"required,uuid"`
	ExpirySeconds int    `json:"expiry_seconds" validate:"required,min=1,max=60"` // Max 60s for safety
}

// GenerateShortLivedTokenForTestingResponse defines the response.
type GenerateShortLivedTokenForTestingResponse struct {
	ShortLivedToken string `json:"short_lived_token"`
	ExpiresIn       string `json:"expires_in"`
}

// GenerateShortLivedTokenForTestingHandler creates a short-lived access token for a given user ID.
// This handler is intended for testing purposes ONLY and should not be exposed in production.
func (as *AuthnService) GenerateShortLivedTokenForTestingHandler(c echo.Context) error {
	ctx := c.Request().Context()
	log.Ctx(ctx).Warn().Msg("GenerateShortLivedTokenForTestingHandler: Attempting to generate a short-lived token via testing endpoint. THIS SHOULD NOT BE ENABLED IN PRODUCTION.")

	var req GenerateShortLivedTokenForTestingRequest
	if err := c.Bind(&req); err != nil {
		return httputil.HandleError(c, http.StatusBadRequest, "Invalid request payload", err)
	}
	if err := c.Validate(req); err != nil {
		return httputil.HandleError(c, http.StatusBadRequest, "Validation failed", err)
	}

	userIDUUID, err := uuid.Parse(req.UserID)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Str("user_id_str", req.UserID).Msg("Failed to parse UserID string to UUID")
		return httputil.HandleError(c, http.StatusBadRequest, "Invalid UserID format", err)
	}

	expiryDuration := time.Duration(req.ExpirySeconds) * time.Second

	shortToken, err := token.GenerateAccessTokenWithCustomExpiry(ctx, as.Store, userIDUUID, expiryDuration)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", req.UserID).Msg("Failed to generate short-lived token")
		return httputil.HandleError(c, http.StatusInternalServerError, "Failed to generate token", err)
	}

	log.Ctx(ctx).Info().Str("user_id", req.UserID).Dur("expiry", expiryDuration).Msg("Successfully generated short-lived token via testing endpoint.")
	return c.JSON(http.StatusOK, GenerateShortLivedTokenForTestingResponse{
		ShortLivedToken: shortToken,
		ExpiresIn:       expiryDuration.String(),
	})
}
