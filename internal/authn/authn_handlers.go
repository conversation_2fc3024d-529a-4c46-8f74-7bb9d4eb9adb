package authn

import (
	"crypto/sha256"
	"encoding/base64"
	"errors"
	"fmt"
	"net/http"
	"os"
	"time"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/locales"
	"Membership-SAAS-System-Backend/internal/token"
	"Membership-SAAS-System-Backend/internal/utils"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/labstack/echo/v4"
	"github.com/rs/zerolog/log"
)

const (
	MaxOTPAttempts     = 5
	OTPLockoutDuration = 15 * time.Minute
)

// CheckPhoneRequest defines the structure for the phone check request.
// Note: We are keeping Phone as string here. The handler will pass its address.
// If Phone were *string, binding would be more complex for a simple required field.
type CheckPhoneRequest struct {
	Phone string `json:"phone" validate:"required"`
}

// CheckPhoneResponse defines the structure for the phone check response.
type CheckPhoneResponse struct {
	Exists bool     `json:"exists"`
	User   *db.User `json:"user,omitempty"` // Optionally return user details if exists
}

// CheckPhoneHandler godoc
// @Summary Check if phone number exists
// @Description Checks if a given phone number is already registered in the system.
// @Tags Authentication
// @Accept json
// @Produce json
// @Param body body CheckPhoneRequest true "Phone number to check"
// @Success 200 {object} CheckPhoneResponse "Existence status"
// @Failure 400 {object} map[string]string "Invalid request payload or Phone number is required"
// @Failure 500 {object} map[string]string "Failed to check phone number"
// @Router /authn/phone/check [post]
func (s *AuthnService) CheckPhoneHandler(c echo.Context) error {
	ctx := c.Request().Context()
	var req CheckPhoneRequest
	if err := c.Bind(&req); err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("CheckPhoneHandler: Failed to bind request")
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request payload"})
	}

	if req.Phone == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Phone number is required"})
	}

	// Pass the address of req.Phone because GetUserByPhone expects *string
	// due to the 'phone' column being nullable in the users table.
	phoneParam := &req.Phone

	user, err := s.Queries.GetUserByPhone(ctx, phoneParam)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return c.JSON(http.StatusOK, CheckPhoneResponse{Exists: false})
		}
		log.Ctx(ctx).Error().Err(err).Str("phone", req.Phone).Msg("Error checking phone number")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to check phone number"})
	}

	return c.JSON(http.StatusOK, CheckPhoneResponse{Exists: true, User: &user})
}

// InitiatePhoneOTPRequest defines the structure for initiating a phone OTP flow for LOGIN.
type InitiatePhoneOTPRequest struct {
	Phone               string `json:"phone" validate:"required"`
	ClientID            string `json:"client_id" validate:"required"`
	RedirectURI         string `json:"redirect_uri,omitempty"`
	CodeChallenge       string `json:"code_challenge" validate:"required"`
	CodeChallengeMethod string `json:"code_challenge_method" validate:"required,eq=S256"`
	State               string `json:"state" validate:"required"`
	PhoneOTPChannel     string `json:"phone_otp_channel,omitempty"` // e.g., 'whatsapp', 'sms'
}

// InitiatePhoneOTPResponse defines the response after successfully initiating OTP.
// For now, it might just echo back the state or indicate success.
// In a real scenario, no sensitive details like code_verifier are sent back here.
type InitiatePhoneOTPResponse struct {
	State   string `json:"state"`             // Echo back the state for client verification
	Message string `json:"message,omitempty"` // e.g., "OTP initiated successfully"
	FlowID  string `json:"flow_id,omitempty"` // Optionally, the ID of the auth_flow created
}

// InitiatePhoneOTPHandler godoc
// @Summary Initiate OTP for existing user login
// @Description Sends an OTP to the user's phone for login. Requires PKCE parameters.
// @Tags Authentication
// @Accept json
// @Produce json
// @Param body body InitiatePhoneOTPRequest true "Phone number and PKCE details"
// @Success 200 {object} InitiatePhoneOTPResponse "State and message"
// @Failure 400 {object} map[string]string "Invalid request payload or missing required fields"
// @Failure 404 {object} map[string]string "User with this phone number not found. Please register first."
// @Failure 500 {object} map[string]string "Failed to check phone number or Failed to initiate login flow or Failed to send OTP"
// @Router /authn/phone/otp/initiate [post]
func (s *AuthnService) InitiatePhoneOTPHandler(c echo.Context) error {
	ctx := c.Request().Context()
	var req InitiatePhoneOTPRequest
	if err := c.Bind(&req); err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("InitiatePhoneOTPHandler: Failed to bind request")
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request payload", "details": err.Error()})
	}

	// Validations
	if req.Phone == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Phone number is required"})
	}
	if req.ClientID == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Client ID is required"})
	}
	if req.CodeChallenge == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Code challenge is required"})
	}
	if req.State == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "State is required"})
	}
	if req.CodeChallengeMethod != "S256" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Code challenge method must be S256"})
	}

	// 1. Check if user exists with this phone number
	phoneParam := &req.Phone
	user, err := s.Queries.GetUserByPhone(ctx, phoneParam)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return c.JSON(http.StatusNotFound, map[string]string{"error": "User with this phone number not found. Please register first."})
		}
		log.Ctx(ctx).Error().Err(err).Str("phone", req.Phone).Msg("Error checking phone number for login")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to check phone number"})
	}

	// User exists, proceed to create auth flow and send OTP
	flowExpiresAt := time.Now().Add(10 * time.Minute) // OTP/flow valid for 10 minutes
	authFlowParams := db.CreateAuthFlowParams{
		FlowType:            "phone_otp", // For login
		CodeChallenge:       req.CodeChallenge,
		CodeChallengeMethod: req.CodeChallengeMethod,
		State:               req.State,
		ClientID:            req.ClientID,
		RedirectUri:         req.RedirectURI,
		Phone:               &req.Phone,
		Email:               nil, // Not an email flow
		ExpiresAt:           flowExpiresAt,
		CodeVerifier:        nil, // Will be nil until token exchange
	}

	createdFlow, err := s.Queries.CreateAuthFlow(ctx, authFlowParams)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("phone", req.Phone).Msg("Error creating auth flow for login")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to initiate login flow"})
	}

	log.Ctx(ctx).Info().Str("phone", req.Phone).Str("flow_id", createdFlow.ID.String()).Msg("Created auth flow for login. Attempting to send OTP.")

	// Determine OTP channel: use request, fallback to user's preference, then system default
	otpChannel := req.PhoneOTPChannel
	if otpChannel == "" {
		// user.PhoneOtpChannel is now string due to NOT NULL in schema
		// and assuming GetUserByPhone selects it.
		otpChannel = user.PhoneOtpChannel
	}
	// If still empty here, it means user.PhoneOtpChannel was empty (shouldn't be if DB has DEFAULT and it was set)
	// or req was empty and user field was also empty. Schema DEFAULT handles DB level.
	if otpChannel == "" { // Final fallback for safety, though DB DEFAULT should prevent empty in user.PhoneOtpChannel
		otpChannel = "whatsapp" // Default to 'whatsapp' as per schema, or could be from config
	}

	otpSid, err := s.Twilio.SendOTP(req.Phone, otpChannel)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("phone", req.Phone).Str("flow_id", createdFlow.ID.String()).Msg("Error sending OTP via Twilio for login")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to send OTP. Please try again.", "details": err.Error()})
	}
	log.Ctx(ctx).Info().Str("phone", req.Phone).Str("flow_id", createdFlow.ID.String()).Str("otp_sid", otpSid).Msg("OTP sent successfully for login")

	// Update the auth_flow with otp_sid
	_, errUpdate := s.Queries.UpdateAuthFlowOTPSID(ctx, db.UpdateAuthFlowOTPSIDParams{
		ID:     createdFlow.ID,
		OtpSid: &otpSid,
	})
	if errUpdate != nil {
		if errors.Is(errUpdate, pgx.ErrNoRows) {
			log.Ctx(ctx).Warn().Str("flow_id", createdFlow.ID.String()).Msg("Auth flow (login) not updated with otp_sid, likely expired/consumed.")
		} else {
			log.Ctx(ctx).Error().Err(errUpdate).Str("flow_id", createdFlow.ID.String()).Str("otp_sid", otpSid).Msg("Error updating auth flow (login) with otp_sid")
		}
	} else {
		log.Ctx(ctx).Info().Str("flow_id", createdFlow.ID.String()).Msg("Successfully updated auth flow (login) with otp_sid.")
	}

	return c.JSON(http.StatusOK, InitiatePhoneOTPResponse{
		State:   req.State,
		Message: "Login OTP initiated successfully. Please check your " + otpChannel + ".",
		FlowID:  createdFlow.ID.String(), // Optionally return flow ID
	})
}

// InitiatePhoneRegistrationRequest defines the structure for initiating a new user registration via phone OTP.
// It's very similar to InitiatePhoneOTPRequest as both start a PKCE flow.
type InitiatePhoneRegistrationRequest struct {
	Phone               string `json:"phone" validate:"required"`
	ClientID            string `json:"client_id" validate:"required"`
	RedirectURI         string `json:"redirect_uri,omitempty"` // Optional, but good for consistency
	CodeChallenge       string `json:"code_challenge" validate:"required"`
	CodeChallengeMethod string `json:"code_challenge_method" validate:"required,eq=S256"`                   // e.g., "S256"
	State               string `json:"state" validate:"required"`                                           // Opaque value for CSRF protection
	PhoneOTPChannel     string `json:"phone_otp_channel,omitempty" validate:"omitempty,oneof=whatsapp sms"` // e.g., 'whatsapp', 'sms'
}

// InitiatePhoneRegistrationResponse defines the response after successfully initiating registration.
type InitiatePhoneRegistrationResponse struct {
	State   string `json:"state"`             // Echo back the state for client verification
	Message string `json:"message,omitempty"` // e.g., "Registration OTP initiated successfully"
	FlowID  string `json:"flow_id,omitempty"` // The ID of the created auth_flow, useful for client tracking
}

// InitiatePhoneRegistrationHandler godoc
// @Summary Initiate OTP for new user registration
// @Description Starts the registration process for a new user with phone OTP and PKCE.
// @Tags Authentication
// @Accept json
// @Produce json
// @Param body body InitiatePhoneRegistrationRequest true "Phone number, client details, PKCE parameters, and OTP channel preference"
// @Success 200 {object} InitiatePhoneRegistrationResponse "State, message and FlowID"
// @Failure 400 {object} map[string]string "Invalid request payload or missing required fields"
// @Failure 500 {object} map[string]string "Failed to initiate registration flow or Failed to send OTP"
// @Router /authn/register/phone/initiate [post]
func (s *AuthnService) InitiatePhoneRegistrationHandler(c echo.Context) error {
	ctx := c.Request().Context()
	var req InitiatePhoneRegistrationRequest
	if err := c.Bind(&req); err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("InitiatePhoneRegistrationHandler: Failed to bind request")
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request payload", "details": err.Error()})
	}

	// Validations (similar to InitiatePhoneOTPHandler)
	if req.Phone == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Phone number is required"})
	}
	if req.ClientID == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Client ID is required"})
	}
	if req.CodeChallenge == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Code challenge is required"})
	}
	if req.State == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "State is required"})
	}
	if req.CodeChallengeMethod != "S256" { // Enforce S256 for now
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Code challenge method must be S256"})
	}

	// TODO: Check if phone number ALREADY exists.
	// This endpoint assumes frontend calls it *after* /phone/check confirmed non-existence.
	// However, a race condition is possible. A check here might be redundant if frontend flow is strict,
	// or a good safeguard. For now, we proceed assuming non-existence based on user flow.

	flowExpiresAt := time.Now().Add(10 * time.Minute) // OTP/flow valid for 10 minutes

	params := db.CreateAuthFlowParams{
		FlowType:            "phone_registration", // Explicitly "phone_registration"
		CodeChallenge:       req.CodeChallenge,
		CodeChallengeMethod: req.CodeChallengeMethod,
		State:               req.State,
		ClientID:            req.ClientID,
		RedirectUri:         req.RedirectURI,
		Phone:               &req.Phone, // Phone is present
		Email:               nil,        // Email is absent for phone registration
		ExpiresAt:           flowExpiresAt,
		CodeVerifier:        nil, // Will be nil until token exchange
	}

	createdFlow, err := s.Queries.CreateAuthFlow(ctx, params)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("phone", req.Phone).Msg("Error creating auth flow for registration")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to initiate registration flow"})
	}
	log.Ctx(ctx).Info().Str("phone", req.Phone).Str("flow_id", createdFlow.ID.String()).Msg("Created auth flow for registration. Attempting to send OTP.")

	// Determine OTP channel
	otpChannel := req.PhoneOTPChannel
	if otpChannel == "" {
		otpChannel = "whatsapp" // Default to 'whatsapp' if not specified by client for registration
		// For registration, there's no existing user preference yet.
	}
	if otpChannel != "sms" && otpChannel != "whatsapp" { // Validate channel
		log.Ctx(ctx).Warn().Str("phone", req.Phone).Str("flow_id", createdFlow.ID.String()).Str("channel", otpChannel).Msg("Invalid OTP channel specified, defaulting to whatsapp")
		otpChannel = "whatsapp" // Default to a valid channel
	}

	otpSid, err := s.Twilio.SendOTP(req.Phone, otpChannel)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("phone", req.Phone).Str("flow_id", createdFlow.ID.String()).Msg("Error sending OTP via Twilio for registration")
		// Consider deleting the createdFlow or marking it as failed if OTP send fails critically
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to send OTP. Please try again.", "details": err.Error()})
	}
	log.Ctx(ctx).Info().Str("phone", req.Phone).Str("flow_id", createdFlow.ID.String()).Str("otp_sid", otpSid).Str("channel", otpChannel).Msg("OTP sent successfully for registration")

	// Update the auth_flow with otp_sid
	_, errUpdate := s.Queries.UpdateAuthFlowOTPSID(ctx, db.UpdateAuthFlowOTPSIDParams{
		ID:     createdFlow.ID,
		OtpSid: &otpSid,
	})
	if errUpdate != nil {
		// Log the error but don't necessarily fail the whole operation if OTP was sent.
		// The Verify step will rely on the user entering the code.
		// However, if otp_sid is crucial for some other logic (e.g. resends without re-creating flow), this could be an issue.
		if errors.Is(errUpdate, pgx.ErrNoRows) {
			log.Ctx(ctx).Warn().Str("flow_id", createdFlow.ID.String()).Msg("Auth flow (registration) not updated with otp_sid, likely expired/consumed before update.")
		} else {
			log.Ctx(ctx).Error().Err(errUpdate).Str("flow_id", createdFlow.ID.String()).Str("otp_sid", otpSid).Msg("Error updating auth flow (registration) with otp_sid")
		}
	} else {
		log.Ctx(ctx).Info().Str("flow_id", createdFlow.ID.String()).Msg("Successfully updated auth flow (registration) with otp_sid.")
	}

	return c.JSON(http.StatusOK, InitiatePhoneRegistrationResponse{
		State:   req.State,
		Message: "Registration OTP initiated successfully. Please check your " + otpChannel + ".",
		FlowID:  createdFlow.ID.String(),
	})
}

// VerifyPhoneRegistrationRequest defines the request for verifying phone OTP and completing registration.
// It includes user profile data that will be used to create the user.
type VerifyPhoneRegistrationRequest struct {
	State                 string `json:"state" validate:"required"`         // The state value from the initiation step
	OTP                   string `json:"otp" validate:"required"`           // The OTP received by the user
	CodeVerifier          string `json:"code_verifier" validate:"required"` // PKCE code verifier
	DisplayName           string `json:"display_name" validate:"required"`  // User's chosen display name
	InterfaceLanguage     string `json:"interface_language,omitempty"`
	CommunicationLanguage string `json:"communication_language,omitempty"`
	PhoneOTPChannel       string `json:"phone_otp_channel,omitempty" validate:"omitempty,oneof=whatsapp sms"` // e.g., 'whatsapp', 'sms'
}

// VerifyPhoneRegistrationResponse defines the response after successful registration and verification.
type VerifyPhoneRegistrationResponse struct {
	Message      string    `json:"message"`
	UserID       uuid.UUID `json:"user_id"`
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
}

// Helper function for PKCE S256 challenge verification
func verifyPKCEChallenge(codeVerifier string, storedChallenge string) bool {
	log.Debug().Str("received_code_verifier", codeVerifier).Str("stored_code_challenge", storedChallenge).Msg("verifyPKCEChallenge: Inputs")

	pkceEnabled := os.Getenv("AUTHN_PKCE_VERIFICATION_ENABLED")
	// Default to true if not set or set to anything other than "false"
	if pkceEnabled == "false" {
		log.Warn().Msg("PKCE CHECK IS CURRENTLY BYPASSED DUE TO ENVIRONMENT VARIABLE AUTHN_PKCE_VERIFICATION_ENABLED=false")
		return true
	}

	// Original logic
	if codeVerifier == "" || storedChallenge == "" {
		log.Debug().Msg("verifyPKCEChallenge: Verifier or stored challenge is empty")
		return false
	}

	log.Debug().Str("verifier_before_hash", fmt.Sprintf("'%s'", codeVerifier)).Int("verifier_len", len(codeVerifier)).Msg("verifyPKCEChallenge: Verifier details just before hashing")

	h := sha256.Sum256([]byte(codeVerifier))

	// ADD THIS LOG LINE to see the raw hash bytes (hex encoded for readability)
	log.Debug().Str("sha256_hex_output", fmt.Sprintf("%x", h[:])).Msg("verifyPKCEChallenge: SHA256 output (hex)")

	generatedChallenge := base64.RawURLEncoding.EncodeToString(h[:])
	log.Debug().Str("calculated_generated_challenge", generatedChallenge).Msg("verifyPKCEChallenge: Calculated challenge")

	result := generatedChallenge == storedChallenge
	log.Debug().Bool("pkce_match_result", result).Msg("verifyPKCEChallenge: Comparison result")
	return result
}

// VerifyPhoneRegistrationHandler godoc
// @Summary Verify OTP for registration
// @Description Verifies OTP and PKCE, creates the user, and issues tokens.
// @Tags Authentication
// @Accept json
// @Produce json
// @Param body body VerifyPhoneRegistrationRequest true "State, OTP, code verifier and user details"
// @Success 201 {object} VerifyPhoneRegistrationResponse "Message, UserID, AccessToken, RefreshToken"
// @Failure 400 {object} map[string]string "Invalid request payload or missing required fields or Invalid PKCE code verifier or Invalid OTP"
// @Failure 403 {object} map[string]string "Account locked due to too many failed OTP attempts or Too many failed OTP attempts. Account locked for a short period."
// @Failure 404 {object} map[string]string "Invalid or expired registration state"
// @Failure 500 {object} map[string]string "Failed to retrieve registration flow or Registration flow integrity error or Failed to process OTP attempts or Unsupported code challenge method or OTP verification failed or Failed to create user account or Failed to generate access token or Failed to generate refresh token"
// @Router /authn/register/phone/verify [post]
func (s *AuthnService) VerifyPhoneRegistrationHandler(c echo.Context) error {
	ctx := c.Request().Context()
	log.Ctx(ctx).Info().Msg("VerifyPhoneRegistrationHandler: Entered handler")

	var req VerifyPhoneRegistrationRequest
	if err := c.Bind(&req); err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("VerifyPhoneRegistrationHandler: Failed to bind request")
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request payload", "details": err.Error()})
	}

	// Basic Validations
	if req.State == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "State is required"})
	}
	if req.OTP == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "OTP is required"})
	}
	if req.CodeVerifier == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Code verifier is required"})
	}
	if req.DisplayName == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Display name is required"})
	}

	// 1. Fetch Auth Flow by State
	activeFlow, err := s.Queries.GetAuthFlowByStateAndType(ctx, db.GetAuthFlowByStateAndTypeParams{
		State:    req.State,
		FlowType: "phone_registration",
	})
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return c.JSON(http.StatusNotFound, map[string]string{"error": "Invalid or expired registration state"})
		}
		log.Ctx(ctx).Error().Err(err).Str("state", req.State).Msg("Error fetching auth flow by state")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to retrieve registration flow"})
	}

	if activeFlow.Phone == nil || *activeFlow.Phone == "" { // Should not happen if flow is valid
		log.Ctx(ctx).Error().Str("flow_id", activeFlow.ID.String()).Str("state", req.State).Msg("Auth flow is missing phone number.")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Registration flow integrity error."})
	}
	currentPhone := *activeFlow.Phone

	// --- OTP Attempt Limiting Logic ---
	attempt, err := s.Queries.GetOTPAttempt(ctx, currentPhone)
	if err != nil && !errors.Is(err, pgx.ErrNoRows) {
		log.Ctx(ctx).Error().Err(err).Str("phone", currentPhone).Msg("Error getting OTP attempt")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to process OTP attempts"})
	}
	if err == nil && attempt.LockedUntil != nil && attempt.LockedUntil.After(time.Now()) {
		lockoutMsg := fmt.Sprintf("Account locked due to too many failed OTP attempts. Try again after %s.",
			time.Until(*attempt.LockedUntil).Round(time.Second).String())
		log.Ctx(ctx).Warn().Str("phone", currentPhone).Msg("Account locked for OTP")
		return c.JSON(http.StatusForbidden, map[string]string{"error": lockoutMsg})
	}
	// --- End OTP Attempt Limiting Logic Check ---

	// 2. Verify PKCE Challenge
	if activeFlow.CodeChallengeMethod != "S256" {
		log.Ctx(ctx).Warn().Str("method", activeFlow.CodeChallengeMethod).Str("flow_id", activeFlow.ID.String()).Msg("Unsupported code challenge method")
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Unsupported code challenge method"})
	}
	log.Ctx(ctx).Info().Str("active_flow_code_challenge_from_db", activeFlow.CodeChallenge).Str("code_verifier_from_request", req.CodeVerifier).Msg("Before calling verifyPKCEChallenge in VerifyPhoneRegistrationHandler")
	if !verifyPKCEChallenge(req.CodeVerifier, activeFlow.CodeChallenge) {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid PKCE code verifier"})
	}

	// 3. Verify OTP with Twilio
	if activeFlow.OtpSid == nil || *activeFlow.OtpSid == "" {
		log.Ctx(ctx).Error().Str("flow_id", activeFlow.ID.String()).Str("state", req.State).Msg("OTP SID missing in auth flow. Cannot verify OTP.")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "OTP verification failed due to missing flow details. Please try initiating again."})
	}

	otpValid, err := s.Twilio.VerifyOTPWithSID(*activeFlow.OtpSid, req.OTP)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("otp_sid", *activeFlow.OtpSid).Msg("Error verifying OTP with Twilio")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "OTP verification failed. Please try again.", "details": err.Error()})
	}

	if !otpValid {
		log.Ctx(ctx).Warn().Str("otp_sid", *activeFlow.OtpSid).Str("phone", currentPhone).Msg("Invalid OTP provided")
		// --- Handle Failed OTP Attempt ---
		updatedAttempt, upsertErr := s.Queries.UpsertOTPAttempt(ctx, currentPhone)
		if upsertErr != nil {
			log.Ctx(ctx).Error().Err(upsertErr).Str("phone", currentPhone).Msg("Error upserting OTP attempt")
		} else if updatedAttempt.AttemptCount >= MaxOTPAttempts {
			lockUntilTime := time.Now().Add(OTPLockoutDuration)
			_, lockErr := s.Queries.LockOTPAttempts(ctx, db.LockOTPAttemptsParams{
				Phone:       currentPhone,
				LockedUntil: &lockUntilTime,
			})
			if lockErr != nil {
				log.Ctx(ctx).Error().Err(lockErr).Str("phone", currentPhone).Msg("Error locking OTP attempts")
			} else {
				log.Ctx(ctx).Info().Str("phone", currentPhone).Time("lock_until", lockUntilTime).Msg("OTP attempts locked")
			}
			return c.JSON(http.StatusForbidden, map[string]string{"error": "Too many failed OTP attempts. Account locked for a short period."})
		}
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid OTP"})
		// --- End Handle Failed OTP Attempt ---
	}

	log.Ctx(ctx).Info().Str("flow_id", activeFlow.ID.String()).Str("phone", currentPhone).Str("otp_sid", *activeFlow.OtpSid).Msg("OTP verified successfully")

	// --- Reset OTP Attempts on Success ---
	resetErr := s.Queries.ResetOTPAttempts(ctx, currentPhone)
	if resetErr != nil {
		log.Ctx(ctx).Error().Err(resetErr).Str("phone", currentPhone).Msg("Error resetting OTP attempts after successful verification")
		// Log and continue, user is verified.
	}
	// --- End Reset OTP Attempts ---

	// 4. Create User
	now := time.Now()

	interfaceLang := req.InterfaceLanguage
	if interfaceLang == "" {
		interfaceLang = locales.LangZHHK // Default value if not provided by client
	}
	commLang := req.CommunicationLanguage
	if commLang == "" {
		commLang = locales.LangZHHK // Default value if not provided by client
	}
	phoneOTPChan := req.PhoneOTPChannel
	if phoneOTPChan == "" {
		phoneOTPChan = "whatsapp" // Default value if not provided by client
	}

	createUserParams := db.CreateUserWithPhoneParams{
		DisplayName:     req.DisplayName,
		Phone:           activeFlow.Phone,
		PhoneVerifiedAt: &now, // This is TIMESTAMPTZ NULL, so pointer is correct.
		// These are now direct string and bool types in CreateUserWithPhoneParams
		InterfaceLanguage:           interfaceLang,
		CommunicationLanguage:       commLang,
		EnableAppNotifications:      true,  // Schema DEFAULT TRUE
		EnableWhatsappNotifications: true,  // Schema DEFAULT TRUE
		EnableSmsNotifications:      false, // Schema DEFAULT FALSE
		EnableEmailNotifications:    false, // Schema DEFAULT FALSE
		PhoneOtpChannel:             phoneOTPChan,
		// IsStaff: false, // Ensure your CreateUserWithPhone query handles is_staff
	}

	newUser, err := s.Queries.CreateUserWithPhone(ctx, createUserParams)
	if err != nil {
		// Could be a unique constraint violation if by some chance phone number was registered between checks
		if activeFlow.Phone != nil {
			log.Ctx(ctx).Error().Err(err).Str("phone", *activeFlow.Phone).Msg("Error creating user")
		} else {
			log.Ctx(ctx).Error().Err(err).Msg("Error creating user, activeFlow.Phone was nil")
		}
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to create user account"})
	}

	// 5. Mark Auth Flow as consumed/completed
	_, errUpdate := s.Queries.UpdateAuthFlowConsumed(ctx, db.UpdateAuthFlowConsumedParams{
		ID:           activeFlow.ID,
		CodeVerifier: &req.CodeVerifier,
		OtpSid:       activeFlow.OtpSid, // Persist the SID that was verified
	})
	if errUpdate != nil {
		log.Ctx(ctx).Error().Err(errUpdate).Str("flow_id", activeFlow.ID.String()).Msg("Error updating auth flow as consumed")
		// Non-critical for user, but log it. User is already created.
	}

	if newUser.Phone != nil {
		log.Ctx(ctx).Info().Str("user_id", newUser.ID.String()).Str("phone", *newUser.Phone).Msg("User registered successfully for phone")
	} else {
		log.Ctx(ctx).Info().Str("user_id", newUser.ID.String()).Msg("User registered successfully for phone (phone is nil)")
	}

	// Add user to the Default Organization with "member" role
	// Ensure s.OrgManager is not nil and properly initialized.
	if s.OrgManager == nil { // Changed from s.OrgService to s.OrgManager
		log.Ctx(ctx).Error().Str("user_id", newUser.ID.String()).Msg("Organization service (manager) is not initialized in AuthnService. Cannot add user to default org.")
		// Decide if this is a critical failure. For now, token generation will proceed.
	} else {
		err = s.OrgManager.AddUserToDefaultOrganizationAndGrantRole(ctx, newUser.ID, "member") // Changed from s.OrgService to s.OrgManager
		if err != nil {
			// Log the error but don't fail the entire registration if this step fails.
			// The user is created, tokens will be issued. Admin might need to manually check default org membership.
			log.Ctx(ctx).Error().Err(err).Str("user_id", newUser.ID.String()).Msg("Failed to add user to default organization or grant role during registration")
		}
	}

	// 6. Generate JWT (Access & Refresh Tokens)
	accessToken, err := token.GenerateAccessToken(ctx, s.Queries, newUser.ID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", newUser.ID.String()).Msg("Error generating access token for user")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to generate access token"})
	}
	// Pass context and s.Queries to GenerateRefreshToken
	refreshToken, err := token.GenerateRefreshToken(ctx, s.Queries, newUser.ID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", newUser.ID.String()).Msg("Error generating refresh token for user")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to generate refresh token"})
	}

	return c.JSON(http.StatusCreated, VerifyPhoneRegistrationResponse{
		Message:      "User registered and phone verified successfully.",
		UserID:       newUser.ID,
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
	})
}

// VerifyPhoneOTPRequest defines the request for verifying phone OTP for LOGIN.
type VerifyPhoneOTPRequest struct {
	State        string `json:"state" validate:"required"`         // The state value from the initiation step
	OTP          string `json:"otp" validate:"required"`           // The OTP received by the user
	CodeVerifier string `json:"code_verifier" validate:"required"` // PKCE code verifier
}

// VerifyPhoneOTPResponse defines the response after successful login OTP verification.
type VerifyPhoneOTPResponse struct {
	Message      string    `json:"message"`
	UserID       uuid.UUID `json:"user_id"` // ID of the logged-in user
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
}

// VerifyPhoneOTPHandler godoc
// @Summary Verify OTP for login
// @Description Verifies the OTP and PKCE challenge for phone login and issues tokens.
// @Tags Authentication
// @Accept json
// @Produce json
// @Param body body VerifyPhoneOTPRequest true "State, OTP, and PKCE verifier"
// @Success 200 {object} VerifyPhoneOTPResponse "Message, UserID, AccessToken, RefreshToken"
// @Failure 400 {object} map[string]string "Invalid request payload or missing required fields or Invalid PKCE code verifier or Invalid OTP"
// @Failure 403 {object} map[string]string "Account locked due to too many failed OTP attempts or Too many failed OTP attempts. Account locked for a short period."
// @Failure 404 {object} map[string]string "Invalid or expired login state"
// @Failure 500 {object} map[string]string "Failed to retrieve login flow or Login flow integrity error or Failed to process OTP attempts or Unsupported code challenge method or OTP verification failed or User account inconsistency or Failed to retrieve user details or Failed to generate access token or Failed to generate refresh token"
// @Router /authn/phone/otp/verify [post]
func (s *AuthnService) VerifyPhoneOTPHandler(c echo.Context) error {
	ctx := c.Request().Context()
	var req VerifyPhoneOTPRequest
	if err := c.Bind(&req); err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("VerifyPhoneOTPHandler: Failed to bind request")
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request payload", "details": err.Error()})
	}

	// Basic Validations
	if req.State == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "State is required"})
	}
	if req.OTP == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "OTP is required"})
	}
	if req.CodeVerifier == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Code verifier is required"})
	}

	// 1. Fetch Auth Flow by State
	activeFlow, err := s.Queries.GetAuthFlowByStateAndType(ctx, db.GetAuthFlowByStateAndTypeParams{
		State:    req.State,
		FlowType: "phone_otp",
	})
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return c.JSON(http.StatusNotFound, map[string]string{"error": "Invalid or expired login state"})
		}
		log.Ctx(ctx).Error().Err(err).Str("state", req.State).Msg("Error fetching auth flow by state for login")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to retrieve login flow"})
	}

	// Ensure the flow has a phone number associated (should be guaranteed by GetAuthFlowByState if flow_type is correct)
	if activeFlow.Phone == nil || *activeFlow.Phone == "" {
		log.Ctx(ctx).Error().Str("flow_id", activeFlow.ID.String()).Str("state", req.State).Msg("Auth flow is missing phone number for login.")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Login flow integrity error."})
	}
	currentPhone := *activeFlow.Phone

	// --- OTP Attempt Limiting Logic ---
	attempt, err := s.Queries.GetOTPAttempt(ctx, currentPhone)
	if err != nil && !errors.Is(err, pgx.ErrNoRows) {
		log.Ctx(ctx).Error().Err(err).Str("phone", currentPhone).Msg("Error getting OTP attempt for login")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to process OTP attempts"})
	}
	if err == nil && attempt.LockedUntil != nil && attempt.LockedUntil.After(time.Now()) {
		lockoutMsg := fmt.Sprintf("Account locked due to too many failed OTP attempts. Try again after %s.",
			time.Until(*attempt.LockedUntil).Round(time.Second).String())
		log.Ctx(ctx).Warn().Str("phone", currentPhone).Msg("Account locked for OTP (login)")
		return c.JSON(http.StatusForbidden, map[string]string{"error": lockoutMsg})
	}
	// --- End OTP Attempt Limiting Logic Check ---

	// 2. Verify PKCE Challenge
	if activeFlow.CodeChallengeMethod != "S256" {
		log.Ctx(ctx).Warn().Str("method", activeFlow.CodeChallengeMethod).Str("flow_id", activeFlow.ID.String()).Msg("Unsupported code challenge method (login)")
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Unsupported code challenge method"})
	}
	if !verifyPKCEChallenge(req.CodeVerifier, activeFlow.CodeChallenge) {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid PKCE code verifier"})
	}

	// 3. Verify OTP with Twilio
	if activeFlow.OtpSid == nil || *activeFlow.OtpSid == "" {
		log.Ctx(ctx).Error().Str("flow_id", activeFlow.ID.String()).Str("state", req.State).Msg("OTP SID missing in auth flow (login). Cannot verify OTP.")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "OTP verification failed. Please try initiating again."})
	}

	otpValid, err := s.Twilio.VerifyOTPWithSID(*activeFlow.OtpSid, req.OTP)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("otp_sid", *activeFlow.OtpSid).Msg("Error verifying OTP with Twilio (login)")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "OTP verification failed. Please try again.", "details": err.Error()})
	}

	if !otpValid {
		log.Ctx(ctx).Warn().Str("otp_sid", *activeFlow.OtpSid).Str("phone", currentPhone).Msg("Invalid OTP provided (login)")
		// --- Handle Failed OTP Attempt ---
		updatedAttempt, upsertErr := s.Queries.UpsertOTPAttempt(ctx, currentPhone)
		if upsertErr != nil {
			log.Ctx(ctx).Error().Err(upsertErr).Str("phone", currentPhone).Msg("Error upserting OTP attempt (login)")
		} else if updatedAttempt.AttemptCount >= MaxOTPAttempts {
			lockUntilTime := time.Now().Add(OTPLockoutDuration)
			_, lockErr := s.Queries.LockOTPAttempts(ctx, db.LockOTPAttemptsParams{
				Phone:       currentPhone,
				LockedUntil: &lockUntilTime,
			})
			if lockErr != nil {
				log.Ctx(ctx).Error().Err(lockErr).Str("phone", currentPhone).Msg("Error locking OTP attempts (login)")
			} else {
				log.Ctx(ctx).Info().Str("phone", currentPhone).Time("lock_until", lockUntilTime).Msg("OTP attempts locked (login)")
			}
			return c.JSON(http.StatusForbidden, map[string]string{"error": "Too many failed OTP attempts. Account locked for a short period."})
		}
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid OTP"})
		// --- End Handle Failed OTP Attempt ---
	}

	log.Ctx(ctx).Info().Str("flow_id", activeFlow.ID.String()).Str("phone", currentPhone).Str("otp_sid", *activeFlow.OtpSid).Msg("OTP verified successfully (login)")

	// --- Reset OTP Attempts on Success ---
	resetErr := s.Queries.ResetOTPAttempts(ctx, currentPhone)
	if resetErr != nil {
		log.Ctx(ctx).Error().Err(resetErr).Str("phone", currentPhone).Msg("Error resetting OTP attempts after successful login")
	}
	// --- End Reset OTP Attempts ---

	// 4. Get User (user should exist as this is login flow)
	user, err := s.Queries.GetUserByPhone(ctx, activeFlow.Phone) // activeFlow.Phone is *string
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			if activeFlow.Phone != nil {
				log.Ctx(ctx).Error().Str("phone", *activeFlow.Phone).Msg("User not found during login verification, though flow existed. Critical inconsistency.")
			} else {
				log.Ctx(ctx).Error().Msg("User not found during login verification (activeFlow.Phone is nil), though flow existed. Critical inconsistency.")
			}
			return c.JSON(http.StatusInternalServerError, map[string]string{"error": "User account inconsistency. Please contact support."})
		}
		if activeFlow.Phone != nil {
			log.Ctx(ctx).Error().Err(err).Str("phone", *activeFlow.Phone).Msg("Error fetching user by phone during login verification")
		} else {
			log.Ctx(ctx).Error().Err(err).Msg("Error fetching user by phone during login verification (activeFlow.Phone is nil)")
		}
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to retrieve user details"})
	}

	// 5. Mark Auth Flow as consumed/completed
	_, errUpdate := s.Queries.UpdateAuthFlowConsumed(ctx, db.UpdateAuthFlowConsumedParams{
		ID:           activeFlow.ID,
		CodeVerifier: &req.CodeVerifier,
		OtpSid:       activeFlow.OtpSid, // Persist the SID that was verified
	})
	if errUpdate != nil {
		log.Ctx(ctx).Error().Err(errUpdate).Str("flow_id", activeFlow.ID.String()).Msg("Error updating auth flow as consumed (login)")
		// Non-critical for user login itself at this point, but log it.
	}

	if user.Phone != nil {
		log.Ctx(ctx).Info().Str("user_id", user.ID.String()).Str("phone", *user.Phone).Msg("User logged in successfully with phone")
	} else {
		log.Ctx(ctx).Info().Str("user_id", user.ID.String()).Msg("User logged in successfully with phone (phone is nil)")
	}

	// 6. Generate JWT (Access & Refresh Tokens)
	accessToken, err := token.GenerateAccessToken(ctx, s.Queries, user.ID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", user.ID.String()).Msg("Error generating access token for login")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to generate access token"})
	}
	// Pass context and s.Queries to GenerateRefreshToken
	refreshToken, err := token.GenerateRefreshToken(ctx, s.Queries, user.ID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", user.ID.String()).Msg("Error generating refresh token for login")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to generate refresh token"})
	}

	return c.JSON(http.StatusOK, VerifyPhoneOTPResponse{
		Message:      "Login successful.",
		UserID:       user.ID,
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
	})
}

// --- Staff Email/Password Flow ---

// CheckStaffEmailRequest defines the request for checking if a staff email exists.
type CheckStaffEmailRequest struct {
	Email string `json:"email" validate:"required,email"`
}

// CheckStaffEmailResponse defines the response for checking staff email.
type CheckStaffEmailResponse struct {
	Exists   bool   `json:"exists"`
	IsStaff  bool   `json:"is_staff"`            // To confirm the user is actually staff
	UserHint string `json:"user_hint,omitempty"` // e.g., if exists but not staff
}

// CheckStaffEmailHandler godoc
// @Summary Check if staff email exists
// @Description Checks if a given email is registered for a staff member.
// @Tags Authentication, Staff
// @Accept json
// @Produce json
// @Param body body CheckStaffEmailRequest true "Email to check"
// @Success 200 {object} CheckStaffEmailResponse "Existence status and staff status"
// @Failure 400 {object} map[string]string "Invalid request payload or Email is required"
// @Failure 500 {object} map[string]string "Failed to check staff email"
// @Router /authn/staff/email/check [post]
func (s *AuthnService) CheckStaffEmailHandler(c echo.Context) error {
	ctx := c.Request().Context()
	var req CheckStaffEmailRequest
	if err := c.Bind(&req); err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("CheckStaffEmailHandler: Failed to bind request")
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request payload"})
	}

	if req.Email == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Email is required"})
	}

	// Ensure email is a pointer for GetUserByEmail if the column is nullable,
	// or direct string if NOT NULL. Assuming users.email is UNIQUE NOT NULL or UNIQUE DEFAULT NULL.
	// Your GetUserByEmail query returns *db.User, which implies it handles not found via pgx.ErrNoRows.
	// The 'email' field in your db.User struct is *string. So GetUserByEmail likely takes *string.
	emailParam := &req.Email

	user, err := s.Queries.GetUserByEmail(ctx, emailParam)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return c.JSON(http.StatusOK, CheckStaffEmailResponse{Exists: false, IsStaff: false})
		}
		log.Ctx(ctx).Error().Err(err).Str("email", req.Email).Msg("Error checking staff email")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to check email"})
	}

	// User.IsStaff should now be available and correctly typed as bool
	if !(user.Role == "admin" || user.Role == "superadmin") {
		return c.JSON(http.StatusOK, CheckStaffEmailResponse{Exists: true, IsStaff: false, UserHint: "User exists but is not authorized for staff login."})
	}
	return c.JSON(http.StatusOK, CheckStaffEmailResponse{Exists: true, IsStaff: true})
}

// InitiateStaffLoginRequest defines the request for initiating staff email/password PKCE flow.
type InitiateStaffLoginRequest struct {
	Email               string `json:"email" validate:"required,email"`
	ClientID            string `json:"client_id" validate:"required"`
	RedirectURI         string `json:"redirect_uri,omitempty"`
	CodeChallenge       string `json:"code_challenge" validate:"required"`
	CodeChallengeMethod string `json:"code_challenge_method" validate:"required,eq=S256"`
	State               string `json:"state" validate:"required"`
}

// InitiateStaffLoginResponse defines the response for initiating staff login.
type InitiateStaffLoginResponse struct {
	State  string `json:"state"`             // Echo back state
	FlowID string `json:"flow_id,omitempty"` // ID of the created auth_flow
}

// InitiateStaffLoginHandler godoc
// @Summary Initiate staff login
// @Description Initiates the login process for a staff member using email and password (first step).
// @Tags Authentication, Staff
// @Accept json
// @Produce json
// @Param body body InitiateStaffLoginRequest true "Staff email"
// @Success 200 {object} InitiateStaffLoginResponse "Login initiated, includes state for verification step"
// @Failure 400 {object} map[string]string "Invalid request payload or Email is required"
// @Failure 401 {object} map[string]string "User is not authorized as staff or Account is locked"
// @Failure 404 {object} map[string]string "Staff user not found with this email"
// @Failure 500 {object} map[string]string "Failed to initiate staff login or Failed to process login attempts"
// @Router /authn/staff/login/initiate [post]
func (s *AuthnService) InitiateStaffLoginHandler(c echo.Context) error {
	ctx := c.Request().Context()
	var req InitiateStaffLoginRequest
	if err := c.Bind(&req); err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("InitiateStaffLoginHandler: Failed to bind request")
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request payload"})
	}

	// Validations (Email, ClientID, CodeChallenge, CodeChallengeMethod, State)
	if req.Email == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Email is required"})
	}
	if req.ClientID == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Client ID is required"})
	}
	if req.CodeChallenge == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Code challenge is required"})
	}
	if req.CodeChallengeMethod != "S256" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Code challenge method must be S256"})
	}
	if req.State == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "State is required"})
	}

	// 1. Check if user exists and is staff - this step might be redundant if client calls /email/check first
	// For robustness, or if /email/check is not mandatory, include it.
	emailParam := &req.Email
	user, err := s.Queries.GetUserByEmail(ctx, emailParam)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return c.JSON(http.StatusNotFound, map[string]string{"error": "Email not found or user is not authorized for staff login."})
		}
		log.Ctx(ctx).Error().Err(err).Str("email", req.Email).Msg("Error checking staff email for login initiation")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to verify staff email"})
	}
	// User.IsStaff should now be available
	if !(user.Role == "admin" || user.Role == "superadmin") {
		return c.JSON(http.StatusForbidden, map[string]string{"error": "User is not authorized for staff login."})
	}

	// 2. Create AuthFlow
	flowExpiresAt := time.Now().Add(10 * time.Minute) // Flow valid for 10 minutes
	authFlowParams := db.CreateAuthFlowParams{
		FlowType:            "email_password",
		CodeChallenge:       req.CodeChallenge,
		CodeChallengeMethod: req.CodeChallengeMethod,
		State:               req.State,
		ClientID:            req.ClientID,
		RedirectUri:         req.RedirectURI,
		Email:               &req.Email, // Email is present
		Phone:               nil,        // Phone is absent
		ExpiresAt:           flowExpiresAt,
		CodeVerifier:        nil, // PKCE verifier not provided at this stage
	}

	createdFlow, err := s.Queries.CreateAuthFlow(ctx, authFlowParams)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("email", req.Email).Msg("Error creating auth flow for staff login")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to initiate staff login flow"})
	}

	log.Ctx(ctx).Info().Str("email", req.Email).Str("flow_id", createdFlow.ID.String()).Msg("Initiated staff login flow")

	return c.JSON(http.StatusCreated, InitiateStaffLoginResponse{
		State:  req.State,
		FlowID: createdFlow.ID.String(),
	})
}

// VerifyStaffLoginRequest defines the request for verifying staff email/password and PKCE.
type VerifyStaffLoginRequest struct {
	State        string `json:"state" validate:"required"`
	Email        string `json:"email" validate:"required,email"` // For verification against the flow
	Password     string `json:"password" validate:"required"`
	CodeVerifier string `json:"code_verifier" validate:"required"`
}

// VerifyStaffLoginResponse is the same as VerifyPhoneOTPResponse.
type VerifyStaffLoginResponse struct {
	Message      string    `json:"message"`
	UserID       uuid.UUID `json:"user_id"`
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
}

// VerifyStaffLoginHandler godoc
// @Summary Verify staff login
// @Description Verifies the staff member's password against the initiated login flow and issues tokens.
// @Tags Authentication, Staff
// @Accept json
// @Produce json
// @Param body body VerifyStaffLoginRequest true "State from initiation and password"
// @Success 200 {object} VerifyStaffLoginResponse "Login successful, includes access and refresh tokens"
// @Failure 400 {object} map[string]string "Invalid request payload, Missing state or password, or Invalid password"
// @Failure 401 {object} map[string]string "Account locked due to too many failed login attempts"
// @Failure 404 {object} map[string]string "Invalid or expired login state"
// @Failure 500 {object} map[string]string "Failed to retrieve login flow, Login flow integrity error, Failed to process login attempts, User account inconsistency, Failed to retrieve user details, Failed to generate access token, or Failed to generate refresh token"
// @Router /authn/staff/login/verify [post]
func (s *AuthnService) VerifyStaffLoginHandler(c echo.Context) error {
	ctx := c.Request().Context()
	var req VerifyStaffLoginRequest
	if err := c.Bind(&req); err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("VerifyStaffLoginHandler: Failed to bind request")
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid request payload"})
	}

	// Validations (State, Email, Password, CodeVerifier)
	if req.State == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "State is required"})
	}
	if req.Email == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Email is required"})
	}
	if req.Password == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Password is required"})
	}
	if req.CodeVerifier == "" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Code verifier is required"})
	}

	// 1. Fetch AuthFlow by State and Type
	activeFlow, err := s.Queries.GetAuthFlowByStateAndType(ctx, db.GetAuthFlowByStateAndTypeParams{
		State:    req.State,
		FlowType: "email_password",
	})
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return c.JSON(http.StatusNotFound, map[string]string{"error": "Invalid or expired login state"})
		}
		log.Ctx(ctx).Error().Err(err).Str("state", req.State).Msg("Error fetching auth flow by state for staff login")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to retrieve login flow"})
	}

	// No longer need manual FlowType check here as query handles it.
	if activeFlow.Email == nil || *activeFlow.Email != req.Email {
		log.Ctx(ctx).Warn().Str("flow_email", *activeFlow.Email).Str("req_email", req.Email).Msg("Email mismatch for staff login flow")
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Email does not match login flow"})
	}

	// 2. Verify PKCE Challenge
	if activeFlow.CodeChallengeMethod != "S256" {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Unsupported code challenge method"})
	}
	log.Ctx(ctx).Info().Str("active_flow_code_challenge_from_db", activeFlow.CodeChallenge).Str("code_verifier_from_request", req.CodeVerifier).Msg("Before calling verifyPKCEChallenge in VerifyStaffLoginHandler")
	if !verifyPKCEChallenge(req.CodeVerifier, activeFlow.CodeChallenge) {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "Invalid PKCE code verifier"})
	}

	// 3. Fetch User by Email from the flow
	user, err := s.Queries.GetUserByEmail(ctx, activeFlow.Email)
	if err != nil {
		if activeFlow.Email != nil {
			log.Ctx(ctx).Error().Err(err).Str("email", *activeFlow.Email).Msg("Error fetching staff user by email")
		} else {
			log.Ctx(ctx).Error().Err(err).Msg("Error fetching staff user by email (activeFlow.Email is nil)")
		}
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to retrieve user details"})
	}

	// 4. Check if user is staff and has a password
	// User.IsStaff should now be available
	if !(user.Role == "admin" || user.Role == "superadmin") {
		return c.JSON(http.StatusForbidden, map[string]string{"error": "User is not authorized for staff login"})
	}
	if user.HashedPassword == nil || *user.HashedPassword == "" {
		return c.JSON(http.StatusForbidden, map[string]string{"error": "Staff account has no password set. Please contact admin."})
	}

	// 5. Verify Password
	if !utils.CheckPasswordHash(req.Password, *user.HashedPassword) {
		return c.JSON(http.StatusUnauthorized, map[string]string{"error": "Invalid email or password"})
	}

	// 6. Mark Auth Flow as consumed
	// Note: UpdateAuthFlowConsumed also expires the flow.
	// OtpSid can be nil for email/password flow.
	_, errUpdate := s.Queries.UpdateAuthFlowConsumed(ctx, db.UpdateAuthFlowConsumedParams{
		ID:           activeFlow.ID,
		CodeVerifier: &req.CodeVerifier,
		OtpSid:       nil, // No OTP SID for email/password flow
	})
	if errUpdate != nil {
		log.Ctx(ctx).Error().Err(errUpdate).Str("flow_id", activeFlow.ID.String()).Msg("Error updating auth flow as consumed (staff login)")
	}

	if user.Email != nil {
		log.Ctx(ctx).Info().Str("user_id", user.ID.String()).Str("email", *user.Email).Msg("Staff user logged in successfully.")
	} else {
		log.Ctx(ctx).Info().Str("user_id", user.ID.String()).Msg("Staff user logged in successfully (email is nil).")
	}

	// 7. Generate JWTs
	accessToken, err := token.GenerateAccessToken(ctx, s.Queries, user.ID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", user.ID.String()).Msg("Error generating access token for staff login")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to generate access token"})
	}
	// Pass context and s.Queries to GenerateRefreshToken
	refreshToken, err := token.GenerateRefreshToken(ctx, s.Queries, user.ID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", user.ID.String()).Msg("Error generating refresh token for staff login")
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Failed to generate refresh token"})
	}

	return c.JSON(http.StatusOK, VerifyStaffLoginResponse{
		Message:      "Staff login successful.",
		UserID:       user.ID,
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
	})
}
