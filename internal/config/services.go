package config

import (
	"context"
	"crypto/rand"
	"errors"
	"fmt"
	"math/big"
	"os"
	"time"

	"Membership-SAAS-System-Backend/db"
	authn "Membership-SAAS-System-Backend/internal/authentication"
	"Membership-SAAS-System-Backend/internal/handlers"
	"Membership-SAAS-System-Backend/internal/handlers/event"
	"Membership-SAAS-System-Backend/internal/handlers/user"
	"Membership-SAAS-System-Backend/internal/infrastructure/batch"
	"Membership-SAAS-System-Backend/internal/infrastructure/cache"
	"Membership-SAAS-System-Backend/internal/infrastructure/dataloader"
	"Membership-SAAS-System-Backend/internal/infrastructure/filestore"
	"Membership-SAAS-System-Backend/internal/infrastructure/monitoring"
	"Membership-SAAS-System-Backend/internal/interfaces"
	"Membership-SAAS-System-Backend/internal/locales"
	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/scheduler"
	"Membership-SAAS-System-Backend/internal/services"
	"Membership-SAAS-System-Backend/internal/services/shared"
	"Membership-SAAS-System-Backend/internal/services/test_helpers"
	"Membership-SAAS-System-Backend/internal/store"
	"Membership-SAAS-System-Backend/internal/token"
	"Membership-SAAS-System-Backend/internal/twilio_service"
	"Membership-SAAS-System-Backend/internal/utils/validation"

	fileHandler "Membership-SAAS-System-Backend/internal/handlers/file"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/rs/zerolog/log"
	"go.uber.org/zap"
	"golang.org/x/crypto/bcrypt"

	analyticsservices "Membership-SAAS-System-Backend/internal/services/analytics"
	eventservices "Membership-SAAS-System-Backend/internal/services/event"
	fileservices "Membership-SAAS-System-Backend/internal/services/file"
	notificationservices "Membership-SAAS-System-Backend/internal/services/notification"
	organizationservices "Membership-SAAS-System-Backend/internal/services/organization"
	postservices "Membership-SAAS-System-Backend/internal/services/post"
	registrationservices "Membership-SAAS-System-Backend/internal/services/registration"
	resourceservices "Membership-SAAS-System-Backend/internal/services/resource"
	schedulerservices "Membership-SAAS-System-Backend/internal/services/scheduler"
	socialservices "Membership-SAAS-System-Backend/internal/services/social"
	userservices "Membership-SAAS-System-Backend/internal/services/user"
	verificationservices "Membership-SAAS-System-Backend/internal/services/verification"
	volunteerservices "Membership-SAAS-System-Backend/internal/services/volunteer"

	"Membership-SAAS-System-Backend/internal/services/bundle"
	whatsappservices "Membership-SAAS-System-Backend/internal/services/whatsapp"
)

// auditServiceAdapter adapts services.AuditService to authn.AuditServiceInterface to avoid import cycle
type auditServiceAdapter struct {
	auditSvc *services.AuditService
}

// LogAction adapts the AuditService.LogAction method to the authn interface
func (a *auditServiceAdapter) LogAction(ctx context.Context, params authn.AuditLogActionParams) error {
	// Convert authn.AuditLogActionParams to services.LogActionParams
	serviceParams := services.LogActionParams(params)
	return a.auditSvc.LogAction(ctx, serviceParams)
}

// jobServiceAdapter adapts the main JobService interface to the event domain JobService interface
type jobServiceAdapter struct {
	jobSvc *services.JobService
}

// CreateJob adapts the JobService.CreateJob method to return only an error
func (j *jobServiceAdapter) CreateJob(ctx context.Context, jobType string, payload interface{}) error {
	_, err := j.jobSvc.CreateJob(ctx, jobType, payload)
	return err
}

// organizationServiceAdapter adapts the domain ManagementService to the interfaces.OrganizationService interface
type organizationServiceAdapter struct {
	managementSvc organizationservices.ManagementService
}

// CreateOrganization adapts the OrganizationService.CreateOrganization method to the interface
func (o *organizationServiceAdapter) CreateOrganization(ctx context.Context, req interface{}) (interface{}, error) {
	// This method is not expected to be called in practice, return not implemented
	return nil, fmt.Errorf("createOrganization not implemented in adapter")
}

// GetOrganization adapts the OrganizationService.GetOrganization method to the interface
func (o *organizationServiceAdapter) GetOrganization(ctx context.Context, orgID uuid.UUID) (interface{}, error) {
	return o.managementSvc.GetOrganizationByID(ctx, orgID)
}

// UpdateOrganization adapts the OrganizationService.UpdateOrganization method to the interface
func (o *organizationServiceAdapter) UpdateOrganization(ctx context.Context, orgID uuid.UUID, req interface{}) (interface{}, error) {
	// This method is not expected to be called in practice, return not implemented
	return nil, fmt.Errorf("updateOrganization not implemented in adapter")
}

// DeleteOrganization adapts the OrganizationService.DeleteOrganization method to the interface
func (o *organizationServiceAdapter) DeleteOrganization(ctx context.Context, orgID uuid.UUID) error {
	// This method is not expected to be called in practice, return not implemented
	return fmt.Errorf("deleteOrganization not implemented in adapter")
}

// registrationServiceAdapter adapts the registration domain service to the interfaces package interface
type registrationServiceAdapter struct {
	regSvc registrationservices.RegistrationService
}

// notificationServiceAdapter adapts the legacy NotificationService to the user domain NotificationService interface
type notificationServiceAdapter struct {
	notifySvc services.NotificationService
}

// SendToUser adapts the NotificationService.SendToUser method to the user domain interface
func (n *notificationServiceAdapter) SendToUser(ctx context.Context, userID uuid.UUID, messageType string, payload map[string]interface{}) error {
	return n.notifySvc.SendToUser(ctx, userID, messageType, payload)
}

// registrationNotificationAdapter adapts the facade NotificationService to the notification domain interface
type registrationNotificationAdapter struct {
	facade services.NotificationService
	store  db.Store
}

// CreateNotification implements notification.NotificationService - not used by registration service
func (a *registrationNotificationAdapter) CreateNotification(ctx context.Context, params notificationservices.CreateNotificationParams) (*db.Notification, error) {
	return nil, fmt.Errorf("CreateNotification not implemented - use domain services directly")
}

// GetUserNotifications implements notification.NotificationService - not used by registration service
func (a *registrationNotificationAdapter) GetUserNotifications(ctx context.Context, userID uuid.UUID, orgID *uuid.UUID, limit, offset int32) ([]db.Notification, error) {
	return nil, fmt.Errorf("GetUserNotifications not implemented - use domain services directly")
}

// MarkAsRead implements notification.NotificationService - not used by registration service
func (a *registrationNotificationAdapter) MarkAsRead(ctx context.Context, notificationID, userID uuid.UUID) (*db.Notification, error) {
	return nil, fmt.Errorf("MarkAsRead not implemented - use domain services directly")
}

// MarkAllAsRead implements notification.NotificationService - not used by registration service
func (a *registrationNotificationAdapter) MarkAllAsRead(ctx context.Context, userID uuid.UUID, orgID *uuid.UUID) error {
	return fmt.Errorf("MarkAllAsRead not implemented - use domain services directly")
}

// DeleteNotification implements notification.NotificationService - not used by registration service
func (a *registrationNotificationAdapter) DeleteNotification(ctx context.Context, notificationID, userID uuid.UUID) error {
	return fmt.Errorf("DeleteNotification not implemented - use domain services directly")
}

// GetUnreadCount implements notification.NotificationService - not used by registration service
func (a *registrationNotificationAdapter) GetUnreadCount(ctx context.Context, userID uuid.UUID, orgID *uuid.UUID) (int64, error) {
	return 0, fmt.Errorf("GetUnreadCount not implemented - use domain services directly")
}

// ScheduleNotification implements notification.NotificationService - not used by registration service
func (a *registrationNotificationAdapter) ScheduleNotification(ctx context.Context, params notificationservices.CreateNotificationParams, scheduledFor time.Time) (*db.Notification, error) {
	return nil, fmt.Errorf("ScheduleNotification not implemented - use domain services directly")
}

// SendBatchNotifications implements notification.NotificationService - not used by registration service
func (a *registrationNotificationAdapter) SendBatchNotifications(ctx context.Context, userIDs []uuid.UUID, params notificationservices.CreateNotificationParams) error {
	return fmt.Errorf("SendBatchNotifications not implemented - use domain services directly")
}

// RegisterForEvent adapts the registration service method
func (r *registrationServiceAdapter) RegisterForEvent(ctx context.Context, userID, eventID uuid.UUID) (*payloads.EventRegistrationResponse, error) {
	return r.regSvc.RegisterForEvent(ctx, userID, eventID)
}

// CancelRegistration adapts the registration service method
func (r *registrationServiceAdapter) CancelRegistration(ctx context.Context, userID, registrationID uuid.UUID) (*payloads.EventRegistrationResponse, error) {
	return nil, fmt.Errorf("cancelRegistration not implemented in adapter")
}

// GetRegistration adapts the registration service method
func (r *registrationServiceAdapter) GetRegistration(ctx context.Context, userID, eventID uuid.UUID) (*payloads.EventRegistrationResponse, error) {
	return r.regSvc.GetRegistration(ctx, userID, eventID)
}

// PromoteFromWaitlist adapts the registration service method
func (r *registrationServiceAdapter) PromoteFromWaitlist(ctx context.Context, eventID uuid.UUID, slotsToFill int) ([]payloads.EventRegistrationResponse, error) {
	// Call the service method which returns []interface{}
	results, err := r.regSvc.PromoteFromWaitlist(ctx, eventID, slotsToFill)
	if err != nil {
		return nil, err
	}

	// Convert []interface{} back to []payloads.EventRegistrationResponse
	responses := make([]payloads.EventRegistrationResponse, len(results))
	for i, result := range results {
		resp, ok := result.(payloads.EventRegistrationResponse)
		if !ok {
			return nil, fmt.Errorf("unexpected type in PromoteFromWaitlist result at index %d", i)
		}
		responses[i] = resp
	}
	return responses, nil
}

// ServiceContainer holds all application services and dependencies.
//
// This container supports a dual data access pattern as part of Plan 89 migration strategy:
//
// 1. Store Pattern (New): Services use db.Store interface for full transaction support
//   - Enables proper transaction handling via ExecTx method
//   - Used by newly migrated services that need transactional operations
//   - Provides both query execution and transaction management
//
// 2. Queries Pattern (Legacy): Services use *db.Queries directly
//   - For services not yet updated to handle transactions
//   - Maintains backward compatibility during gradual migration
//   - Will be phased out as services are migrated to Store pattern
//
// Migration Path:
// - New services should use sc.Store
// - Legacy services continue using sc.LegacyQueries until migrated
// - Update services incrementally to accept db.Store interface
type ServiceContainer struct {
	// External dependencies
	DB           *DatabaseManager
	TwilioClient *twilio_service.TwilioService
	Validator    *validation.RequestValidator
	Config       *Config

	// Data access patterns - supporting gradual migration from Queries to Store
	Store         db.Store    // New pattern: full Store interface with transaction support
	LegacyQueries *db.Queries // Legacy pattern: direct Queries for services not yet migrated

	// Core services (legacy - to be removed)
	// OrganizationService removed - use organization domain services directly
	AuthenticationService *authn.AuthnService
	// UserService removed - use user domain services directly
	VolunteerService *services.VolunteerService // Legacy - use volunteer domain services
	// PostService removed - use post domain services directly
	// PostTagService removed - use PostTagService2 (domain service)
	NotificationService services.NotificationService // Legacy - use notification domain services
	JobService          *services.JobService
	SchedulerService    *scheduler.SchedulerService
	AuditService        *services.AuditService

	// New Event Domain Services
	EventManagementService eventservices.ManagementService
	EventMediaService      eventservices.MediaService
	EventVolunteerService  eventservices.VolunteerService
	EventStatisticsService eventservices.StatisticsService
	EventTagService        eventservices.TagService
	TagManagementService   eventservices.TagManagementService
	EnrollmentService      *services.EnrollmentService

	// New Registration Domain Services
	RegistrationService        registrationservices.RegistrationService
	RegistrationCheckinService registrationservices.CheckInService
	RegistrationPaymentService registrationservices.PaymentService
	RegistrationQueryService   registrationservices.QueryService

	// New Resource Domain Services
	ResourceManagementService resourceservices.ManagementService
	ResourceMediaService      resourceservices.MediaService
	ResourceFilesystemService resourceservices.FilesystemService

	// New Verification Domain Services
	VerificationRequestService  verificationservices.RequestService
	VerificationDocumentService verificationservices.DocumentService

	// New User Domain Services
	UserProfileService     userservices.ProfileService
	UserPhoneService       userservices.PhoneService
	UserStatisticsService  userservices.StatisticsService
	UserAdminService       userservices.AdminService
	UserMediaService       userservices.MediaService
	UserPasswordService    userservices.PasswordService
	AccountDeletionService *services.AccountDeletionService

	// New Organization Domain Services
	OrgManagementService organizationservices.ManagementService
	OrgMembershipService organizationservices.MembershipService
	OrgMediaService      organizationservices.MediaService

	// Composite Organization Service (for legacy compatibility and comment management)
	OrganizationService *services.OrganizationService

	// New Post Domain Services
	PostCrudService  postservices.CRUDService
	PostQueryService postservices.QueryService
	PostMediaService postservices.MediaService
	PostTagService2  postservices.TagService // Named PostTagService2 to avoid conflict with legacy

	// New Notification Domain Services
	NotificationWebsocketService notificationservices.WebSocketService
	NotificationMessageService   notificationservices.MessageService
	NotificationEventService     notificationservices.EventNotificationService

	// New Notification System Services
	NotificationService2          notificationservices.NotificationService
	NotificationPreferenceService notificationservices.NotificationPreferenceService
	NotificationPushTokenService  notificationservices.PushTokenService
	NotificationTemplateService   notificationservices.NotificationTemplateService
	NotificationQueueService      notificationservices.NotificationQueueService

	// New Volunteer Domain Services
	VolunteerApplicationService volunteerservices.ApplicationService
	VolunteerAdminService       volunteerservices.AdminService

	// New Unified Tag Service
	TagService interfaces.TagService

	// New File Domain Services
	FileUploadService fileservices.FileService

	// New Scheduler Domain Services
	SchedulerJobService       schedulerservices.JobService
	SchedulerExecutionService schedulerservices.ExecutionService
	SchedulerHistoryService   schedulerservices.HistoryService

	// New Social Domain Services
	SocialService socialservices.SocialService

	// New Analytics Domain Services
	AnalyticsService analyticsservices.AnalyticsService

	// New Bundle Domain Services
	BundleService bundle.BundleService

	// New WhatsApp Business API Services
	WhatsAppClient       whatsappservices.WhatsAppClient
	WhatsAppService      whatsappservices.WhatsAppService
	WhatsAppQueueService whatsappservices.WhatsAppQueueService

	// Shared Services
	FileService shared.FileService
	FileStore   filestore.FileStore

	// Admin Services
	AdminRegistrationService *services.AdminRegistrationService

	// Handlers
	UserHandler             *handlers.UserHandler
	UserVerificationHandler *handlers.UserVerificationHandler
	UserPasswordHandler     *user.PasswordHandler
	AccountDeletionHandler  *handlers.AccountDeletionHandler
	VolunteerHandler        *handlers.VolunteerHandler
	// EventHandler removed - replaced by domain handlers
	EventRegistrationHandler *handlers.EventRegistrationHandler
	PostHandler              *handlers.PostHandler
	ResourceHandler          *handlers.ResourceHandler
	// VolunteerApplicationHandler removed - replaced by event domain handlers

	// New Event Domain Handlers
	EventManagementHandler *event.ManagementHandler
	EventMediaHandler      *event.MediaHandler
	EventVolunteerHandler  *event.VolunteerHandler
	EventStatisticsHandler *event.StatisticsHandler
	EventTagHandler        *event.TagHandler
	EnrollmentHandler      *handlers.EnrollmentHandler

	// File Handler
	FileHandler *fileHandler.Handler

	// Tag Handler
	TagHandler *handlers.TagHandler

	// Notification Handler
	NotificationHandler *handlers.NotificationHandlers

	// WhatsApp Webhook Handler
	WhatsAppWebhookHandler *handlers.WhatsAppWebhookHandler

	// Admin Handlers
	AdminManualRegistrationHandler *handlers.AdminManualRegistrationHandler

	// Test API Service (test environments only)
	TestAPIService interfaces.TestAPIService

	// Performance Infrastructure (Phase 4.2)
	Cache          cache.Cache
	CacheService   *cache.CacheService
	CacheWarmer    *cache.CacheWarmer
	QueryMonitor   *monitoring.QueryMonitor
	DataLoaders    *dataloader.LoaderCollection
	BatchStore     *batch.BatchStore
	BatchProcessor *batch.BatchProcessor
}

// NewServiceContainer creates and initializes all services
func NewServiceContainer(cfg *Config) (*ServiceContainer, error) {
	// Initialize database
	dbManager, err := NewDatabaseManager(cfg.Database)
	if err != nil {
		return nil, err
	}

	// Load JWT configuration
	token.LoadJWTConfig()
	_, err = token.GetJWTConfig()
	if err != nil {
		return nil, err
	}
	log.Info().Msg("JWT configuration loaded.")

	// Initialize Twilio service
	twilioClient, err := twilio_service.NewTwilioService()
	if err != nil {
		log.Warn().Err(err).Msg("Failed to initialize Twilio client or it's not configured. OTP/Notification features may be impaired.")
		twilioClient = nil
	} else {
		log.Info().Msg("Twilio client initialized successfully.")
	}

	// Initialize request validator
	requestValidator := validation.NewRequestValidator()
	log.Info().Msg("Request validator initialized.")

	// Initialize performance infrastructure
	var cacheInstance cache.Cache
	var cacheMetrics *cache.CacheMetrics

	if cfg.Cache.Enabled {
		// Initialize cache metrics
		cacheMetrics = cache.NewCacheMetrics("membership", "cache")

		// Initialize Redis cache
		if cfg.Cache.Backend == "redis" {
			redisCache, err := cache.NewRedisCache(&cache.RedisConfig{
				Addr:         cfg.Cache.RedisAddr,
				Password:     cfg.Cache.RedisPassword,
				DB:           cfg.Cache.RedisDB,
				MaxRetries:   cfg.Cache.MaxRetries,
				DialTimeout:  cfg.Cache.DialTimeout,
				ReadTimeout:  cfg.Cache.ReadTimeout,
				WriteTimeout: cfg.Cache.WriteTimeout,
				PoolSize:     cfg.Cache.PoolSize,
				MinIdleConns: cfg.Cache.MinIdleConns,
				MaxIdleTime:  cfg.Cache.MaxIdleTime,
				KeyPrefix:    cfg.Cache.KeyPrefix,
			}, cacheMetrics)
			if err != nil {
				log.Warn().Err(err).Msg("Failed to initialize Redis cache, falling back to no-op cache")
				cacheInstance = cache.NewNoOpCache()
			} else {
				cacheInstance = redisCache
				log.Info().Str("addr", cfg.Cache.RedisAddr).Msg("Redis cache initialized")
			}
		} else {
			cacheInstance = cache.NewNoOpCache()
		}
	} else {
		cacheInstance = cache.NewNoOpCache()
		log.Info().Msg("Cache disabled, using no-op cache")
	}

	// Initialize query monitor
	queryMonitor := monitoring.NewQueryMonitor(100 * time.Millisecond) // 100ms slow query threshold

	container := &ServiceContainer{
		DB:           dbManager,
		TwilioClient: twilioClient,
		Validator:    requestValidator,
		Config:       cfg,

		// Initialize dual data access patterns for Plan 89 migration strategy:
		// Both Store and LegacyQueries point to the same database connection pool,
		// but provide different interfaces for gradual migration
		Store:         dbManager.Store(),   // Direct store access
		LegacyQueries: dbManager.Queries(), // Legacy: Direct SQLC queries for unmigrated services

		// Performance infrastructure
		Cache:        cacheInstance,
		QueryMonitor: queryMonitor,
	}

	// Initialize services
	if err := container.initializeServices(); err != nil {
		return nil, err
	}

	// Initialize handlers
	container.initializeHandlers()

	// Start background services
	container.startBackgroundServices()

	return container, nil
}

// initializeServices initializes all business services
func (sc *ServiceContainer) initializeServices() error {
	// Initialize notification service
	sc.NotificationService = services.NewNotificationService(sc.TwilioClient, sc.Store)
	log.Info().Msg("Notification service initialized.")

	// Initialize audit service with encryption if configured
	if sc.Config.Security.EnableAuditEncryption && sc.Config.Security.AuditLogEncryptionKey != "" {
		auditService, err := services.NewAuditServiceWithEncryption(sc.Store, sc.Config.Security.AuditLogEncryptionKey)
		if err != nil {
			return fmt.Errorf("failed to initialize audit service with encryption: %w", err)
		}
		sc.AuditService = auditService
		log.Info().Msg("Audit service initialized with encryption.")
	} else {
		sc.AuditService = services.NewAuditService(sc.Store)
		log.Info().Msg("Audit service initialized without encryption.")
	}

	// Initialize unified TagService
	sc.TagService = services.NewTagService(sc.Store)
	log.Info().Msg("Unified TagService initialized.")

	// Initialize new scheduler domain services
	sc.SchedulerJobService = schedulerservices.NewJobService(sc.Store)
	log.Info().Msg("SchedulerJobService initialized.")

	// ExecutionService will be initialized after we have all dependencies
	// HistoryService can be initialized now
	sc.SchedulerHistoryService = schedulerservices.NewHistoryService(sc.Store)
	log.Info().Msg("SchedulerHistoryService initialized.")

	// Initialize legacy job service facade (without registration service initially to break circular dependency)
	sc.JobService = services.NewJobService(sc.Store, nil)
	log.Info().Msg("Job service facade initialized.")

	// Initialize organization domain services first (needed by AuthnService)
	sc.OrgManagementService = organizationservices.NewManagementService(sc.Store)
	log.Info().Msg("OrgManagementService initialized.")

	sc.OrgMembershipService = organizationservices.NewMembershipService(sc.Store, sc.OrgManagementService)
	log.Info().Msg("OrgMembershipService initialized.")

	sc.OrgMediaService = organizationservices.NewMediaService(sc.Store, sc.Config.App.BaseURL)
	log.Info().Msg("OrgMediaService initialized.")

	// Initialize composite OrganizationService (needed for user comment management)
	sc.OrganizationService = services.NewOrganizationService(sc.Store, sc.Validator)
	log.Info().Msg("OrganizationService initialized.")

	// Ensure default organization and super admin exist
	superAdminID, err := sc.ensureDefaultOrgAndSuperAdmin()
	if err != nil {
		return err
	}

	// Initialize authentication service with domain services
	tokenService := authn.NewStubTokenService()
	auditAdapter := &auditServiceAdapter{auditSvc: sc.AuditService}
	sc.AuthenticationService = authn.NewAuthnServiceWithDomainServices(
		sc.Store,
		sc.TwilioClient,
		superAdminID,
		tokenService,
		auditAdapter, // Add audit service adapter for PKCE bypass logging
		sc.OrgManagementService,
		sc.OrgMembershipService,
		sc.OrgMediaService,
		sc.Config.App.Environment,
	)
	log.Info().Str("environment", sc.Config.App.Environment).Msg("AuthnService initialized with domain services.")

	// Initialize verification domain services
	sc.VerificationDocumentService = verificationservices.NewDocumentService(sc.Store)
	log.Info().Msg("VerificationDocumentService initialized.")

	sc.VerificationRequestService = verificationservices.NewRequestService(sc.Store, sc.VerificationDocumentService)
	log.Info().Msg("VerificationRequestService initialized.")

	// Initialize shared services first (needed by other services)
	sc.FileService = shared.NewFileService()
	log.Info().Msg("FileService initialized.")

	// Initialize FileStore based on configuration
	fileStoreCfg := &filestore.ConfigFromEnv{
		Backend:           sc.Config.Storage.Backend,
		LocalBasePath:     sc.Config.Storage.LocalBasePath,
		LocalBaseURL:      sc.Config.Storage.LocalBaseURL,
		S3Bucket:          sc.Config.Storage.S3Bucket,
		S3Region:          sc.Config.Storage.S3Region,
		S3Endpoint:        sc.Config.Storage.S3Endpoint,
		S3AccessKeyID:     sc.Config.Storage.S3AccessKeyID,
		S3SecretAccessKey: sc.Config.Storage.S3SecretAccessKey,
		S3PathPrefix:      sc.Config.Storage.S3PathPrefix,
	}
	fileStore, err := filestore.NewFromConfig(fileStoreCfg)
	if err != nil {
		return fmt.Errorf("failed to initialize file store: %w", err)
	}
	sc.FileStore = fileStore
	log.Info().Str("backend", sc.Config.Storage.Backend).Msg("FileStore initialized.")

	// UserService removed - using domain services directly
	sc.VolunteerService = services.NewVolunteerService(sc.Store, sc.OrgMembershipService)

	// Initialize new user domain services
	sc.UserProfileService = userservices.NewProfileService(sc.Store, sc.VerificationRequestService)
	log.Info().Msg("UserProfileService initialized.")

	// Create notification service adapter for user domain
	userNotificationAdapter := &notificationServiceAdapter{notifySvc: sc.NotificationService}
	sc.UserPhoneService = userservices.NewPhoneService(
		sc.Store,
		userservices.WithPhoneVerification(sc.AuthenticationService),
		userservices.WithNotificationService(userNotificationAdapter),
	)
	log.Info().Msg("UserPhoneService initialized.")

	sc.UserStatisticsService = userservices.NewStatisticsService(sc.Store)
	log.Info().Msg("UserStatisticsService initialized.")

	sc.UserAdminService = userservices.NewAdminService(sc.Store)
	log.Info().Msg("UserAdminService initialized.")

	// Initialize admin registration service (for manual user registration by admins)
	sc.AdminRegistrationService = services.NewAdminRegistrationService(sc.Store, sc.AuditService)
	log.Info().Msg("AdminRegistrationService initialized.")

	sc.UserMediaService = userservices.NewMediaService(sc.Store)
	log.Info().Msg("UserMediaService initialized.")

	sc.UserPasswordService = userservices.NewPasswordService(sc.Store)
	log.Info().Msg("UserPasswordService initialized.")

	// Initialize account deletion service
	accountDeletionConfig := services.AccountDeletionConfig{
		MockTwilioOTP:           sc.Config.Twilio.MockOTP,
		MockTwilioNotifications: sc.Config.Twilio.MockNotifications,
	}
	sc.AccountDeletionService = services.NewAccountDeletionService(sc.Store, accountDeletionConfig)
	log.Info().Msg("AccountDeletionService initialized.")

	// Organization domain services already initialized above (before AuthnService)

	// Initialize new volunteer domain services
	sc.VolunteerApplicationService = volunteerservices.NewApplicationService(sc.Store, sc.OrgMembershipService)
	log.Info().Msg("VolunteerApplicationService initialized.")

	sc.VolunteerAdminService = volunteerservices.NewAdminService(sc.Store)
	log.Info().Msg("VolunteerAdminService initialized.")

	// Shared services already initialized above

	// Initialize new event domain services
	// Create job service adapter for event domain
	eventJobServiceAdapter := &jobServiceAdapter{jobSvc: sc.JobService}

	// Create unified EventService
	eventService := eventservices.NewEventService(sc.Store, eventJobServiceAdapter)
	eventTagService := eventservices.NewTagService(sc.Store)
	eventLifecycleService := eventservices.NewLifecycleService(sc.Store, eventJobServiceAdapter)

	// Create a temporary adapter that implements ManagementService interface using EventService
	// This will be removed once all handlers are updated to use EventService directly
	sc.EventManagementService = &eventManagementAdapter{
		eventService:     eventService,
		tagService:       eventTagService,
		lifecycleService: eventLifecycleService,
	}
	log.Info().Msg("EventManagementService initialized.")

	sc.EventMediaService = eventservices.NewMediaService(sc.Store)
	log.Info().Msg("EventMediaService initialized.")

	sc.EventVolunteerService = eventservices.NewVolunteerService(sc.Store)
	log.Info().Msg("EventVolunteerService initialized.")

	sc.EventStatisticsService = eventservices.NewStatisticsService(sc.Store)
	log.Info().Msg("EventStatisticsService initialized.")

	sc.EventTagService = eventTagService // Assign to EventTagService field
	log.Info().Msg("EventTagService initialized.")

	sc.TagManagementService = eventservices.NewTagManagementService(sc.Store)
	log.Info().Msg("TagManagementService initialized.")

	sc.EnrollmentService = services.NewEnrollmentService(sc.DB.Pool(), log.Logger)
	log.Info().Msg("EnrollmentService initialized.")

	// Legacy event service removed - using domain services instead

	// Initialize new registration domain services
	// Create organization service adapter for registration domain
	orgServiceAdapter := &organizationServiceAdapter{managementSvc: sc.OrgManagementService}
	notificationAdapter := &registrationNotificationAdapter{
		facade: sc.NotificationService,
		store:  sc.DB.Store(),
	}

	sc.RegistrationService = registrationservices.NewRegistrationService(
		sc.DB.Store(),
		sc.JobService, // Pass JobService directly - it implements JobCreator interface
		notificationAdapter,
		orgServiceAdapter,
	)
	log.Info().Msg("RegistrationService initialized.")

	sc.RegistrationCheckinService = registrationservices.NewCheckInService(
		sc.DB.Store(),
	)
	log.Info().Msg("RegistrationCheckinService initialized.")

	sc.RegistrationPaymentService = registrationservices.NewPaymentService(
		sc.DB.Store(),
		orgServiceAdapter,
		sc.JobService,
	)
	log.Info().Msg("RegistrationPaymentService initialized.")

	sc.RegistrationQueryService = registrationservices.NewQueryService(
		sc.DB.Store(),
		orgServiceAdapter,
	)
	log.Info().Msg("RegistrationQueryService initialized.")

	// Now that both services exist, update the job service with the registration service
	// Using DIP pattern - JobService expects RegistrationCompleter interface
	sc.JobService.SetRegistrationCompleter(sc.RegistrationService)
	log.Info().Msg("JobService updated with RegistrationService dependency.")

	// Now we can initialize the ExecutionService with all dependencies
	sc.SchedulerExecutionService = schedulerservices.NewExecutionService(
		sc.Store,
		sc.RegistrationService, // Implements RegistrationCompleter
		sc.NotificationService, // Implements NotificationSender
	)
	log.Info().Msg("SchedulerExecutionService initialized.")

	// Update the legacy JobService facade with notification service
	sc.JobService.SetNotificationService(sc.NotificationService)

	// Initialize legacy content services (kept for backward compatibility)
	// PostTagService removed - use PostTagService2 (domain service)
	log.Info().Msg("PostTagService initialized.")

	// PostService removed - using domain services directly

	// Initialize new post domain services
	sc.PostTagService2 = postservices.NewTagService(sc.Store)
	log.Info().Msg("PostTagService2 initialized.")

	sc.PostCrudService = postservices.NewCRUDService(sc.Store, sc.PostTagService2)
	log.Info().Msg("PostCrudService initialized.")

	sc.PostQueryService = postservices.NewQueryService(sc.Store)
	log.Info().Msg("PostQueryService initialized.")

	sc.PostMediaService = postservices.NewMediaService(sc.Store)
	log.Info().Msg("PostMediaService initialized.")

	// Initialize new notification domain services
	sc.NotificationWebsocketService = notificationservices.NewWebSocketService()
	log.Info().Msg("NotificationWebsocketService initialized.")

	sc.NotificationMessageService = notificationservices.NewMessageService(sc.NotificationWebsocketService, sc.TwilioClient, sc.Store)
	log.Info().Msg("NotificationMessageService initialized.")

	sc.NotificationEventService = notificationservices.NewEventNotificationService(sc.NotificationMessageService)
	log.Info().Msg("NotificationEventService initialized.")

	// Initialize new notification system services
	storeProvider := store.NewProvider(sc.Store)

	sc.NotificationPreferenceService = notificationservices.NewPreferenceService(storeProvider)
	log.Info().Msg("NotificationPreferenceService initialized.")

	sc.NotificationPushTokenService = notificationservices.NewPushTokenService(storeProvider)
	log.Info().Msg("NotificationPushTokenService initialized.")

	sc.NotificationTemplateService = notificationservices.NewTemplateService(storeProvider)
	log.Info().Msg("NotificationTemplateService initialized.")

	// Initialize channel services
	pushProvider := notificationservices.NewExpoPushProvider()
	channelServices := map[db.NotificationChannel]notificationservices.NotificationChannelService{
		db.NotificationChannelPush: notificationservices.NewPushChannelService(
			storeProvider,
			sc.NotificationPushTokenService,
			pushProvider,
		),
		db.NotificationChannelWebsocket: notificationservices.NewWebSocketChannelService(
			sc.NotificationWebsocketService,
		),
	}

	// Initialize WhatsApp Business API services
	var whatsappChannelService notificationservices.NotificationChannelService
	if os.Getenv("WHATSAPP_PHONE_NUMBER_ID") != "" && os.Getenv("WHATSAPP_ACCESS_TOKEN") != "" {
		// Initialize WhatsApp Business API client
		whatsappConfig := whatsappservices.ConfigFromEnv()
		if err := whatsappservices.ValidateConfig(whatsappConfig); err != nil {
			log.Warn().Err(err).Msg("WhatsApp Business API configuration invalid, using Twilio fallback")
			// Use Twilio WhatsApp fallback if configured
			if sc.TwilioClient != nil {
				whatsappChannelService = notificationservices.NewWhatsAppChannelService(
					storeProvider,
					sc.TwilioClient,
					sc.NotificationTemplateService,
				)
			}
		} else {
			// Use WhatsApp Business API
			sc.WhatsAppClient = whatsappservices.NewWhatsAppClient(whatsappConfig)
			sc.WhatsAppQueueService = whatsappservices.NewWhatsAppQueueService(storeProvider, sc.WhatsAppClient)
			sc.WhatsAppService = whatsappservices.NewWhatsAppService(
				storeProvider,
				sc.WhatsAppClient,
				sc.WhatsAppQueueService,
				"+***********", // The Moment platform WhatsApp number
			)
			log.Info().Str("phone_number", "+***********").Msg("WhatsApp Business API services initialized")
		}
	} else if sc.TwilioClient != nil {
		// Fallback to Twilio WhatsApp
		whatsappChannelService = notificationservices.NewWhatsAppChannelService(
			storeProvider,
			sc.TwilioClient,
			sc.NotificationTemplateService,
		)
		log.Info().Msg("Using Twilio WhatsApp fallback (WhatsApp Business API not configured)")
	}

	// Add WhatsApp channel if available
	if whatsappChannelService != nil {
		channelServices[db.NotificationChannelWhatsapp] = whatsappChannelService
	}

	sc.NotificationQueueService = notificationservices.NewQueueService(storeProvider, channelServices)
	log.Info().Msg("NotificationQueueService initialized.")

	sc.NotificationService2 = notificationservices.NewNotificationService(
		storeProvider,
		sc.NotificationPreferenceService,
		sc.NotificationTemplateService,
		sc.NotificationQueueService,
		channelServices,
	)
	log.Info().Msg("NotificationService2 initialized.")

	// Initialize new resource domain services
	sc.ResourceManagementService = resourceservices.NewManagementService(sc.Store)
	log.Info().Msg("ResourceManagementService initialized.")

	sc.ResourceMediaService = resourceservices.NewMediaService(sc.Store)
	log.Info().Msg("ResourceMediaService initialized.")

	sc.ResourceFilesystemService = resourceservices.NewFilesystemService(sc.Store)
	log.Info().Msg("ResourceFilesystemService initialized.")

	// Initialize file service
	storageBackend := fileservices.NewLocalStorage("uploads/files")
	validator := fileservices.NewValidator()
	sc.FileUploadService = fileservices.NewService(sc.Store, storageBackend, validator)
	log.Info().Msg("FileUploadService initialized.")

	// Initialize scheduler service
	sc.SchedulerService = scheduler.NewSchedulerService(sc.NotificationService, sc.Store, sc.AccountDeletionService)
	log.Info().Msg("Scheduler service initialized.")

	// Initialize performance services
	sc.CacheService = cache.NewCacheService(sc.Cache, sc.Store)
	log.Info().Msg("CacheService initialized.")

	sc.CacheWarmer = cache.NewCacheWarmer(sc.CacheService, 5*time.Minute)
	log.Info().Msg("CacheWarmer initialized.")

	sc.DataLoaders = dataloader.CreateLoaders(sc.Store)
	log.Info().Msg("DataLoaders initialized.")

	sc.BatchStore = batch.NewBatchStore(sc.Store)
	log.Info().Msg("BatchStore initialized.")

	sc.BatchProcessor = batch.NewBatchProcessor(sc.Store, 100, 30*time.Second)
	log.Info().Msg("BatchProcessor initialized.")

	// Initialize bundle services
	bundleStoragePath := sc.Config.Storage.LocalBasePath + "/bundles"
	bundleLogger, _ := zap.NewProduction() // Simple logger for now
	bundleCollectorService := bundle.NewContentCollectorService(sc.Store, bundleLogger)
	bundleGenerationService := bundle.NewBundleGenerationService(bundleCollectorService, bundleLogger, bundleStoragePath)
	sc.BundleService = bundle.NewBundleService(sc.Store, bundleGenerationService, bundleCollectorService, bundleLogger, bundleStoragePath)
	log.Info().Msg("BundleService initialized.")

	// Initialize analytics services
	sc.AnalyticsService = analyticsservices.NewAnalyticsService(sc.Store)
	log.Info().Msg("AnalyticsService initialized.")

	// Initialize Test API Service (test environments only)
	if sc.Config.App.Environment == "test" || sc.Config.App.Environment == "testing" || sc.Config.App.Environment == "development" {
		if os.Getenv("TEST_API_ENABLED") == "true" {
			// Import test_helpers package at the top of the file
			sc.TestAPIService = test_helpers.NewTestAPIService(sc.DB.Pool())
			log.Warn().Msg("Test API Service initialized - ensure this is not a production environment")
		} else {
			log.Info().Msg("Test API Service disabled (TEST_API_ENABLED not set to true)")
		}
	}

	return nil
}

// initializeHandlers initializes all HTTP handlers
func (sc *ServiceContainer) initializeHandlers() {
	// Initialize UserHandler with domain services directly (Phase 2.1 of plan95.md)
	sc.UserHandler = handlers.NewUserHandlerWithDomainServices(
		sc.UserProfileService,
		sc.UserPhoneService,
		sc.UserStatisticsService,
		sc.UserAdminService,
		sc.UserMediaService,
		sc.Validator,
	)
	log.Info().Msg("UserHandler initialized with domain services.")

	sc.UserVerificationHandler = handlers.NewUserVerificationHandler(
		sc.VerificationRequestService,
		sc.VerificationDocumentService,
		sc.Validator,
	)
	log.Info().Msg("UserVerificationHandler initialized.")

	sc.UserPasswordHandler = user.NewPasswordHandler(sc.UserPasswordService, sc.Validator)
	log.Info().Msg("UserPasswordHandler initialized.")

	sc.AccountDeletionHandler = handlers.NewAccountDeletionHandler(sc.AccountDeletionService)
	log.Info().Msg("AccountDeletionHandler initialized.")

	sc.VolunteerHandler = handlers.NewVolunteerHandlerWithDomainServices(
		sc.VolunteerService,
		sc.VerificationRequestService,
		sc.OrgMembershipService,
	)
	log.Info().Msg("VolunteerHandler initialized.")

	// Legacy event handler removed - using domain handlers instead

	// Initialize new event domain handlers
	sc.EventManagementHandler = event.NewManagementHandler(sc.EventManagementService, sc.Validator)
	log.Info().Msg("EventManagementHandler initialized.")

	sc.EventMediaHandler = event.NewMediaHandler(sc.EventMediaService, sc.Validator)
	log.Info().Msg("EventMediaHandler initialized.")

	sc.EventVolunteerHandler = event.NewVolunteerHandler(sc.EventVolunteerService, sc.Validator)
	log.Info().Msg("EventVolunteerHandler initialized.")

	sc.EventStatisticsHandler = event.NewStatisticsHandler(sc.EventStatisticsService, sc.Validator)
	log.Info().Msg("EventStatisticsHandler initialized.")

	sc.EventTagHandler = event.NewTagHandler(sc.TagManagementService, sc.Validator)
	log.Info().Msg("EventTagHandler initialized.")

	sc.EnrollmentHandler = handlers.NewEnrollmentHandler(sc.EnrollmentService)
	log.Info().Msg("EnrollmentHandler initialized.")

	sc.EventRegistrationHandler = handlers.NewEventRegistrationHandler(
		sc.RegistrationService,
		sc.RegistrationCheckinService,
		sc.RegistrationPaymentService,
		sc.RegistrationQueryService,
		sc.Validator,
	)
	log.Info().Msg("EventRegistrationHandler initialized.")

	// Use domain services directly instead of facade
	sc.PostHandler = handlers.NewPostHandlerWithDomainServices(
		sc.PostCrudService,
		sc.PostQueryService,
		sc.PostMediaService,
		sc.PostTagService2,
		sc.Validator,
		sc.Config.App.BaseURL,
	)
	log.Info().Msg("PostHandler initialized with domain services.")

	sc.TagHandler = handlers.NewTagHandler(sc.TagService)
	log.Info().Msg("TagHandler initialized.")

	sc.NotificationHandler = handlers.NewNotificationHandlers(
		sc.NotificationService2,
		sc.NotificationPreferenceService,
		sc.NotificationPushTokenService,
	)
	log.Info().Msg("NotificationHandler initialized.")

	// Initialize admin manual registration handler
	sc.AdminManualRegistrationHandler = handlers.NewAdminManualRegistrationHandler(sc.AdminRegistrationService)
	log.Info().Msg("AdminManualRegistrationHandler initialized.")

	sc.ResourceHandler = handlers.NewResourceHandler(
		sc.ResourceManagementService,
		sc.ResourceMediaService,
		sc.ResourceFilesystemService,
		sc.Validator,
		sc.Config.App.BaseURL,
	)
	log.Info().Msg("ResourceHandler initialized.")

	// VolunteerApplicationHandler removed - functionality in event domain handlers

	// Initialize file handler
	sc.FileHandler = fileHandler.NewHandler(sc.FileUploadService, sc.Config.App.BaseURL)
	log.Info().Msg("FileHandler initialized.")

	// Initialize WhatsApp webhook handler if WhatsApp services are available
	if sc.WhatsAppService != nil && sc.WhatsAppQueueService != nil {
		sc.WhatsAppWebhookHandler = handlers.NewWhatsAppWebhookHandler(
			sc.WhatsAppService,
			sc.WhatsAppQueueService,
		)
		log.Info().Msg("WhatsAppWebhookHandler initialized.")
	}

	log.Info().Msg("All handlers initialized.")
}

// startBackgroundServices starts background workers and schedulers
func (sc *ServiceContainer) startBackgroundServices() {
	// Start job worker using new ExecutionService
	go sc.SchedulerExecutionService.StartWorker(context.Background(), "worker-1", 5*time.Second)
	log.Info().Msg("Background job worker started using new ExecutionService.")

	// Legacy facade also available for backward compatibility
	// go sc.JobService.StartWorker(context.Background(), "worker-1", 5*time.Second, sc.NotificationService)

	// Start scheduler service
	sc.SchedulerService.Start()
	log.Info().Msg("Event reminder scheduler service started.")

	// Start cache warmer if cache is enabled
	if sc.Config.Cache.Enabled && sc.CacheWarmer != nil {
		go sc.CacheWarmer.Start(context.Background())
		log.Info().Msg("Cache warmer started.")
	}

	// Start WhatsApp queue processing if WhatsApp Business API is enabled
	if sc.WhatsAppQueueService != nil {
		go func() {
			ticker := time.NewTicker(30 * time.Second) // Process queue every 30 seconds
			defer ticker.Stop()

			for {
				select {
				case <-ticker.C:
					if err := sc.WhatsAppQueueService.ProcessPending(context.Background(), 50); err != nil {
						log.Error().Err(err).Msg("Failed to process WhatsApp message queue")
					}

					// Also retry failed messages every 5 minutes
					if time.Now().Minute()%5 == 0 {
						if err := sc.WhatsAppQueueService.RetryFailed(context.Background()); err != nil {
							log.Error().Err(err).Msg("Failed to retry failed WhatsApp messages")
						}
					}
				}
			}
		}()
		log.Info().Msg("WhatsApp message queue processor started.")
	}
}

// getEnvOrDefault returns environment variable value or default
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// Helper functions for pointer creation
func stringPtr(s string) *string {
	return &s
}

func timePtr(t time.Time) *time.Time {
	return &t
}

// generateSecurePassword generates a secure random password
func generateSecurePassword() string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"
	const length = 16

	password := make([]byte, length)
	for i := range password {
		num, _ := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		password[i] = charset[num.Int64()]
	}
	return string(password)
}

// createSuperAdminUser creates a superadmin user with the given email using environment configuration
func (sc *ServiceContainer) createSuperAdminUser(ctx context.Context, email string) (db.User, error) {
	// Get configuration from environment variables
	superAdminName := getEnvOrDefault("SUPERADMIN_NAME", "Super Administrator")
	superAdminPassword := os.Getenv("SUPERADMIN_PASSWORD")
	superAdminPhone := os.Getenv("SUPERADMIN_PHONE")

	// Generate password if not provided
	if superAdminPassword == "" {
		superAdminPassword = generateSecurePassword()
		log.Printf("Generated superadmin password: %s", superAdminPassword)
	}

	// Hash the password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(superAdminPassword), bcrypt.DefaultCost)
	if err != nil {
		return db.User{}, fmt.Errorf("failed to hash password: %w", err)
	}

	// Create superadmin user
	emailPtr := &email
	emailVerifiedAtPtr := timePtr(time.Now())
	hashedPasswordPtr := stringPtr(string(hashedPassword))

	createUserParams := db.CreateStaffUserWithEmailPasswordParams{
		DisplayName:              superAdminName,
		Email:                    emailPtr,
		EmailVerifiedAt:          emailVerifiedAtPtr,
		HashedPassword:           hashedPasswordPtr,
		Phone:                    superAdminPhone,
		InterfaceLanguage:        locales.LangZHHK,
		CommunicationLanguage:    locales.LangZHHK,
		EnableAppNotifications:   false,
		EnableEmailNotifications: true,
	}

	user, err := sc.DB.Queries().CreateStaffUserWithEmailPassword(ctx, createUserParams)
	if err != nil {
		return db.User{}, fmt.Errorf("failed to create superadmin: %w", err)
	}

	// Update to super_admin platform role
	err = sc.DB.Queries().UpdateUserPlatformRole(ctx, db.UpdateUserPlatformRoleParams{
		ID:           user.ID,
		PlatformRole: db.PlatformRoleEnumSuperAdmin,
	})
	if err != nil {
		return db.User{}, fmt.Errorf("failed to update superadmin platform role: %w", err)
	}

	log.Printf("Created superadmin user: %s", email)
	return user, nil
}

// ensureDefaultOrgAndSuperAdmin ensures default organization and superadmin exist
func (sc *ServiceContainer) ensureDefaultOrgAndSuperAdmin() (uuid.UUID, error) {
	// Get superadmin email from environment variable, defaulting to "<EMAIL>"
	superAdminEmail := getEnvOrDefault("SUPERADMIN_EMAIL", "<EMAIL>")
	ctx := context.Background()

	superAdminUser, err := sc.DB.Queries().GetUserByEmail(ctx, &superAdminEmail)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			log.Warn().Str("email", superAdminEmail).Msg("Superadmin user not found on first attempt, retrying after brief delay...")
			// Wait briefly for connection pool to fully initialize, then retry
			time.Sleep(100 * time.Millisecond)
			superAdminUser, err = sc.DB.Queries().GetUserByEmail(ctx, &superAdminEmail)
			if err != nil {
				if errors.Is(err, pgx.ErrNoRows) {
					log.Warn().Str("email", superAdminEmail).Msg("Superadmin user not found, creating automatically...")

					// Create superadmin user using inline initialization logic
					superAdminUser, err = sc.createSuperAdminUser(ctx, superAdminEmail)
					if err != nil {
						return uuid.Nil, fmt.Errorf("failed to create superadmin user: %w", err)
					}

					log.Info().Msg("Superadmin user created successfully")
				} else {
					return uuid.Nil, err
				}
			}
		} else {
			return uuid.Nil, err
		}
	}
	log.Info().Str("user_id", superAdminUser.ID.String()).Msg("Superadmin user ensured.")

	// Call GetOrCreateDefaultOrganization with superAdminUser ID
	_, err = sc.OrgManagementService.GetOrCreateDefaultOrganization(ctx, superAdminUser.ID)
	if err != nil {
		return uuid.Nil, err
	}
	log.Info().Str("default_org_owner_id", superAdminUser.ID.String()).Msg("Default organization ensured.")

	return superAdminUser.ID, nil
}

// Close gracefully shuts down all services
func (sc *ServiceContainer) Close() {
	log.Info().Msg("Shutting down services...")

	// Stop cache warmer
	if sc.CacheWarmer != nil {
		sc.CacheWarmer.Stop()
		log.Info().Msg("Cache warmer stopped.")
	}

	// Shutdown audit service to flush pending logs
	if sc.AuditService != nil {
		sc.AuditService.Shutdown()
		log.Info().Msg("Audit service shut down and logs flushed.")
	}

	// Close cache connection
	if sc.Cache != nil {
		if err := sc.Cache.Close(); err != nil {
			log.Error().Err(err).Msg("Failed to close cache connection")
		} else {
			log.Info().Msg("Cache connection closed.")
		}
	}

	if sc.SchedulerService != nil {
		// Note: Add Stop method to SchedulerService if needed
		log.Info().Msg("Scheduler service stopped.")
	}

	if sc.DB != nil {
		sc.DB.Close()
		log.Info().Msg("Database connection closed.")
	}

	log.Info().Msg("All services shut down gracefully.")
}
