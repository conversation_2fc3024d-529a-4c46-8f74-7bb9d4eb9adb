package event_test

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/internal/domain"
	"Membership-SAAS-System-Backend/internal/handlers/event"
	mocks "Membership-SAAS-System-Backend/internal/mocks/services/event"
	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/token"
	"Membership-SAAS-System-Backend/internal/utils/validation"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/suite"
)

// ManagementHandlerTestSuite tests the ManagementHandler
type ManagementHandlerTestSuite struct {
	suite.Suite
	echo      *echo.Echo
	handler   *event.ManagementHandler
	mockSvc   *mocks.MockManagementService
	validator *validation.RequestValidator

	// Test data
	testOrgID   uuid.UUID
	testUserID  uuid.UUID
	testEventID uuid.UUID
}

// setupAuthenticatedContext sets up a proper authentication context for tests
func (suite *ManagementHandlerTestSuite) setupAuthenticatedContext(c echo.Context) {
	claims := &token.EnhancedClaims{
		UserID:       suite.testUserID,
		PlatformRole: "admin",
		ActiveOrg: &token.OrgContext{
			ID:   suite.testOrgID.String(),
			Role: "admin",
		},
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Subject:   suite.testUserID.String(),
		},
	}

	// Create a JWT token with the claims
	jwtToken := &jwt.Token{
		Claims: claims,
		Valid:  true,
	}

	// Set the token in the context as expected by the middleware
	c.Set("user", jwtToken)

	// Also set user_claims and user_id for backward compatibility
	c.Set("user_claims", claims)
	c.Set("user_id", suite.testUserID)
}

// SetupTest runs before each test
func (suite *ManagementHandlerTestSuite) SetupTest() {
	suite.echo = echo.New()
	suite.validator = validation.NewRequestValidator()
	suite.echo.Validator = suite.validator
	suite.mockSvc = mocks.NewMockManagementService(suite.T())
	suite.handler = event.NewManagementHandler(suite.mockSvc, suite.validator)

	// Initialize test data
	suite.testOrgID = uuid.New()
	suite.testUserID = uuid.New()
	suite.testEventID = uuid.New()
}

// TestUpdateEventStatus_Success tests successful event status update
func (suite *ManagementHandlerTestSuite) TestUpdateEventStatus_Success() {
	// Arrange
	requestBody := payloads.UpdateEventStatusRequest{
		NewStatus: "published",
	}
	bodyBytes, _ := json.Marshal(requestBody)

	req := httptest.NewRequest(http.MethodPatch, fmt.Sprintf("/organizations/%s/events/%s/status", suite.testOrgID, suite.testEventID), bytes.NewReader(bodyBytes))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("orgId", "eventId")
	c.SetParamValues(suite.testOrgID.String(), suite.testEventID.String())

	// Set authentication context
	suite.setupAuthenticatedContext(c)

	// Mock expectations
	suite.mockSvc.On("UpdateEventStatus", c.Request().Context(), suite.testEventID, suite.testOrgID, suite.testUserID, "published").
		Return(nil)

	// Act
	err := suite.handler.UpdateEventStatus(c)

	// Assert
	suite.NoError(err)
	suite.Equal(http.StatusOK, rec.Code)

	var response payloads.SuccessResponse
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Contains(response.Message, "published")
	suite.mockSvc.AssertExpectations(suite.T())
}

// TestUpdateEventStatus_InvalidOrgID tests with invalid organization ID
func (suite *ManagementHandlerTestSuite) TestUpdateEventStatus_InvalidOrgID() {
	// Arrange
	requestBody := payloads.UpdateEventStatusRequest{
		NewStatus: "published",
	}
	bodyBytes, _ := json.Marshal(requestBody)

	req := httptest.NewRequest(http.MethodPatch, "/organizations/invalid-uuid/events/123/status", bytes.NewReader(bodyBytes))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("orgID", "eventID")
	c.SetParamValues("invalid-uuid", "123")

	// Act
	err := suite.handler.UpdateEventStatus(c)

	// Assert
	// httputil.HandleError returns nil after writing the response
	suite.NoError(err)
	suite.Equal(http.StatusBadRequest, rec.Code)

	var errorResponse map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &errorResponse)
	suite.NoError(err)
	suite.NotEmpty(errorResponse["error"])
	suite.Contains(errorResponse["message"], "Invalid organization ID")
}

// TestUpdateEventStatus_InvalidEventID tests with invalid event ID
func (suite *ManagementHandlerTestSuite) TestUpdateEventStatus_InvalidEventID() {
	// Arrange
	requestBody := payloads.UpdateEventStatusRequest{
		NewStatus: "published",
	}
	bodyBytes, _ := json.Marshal(requestBody)

	req := httptest.NewRequest(http.MethodPatch, fmt.Sprintf("/organizations/%s/events/invalid-uuid/status", suite.testOrgID), bytes.NewReader(bodyBytes))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("orgId", "eventId")
	c.SetParamValues(suite.testOrgID.String(), "invalid-uuid")

	// Act
	err := suite.handler.UpdateEventStatus(c)

	// Assert
	// httputil.HandleError returns nil after writing the response
	suite.NoError(err)
	suite.Equal(http.StatusBadRequest, rec.Code)

	var errorResponse map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &errorResponse)
	suite.NoError(err)
	suite.NotEmpty(errorResponse["error"])
	suite.Contains(errorResponse["message"], "Invalid event ID")
}

// TestUpdateEventStatus_Unauthorized tests without authentication
func (suite *ManagementHandlerTestSuite) TestUpdateEventStatus_Unauthorized() {
	// Arrange
	requestBody := payloads.UpdateEventStatusRequest{
		NewStatus: "published",
	}
	bodyBytes, _ := json.Marshal(requestBody)

	req := httptest.NewRequest(http.MethodPatch, fmt.Sprintf("/organizations/%s/events/%s/status", suite.testOrgID, suite.testEventID), bytes.NewReader(bodyBytes))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("orgId", "eventId")
	c.SetParamValues(suite.testOrgID.String(), suite.testEventID.String())

	// No authentication context set

	// Act
	err := suite.handler.UpdateEventStatus(c)

	// Assert
	// httputil.HandleError returns nil after writing the response
	suite.NoError(err)
	suite.Equal(http.StatusUnauthorized, rec.Code)

	var errorResponse map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &errorResponse)
	suite.NoError(err)
	suite.NotEmpty(errorResponse["error"])
	suite.Contains(errorResponse["message"], "Unauthorized")
}

// TestUpdateEventStatus_ValidationFailed tests with invalid request body
func (suite *ManagementHandlerTestSuite) TestUpdateEventStatus_ValidationFailed() {
	// Arrange
	requestBody := payloads.UpdateEventStatusRequest{
		NewStatus: "invalid_status", // This should fail validation
	}
	bodyBytes, _ := json.Marshal(requestBody)

	req := httptest.NewRequest(http.MethodPatch, fmt.Sprintf("/organizations/%s/events/%s/status", suite.testOrgID, suite.testEventID), bytes.NewReader(bodyBytes))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("orgId", "eventId")
	c.SetParamValues(suite.testOrgID.String(), suite.testEventID.String())

	// Set authentication context
	suite.setupAuthenticatedContext(c)

	// Act
	err := suite.handler.UpdateEventStatus(c)

	// Assert
	// httputil.HandleError returns nil after writing the response
	suite.NoError(err)
	suite.Equal(http.StatusBadRequest, rec.Code)

	var errorResponse map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &errorResponse)
	suite.NoError(err)
	suite.NotEmpty(errorResponse["error"])
	suite.Contains(errorResponse["message"], "Validation failed")
}

// TestUpdateEventStatus_ServiceError tests service layer error handling
func (suite *ManagementHandlerTestSuite) TestUpdateEventStatus_ServiceError() {
	// Arrange
	requestBody := payloads.UpdateEventStatusRequest{
		NewStatus: "published",
	}
	bodyBytes, _ := json.Marshal(requestBody)

	req := httptest.NewRequest(http.MethodPatch, fmt.Sprintf("/organizations/%s/events/%s/status", suite.testOrgID, suite.testEventID), bytes.NewReader(bodyBytes))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("orgId", "eventId")
	c.SetParamValues(suite.testOrgID.String(), suite.testEventID.String())

	// Set authentication context
	suite.setupAuthenticatedContext(c)

	// Mock expectations - return error
	suite.mockSvc.On("UpdateEventStatus", c.Request().Context(), suite.testEventID, suite.testOrgID, suite.testUserID, "published").
		Return(fmt.Errorf("service error"))

	// Act
	err := suite.handler.UpdateEventStatus(c)

	// Assert
	// httputil.HandleError returns nil after writing the response
	suite.NoError(err)
	suite.Equal(http.StatusInternalServerError, rec.Code)

	var errorResponse map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &errorResponse)
	suite.NoError(err)
	suite.NotEmpty(errorResponse["error"])
	suite.Contains(errorResponse["message"], "Failed to update event status")
	suite.mockSvc.AssertExpectations(suite.T())
}

// TestUpdateEventStatus_EventNotFound tests event not found error
func (suite *ManagementHandlerTestSuite) TestUpdateEventStatus_EventNotFound() {
	// Arrange
	requestBody := payloads.UpdateEventStatusRequest{
		NewStatus: "published",
	}
	bodyBytes, _ := json.Marshal(requestBody)

	req := httptest.NewRequest(http.MethodPatch, fmt.Sprintf("/organizations/%s/events/%s/status", suite.testOrgID, suite.testEventID), bytes.NewReader(bodyBytes))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("orgId", "eventId")
	c.SetParamValues(suite.testOrgID.String(), suite.testEventID.String())

	// Set authentication context
	suite.setupAuthenticatedContext(c)

	// Mock expectations - return not found error
	suite.mockSvc.On("UpdateEventStatus", c.Request().Context(), suite.testEventID, suite.testOrgID, suite.testUserID, "published").
		Return(&domain.EventNotFoundError{EventID: suite.testEventID})

	// Act
	err := suite.handler.UpdateEventStatus(c)

	// Assert
	// httputil.HandleError returns nil after writing the response
	suite.NoError(err)
	suite.Equal(http.StatusNotFound, rec.Code)

	var errorResponse map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &errorResponse)
	suite.NoError(err)
	suite.NotEmpty(errorResponse["error"])
	suite.Contains(errorResponse["message"], "Event not found")
	suite.mockSvc.AssertExpectations(suite.T())
}

// TestUpdateEventStatus_InsufficientPermissions tests insufficient permissions error
func (suite *ManagementHandlerTestSuite) TestUpdateEventStatus_InsufficientPermissions() {
	// Arrange
	requestBody := payloads.UpdateEventStatusRequest{
		NewStatus: "published",
	}
	bodyBytes, _ := json.Marshal(requestBody)

	req := httptest.NewRequest(http.MethodPatch, fmt.Sprintf("/organizations/%s/events/%s/status", suite.testOrgID, suite.testEventID), bytes.NewReader(bodyBytes))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("orgId", "eventId")
	c.SetParamValues(suite.testOrgID.String(), suite.testEventID.String())

	// Set authentication context
	suite.setupAuthenticatedContext(c)

	// Mock expectations - return insufficient permissions error
	suite.mockSvc.On("UpdateEventStatus", c.Request().Context(), suite.testEventID, suite.testOrgID, suite.testUserID, "published").
		Return(&domain.InsufficientPermissionsError{
			UserID:       suite.testUserID,
			RequiredRole: "manager/admin/owner",
			Action:       "update event status",
		})

	// Act
	err := suite.handler.UpdateEventStatus(c)

	// Assert
	// httputil.HandleError returns nil after writing the response
	suite.NoError(err)
	suite.Equal(http.StatusForbidden, rec.Code)

	var errorResponse map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &errorResponse)
	suite.NoError(err)
	suite.NotEmpty(errorResponse["error"])
	suite.Contains(errorResponse["message"], "Insufficient permissions")
	suite.mockSvc.AssertExpectations(suite.T())
}

// Run the test suite
func TestManagementHandlerTestSuite(t *testing.T) {
	suite.Run(t, new(ManagementHandlerTestSuite))
}
