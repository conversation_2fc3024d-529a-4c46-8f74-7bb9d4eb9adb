package event

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/token"
	"Membership-SAAS-System-Backend/internal/utils/validation"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

// MockTagManagementService is a mock implementation of TagManagementService
type MockTagManagementService struct {
	mock.Mock
}

func (m *MockTagManagementService) CreateTag(ctx context.Context, userID uuid.UUID, req payloads.CreateTagRequest) (payloads.TagResponse, error) {
	args := m.Called(ctx, userID, req)
	return args.Get(0).(payloads.TagResponse), args.Error(1)
}

func (m *MockTagManagementService) ListTags(ctx context.Context, languageCode *string, approved *bool, limit int, offset int) ([]payloads.TagResponse, error) {
	args := m.Called(ctx, languageCode, approved, limit, offset)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]payloads.TagResponse), args.Error(1)
}

// setupAuthenticatedContext sets up a proper authentication context for tests
func setupAuthenticatedContext(c echo.Context, userID, orgID uuid.UUID) {
	claims := &token.EnhancedClaims{
		UserID:       userID,
		PlatformRole: "admin",
		ActiveOrg: &token.OrgContext{
			ID:   orgID.String(),
			Role: "admin",
		},
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Subject:   userID.String(),
		},
	}
	c.Set("user", &jwt.Token{
		Claims: claims,
		Valid:  true,
	})
}

func TestTagHandler_CreateTag(t *testing.T) {
	tests := []struct {
		name           string
		requestBody    payloads.CreateTagRequest
		userID         uuid.UUID
		mockResponse   payloads.TagResponse
		mockError      error
		expectedStatus int
		expectedError  string
	}{
		{
			name: "successful tag creation",
			requestBody: payloads.CreateTagRequest{
				TagName:      "Community",
				LanguageCode: "en",
				Description:  stringPtr("Community related events"),
			},
			userID: uuid.New(),
			mockResponse: payloads.TagResponse{
				ID:                 uuid.New(),
				TagName:            "Community",
				LanguageCode:       "en",
				Description:        stringPtr("Community related events"),
				IsGloballyApproved: true,
				CreatedAt:          time.Now(),
				UpdatedAt:          time.Now(),
			},
			mockError:      nil,
			expectedStatus: http.StatusCreated,
		},
		{
			name: "duplicate tag error",
			requestBody: payloads.CreateTagRequest{
				TagName:      "Community",
				LanguageCode: "en",
			},
			userID:         uuid.New(),
			mockResponse:   payloads.TagResponse{},
			mockError:      fmt.Errorf("tag with this name and language already exists"),
			expectedStatus: http.StatusConflict,
		},
		{
			name: "validation error - empty tag name",
			requestBody: payloads.CreateTagRequest{
				TagName:      "",
				LanguageCode: "en",
			},
			userID:         uuid.New(),
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "validation error - invalid language code",
			requestBody: payloads.CreateTagRequest{
				TagName:      "Community",
				LanguageCode: "invalid",
			},
			userID:         uuid.New(),
			expectedStatus: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			e := echo.New()
			mockService := new(MockTagManagementService)
			validator := validation.NewRequestValidator()
			e.Validator = validator
			handler := NewTagHandler(mockService, validator)

			// Setup mock expectations (only for valid requests)
			if tt.expectedStatus == http.StatusCreated || tt.expectedStatus == http.StatusConflict {
				mockService.On("CreateTag", mock.Anything, tt.userID, tt.requestBody).
					Return(tt.mockResponse, tt.mockError)
			}

			// Create request
			body, _ := json.Marshal(tt.requestBody)
			req := httptest.NewRequest(http.MethodPost, "/api/v1/event-tags", bytes.NewReader(body))
			req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			// Set user in context - simulate JWT middleware setting the token
			orgID := uuid.New() // Create an organization ID for the test
			setupAuthenticatedContext(c, tt.userID, orgID)

			// Execute
			err := handler.CreateTag(c)

			// Assert
			// httputil.HandleError returns nil after writing the response
			require.NoError(t, err)
			assert.Equal(t, tt.expectedStatus, rec.Code)

			if tt.expectedStatus == http.StatusCreated {
				var response payloads.TagResponse
				err = json.Unmarshal(rec.Body.Bytes(), &response)
				require.NoError(t, err)
				assert.Equal(t, tt.mockResponse.TagName, response.TagName)
				assert.Equal(t, tt.mockResponse.LanguageCode, response.LanguageCode)
			} else {
				// For error cases, check the error response
				var errorResponse map[string]interface{}
				err = json.Unmarshal(rec.Body.Bytes(), &errorResponse)
				require.NoError(t, err)
				assert.NotEmpty(t, errorResponse["error"])
				assert.NotEmpty(t, errorResponse["message"])
			}

			mockService.AssertExpectations(t)
		})
	}
}

func TestTagHandler_ListTags(t *testing.T) {
	tests := []struct {
		name           string
		queryParams    map[string]string
		mockResponse   []payloads.TagResponse
		mockError      error
		expectedStatus int
		expectedLang   *string
		expectedLimit  int
		expectedOffset int
	}{
		{
			name: "successful list without filters",
			mockResponse: []payloads.TagResponse{
				{
					ID:                 uuid.New(),
					TagName:            "Community",
					LanguageCode:       "en",
					IsGloballyApproved: true,
				},
				{
					ID:                 uuid.New(),
					TagName:            "Youth Activity",
					LanguageCode:       "en",
					IsGloballyApproved: true,
				},
			},
			mockError:      nil,
			expectedStatus: http.StatusOK,
			expectedLang:   nil,
			expectedLimit:  50,
			expectedOffset: 0,
		},
		{
			name: "successful list with language filter",
			queryParams: map[string]string{
				"lang_code": "zh_HK",
			},
			mockResponse: []payloads.TagResponse{
				{
					ID:                 uuid.New(),
					TagName:            "青少年活動",
					LanguageCode:       "zh_HK",
					IsGloballyApproved: true,
				},
			},
			mockError:      nil,
			expectedStatus: http.StatusOK,
			expectedLang:   stringPtr("zh_HK"),
			expectedLimit:  50,
			expectedOffset: 0,
		},
		{
			name: "successful list with pagination",
			queryParams: map[string]string{
				"limit":  "10",
				"offset": "20",
			},
			mockResponse:   []payloads.TagResponse{},
			mockError:      nil,
			expectedStatus: http.StatusOK,
			expectedLang:   nil,
			expectedLimit:  10,
			expectedOffset: 20,
		},
		{
			name: "limit exceeds maximum",
			queryParams: map[string]string{
				"limit": "200",
			},
			mockResponse:   []payloads.TagResponse{},
			mockError:      nil,
			expectedStatus: http.StatusOK,
			expectedLang:   nil,
			expectedLimit:  100, // Should be capped at 100
			expectedOffset: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			e := echo.New()
			mockService := new(MockTagManagementService)
			validator := validation.NewRequestValidator()
			e.Validator = validator
			handler := NewTagHandler(mockService, validator)

			// Setup mock expectations
			var expectedApproved *bool = nil
			if approved, ok := tt.queryParams["approved"]; ok {
				if approved == "true" {
					expectedApproved = boolPtr(true)
				} else if approved == "false" {
					expectedApproved = boolPtr(false)
				}
			}

			mockService.On("ListTags", mock.Anything, tt.expectedLang, expectedApproved, tt.expectedLimit, tt.expectedOffset).
				Return(tt.mockResponse, tt.mockError)

			// Create request
			req := httptest.NewRequest(http.MethodGet, "/api/v1/event-tags", nil)

			// Add query parameters
			q := req.URL.Query()
			for key, value := range tt.queryParams {
				q.Add(key, value)
			}
			req.URL.RawQuery = q.Encode()

			rec := httptest.NewRecorder()
			c := e.NewContext(req, rec)

			// Execute
			err := handler.ListTags(c)

			// Assert
			require.NoError(t, err)
			assert.Equal(t, tt.expectedStatus, rec.Code)

			if tt.expectedStatus == http.StatusOK {
				var response payloads.TagsListResponse
				err = json.Unmarshal(rec.Body.Bytes(), &response)
				require.NoError(t, err)
				assert.Equal(t, len(tt.mockResponse), len(response.Tags))
				assert.Equal(t, tt.expectedLimit, response.Limit)
				assert.Equal(t, tt.expectedOffset, response.Offset)
			}

			mockService.AssertExpectations(t)
		})
	}
}

// Helper functions
func stringPtr(s string) *string {
	return &s
}

func boolPtr(b bool) *bool {
	return &b
}
