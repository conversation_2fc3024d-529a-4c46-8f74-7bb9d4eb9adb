package handlers_test

import (
	"bytes"
	"encoding/json"
	"fmt"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/internal/handlers"
	userMocks "Membership-SAAS-System-Backend/internal/mocks/services/user"
	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/testutil"
	"Membership-SAAS-System-Backend/internal/token"
	"Membership-SAAS-System-Backend/internal/utils/validation"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
)

// UserHandlerTestSuite tests the UserHandler
type UserHandlerTestSuite struct {
	suite.Suite
	echo           *echo.Echo
	handler        *handlers.UserHandler
	mockProfileSvc *userMocks.MockProfileService
	mockPhoneSvc   *userMocks.MockPhoneService
	mockStatsSvc   *userMocks.MockStatisticsService
	mockAdminSvc   *userMocks.MockAdminService
	mockMediaSvc   *userMocks.MockMediaService
	validator      echo.Validator
	testUserID     uuid.UUID
	testOrgID      uuid.UUID
}

// setupAuthenticatedContext sets up a proper authentication context for tests
func (suite *UserHandlerTestSuite) setupAuthenticatedContext(c echo.Context, platformRole string, orgRole string) {
	claims := &token.EnhancedClaims{
		UserID:       suite.testUserID,
		PlatformRole: platformRole,
		ActiveOrg: &token.OrgContext{
			ID:   suite.testOrgID.String(),
			Role: orgRole,
		},
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			Subject:   suite.testUserID.String(),
		},
	}
	c.Set("user", &jwt.Token{
		Claims: claims,
		Valid:  true,
	})
}

// SetupTest runs before each test
func (suite *UserHandlerTestSuite) SetupTest() {
	// Initialize test data
	suite.testUserID = uuid.New()
	suite.testOrgID = uuid.New()

	suite.echo = echo.New()

	// Set up validator
	suite.validator = testutil.NewValidator()
	suite.echo.Validator = suite.validator

	// Create mocks
	suite.mockProfileSvc = userMocks.NewMockProfileService(suite.T())
	suite.mockPhoneSvc = userMocks.NewMockPhoneService(suite.T())
	suite.mockStatsSvc = userMocks.NewMockStatisticsService(suite.T())
	suite.mockAdminSvc = userMocks.NewMockAdminService(suite.T())
	suite.mockMediaSvc = userMocks.NewMockMediaService(suite.T())

	// Create handler with domain services
	suite.handler = handlers.NewUserHandlerWithDomainServices(
		suite.mockProfileSvc,
		suite.mockPhoneSvc,
		suite.mockStatsSvc,
		suite.mockAdminSvc,
		suite.mockMediaSvc,
		suite.validator.(*validation.RequestValidator),
	)
}

// TestGetUserProfile tests the GetUserProfileHandler
func (suite *UserHandlerTestSuite) TestGetUserProfile_Success() {
	// Arrange
	expectedProfile := &payloads.UserProfileResponse{
		ID:          suite.testUserID,
		DisplayName: "Test User",
		Email:       stringPtr("<EMAIL>"),
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	req := httptest.NewRequest(http.MethodGet, "/users/me", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	// Set authentication context
	suite.setupAuthenticatedContext(c, "user", "member")

	// Mock expectations
	suite.mockProfileSvc.On("GetUserProfile", c.Request().Context(), suite.testUserID).
		Return(expectedProfile, nil)

	// Act
	err := suite.handler.GetUserProfileHandler(c)

	// Assert
	suite.NoError(err)
	suite.Equal(http.StatusOK, rec.Code)

	var response payloads.UserProfileResponse
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal(expectedProfile.ID, response.ID)
	suite.Equal(expectedProfile.DisplayName, response.DisplayName)
	suite.mockProfileSvc.AssertExpectations(suite.T())
}

func (suite *UserHandlerTestSuite) TestGetUserProfile_Unauthorized() {
	// Arrange
	req := httptest.NewRequest(http.MethodGet, "/users/me", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	// No authentication context set

	// Act
	err := suite.handler.GetUserProfileHandler(c)

	// Assert
	suite.NoError(err) // httputil.HandleError returns nil
	suite.Equal(http.StatusUnauthorized, rec.Code)

	var errorResponse map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &errorResponse)
	suite.NoError(err)
	suite.Contains(errorResponse["message"], "Unauthorized")
}

func (suite *UserHandlerTestSuite) TestGetUserProfile_ServiceError() {
	// Arrange
	req := httptest.NewRequest(http.MethodGet, "/users/me", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	// Set authentication context
	suite.setupAuthenticatedContext(c, "user", "member")

	// Mock expectations - return error
	suite.mockProfileSvc.On("GetUserProfile", c.Request().Context(), suite.testUserID).
		Return(nil, fmt.Errorf("service error"))

	// Act
	err := suite.handler.GetUserProfileHandler(c)

	// Assert
	suite.Error(err) // Service error is returned directly for error mapping middleware
	suite.mockProfileSvc.AssertExpectations(suite.T())
}

// TestUpdateUserProfile tests the UpdateUserProfileHandler
func (suite *UserHandlerTestSuite) TestUpdateUserProfile_Success() {
	// Arrange
	updateReq := payloads.UpdateUserProfileRequest{
		DisplayName: stringPtr("Updated Name"),
		LanguagePreferences: &payloads.UpdateLanguagePreferencesPayload{
			InterfaceLanguage: stringPtr("zh_HK"),
		},
	}

	expectedProfile := &payloads.UserProfileResponse{
		ID:          suite.testUserID,
		DisplayName: "Updated Name",
		Email:       stringPtr("<EMAIL>"),
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	bodyBytes, _ := json.Marshal(updateReq)
	req := httptest.NewRequest(http.MethodPatch, "/users/me", bytes.NewReader(bodyBytes))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	// Set authentication context
	suite.setupAuthenticatedContext(c, "user", "member")

	// Mock expectations
	suite.mockProfileSvc.On("UpdateUserProfile", c.Request().Context(), suite.testUserID, &updateReq).
		Return(expectedProfile, nil)

	// Act
	err := suite.handler.UpdateUserProfileHandler(c)

	// Assert
	suite.NoError(err)
	suite.Equal(http.StatusOK, rec.Code)

	var response payloads.UserProfileResponse
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal(expectedProfile.DisplayName, response.DisplayName)
	suite.mockProfileSvc.AssertExpectations(suite.T())
}

func (suite *UserHandlerTestSuite) TestUpdateUserProfile_InvalidJSON() {
	// Arrange
	req := httptest.NewRequest(http.MethodPatch, "/users/me", strings.NewReader("invalid json"))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	// Set authentication context
	suite.setupAuthenticatedContext(c, "user", "member")

	// Act
	err := suite.handler.UpdateUserProfileHandler(c)

	// Assert
	suite.NoError(err) // httputil.HandleError returns nil
	suite.Equal(http.StatusBadRequest, rec.Code)

	var errorResponse map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &errorResponse)
	suite.NoError(err)
	suite.Contains(errorResponse["message"], "Invalid request payload")
}

// TestUploadProfilePicture tests the UploadProfilePictureHandler
func (suite *UserHandlerTestSuite) TestUploadProfilePicture_Success() {
	// Arrange
	expectedProfile := &payloads.UserProfileResponse{
		ID:                suite.testUserID,
		DisplayName:       "Test User",
		ProfilePictureURL: stringPtr("https://example.com/profile.jpg"),
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
	}

	// Create multipart form
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	part, _ := writer.CreateFormFile("file", "test.jpg")
	part.Write([]byte("fake image content"))
	writer.Close()

	req := httptest.NewRequest(http.MethodPost, "/users/me/profile-picture", body)
	req.Header.Set("Content-Type", writer.FormDataContentType())
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	// Set authentication context
	suite.setupAuthenticatedContext(c, "user", "member")

	// Mock expectations
	suite.mockMediaSvc.On("UploadProfilePicture", c.Request().Context(), suite.testUserID, mock.AnythingOfType("*multipart.FileHeader")).
		Return(expectedProfile, nil)

	// Act
	err := suite.handler.UploadProfilePictureHandler(c)

	// Assert
	suite.NoError(err)
	suite.Equal(http.StatusOK, rec.Code)

	var response payloads.UserProfileResponse
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal(expectedProfile.ProfilePictureURL, response.ProfilePictureURL)
	suite.mockMediaSvc.AssertExpectations(suite.T())
}

func (suite *UserHandlerTestSuite) TestUploadProfilePicture_MissingFile() {
	// Arrange
	req := httptest.NewRequest(http.MethodPost, "/users/me/profile-picture", nil)
	req.Header.Set("Content-Type", "multipart/form-data")
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	// Set authentication context
	suite.setupAuthenticatedContext(c, "user", "member")

	// Act
	err := suite.handler.UploadProfilePictureHandler(c)

	// Assert
	suite.NoError(err) // httputil.HandleError returns nil
	suite.Equal(http.StatusBadRequest, rec.Code)

	var errorResponse map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &errorResponse)
	suite.NoError(err)
	suite.Contains(errorResponse["message"], "Error processing uploaded file")
}

// TestInitiatePhoneNumberChange tests the InitiatePhoneNumberChangeHandler
func (suite *UserHandlerTestSuite) TestInitiatePhoneNumberChange_Success() {
	// Arrange
	changeReq := payloads.InitiatePhoneChangeRequest{
		NewPhoneNumber: "+85298765432",
		ClientID:       "test-client",
		State:          "test-state-123",
	}

	expectedResp := &payloads.InitiatePhoneChangeResponse{
		State:        "test-state-123",
		FlowID:       "flow-123",
		Message:      "OTP sent successfully",
		OtpChannel:   "sms",
		ExpiresInSec: 300,
	}

	bodyBytes, _ := json.Marshal(changeReq)
	req := httptest.NewRequest(http.MethodPost, "/users/me/phone/initiate-change", bytes.NewReader(bodyBytes))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	// Set authentication context
	suite.setupAuthenticatedContext(c, "user", "member")

	// Mock expectations
	suite.mockPhoneSvc.On("InitiatePhoneNumberChange", c.Request().Context(), suite.testUserID, &changeReq).
		Return(expectedResp, nil)

	// Act
	err := suite.handler.InitiatePhoneNumberChangeHandler(c)

	// Assert
	suite.NoError(err)
	suite.Equal(http.StatusOK, rec.Code)

	var response payloads.InitiatePhoneChangeResponse
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal(expectedResp.State, response.State)
	suite.Equal(expectedResp.FlowID, response.FlowID)
	suite.mockPhoneSvc.AssertExpectations(suite.T())
}

func (suite *UserHandlerTestSuite) TestInitiatePhoneNumberChange_ConflictError() {
	// Arrange
	changeReq := payloads.InitiatePhoneChangeRequest{
		NewPhoneNumber: "+85298765432",
		ClientID:       "test-client",
		State:          "test-state-123",
	}

	bodyBytes, _ := json.Marshal(changeReq)
	req := httptest.NewRequest(http.MethodPost, "/users/me/phone/initiate-change", bytes.NewReader(bodyBytes))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	// Set authentication context
	suite.setupAuthenticatedContext(c, "user", "member")

	// Mock expectations - return conflict error
	conflictErr := &payloads.ConflictError{Message: "Phone number already in use"}
	suite.mockPhoneSvc.On("InitiatePhoneNumberChange", c.Request().Context(), suite.testUserID, &changeReq).
		Return(nil, conflictErr)

	// Act
	err := suite.handler.InitiatePhoneNumberChangeHandler(c)

	// Assert
	suite.NoError(err) // httputil.HandleError returns nil
	suite.Equal(http.StatusConflict, rec.Code)

	var errorResponse map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &errorResponse)
	suite.NoError(err)
	suite.Contains(errorResponse["message"], "Phone number already in use")
	suite.mockPhoneSvc.AssertExpectations(suite.T())
}

// TestGetUserStats tests the GetUserStats handler
func (suite *UserHandlerTestSuite) TestGetUserStats_Success() {
	// Arrange
	expectedStats := &payloads.APIUserStats{
		TotalEvents:     25,
		UserJoinedAt:    time.Now().AddDate(0, -6, 0),
		VolunteerEvents: 5,
		MonthlyAttendedEvents: []payloads.MonthlyAttendedEvent{
			{Month: "2023-12", Count: 3},
			{Month: "2023-11", Count: 5},
		},
		TopAttendedEventTags: []payloads.TopAttendedEventTag{
			{Name: "Community", Count: 12},
			{Name: "Workshop", Count: 8},
		},
	}

	req := httptest.NewRequest(http.MethodGet, "/users/me/stats", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	// Set authentication context
	suite.setupAuthenticatedContext(c, "user", "member")

	// Mock expectations
	suite.mockStatsSvc.On("GetUserStats", c.Request().Context(), suite.testUserID).
		Return(expectedStats, nil)

	// Act
	err := suite.handler.GetUserStats(c)

	// Assert
	suite.NoError(err)
	suite.Equal(http.StatusOK, rec.Code)

	var response payloads.APIUserStats
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal(expectedStats.TotalEvents, response.TotalEvents)
	suite.Equal(expectedStats.VolunteerEvents, response.VolunteerEvents)
	suite.mockStatsSvc.AssertExpectations(suite.T())
}

func (suite *UserHandlerTestSuite) TestGetUserStats_NotFound() {
	// Arrange
	req := httptest.NewRequest(http.MethodGet, "/users/me/stats", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	// Set authentication context
	suite.setupAuthenticatedContext(c, "user", "member")

	// Mock expectations - return not found error
	notFoundErr := &payloads.NotFoundError{Message: "User statistics not found"}
	suite.mockStatsSvc.On("GetUserStats", c.Request().Context(), suite.testUserID).
		Return(nil, notFoundErr)

	// Act
	err := suite.handler.GetUserStats(c)

	// Assert
	suite.NoError(err) // httputil.HandleError returns nil
	suite.Equal(http.StatusNotFound, rec.Code)

	var errorResponse map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &errorResponse)
	suite.NoError(err)
	suite.Contains(errorResponse["message"], "User statistics not found")
	suite.mockStatsSvc.AssertExpectations(suite.T())
}

// TestGetUserOrganizations tests the GetUserOrganizationsHandler
func (suite *UserHandlerTestSuite) TestGetUserOrganizations_Success() {
	// Arrange
	expectedOrgs := []payloads.OrganizationResponse{
		{
			ID:   suite.testOrgID,
			Name: "Test Organization",
		},
	}

	req := httptest.NewRequest(http.MethodGet, "/users/me/organizations", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	// Set authentication context
	suite.setupAuthenticatedContext(c, "user", "member")

	// Mock expectations
	suite.mockProfileSvc.On("ListUserOrganizations", c.Request().Context(), suite.testUserID).
		Return(expectedOrgs, nil)

	// Act
	err := suite.handler.GetUserOrganizationsHandler(c)

	// Assert
	suite.NoError(err)
	suite.Equal(http.StatusOK, rec.Code)

	var response []payloads.OrganizationResponse
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Len(response, 1)
	suite.Equal(expectedOrgs[0].ID, response[0].ID)
	suite.mockProfileSvc.AssertExpectations(suite.T())
}

// TestListUsersHandler tests admin functionality
func (suite *UserHandlerTestSuite) TestListUsers_Success() {
	// Arrange
	expectedUsers := &payloads.PaginatedUsersResponse{
		Users: []payloads.AdminUserResponse{
			{
				ID:          uuid.New(),
				DisplayName: "Test User 1",
				Email:       stringPtr("<EMAIL>"),
			},
			{
				ID:          uuid.New(),
				DisplayName: "Test User 2",
				Email:       stringPtr("<EMAIL>"),
			},
		},
		TotalCount: 2,
		Page:       1,
		Limit:      10,
	}

	req := httptest.NewRequest(http.MethodGet, "/admin/users?page=1&limit=10", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	// Set authentication context as admin
	suite.setupAuthenticatedContext(c, "staff", "admin")

	// Mock expectations
	suite.mockAdminSvc.On("ListUsers", c.Request().Context(), 1, 10, (*string)(nil)).
		Return(expectedUsers, nil)

	// Act
	err := suite.handler.ListUsersHandler(c)

	// Assert
	suite.NoError(err)
	suite.Equal(http.StatusOK, rec.Code)

	var response payloads.PaginatedUsersResponse
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Len(response.Users, 2)
	suite.Equal(expectedUsers.TotalCount, response.TotalCount)
	suite.mockAdminSvc.AssertExpectations(suite.T())
}

func (suite *UserHandlerTestSuite) TestListUsers_WithRoleFilter() {
	// Arrange
	expectedUsers := &payloads.PaginatedUsersResponse{
		Users: []payloads.AdminUserResponse{
			{
				ID:          uuid.New(),
				DisplayName: "Staff User",
				Email:       stringPtr("<EMAIL>"),
			},
		},
		TotalCount: 1,
		Page:       1,
		Limit:      10,
	}

	req := httptest.NewRequest(http.MethodGet, "/admin/users?page=1&limit=10&platform_role=staff", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	// Set authentication context as admin
	suite.setupAuthenticatedContext(c, "super_admin", "admin")

	// Mock expectations
	roleFilter := "staff"
	suite.mockAdminSvc.On("ListUsers", c.Request().Context(), 1, 10, &roleFilter).
		Return(expectedUsers, nil)

	// Act
	err := suite.handler.ListUsersHandler(c)

	// Assert
	suite.NoError(err)
	suite.Equal(http.StatusOK, rec.Code)

	var response payloads.PaginatedUsersResponse
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Len(response.Users, 1)
	suite.mockAdminSvc.AssertExpectations(suite.T())
}

func (suite *UserHandlerTestSuite) TestListUsers_InvalidRoleFilter() {
	// Arrange
	req := httptest.NewRequest(http.MethodGet, "/admin/users?platform_role=invalid_role", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	// Set authentication context as admin
	suite.setupAuthenticatedContext(c, "staff", "admin")

	// Act
	err := suite.handler.ListUsersHandler(c)

	// Assert
	suite.NoError(err) // httputil.HandleError returns nil
	suite.Equal(http.StatusBadRequest, rec.Code)

	var errorResponse map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &errorResponse)
	suite.NoError(err)
	suite.Contains(errorResponse["message"], "Invalid platform_role")
}

// TestGetUserByEmail tests the GetUserByEmailHandler
func (suite *UserHandlerTestSuite) TestGetUserByEmail_Success() {
	// Arrange
	expectedUser := &payloads.AdminUserResponse{
		ID:          suite.testUserID,
		DisplayName: "Test User",
		Email:       stringPtr("<EMAIL>"),
	}

	req := httptest.NewRequest(http.MethodGet, "/admin/users/email/<EMAIL>", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("email")
	c.SetParamValues("<EMAIL>")

	// Set authentication context as admin
	suite.setupAuthenticatedContext(c, "staff", "admin")

	// Mock expectations
	suite.mockProfileSvc.On("FindUserByEmail", c.Request().Context(), "<EMAIL>").
		Return(expectedUser, nil)

	// Act
	err := suite.handler.GetUserByEmailHandler(c)

	// Assert
	suite.NoError(err)
	suite.Equal(http.StatusOK, rec.Code)

	var response payloads.AdminUserResponse
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal(expectedUser.ID, response.ID)
	suite.Equal(expectedUser.Email, response.Email)
	suite.mockProfileSvc.AssertExpectations(suite.T())
}

func (suite *UserHandlerTestSuite) TestGetUserByEmail_NotFound() {
	// Arrange
	req := httptest.NewRequest(http.MethodGet, "/admin/users/email/<EMAIL>", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)
	c.SetParamNames("email")
	c.SetParamValues("<EMAIL>")

	// Set authentication context as admin
	suite.setupAuthenticatedContext(c, "staff", "admin")

	// Mock expectations - return not found error
	notFoundErr := &payloads.NotFoundError{Message: "User not found"}
	suite.mockProfileSvc.On("FindUserByEmail", c.Request().Context(), "<EMAIL>").
		Return(nil, notFoundErr)

	// Act
	err := suite.handler.GetUserByEmailHandler(c)

	// Assert
	suite.NoError(err) // httputil.HandleError returns nil
	suite.Equal(http.StatusNotFound, rec.Code)

	var errorResponse map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &errorResponse)
	suite.NoError(err)
	suite.Contains(errorResponse["message"], "User not found")
	suite.mockProfileSvc.AssertExpectations(suite.T())
}

// TestCreateStaffUser tests the CreateStaffUserHandler
func (suite *UserHandlerTestSuite) TestCreateStaffUser_Success() {
	// Arrange
	createReq := payloads.CreateStaffUserRequest{
		DisplayName: "New Staff User",
		Email:       "<EMAIL>",
		Password:    "securepassword123",
	}

	expectedUser := &payloads.AdminUserResponse{
		ID:          uuid.New(),
		DisplayName: "New Staff User",
		Email:       stringPtr("<EMAIL>"),
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	bodyBytes, _ := json.Marshal(createReq)
	req := httptest.NewRequest(http.MethodPost, "/admin/users", bytes.NewReader(bodyBytes))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	// Set authentication context as admin
	suite.setupAuthenticatedContext(c, "super_admin", "admin")

	// Mock expectations
	suite.mockAdminSvc.On("CreateStaffUser", c.Request().Context(), &createReq).
		Return(expectedUser, nil)

	// Act
	err := suite.handler.CreateStaffUserHandler(c)

	// Assert
	suite.NoError(err)
	suite.Equal(http.StatusCreated, rec.Code)

	var response payloads.AdminUserResponse
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal(expectedUser.DisplayName, response.DisplayName)
	suite.Equal(expectedUser.Email, response.Email)
	suite.mockAdminSvc.AssertExpectations(suite.T())
}

func (suite *UserHandlerTestSuite) TestCreateStaffUser_ValidationError() {
	// Arrange
	createReq := payloads.CreateStaffUserRequest{
		DisplayName: "", // Missing required field
		Email:       "invalid-email",
		Password:    "123", // Too short
	}

	bodyBytes, _ := json.Marshal(createReq)
	req := httptest.NewRequest(http.MethodPost, "/admin/users", bytes.NewReader(bodyBytes))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	// Set authentication context as admin
	suite.setupAuthenticatedContext(c, "super_admin", "admin")

	// Act
	err := suite.handler.CreateStaffUserHandler(c)

	// Assert
	suite.NoError(err) // httputil.HandleError returns nil
	suite.Equal(http.StatusBadRequest, rec.Code)

	var errorResponse map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &errorResponse)
	suite.NoError(err)
	suite.Contains(errorResponse["message"], "Validation failed")
}

func (suite *UserHandlerTestSuite) TestCreateStaffUser_ConflictError() {
	// Arrange
	createReq := payloads.CreateStaffUserRequest{
		DisplayName: "New Staff User",
		Email:       "<EMAIL>",
		Password:    "securepassword123",
	}

	bodyBytes, _ := json.Marshal(createReq)
	req := httptest.NewRequest(http.MethodPost, "/admin/users", bytes.NewReader(bodyBytes))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	// Set authentication context as admin
	suite.setupAuthenticatedContext(c, "super_admin", "admin")

	// Mock expectations - return conflict error
	conflictErr := &payloads.ConflictError{Message: "Email already exists"}
	suite.mockAdminSvc.On("CreateStaffUser", c.Request().Context(), &createReq).
		Return(nil, conflictErr)

	// Act
	err := suite.handler.CreateStaffUserHandler(c)

	// Assert
	suite.NoError(err) // httputil.HandleError returns nil
	suite.Equal(http.StatusConflict, rec.Code)

	var errorResponse map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &errorResponse)
	suite.NoError(err)
	suite.Contains(errorResponse["message"], "Email already exists")
	suite.mockAdminSvc.AssertExpectations(suite.T())
}

// TestHandleGetUserUUID tests the HandleGetUserUUID handler
func (suite *UserHandlerTestSuite) TestHandleGetUserUUID_Success() {
	// Arrange
	req := httptest.NewRequest(http.MethodGet, "/users/me/uuid", nil)
	rec := httptest.NewRecorder()
	c := suite.echo.NewContext(req, rec)

	// Set authentication context
	suite.setupAuthenticatedContext(c, "user", "member")

	// Act
	err := suite.handler.HandleGetUserUUID(c)

	// Assert
	suite.NoError(err)
	suite.Equal(http.StatusOK, rec.Code)

	var response payloads.UserUUIDResponse
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal(suite.testUserID.String(), response.UUID)
}

// Helper function
func stringPtr(s string) *string {
	return &s
}

// Run the test suite
func TestUserHandlerTestSuite(t *testing.T) {
	testutil.RunSuite(t, new(UserHandlerTestSuite))
}
