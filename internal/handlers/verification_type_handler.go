package handlers

import (
	"net/http"

	"Membership-SAAS-System-Backend/internal/locales"
	"github.com/labstack/echo/v4"
)

// VerificationTypeResponse defines the structure for a localized verification type.
type VerificationTypeResponse struct {
	Key      string `json:"key" example:"passport"`
	Name     string `json:"name" example:"Passport"`
	LangCode string `json:"langcode" example:"en"`
}

// verificationTypesData holds the predefined list of verification types with localizations.
// In a real application, this might come from a database or configuration.
var verificationTypesData = []VerificationTypeResponse{
	{Key: "passport", Name: "Passport", LangCode: locales.LangEN},
	{Key: "passport", Name: "護照", LangCode: locales.LangZHHK},
	{Key: "passport", Name: "护照", LangCode: locales.LangZHCN},
	{Key: "hk_id_card", Name: "Hong Kong Identity Card", LangCode: locales.LangEN},
	{Key: "hk_id_card", Name: "香港身份證", LangCode: locales.LangZHHK},
	{Key: "hk_id_card", Name: "香港身份证", LangCode: locales.LangZHCN},
	{Key: "student_id", Name: "Student ID Card", LangCode: locales.LangEN},
	{Key: "student_id", Name: "學生證", LangCode: locales.LangZHHK},
	{Key: "student_id", Name: "学生证", LangCode: locales.LangZHCN},
	{Key: "address_proof", Name: "Address Proof", LangCode: locales.LangEN},
	{Key: "address_proof", Name: "地址證明", LangCode: locales.LangZHHK},
	{Key: "address_proof", Name: "地址证明", LangCode: locales.LangZHCN},
	{Key: "hk_youth_plus", Name: "HK Youth+", LangCode: locales.LangEN},
	{Key: "hk_youth_plus", Name: "HK Youth+", LangCode: locales.LangZHHK},
	{Key: "hk_youth_plus", Name: "HK Youth+", LangCode: locales.LangZHCN}, // Assuming same for zh-CN for now
	{Key: "mainland_china_id_card", Name: "Mainland China ID Card", LangCode: locales.LangEN},
	{Key: "mainland_china_id_card", Name: "中國大陸身份證", LangCode: locales.LangZHHK},
	{Key: "mainland_china_id_card", Name: "中国大陆身份证", LangCode: locales.LangZHCN},
	{Key: "mainland_travel_permit", Name: "Mainland Travel Permit for Hong Kong and Macao Residents", LangCode: locales.LangEN},
	{Key: "mainland_travel_permit", Name: "港澳居民來往內地通行證", LangCode: locales.LangZHHK},
	{Key: "mainland_travel_permit", Name: "港澳居民来往内地通行证", LangCode: locales.LangZHCN},
	{Key: "home_visit", Name: "Home Visit", LangCode: locales.LangEN},
	{Key: "home_visit", Name: "家訪", LangCode: locales.LangZHHK},
	{Key: "home_visit", Name: "家访", LangCode: locales.LangZHCN},
	// Add other verification types as needed, with their localizations
}

// GetVerificationTypes godoc
// @Summary List all supported verification types with localizations
// @Description Retrieves a list of all possible verification types supported by the system, with names localized by language code. If the 'lang_code' query parameter is provided, the list is filtered to include only types matching that language code. Otherwise, all verification types with their available localizations are returned.
// @Tags Public, Verification
// @Accept json
// @Produce json
// @Param lang_code query string false "Language code (e.g., 'en-US', 'zh-HK') to filter verification types. If omitted, all types with all localizations are returned."
// @Success 200 {array} VerificationTypeResponse "Successfully retrieved localized verification types"
// @Router /api/v1/verification-types [get]
func GetVerificationTypes(c echo.Context) error {
	langCode := c.QueryParam("lang_code")

	if langCode == "" {
		// If no lang_code is provided, return all verification types with all localizations
		return c.JSON(http.StatusOK, verificationTypesData)
	}

	var filteredTypes []VerificationTypeResponse
	for _, vt := range verificationTypesData {
		if vt.LangCode == langCode {
			filteredTypes = append(filteredTypes, vt)
		}
	}

	// If a lang_code was provided but no types matched, return an empty list rather than all types.
	// If filteredTypes is empty and langCode was not empty, it means no match was found.
	// If filteredTypes is empty and langCode was empty, it means verificationTypesData was empty (unlikely but possible).
	// The current logic correctly returns an empty list if no matches, or the full list if langCode is empty.
	return c.JSON(http.StatusOK, filteredTypes)
}

// --- New code for Government Funding Types ---

// GovernmentFundingTypeResponse defines the structure for a localized government funding type.
type GovernmentFundingTypeResponse struct {
	Key      string `json:"key" example:"gov_funded_prog"`
	Name     string `json:"name" example:"Government-funded program"`
	LangCode string `json:"langcode" example:"en"`
}

// governmentFundingTypesData holds the predefined list of government funding types with localizations.
var governmentFundingTypesData = []GovernmentFundingTypeResponse{
	// Government-funded programme
	{Key: "gov_funded_prog", Name: "Government-funded program", LangCode: locales.LangEN},
	{Key: "gov_funded_prog", Name: "政府資助計劃", LangCode: locales.LangZHHK},
	{Key: "gov_funded_prog", Name: "政府资助计划", LangCode: locales.LangZHCN},

	// Youth Development Commission
	{Key: "youth_dev_commission", Name: "Youth Development Commission", LangCode: locales.LangEN},
	{Key: "youth_dev_commission", Name: "青年發展委員會", LangCode: locales.LangZHHK},
	{Key: "youth_dev_commission", Name: "青年发展委员会", LangCode: locales.LangZHCN},

	// Home and Youth Affairs Bureau, HKSAR Government
	{Key: "hksar_hyab", Name: "Home and Youth Affairs Bureau", LangCode: locales.LangEN},
	{Key: "hksar_hyab", Name: "中華人民共和國香港特別行政區政府民政及青年事務局", LangCode: locales.LangZHHK},
	{Key: "hksar_hyab", Name: "中华人民共和国香港特别行政区政府民政及青年事务局", LangCode: locales.LangZHCN},
}

// GetGovernmentFundingTypes retrieves a list of government funding types for a given language code.
// It returns one entry per unique key, preserving the original key order from the data slice.
// If a localization for the given langCode is not found for a type, it defaults to English ('en').
// If an English version is also not available for a key, that key is omitted from the results.
func GetGovernmentFundingTypes(langCode string) []GovernmentFundingTypeResponse {
	uniqueKeysSourceOrder := []string{}
	seenKeys := make(map[string]bool)

	// Preserve the order of keys as they appear in governmentFundingTypesData
	// This ensures the output list has a predictable (based on source data) order.
	for _, gft := range governmentFundingTypesData {
		if !seenKeys[gft.Key] {
			uniqueKeysSourceOrder = append(uniqueKeysSourceOrder, gft.Key)
			seenKeys[gft.Key] = true
		}
	}

	var result []GovernmentFundingTypeResponse

	for _, key := range uniqueKeysSourceOrder {
		var chosenEntry *GovernmentFundingTypeResponse
		var englishFallbackEntry *GovernmentFundingTypeResponse

		// Iterate over the data to find the matching entries for the current key
		for i := range governmentFundingTypesData {
			entry := governmentFundingTypesData[i] // Make a copy to safely take its address
			if entry.Key == key {
				if entry.LangCode == langCode {
					chosenEntry = &entry
					break // Found the specific language version for this key, move to next key
				}
				if entry.LangCode == locales.LangEN {
					englishFallbackEntry = &entry // Found an English fallback
				}
			}
		}

		if chosenEntry != nil {
			result = append(result, *chosenEntry)
		} else if englishFallbackEntry != nil {
			// If specific lang not found, but English fallback was, use it.
			result = append(result, *englishFallbackEntry)
		}
		// If a key has no entry for 'langCode' and no 'en' entry, it's effectively omitted.
	}
	return result
}

// GetGovernmentFundingTypesHandler godoc
// @Summary List all supported government funding types with localizations
// @Description Retrieves a list of all possible government funding types supported by the system, with names localized by language code.
// @Description It uses the 'lang_code' query parameter. If 'lang_code' is provided and a localization exists, it's returned.
// @Description If the specific 'lang_code' localization doesn't exist for a type, it falls back to English ('en-US').
// @Description If neither the specified 'lang_code' nor English is available for a type, that type is omitted.
// @Description If 'lang_code' is not provided, it defaults to Traditional Chinese Hong Kong ('zh-HK') for all types.
// @Tags Public, Verification, GovernmentFunding
// @Accept json
// @Produce json
// @Param lang_code query string false "Language code ('en-US', 'zh-HK', 'zh-CN') to filter types. Defaults to 'zh-HK' if omitted or if specific localization not found."
// @Success 200 {array} GovernmentFundingTypeResponse "Successfully retrieved localized government funding types"
// @Router /api/v1/government-funding-types [get]
func GetGovernmentFundingTypesHandler(c echo.Context) error {
	langCode := c.QueryParam("lang_code")
	if langCode == "" {
		langCode = locales.LangZHHK // Default to zh-HK if no lang_code is provided
	}

	types := GetGovernmentFundingTypes(langCode)
	return c.JSON(http.StatusOK, types)
}
