package payloads

import (
	"time"

	"github.com/google/uuid"
)

// MonthlyAttendedEvent represents the number of events attended in a specific month.
type MonthlyAttendedEvent struct {
	Month string `json:"month" example:"2023-08"` // YYYY-MM
	Count int    `json:"count" example:"5"`
}

// TopAttendedEventTag represents a top attended event tag and its count.
type TopAttendedEventTag struct {
	Name  string `json:"name" example:"Community"`
	Count int    `json:"count" example:"12"`
}

// APIUserStats represents statistics for a user.
// @Description Provides a summary of user activity, including event participation and engagement over time.
type APIUserStats struct {
	TotalEvents           int                    `json:"totalEvents" example:"50"`                    // Total number of events the user has attended.
	UserJoinedAt          time.Time              `json:"userJoinedAt" example:"2023-01-15T10:00:00Z"` // Timestamp when the user registered.
	VolunteerEvents       int                    `json:"volunteerEvents" example:"15"`                // Number of events the user has volunteered for.
	MonthlyAttendedEvents []MonthlyAttendedEvent `json:"monthlyAttendedEvents"`                       // Statistics for events attended over the last 6 months.
	TopAttendedEventTags  []TopAttendedEventTag  `json:"topAttendedEventTags"`                        // Top 5 most frequently attended event tags over the last 6 months.
}

// UserProfileResponse is the structure for GET /users/me
type UserProfileResponse struct {
	ID                          uuid.UUID                       `json:"id"`
	DisplayName                 string                          `json:"display_name"`
	Phone                       *string                         `json:"phone,omitempty"`
	PhoneVerifiedAt             *time.Time                      `json:"phone_verified_at,omitempty"`
	Email                       *string                         `json:"email,omitempty"`
	EmailVerifiedAt             *time.Time                      `json:"email_verified_at,omitempty"`
	ProfilePictureURL           *string                         `json:"profile_picture_url,omitempty"`
	PhoneOtpChannel             *string                         `json:"phone_otp_channel,omitempty"` // e.g., "sms", "whatsapp"
	InterfaceLanguage           string                          `json:"interface_language"`
	CommunicationLanguage       string                          `json:"communication_language"`
	EnableAppNotifications      bool                            `json:"enable_app_notifications"`
	EnableWhatsappNotifications bool                            `json:"enable_whatsapp_notifications"`
	EnableSmsNotifications      bool                            `json:"enable_sms_notifications"`
	EnableEmailNotifications    bool                            `json:"enable_email_notifications"`
	PlatformRole                string                          `json:"platform_role"`
	VerificationStatus          *UserVerificationStatusResponse `json:"verification_status,omitempty"`
	CreatedAt                   time.Time                       `json:"created_at"`
	UpdatedAt                   time.Time                       `json:"updated_at"`
}

// UpdateUserProfileRequest is for PATCH /users/me
type UpdateUserProfileRequest struct {
	DisplayName          *string                            `json:"display_name,omitempty"`
	NotificationSettings *UpdateNotificationSettingsPayload `json:"notification_settings,omitempty"`
	LanguagePreferences  *UpdateLanguagePreferencesPayload  `json:"language_preferences,omitempty"`
	ProfilePictureURL    *string                            `json:"profile_picture_url,omitempty"` // Basic for now, full upload is more complex
}

type UpdateNotificationSettingsPayload struct {
	EnableAppNotifications      *bool   `json:"enable_app_notifications,omitempty"`
	EnableWhatsappNotifications *bool   `json:"enable_whatsapp_notifications,omitempty"`
	EnableSmsNotifications      *bool   `json:"enable_sms_notifications,omitempty"`
	EnableEmailNotifications    *bool   `json:"enable_email_notifications,omitempty"`
	PhoneOtpChannel             *string `json:"phone_otp_channel,omitempty"`
}

type UpdateLanguagePreferencesPayload struct {
	InterfaceLanguage     *string `json:"interface_language,omitempty" validate:"omitempty,oneof=en-US zh-HK zh-CN"`
	CommunicationLanguage *string `json:"communication_language,omitempty" validate:"omitempty,oneof=en-US zh-HK zh-CN"`
}

// InitiatePhoneChangeRequest is for POST /users/me/phone/initiate-change
type InitiatePhoneChangeRequest struct {
	NewPhoneNumber  string  `json:"new_phone_number" validate:"required,e164"`
	PhoneOTPChannel *string `json:"phone_otp_channel,omitempty"` // Added: e.g., "sms", "whatsapp"
	// For PKCE flow, if AuthnService requires it for this flow type for consistency
	// However, for an already authenticated user changing phone, PKCE might be optional for this specific sub-flow.
	// The current AuthnService.InitiatePhoneChangeOTP generates internal PKCE.
	// ClientID, State are good for flow correlation even without full client-side PKCE.
	ClientID string `json:"client_id" validate:"required"` // Can be a fixed client ID for this app type
	State    string `json:"state" validate:"required"`     // Client generated state
}

// VerifyPhoneChangeRequest is for POST /users/me/phone/verify-change
type VerifyPhoneChangeRequest struct {
	State          string `json:"state" validate:"required"`
	Otp            string `json:"otp" validate:"required,len=6,numeric"`
	NewPhoneNumber string `json:"new_phone_number" validate:"required,e164"`
}

// Standardized response for successful phone change OTP initiation
type InitiatePhoneChangeResponse struct {
	State        string `json:"state"`
	FlowID       string `json:"flow_id"`
	Message      string `json:"message,omitempty"`
	OtpChannel   string `json:"otp_channel"`    // Added
	ExpiresInSec int    `json:"expires_in_sec"` // Added
}

// VerifyPhoneChangeResponse details the result of a successful phone change verification.
// (Replaces the generic PhoneChangeResponse for more specific data)
type VerifyPhoneChangeResponse struct {
	Message string               `json:"message"`
	User    *UserProfileResponse `json:"user"` // The updated user profile
}

// UserUUIDResponse is the structure for GET /users/me/uuid
type UserUUIDResponse struct {
	UUID string `json:"uuid"`
}
