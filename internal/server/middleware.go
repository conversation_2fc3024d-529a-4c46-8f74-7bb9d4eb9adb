package server

import (
	"errors"
	"net/http"
	"strings"

	"Membership-SAAS-System-Backend/internal/infrastructure/cache"
	"Membership-SAAS-System-Backend/internal/middleware"
	"Membership-SAAS-System-Backend/internal/token"

	httputil "Membership-SAAS-System-Backend/internal/utils/http"

	"github.com/go-playground/validator/v10"
	"github.com/golang-jwt/jwt/v5"
	"github.com/labstack/echo/v4"
	echomiddleware "github.com/labstack/echo/v4/middleware"
	"github.com/rs/zerolog/log"
)

// CustomValidator wraps the go-playground/validator and implements echo.Validator
type CustomValidator struct {
	validator *validator.Validate
}

// Validate implements the echo.Validator interface
func (cv *CustomValidator) Validate(i interface{}) error {
	if err := cv.validator.Struct(i); err != nil {
		return err
	}
	return nil
}

// setupMiddleware configures all middleware for the server
func (s *Server) setupMiddleware() {
	// Set custom validator
	s.echo.Validator = &CustomValidator{validator: validator.New()}

	// Core middleware (order matters!)
	s.echo.Use(s.zerologLoggerContext())
	s.echo.Use(echomiddleware.Recover())
	s.echo.Use(echomiddleware.RequestID())

	// Security headers middleware - must be early in the chain
	s.echo.Use(middleware.SecurityHeaders())

	// Test API guard middleware - must be early to block test endpoints in production
	testAPIGuard := middleware.NewTestAPIGuard()
	s.echo.Use(testAPIGuard.Middleware())

	// Error mapping middleware - must be registered early to catch all errors
	s.echo.Use(middleware.ErrorMappingMiddleware())

	// CORS middleware with enhanced configuration
	corsConfig := echomiddleware.CORSConfig{
		AllowOrigins:     s.config.CORS.AllowedOrigins,
		AllowMethods:     []string{http.MethodGet, http.MethodPost, http.MethodPut, http.MethodDelete, http.MethodPatch, http.MethodOptions},
		AllowHeaders:     []string{echo.HeaderOrigin, echo.HeaderContentType, echo.HeaderAccept, echo.HeaderAuthorization, "X-Requested-With", "X-CSRF-Token", "Cache-Control", "X-File-Name", "X-File-Size", "X-Client-Version"},
		ExposeHeaders:    []string{"X-Total-Count"},
		AllowCredentials: true,
		MaxAge:           3600,
	}

	// Log CORS configuration
	log.Info().
		Strs("allowed_origins", s.config.CORS.AllowedOrigins).
		Str("environment", s.config.App.Environment).
		Msg("CORS middleware configured")

	s.echo.Use(echomiddleware.CORSWithConfig(corsConfig))

	// Rate limiting middleware (only if Redis is available)
	if s.container.Cache != nil {
		s.setupRateLimiting()
	}

	log.Info().Msg("Middleware configured with security headers and error mapping")
}

// zerologLoggerContext creates a middleware that injects the global zerolog logger into the request's context
func (s *Server) zerologLoggerContext() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			logger := log.Logger
			ctxWithLogger := logger.WithContext(c.Request().Context())
			newReq := c.Request().WithContext(ctxWithLogger)
			c.SetRequest(newReq)
			return next(c)
		}
	}
}

// CreateJWTMiddleware creates JWT middleware with the given configuration
func (s *Server) CreateJWTMiddleware() echo.MiddlewareFunc {
	jwtConfig, err := token.GetJWTConfig()
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to get JWT configuration")
	}

	// Use custom middleware that handles backward compatibility
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Skip JWT for specific volunteer application details endpoint
			targetPath := "/organizations/:orgId/events/:eventId/volunteer-applications/:appId"
			if c.Request().Method == http.MethodGet && c.Path() == targetPath {
				log.Ctx(c.Request().Context()).Info().Str("path", c.Path()).Msgf("Skipping JWT authentication for GET %s", targetPath)
				return next(c)
			}

			// Get token from Authorization header
			authHeader := c.Request().Header.Get("Authorization")
			if authHeader == "" {
				return echo.NewHTTPError(http.StatusUnauthorized, "missing authorization header")
			}

			// Extract token from "Bearer <token>"
			tokenString := strings.TrimPrefix(authHeader, "Bearer ")
			if tokenString == authHeader {
				return echo.NewHTTPError(http.StatusUnauthorized, "invalid authorization header format")
			}

			// Parse token with backward compatibility
			claims, err := token.ParseTokenWithBackwardCompatibility(tokenString, jwtConfig.AccessTokenSecret)
			if err != nil {
				log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to parse JWT token")
				return echo.NewHTTPError(http.StatusUnauthorized, "invalid token")
			}

			// Create a jwt.Token object for compatibility with existing code
			jwtToken := &jwt.Token{
				Claims: claims,
				Valid:  true,
			}

			// Set context values
			c.Set("user", jwtToken)
			c.Set("user_claims", claims)
			c.Set("user_id", claims.UserID)

			return next(c)
		}
	}
}

// CreateOptionalJWTMiddleware creates optional JWT middleware that doesn't fail if no token is provided
func (s *Server) CreateOptionalJWTMiddleware() echo.MiddlewareFunc {
	jwtConfig, err := token.GetJWTConfig()
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to get JWT configuration")
	}

	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Get token from Authorization header
			authHeader := c.Request().Header.Get("Authorization")
			if authHeader == "" {
				// No token, continue without authentication
				return next(c)
			}

			// Extract token from "Bearer <token>"
			if !strings.HasPrefix(strings.ToLower(authHeader), "bearer ") {
				// Invalid format, continue without authentication
				return next(c)
			}

			tokenString := strings.TrimSpace(strings.TrimPrefix(authHeader, "Bearer "))
			if tokenString == "" {
				// Empty token, continue without authentication
				return next(c)
			}

			// Parse token with backward compatibility
			claims, err := token.ParseTokenWithBackwardCompatibility(tokenString, jwtConfig.AccessTokenSecret)
			if err != nil {
				log.Ctx(c.Request().Context()).Warn().Err(err).Msg("Optional JWT Middleware: Failed to parse token")

				// Handle specific errors
				if errors.Is(err, jwt.ErrTokenMalformed) {
					return httputil.HandleError(c, http.StatusBadRequest, "Malformed token.", err)
				}

				// For other errors, continue without authentication
				return next(c)
			}

			// Create a jwt.Token object for compatibility with existing code
			jwtToken := &jwt.Token{
				Claims: claims,
				Valid:  true,
			}

			// Set context values
			c.Set("user", jwtToken)
			c.Set("user_claims", claims)
			c.Set("user_id", claims.UserID)

			return next(c)
		}
	}
}

// CreateAdminRequiredMiddleware creates middleware that checks for staff/admin claims
// This delegates to the middleware.AdminRequired which handles enhanced claims properly
func (s *Server) CreateAdminRequiredMiddleware() echo.MiddlewareFunc {
	return middleware.AdminRequired
}

// CreateRateLimiter creates a rate limiter middleware for specific routes
func (s *Server) CreateRateLimiter() echo.MiddlewareFunc {
	// Try to get Redis client from cache
	if s.container.Cache == nil {
		log.Warn().Msg("Cache not available, returning no-op rate limiter")
		// Return a no-op middleware
		return func(next echo.HandlerFunc) echo.HandlerFunc {
			return next
		}
	}

	redisCache, ok := s.container.Cache.(*cache.RedisCache)
	if !ok {
		log.Warn().Msg("Cache is not Redis-based, returning no-op rate limiter")
		// Return a no-op middleware
		return func(next echo.HandlerFunc) echo.HandlerFunc {
			return next
		}
	}

	// Get the Redis client from the cache
	redisClient := redisCache.GetRedisClient()

	// Create rate limit metrics
	metrics := middleware.NewRateLimitMetrics("membership", "rate_limit")

	// Configure rate limiting
	rateLimitConfig := middleware.DefaultRedisRateLimitConfig()
	rateLimitConfig.Redis = redisClient
	rateLimitConfig.Metrics = metrics
	rateLimitConfig.FallbackRate = s.config.Security.RateLimitFallbackRate
	rateLimitConfig.FallbackBurst = s.config.Security.RateLimitFallbackBurst
	rateLimitConfig.TrustedProxies = s.config.Security.TrustedProxies

	// Set default rate from config
	rateLimitConfig.DefaultRate = s.config.Security.RateLimitPerMinute
	rateLimitConfig.DefaultBurst = s.config.Security.RateLimitPerMinute

	// Apply auth endpoint limits
	rateLimitConfig.EndpointLimits = middleware.AuthEndpointLimits()

	// Return the rate limit middleware
	return middleware.RedisRateLimitMiddleware(rateLimitConfig)
}

// setupRateLimiting configures rate limiting middleware
func (s *Server) setupRateLimiting() {
	// Try to get Redis client from cache
	redisCache, ok := s.container.Cache.(*cache.RedisCache)
	if !ok {
		log.Warn().Msg("Cache is not Redis-based, skipping rate limiting setup")
		return
	}

	// Get the Redis client from the cache
	redisClient := redisCache.GetRedisClient()

	// Create rate limit metrics
	metrics := middleware.NewRateLimitMetrics("membership", "rate_limit")

	// Configure rate limiting
	rateLimitConfig := middleware.DefaultRedisRateLimitConfig()
	rateLimitConfig.Redis = redisClient
	rateLimitConfig.Metrics = metrics
	rateLimitConfig.FallbackRate = s.config.Security.RateLimitFallbackRate
	rateLimitConfig.FallbackBurst = s.config.Security.RateLimitFallbackBurst
	rateLimitConfig.TrustedProxies = s.config.Security.TrustedProxies

	// Set default rate from config
	rateLimitConfig.DefaultRate = s.config.Security.RateLimitPerMinute
	rateLimitConfig.DefaultBurst = s.config.Security.RateLimitPerMinute

	// Apply auth endpoint limits
	rateLimitConfig.EndpointLimits = middleware.AuthEndpointLimits()

	// Apply global rate limiting
	s.echo.Use(middleware.RedisRateLimitMiddleware(rateLimitConfig))

	log.Info().
		Int("default_rate", rateLimitConfig.DefaultRate).
		Int("fallback_rate", rateLimitConfig.FallbackRate).
		Int("trusted_proxies", len(rateLimitConfig.TrustedProxies)).
		Msg("Rate limiting configured")
}
