package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	db "Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/twilio_service"

	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
)

// AccountDeletionConfig holds configuration for the account deletion service
type AccountDeletionConfig struct {
	MockTwilioOTP           bool
	MockTwilioNotifications bool
}

// AccountDeletionService handles user account deletion requests
type AccountDeletionService struct {
	store         db.Store
	config        AccountDeletionConfig
	twilioService *twilio_service.TwilioService
}

// NewAccountDeletionService creates a new account deletion service
func NewAccountDeletionService(store db.Store, cfg AccountDeletionConfig) *AccountDeletionService {
	twilioSvc, err := twilio_service.NewTwilioService()
	if err != nil {
		log.Error().Err(err).Msg("Failed to initialize Twilio service for account deletion")
		// Continue without Twilio service for now, but log the error
	}

	return &AccountDeletionService{
		store:         store,
		config:        cfg,
		twilioService: twilioSvc,
	}
}

// DeletionRequestMetadata holds metadata for deletion requests
type DeletionRequestMetadata struct {
	IP        string `json:"ip,omitempty"`
	UserAgent string `json:"user_agent,omitempty"`
	Reason    string `json:"reason,omitempty"`
}

// InitiateAccountDeletion starts the account deletion process by sending an OTP
func (s *AccountDeletionService) InitiateAccountDeletion(ctx context.Context, userID uuid.UUID, metadata DeletionRequestMetadata) error {
	// Check if user exists and is not already deleted
	user, err := s.store.GetUserByID(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}

	if user.DeletedAt != nil {
		return errors.New("user account is already deleted")
	}

	// Check if there's already an active deletion request
	_, err = s.store.GetActiveDeletionRequest(ctx, userID)
	if err == nil {
		return errors.New("account deletion is already scheduled")
	}

	// Check if there's already a scheduled deletion
	_, err = s.store.GetScheduledDeletion(ctx, userID)
	if err == nil {
		return errors.New("account deletion is already scheduled")
	}

	// Generate 6-digit code
	code := s.generateDeletionCode()
	expiresAt := time.Now().Add(15 * time.Minute) // 15 minutes expiry

	// Create deletion request
	_, err = s.store.CreateDeletionRequest(ctx, db.CreateDeletionRequestParams{
		UserID:    userID,
		Code:      code,
		ExpiresAt: expiresAt,
	})
	if err != nil {
		return fmt.Errorf("failed to create deletion request: %w", err)
	}

	// Send OTP via SMS if Twilio service is available
	if s.twilioService != nil && user.Phone != nil {
		_, err = s.twilioService.SendOTP(*user.Phone, "sms")
		if err != nil {
			log.Error().Err(err).Str("user_id", userID.String()).Msg("Failed to send deletion OTP via SMS")
			// Don't fail the request if SMS fails, user can still use the code from logs in mock mode
		}
	}

	// Create audit log
	metadataJSON, _ := json.Marshal(metadata)
	_, err = s.store.CreateAccountDeletionAuditLog(ctx, db.CreateAccountDeletionAuditLogParams{
		UserID:   userID,
		Action:   "initiated",
		Metadata: metadataJSON,
	})
	if err != nil {
		log.Error().Err(err).Msg("Failed to create audit log for deletion initiation")
	}

	log.Info().Str("user_id", userID.String()).Msg("Account deletion initiated")
	return nil
}

// ConfirmAccountDeletion confirms the deletion request with the provided code
func (s *AccountDeletionService) ConfirmAccountDeletion(ctx context.Context, userID uuid.UUID, code string) error {
	// Get active deletion request
	deletionRequest, err := s.store.GetActiveDeletionRequest(ctx, userID)
	if err != nil {
		return errors.New("no active deletion request found")
	}

	// Verify the code
	if deletionRequest.Code != code {
		return errors.New("invalid confirmation code")
	}

	// Confirm the deletion request
	_, err = s.store.ConfirmDeletionRequest(ctx, db.ConfirmDeletionRequestParams{
		ID:     deletionRequest.ID,
		UserID: userID,
	})
	if err != nil {
		return fmt.Errorf("failed to confirm deletion request: %w", err)
	}

	// Schedule the deletion for 30 days from now
	scheduledFor := time.Now().Add(30 * 24 * time.Hour)
	_, err = s.store.CreateScheduledDeletion(ctx, db.CreateScheduledDeletionParams{
		UserID:       userID,
		ScheduledFor: scheduledFor,
	})
	if err != nil {
		return fmt.Errorf("failed to schedule deletion: %w", err)
	}

	// Create audit log
	confirmMetadata := map[string]interface{}{
		"scheduled_for": scheduledFor,
	}
	confirmMetadataJSON, _ := json.Marshal(confirmMetadata)
	_, err = s.store.CreateAccountDeletionAuditLog(ctx, db.CreateAccountDeletionAuditLogParams{
		UserID:   userID,
		Action:   "confirmed",
		Metadata: confirmMetadataJSON,
	})
	if err != nil {
		log.Error().Err(err).Msg("Failed to create audit log for deletion confirmation")
	}

	log.Info().Str("user_id", userID.String()).Time("scheduled_for", scheduledFor).Msg("Account deletion confirmed and scheduled")
	return nil
}

// ScheduleAccountDeletion schedules immediate account deletion (internal method)
func (s *AccountDeletionService) ScheduleAccountDeletion(ctx context.Context, userID uuid.UUID) error {
	_, err := s.store.CreateScheduledDeletion(ctx, db.CreateScheduledDeletionParams{
		UserID:       userID,
		ScheduledFor: time.Now(),
	})
	return err
}

// CancelScheduledDeletion cancels a scheduled account deletion
func (s *AccountDeletionService) CancelScheduledDeletion(ctx context.Context, userID uuid.UUID) error {
	_, err := s.store.CancelScheduledDeletion(ctx, userID)
	if err != nil {
		return errors.New("no scheduled deletion found")
	}

	// Create audit log
	cancelMetadata := map[string]interface{}{}
	cancelMetadataJSON, _ := json.Marshal(cancelMetadata)
	_, err = s.store.CreateAccountDeletionAuditLog(ctx, db.CreateAccountDeletionAuditLogParams{
		UserID:   userID,
		Action:   "cancelled",
		Metadata: cancelMetadataJSON,
	})
	if err != nil {
		log.Error().Err(err).Msg("Failed to create audit log for deletion cancellation")
	}

	log.Info().Str("user_id", userID.String()).Msg("Account deletion cancelled")
	return nil
}

// ProcessScheduledDeletions processes all deletions that are due
func (s *AccountDeletionService) ProcessScheduledDeletions(ctx context.Context) error {
	// Get all pending deletions that are due
	pendingDeletions, err := s.store.GetPendingScheduledDeletions(ctx, 100) // Process up to 100 at a time
	if err != nil {
		return fmt.Errorf("failed to get pending deletions: %w", err)
	}

	for _, deletion := range pendingDeletions {
		err := s.processAccountDeletion(ctx, deletion.UserID, deletion.ID)
		if err != nil {
			log.Error().Err(err).Str("user_id", deletion.UserID.String()).Msg("Failed to process account deletion")
			continue
		}
	}

	return nil
}

// processAccountDeletion processes a single account deletion
func (s *AccountDeletionService) processAccountDeletion(ctx context.Context, userID uuid.UUID, deletionID uuid.UUID) error {
	return s.store.ExecTx(ctx, func(q db.Querier) error {
		// Mark the scheduled deletion as processed
		_, err := q.MarkScheduledDeletionProcessed(ctx, deletionID)
		if err != nil {
			return fmt.Errorf("failed to mark deletion as processed: %w", err)
		}

		// Remove user from all organizations
		err = q.RemoveUserFromAllOrganizations(ctx, userID)
		if err != nil {
			return fmt.Errorf("failed to remove user from organizations: %w", err)
		}

		// Soft delete user posts (but preserve them for audit)
		err = q.RemoveUserPosts(ctx, userID)
		if err != nil {
			return fmt.Errorf("failed to remove user posts: %w", err)
		}

		// NOTE: We do NOT call RemoveUserEvents here because events should be preserved
		// as per the requirement: "events record need to be kept"

		// Anonymize user data
		_, err = q.AnonymizeUserData(ctx, userID)
		if err != nil {
			return fmt.Errorf("failed to anonymize user data: %w", err)
		}

		// Soft delete the user
		_, err = q.SoftDeleteUser(ctx, userID)
		if err != nil {
			return fmt.Errorf("failed to soft delete user: %w", err)
		}

		// Create audit log
		processMetadata := map[string]interface{}{
			"deletion_id": deletionID,
			"timestamp":   time.Now(),
		}
		processMetadataJSON, _ := json.Marshal(processMetadata)
		_, err = q.CreateAccountDeletionAuditLog(ctx, db.CreateAccountDeletionAuditLogParams{
			UserID:   userID,
			Action:   "processed",
			Metadata: processMetadataJSON,
		})
		if err != nil {
			log.Error().Err(err).Msg("Failed to create audit log for deletion processing")
		}

		log.Info().Str("user_id", userID.String()).Msg("Account deletion processed successfully")
		return nil
	})
}

// GetDeletionStatus gets the deletion status for a user
func (s *AccountDeletionService) GetDeletionStatus(ctx context.Context, userID uuid.UUID) (string, error) {
	// Check if user is already deleted
	user, err := s.store.GetUserByIDIncludingDeleted(ctx, userID)
	if err != nil {
		return "", fmt.Errorf("failed to get user: %w", err)
	}

	if user.DeletedAt != nil {
		return "deleted", nil
	}

	// Check for scheduled deletion
	scheduled, err := s.store.GetScheduledDeletion(ctx, userID)
	if err == nil {
		return fmt.Sprintf("scheduled for %s", scheduled.ScheduledFor.Format("2006-01-02 15:04:05")), nil
	}

	// Check for active deletion request
	_, err = s.store.GetActiveDeletionRequest(ctx, userID)
	if err == nil {
		return "pending_confirmation", nil
	}

	return "none", nil
}

// GetAccountDeletionStatus retrieves the current status with details
func (s *AccountDeletionService) GetAccountDeletionStatus(ctx context.Context, userID uuid.UUID) (map[string]interface{}, error) {
	status := make(map[string]interface{})

	// Check if user is already deleted
	user, err := s.store.GetUserByIDIncludingDeleted(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	if user.DeletedAt != nil {
		status["status"] = "deleted"
		status["deleted_at"] = *user.DeletedAt
		return status, nil
	}

	// Check for scheduled deletion
	scheduled, err := s.store.GetScheduledDeletion(ctx, userID)
	if err == nil {
		status["status"] = "scheduled"
		status["scheduled_for"] = scheduled.ScheduledFor
		status["can_cancel"] = true
		return status, nil
	}

	// Check for active deletion request
	deletionReq, err := s.store.GetActiveDeletionRequest(ctx, userID)
	if err == nil {
		status["status"] = "pending_confirmation"
		status["expires_at"] = deletionReq.ExpiresAt
		return status, nil
	}

	status["status"] = "none"
	return status, nil
}

// generateDeletionCode generates a 6-digit confirmation code
func (s *AccountDeletionService) generateDeletionCode() string {
	// For now, use a simple random 6-digit code
	// In production, this should use crypto/rand for better security
	return fmt.Sprintf("%06d", time.Now().UnixNano()%1000000)
}
