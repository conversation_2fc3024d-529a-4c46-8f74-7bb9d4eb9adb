package init

import (
	"context"
	"crypto/rand"
	"errors"
	"fmt"
	"log"
	"math/big"
	"os"
	"time"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/config"
	"Membership-SAAS-System-Backend/internal/store"

	"golang.org/x/crypto/bcrypt"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

// InitService handles application initialization
type InitService struct {
	config *config.Config
	pool   *pgxpool.Pool
	store  store.TransactionalStore
}

// NewInitService creates a new init service instance
func NewInitService(cfg *config.Config) (*InitService, error) {
	ctx := context.Background()

	// Create connection pool
	pool, err := pgxpool.New(ctx, cfg.Database.URL)
	if err != nil {
		return nil, fmt.Errorf("failed to create connection pool: %w", err)
	}

	// Test the connection
	if err := pool.Ping(ctx); err != nil {
		pool.Close()
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	// Create store
	transactionalStore := store.NewPostgresStore(pool)

	return &InitService{
		config: cfg,
		pool:   pool,
		store:  transactionalStore,
	}, nil
}

// Close closes the database connection
func (s *InitService) Close() error {
	s.pool.Close()
	return nil
}

// Initialize performs application initialization
func (s *InitService) Initialize() error {
	ctx := context.Background()

	// Use ExecTx for transactional operations
	return s.store.ExecTx(ctx, func(txStore db.Querier) error {
		// Get initialization configuration from environment
		initConfig := s.getInitConfigFromEnv()

		// Generate password if not provided
		if initConfig.SuperAdminPassword == "" {
			initConfig.SuperAdminPassword = generateSecurePassword()
			log.Printf("Generated superadmin password: %s", initConfig.SuperAdminPassword)
		}

		// Hash the password
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(initConfig.SuperAdminPassword), bcrypt.DefaultCost)
		if err != nil {
			return fmt.Errorf("failed to hash password: %w", err)
		}

		// Check if superadmin already exists
		var superAdminID uuid.UUID
		superAdminUser, err := txStore.GetUserByEmail(ctx, &initConfig.SuperAdminEmail)
		if err == nil {
			log.Printf("Superadmin with email %s already exists, skipping...", initConfig.SuperAdminEmail)
			superAdminID = superAdminUser.ID
		} else if errors.Is(err, pgx.ErrNoRows) {
			// Create superadmin user
			emailPtr := &initConfig.SuperAdminEmail
			emailVerifiedAtPtr := timePtr(time.Now())
			hashedPasswordPtr := stringPtr(string(hashedPassword))

			createUserParams := db.CreateStaffUserWithEmailPasswordParams{
				DisplayName:              initConfig.SuperAdminName,
				Email:                    emailPtr,
				EmailVerifiedAt:          emailVerifiedAtPtr,
				HashedPassword:           hashedPasswordPtr,
				PhoneOtpChannel:          "email",
				InterfaceLanguage:        "en",
				CommunicationLanguage:    "en",
				EnableAppNotifications:   true,
				EnableEmailNotifications: true,
			}

			user, err := txStore.CreateStaffUserWithEmailPassword(ctx, createUserParams)
			if err != nil {
				return fmt.Errorf("failed to create superadmin: %w", err)
			}

			// Update to super_admin platform role
			err = txStore.UpdateUserPlatformRole(ctx, db.UpdateUserPlatformRoleParams{
				ID:           user.ID,
				PlatformRole: "super_admin",
			})
			if err != nil {
				return fmt.Errorf("failed to create superadmin: %w", err)
			}

			superAdminID = user.ID
			log.Printf("Created superadmin user: %s", initConfig.SuperAdminEmail)
		} else {
			return fmt.Errorf("failed to check existing superadmin: %w", err)
		}

		// Check if default organization already exists
		existingOrgs, err := txStore.ListOrganizations(ctx, db.ListOrganizationsParams{
			Limit:  1,
			Offset: 0,
		})
		if err != nil {
			return fmt.Errorf("failed to check existing organizations: %w", err)
		}

		var defaultOrgExists bool
		for _, org := range existingOrgs {
			if org.IsDefaultOrg {
				defaultOrgExists = true
				log.Printf("Default organization already exists: %s", org.Name)
				break
			}
		}

		if !defaultOrgExists {
			// Create default organization
			imageURL := fmt.Sprintf("/files/organization-resources/%s", initConfig.DefaultOrgImagePath)

			descriptionPtr := &initConfig.DefaultOrgDescription
			themeColorPtr := &initConfig.DefaultOrgThemeColor
			imageUrlPtr := &imageURL

			createOrgParams := db.CreateOrganizationParams{
				Name:         initConfig.DefaultOrgName,
				Description:  descriptionPtr,
				OwnerUserID:  superAdminID,
				IsDefaultOrg: true,
				ThemeColor:   themeColorPtr,
				ImageUrl:     imageUrlPtr,
				Status:       "active",
			}

			defaultOrg, err := txStore.CreateOrganization(ctx, createOrgParams)
			if err != nil {
				return fmt.Errorf("failed to create default organization: %w", err)
			}
			log.Printf("Created default organization: %s", initConfig.DefaultOrgName)

			// Add superadmin as owner of default organization
			addMembershipParams := db.AddUserToOrganizationParams{
				UserID:               superAdminID,
				OrganizationID:       defaultOrg.ID,
				Role:                 "owner",
				IsActive:             true,
				NotificationsEnabled: true,
			}

			_, err = txStore.AddUserToOrganization(ctx, addMembershipParams)
			if err != nil {
				return fmt.Errorf("failed to create membership: %w", err)
			}
			log.Printf("Added superadmin as owner of default organization")
		}

		log.Println("Application initialization completed successfully!")
		return nil
	})
}

// getInitConfigFromEnv retrieves initialization configuration from environment variables
func (s *InitService) getInitConfigFromEnv() initEnvConfig {
	return initEnvConfig{
		SuperAdminName:        getEnvOrDefault("SUPERADMIN_NAME", "Super Administrator"),
		SuperAdminEmail:       getEnvOrDefault("SUPERADMIN_EMAIL", "<EMAIL>"),
		SuperAdminPassword:    os.Getenv("SUPERADMIN_PASSWORD"),
		SuperAdminPhone:       getEnvOrDefault("SUPERADMIN_PHONE", "+1234567890"),
		DefaultOrgName:        getEnvOrDefault("DEFAULT_ORG_NAME", "Default Organization"),
		DefaultOrgSlug:        getEnvOrDefault("DEFAULT_ORG_SLUG", "default-org"),
		DefaultOrgDescription: getEnvOrDefault("DEFAULT_ORG_DESCRIPTION", "The global default organization for all users."),
		DefaultOrgThemeColor:  getEnvOrDefault("DEFAULT_ORG_THEME_COLOR", "red"),
		DefaultOrgImagePath:   getEnvOrDefault("DEFAULT_ORG_IMAGE_PATH", "assets/orgs_logos/default-logo.png"),
	}
}

// initEnvConfig holds configuration from environment variables
type initEnvConfig struct {
	SuperAdminName        string
	SuperAdminEmail       string
	SuperAdminPassword    string
	SuperAdminPhone       string
	DefaultOrgName        string
	DefaultOrgSlug        string
	DefaultOrgDescription string
	DefaultOrgThemeColor  string
	DefaultOrgImagePath   string
}

// getEnvOrDefault returns environment variable value or default
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// generateSecurePassword generates a secure random password
func generateSecurePassword() string {
	const (
		uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
		lowercase = "abcdefghijklmnopqrstuvwxyz"
		numbers   = "0123456789"
		symbols   = "!@#$%^&*()_+-=[]{}|;:,.<>?"
	)

	allChars := uppercase + lowercase + numbers + symbols
	password := make([]byte, 16)

	// Ensure at least one character from each set
	password[0] = uppercase[randInt(len(uppercase))]
	password[1] = lowercase[randInt(len(lowercase))]
	password[2] = numbers[randInt(len(numbers))]
	password[3] = symbols[randInt(len(symbols))]

	// Fill the rest randomly
	for i := 4; i < 16; i++ {
		password[i] = allChars[randInt(len(allChars))]
	}

	// Shuffle the password
	for i := len(password) - 1; i > 0; i-- {
		j := randInt(i + 1)
		password[i], password[j] = password[j], password[i]
	}

	return string(password)
}

// randInt returns a random integer between 0 and n-1
func randInt(n int) int {
	max := big.NewInt(int64(n))
	i, _ := rand.Int(rand.Reader, max)
	return int(i.Int64())
}

// Helper functions for nullable types
func timePtr(t time.Time) *time.Time {
	return &t
}

func stringPtr(s string) *string {
	return &s
}
