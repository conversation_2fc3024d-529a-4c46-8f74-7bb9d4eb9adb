package seed

import (
	"context"
	"encoding/json"
	"fmt"
	"os"

	"Membership-SAAS-System-Backend/db"
)

// ExportData exports database data to a JSON file
func (s *Seeder) ExportData(outputFile string, opts ExportOptions) error {
	ctx := context.Background()
	data := SeedData{}

	// Export organizations if requested
	if opts.IncludeOrganizations {
		orgs, err := s.exportOrganizations(ctx, opts)
		if err != nil {
			return fmt.Errorf("failed to export organizations: %w", err)
		}
		data.Organizations = orgs
	}

	// Export users if requested
	if opts.IncludeUsers {
		users, err := s.exportUsers(ctx, opts)
		if err != nil {
			return fmt.Errorf("failed to export users: %w", err)
		}
		data.Users = users
	}

	// Export memberships if requested
	if opts.IncludeMemberships {
		memberships, err := s.exportMemberships(ctx, opts)
		if err != nil {
			return fmt.E<PERSON>rf("failed to export memberships: %w", err)
		}
		data.Memberships = memberships
	}

	// Write to file
	file, err := os.Create(outputFile)
	if err != nil {
		return fmt.Errorf("failed to create output file: %w", err)
	}
	defer file.Close()

	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	if err := encoder.Encode(data); err != nil {
		return fmt.Errorf("failed to encode data: %w", err)
	}

	return nil
}

func (s *Seeder) exportOrganizations(ctx context.Context, opts ExportOptions) ([]OrganizationSeed, error) {
	// Get all organizations
	orgs, err := s.store.ListOrganizations(ctx, db.ListOrganizationsParams{
		Limit:  1000, // Reasonable limit
		Offset: 0,
	})
	if err != nil {
		return nil, err
	}

	var result []OrganizationSeed
	for _, org := range orgs {
		// Skip default organizations if system entities should not be included
		if !opts.IncludeSystemEntities && org.IsDefaultOrg {
			continue
		}

		// Get owner email
		owner, err := s.store.GetUserByID(ctx, org.OwnerUserID)
		if err != nil {
			return nil, fmt.Errorf("failed to get owner for org %s: %w", org.Name, err)
		}

		seed := OrganizationSeed{
			Name:        org.Name,
			Description: "",
			OwnerEmail:  "",
			IsDefault:   org.IsDefaultOrg,
		}

		// Handle nullable fields
		if org.Description != nil {
			seed.Description = *org.Description
		}

		if owner.Email != nil {
			seed.OwnerEmail = *owner.Email
		}

		if org.ThemeColor != nil {
			seed.ThemeColor = *org.ThemeColor
		}

		if org.ImageUrl != nil {
			// Extract just the filename from the URL
			seed.ImageAssetPath = extractAssetPath(*org.ImageUrl)
		}

		result = append(result, seed)
	}

	return result, nil
}

func (s *Seeder) exportUsers(ctx context.Context, opts ExportOptions) ([]UserSeed, error) {
	// Get all users
	users, err := s.store.ListUsers(ctx, db.ListUsersParams{
		Limit:  1000, // Reasonable limit
		Offset: 0,
	})
	if err != nil {
		return nil, err
	}

	var result []UserSeed
	for _, user := range users {
		isSuperAdmin := user.PlatformRole == "super_admin"

		// Skip super admin users if system entities should not be included
		if !opts.IncludeSystemEntities && isSuperAdmin {
			continue
		}

		seed := UserSeed{
			DisplayName:  user.DisplayName,
			PlatformRole: string(user.PlatformRole),
			IsSuperAdmin: isSuperAdmin, // Keep for backward compatibility
		}

		if user.Email != nil {
			seed.Email = *user.Email
		}

		if user.Phone != nil {
			seed.Phone = *user.Phone
		}

		// Note: We don't export passwords for security reasons
		result = append(result, seed)
	}

	return result, nil
}

func (s *Seeder) exportMemberships(ctx context.Context, opts ExportOptions) ([]MembershipSeed, error) {
	// Query includes platform_role and is_default_org to filter system entities
	query := `
		SELECT u.email, u.phone, o.name, m.role, u.platform_role, o.is_default_org
		FROM user_organization_memberships m
		JOIN users u ON u.id = m.user_id
		JOIN organizations o ON o.id = m.organization_id
		WHERE m.is_active = true
		ORDER BY o.name, u.email
	`

	rows, err := s.pool.Query(ctx, query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var result []MembershipSeed
	for rows.Next() {
		var email, phone *string
		var orgName, role, platformRole string
		var isDefaultOrg bool

		if err := rows.Scan(&email, &phone, &orgName, &role, &platformRole, &isDefaultOrg); err != nil {
			return nil, err
		}

		// Skip memberships for system entities if not included
		if !opts.IncludeSystemEntities {
			// Skip if user is super admin
			if platformRole == "super_admin" {
				continue
			}
			// Skip if organization is default
			if isDefaultOrg {
				continue
			}
		}

		seed := MembershipSeed{
			OrganizationName: orgName,
			Role:             role,
		}

		if email != nil {
			seed.UserEmail = *email
		}
		if phone != nil {
			seed.UserPhone = *phone
		}

		result = append(result, seed)
	}

	return result, rows.Err()
}

// extractAssetPath extracts the asset path from a full URL
func extractAssetPath(url string) string {
	// Remove common prefixes
	prefixes := []string{
		"/files/organization-resources/",
		"/api/v1/files/",
		"http://localhost:8080/files/",
		"https://",
		"http://",
	}

	result := url
	for _, prefix := range prefixes {
		if len(result) > len(prefix) && result[:len(prefix)] == prefix {
			result = result[len(prefix):]
		}
	}

	// If it's still a full path, try to extract just the asset part
	if len(result) > 0 && (result[0] == '/' || result[:4] == "http") {
		// Find "assets/" in the path
		if idx := lastIndex(result, "assets/"); idx >= 0 {
			result = result[idx:]
		}
	}

	return result
}

// lastIndex finds the last occurrence of substr in s
func lastIndex(s, substr string) int {
	for i := len(s) - len(substr); i >= 0; i-- {
		if s[i:i+len(substr)] == substr {
			return i
		}
	}
	return -1
}
