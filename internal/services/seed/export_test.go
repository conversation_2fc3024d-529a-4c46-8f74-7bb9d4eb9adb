package seed

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestExportOptions_IncludeSystemEntities(t *testing.T) {
	t.Run("ExportOptions struct has IncludeSystemEntities field", func(t *testing.T) {
		opts := ExportOptions{
			IncludeOrganizations:  true,
			IncludeUsers:          true,
			IncludeMemberships:    true,
			IncludeSystemEntities: true,
		}

		assert.True(t, opts.IncludeSystemEntities)
	})

	t.Run("ExportOptions default value for IncludeSystemEntities", func(t *testing.T) {
		opts := ExportOptions{
			IncludeOrganizations: true,
			IncludeUsers:         true,
			IncludeMemberships:   true,
		}

		// Default value should be false (zero value for bool)
		assert.False(t, opts.IncludeSystemEntities)
	})
}

// Note: These tests validate the filtering logic without requiring a real database connection
// The actual integration test with database is in seeder_test.go (TestExportImportRoundTrip)

func TestExportData_FilterSystemEntities(t *testing.T) {
	// This test validates that the filtering logic concept works correctly
	// The actual implementation in export.go follows this same pattern
	testCases := []struct {
		name                  string
		includeSystemEntities bool
		userData              []UserSeed
		orgData               []OrganizationSeed
		expectedUsers         int
		expectedOrgs          int
	}{
		{
			name:                  "Include system entities",
			includeSystemEntities: true,
			userData: []UserSeed{
				{DisplayName: "Regular User", PlatformRole: "user", IsSuperAdmin: false},
				{DisplayName: "Super Admin", PlatformRole: "super_admin", IsSuperAdmin: true},
				{DisplayName: "Staff User", PlatformRole: "staff", IsSuperAdmin: false},
			},
			orgData: []OrganizationSeed{
				{Name: "Regular Org", IsDefault: false},
				{Name: "Default Org", IsDefault: true},
			},
			expectedUsers: 3,
			expectedOrgs:  2,
		},
		{
			name:                  "Exclude system entities",
			includeSystemEntities: false,
			userData: []UserSeed{
				{DisplayName: "Regular User", PlatformRole: "user", IsSuperAdmin: false},
				{DisplayName: "Super Admin", PlatformRole: "super_admin", IsSuperAdmin: true},
				{DisplayName: "Staff User", PlatformRole: "staff", IsSuperAdmin: false},
			},
			orgData: []OrganizationSeed{
				{Name: "Regular Org", IsDefault: false},
				{Name: "Default Org", IsDefault: true},
			},
			expectedUsers: 2, // Super Admin should be filtered out
			expectedOrgs:  1, // Default Org should be filtered out
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Test user filtering logic - mimics exportUsers filtering
			var filteredUsers []UserSeed
			for _, user := range tc.userData {
				// This matches the logic in exportUsers:
				// if !opts.IncludeSystemEntities && isSuperAdmin { continue }
				if tc.includeSystemEntities || !user.IsSuperAdmin {
					filteredUsers = append(filteredUsers, user)
				}
			}
			assert.Equal(t, tc.expectedUsers, len(filteredUsers))

			// Test organization filtering logic - mimics exportOrganizations filtering
			var filteredOrgs []OrganizationSeed
			for _, org := range tc.orgData {
				// This matches the logic in exportOrganizations:
				// if !opts.IncludeSystemEntities && org.IsDefaultOrg { continue }
				if tc.includeSystemEntities || !org.IsDefault {
					filteredOrgs = append(filteredOrgs, org)
				}
			}
			assert.Equal(t, tc.expectedOrgs, len(filteredOrgs))
		})
	}
}

func TestExportOptions_Usage(t *testing.T) {
	// This test documents how the ExportOptions struct should be used
	// in the export functions. The actual integration testing happens
	// in TestExportImportRoundTrip in seeder_test.go

	t.Run("Export functions accept opts parameter", func(t *testing.T) {
		// The export functions have been updated to accept ExportOptions:
		// - exportOrganizations(ctx, opts)
		// - exportUsers(ctx, opts)
		// - exportMemberships(ctx, opts)

		// Example usage:
		opts := ExportOptions{
			IncludeOrganizations:  true,
			IncludeUsers:          true,
			IncludeMemberships:    true,
			IncludeSystemEntities: false, // This filters out super admins and default orgs
		}

		// When IncludeSystemEntities is false:
		// - exportOrganizations skips orgs where IsDefaultOrg = true
		// - exportUsers skips users where platform_role = "super_admin"
		// - exportMemberships skips memberships for filtered entities

		assert.NotNil(t, opts)
	})

	t.Run("Filtering behavior documentation", func(t *testing.T) {
		// Document the expected behavior for future developers

		// Case 1: Include all entities (including system entities)
		optsIncludeAll := ExportOptions{
			IncludeOrganizations:  true,
			IncludeUsers:          true,
			IncludeMemberships:    true,
			IncludeSystemEntities: true,
		}
		assert.True(t, optsIncludeAll.IncludeSystemEntities,
			"When true, super admin users and default organizations are included in export")

		// Case 2: Exclude system entities (default behavior)
		optsExcludeSystem := ExportOptions{
			IncludeOrganizations: true,
			IncludeUsers:         true,
			IncludeMemberships:   true,
			// IncludeSystemEntities defaults to false
		}
		assert.False(t, optsExcludeSystem.IncludeSystemEntities,
			"When false (default), super admin users and default organizations are excluded from export")
	})
}
