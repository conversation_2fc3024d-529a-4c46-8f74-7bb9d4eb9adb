package seed

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"time"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/utils/validation"

	"github.com/google/uuid"
	"golang.org/x/crypto/bcrypt"
)

// isSystemUser checks if a user seed represents a system user (superadmin)
func isSystemUser(userSeed UserSeed) bool {
	return userSeed.IsSuperAdmin
}

// isSystemOrganization checks if an organization seed represents a system organization (default org)
func isSystemOrganization(orgSeed OrganizationSeed) bool {
	return orgSeed.IsDefault
}

// initEnvConfig holds configuration from environment variables
type initEnvConfig struct {
	SuperAdminName        string
	SuperAdminEmail       string
	SuperAdminPassword    string
	SuperAdminPhone       string
	DefaultOrgName        string
	DefaultOrgSlug        string
	DefaultOrgDescription string
	DefaultOrgThemeColor  string
	DefaultOrgImagePath   string
}

// getInitConfigFromEnv retrieves initialization configuration from environment variables
func getInitConfigFromEnv() initEnvConfig {
	return initEnvConfig{
		SuperAdminName:        getEnvOrDefault("SUPERADMIN_NAME", "Super Administrator"),
		SuperAdminEmail:       getEnvOrDefault("SUPERADMIN_EMAIL", "<EMAIL>"),
		SuperAdminPassword:    os.Getenv("SUPERADMIN_PASSWORD"),
		SuperAdminPhone:       getEnvOrDefault("SUPERADMIN_PHONE", "+1234567890"),
		DefaultOrgName:        getEnvOrDefault("DEFAULT_ORG_NAME", "Default Organization"),
		DefaultOrgSlug:        getEnvOrDefault("DEFAULT_ORG_SLUG", "default-org"),
		DefaultOrgDescription: getEnvOrDefault("DEFAULT_ORG_DESCRIPTION", "The global default organization for all users."),
		DefaultOrgThemeColor:  getEnvOrDefault("DEFAULT_ORG_THEME_COLOR", "red"),
		DefaultOrgImagePath:   getEnvOrDefault("DEFAULT_ORG_IMAGE_PATH", "assets/orgs_logos/default-logo.png"),
	}
}

// getEnvOrDefault returns environment variable value or default
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// validateInitEnvConfig validates the environment configuration
func validateInitEnvConfig(config initEnvConfig) error {
	// Validate email format
	if err := validation.ValidateEmail(config.SuperAdminEmail); err != nil {
		return fmt.Errorf("invalid SUPERADMIN_EMAIL: %w", err)
	}

	// Validate phone format
	if err := validation.ValidatePhoneE164(config.SuperAdminPhone); err != nil {
		return fmt.Errorf("invalid SUPERADMIN_PHONE: %w", err)
	}

	// Validate name length
	if err := validation.ValidateStringLength(config.SuperAdminName, "SUPERADMIN_NAME", 1, 255); err != nil {
		return err
	}

	// Validate organization name length
	if err := validation.ValidateStringLength(config.DefaultOrgName, "DEFAULT_ORG_NAME", 1, 255); err != nil {
		return err
	}

	// Validate theme color
	validThemeColors := []string{"red", "blue", "green", "yellow", "purple", "orange", "pink", "gray"}
	validColor := false
	for _, color := range validThemeColors {
		if config.DefaultOrgThemeColor == color {
			validColor = true
			break
		}
	}
	if !validColor {
		return fmt.Errorf("invalid DEFAULT_ORG_THEME_COLOR: %s. Valid colors are: red, blue, green, yellow, purple, orange, pink, gray", config.DefaultOrgThemeColor)
	}

	return nil
}

// overrideSuperAdminFromEnv applies environment overrides to a super admin user seed
func overrideSuperAdminFromEnv(userSeed *UserSeed, envConfig initEnvConfig) {
	if userSeed == nil || !userSeed.IsSuperAdmin {
		return
	}

	originalValues := map[string]string{
		"name":     userSeed.DisplayName,
		"email":    userSeed.Email,
		"phone":    userSeed.Phone,
		"password": "<hidden>",
	}

	// Apply overrides
	if envConfig.SuperAdminName != "" && envConfig.SuperAdminName != "Super Administrator" {
		userSeed.DisplayName = envConfig.SuperAdminName
	}
	if envConfig.SuperAdminEmail != "" && envConfig.SuperAdminEmail != "<EMAIL>" {
		userSeed.Email = envConfig.SuperAdminEmail
	}
	if envConfig.SuperAdminPassword != "" {
		userSeed.RawPassword = envConfig.SuperAdminPassword
	}
	if envConfig.SuperAdminPhone != "" && envConfig.SuperAdminPhone != "+1234567890" {
		userSeed.Phone = envConfig.SuperAdminPhone
	}

	// Log overrides
	if userSeed.DisplayName != originalValues["name"] {
		log.Printf("  - Display name: %s → %s", originalValues["name"], userSeed.DisplayName)
	}
	if userSeed.Email != originalValues["email"] {
		log.Printf("  - Email: %s → %s", originalValues["email"], userSeed.Email)
	}
	if userSeed.Phone != originalValues["phone"] {
		log.Printf("  - Phone: %s → %s", originalValues["phone"], userSeed.Phone)
	}
	if envConfig.SuperAdminPassword != "" {
		log.Printf("  - Password: <updated from environment>")
	}
}

// overrideDefaultOrgFromEnv applies environment overrides to a default organization seed
func overrideDefaultOrgFromEnv(orgSeed *OrganizationSeed, envConfig initEnvConfig) {
	if orgSeed == nil || !orgSeed.IsDefault {
		return
	}

	originalValues := map[string]string{
		"name":        orgSeed.Name,
		"description": orgSeed.Description,
		"themeColor":  orgSeed.ThemeColor,
		"imagePath":   orgSeed.ImageAssetPath,
	}

	// Apply overrides
	if envConfig.DefaultOrgName != "" && envConfig.DefaultOrgName != "Default Organization" {
		orgSeed.Name = envConfig.DefaultOrgName
	}
	if envConfig.DefaultOrgDescription != "" && envConfig.DefaultOrgDescription != "The global default organization for all users." {
		orgSeed.Description = envConfig.DefaultOrgDescription
	}
	if envConfig.DefaultOrgThemeColor != "" && envConfig.DefaultOrgThemeColor != "red" {
		orgSeed.ThemeColor = envConfig.DefaultOrgThemeColor
	}
	if envConfig.DefaultOrgImagePath != "" && envConfig.DefaultOrgImagePath != "assets/orgs_logos/default-logo.png" {
		orgSeed.ImageAssetPath = envConfig.DefaultOrgImagePath
	}

	// Log overrides
	if orgSeed.Name != originalValues["name"] {
		log.Printf("  - Name: %s → %s", originalValues["name"], orgSeed.Name)
	}
	if orgSeed.Description != originalValues["description"] {
		log.Printf("  - Description: %s → %s", originalValues["description"], orgSeed.Description)
	}
	if orgSeed.ThemeColor != originalValues["themeColor"] {
		log.Printf("  - Theme color: %s → %s", originalValues["themeColor"], orgSeed.ThemeColor)
	}
	if orgSeed.ImageAssetPath != originalValues["imagePath"] {
		log.Printf("  - Image path: %s → %s", originalValues["imagePath"], orgSeed.ImageAssetPath)
	}
}

// ImportData imports data from a JSON file
func (s *Seeder) ImportData(inputFile string, opts ImportOptions) error {
	// Log import options
	log.Printf("Starting import with options: SkipExisting=%v, SkipSystemEntities=%v, OverrideFromEnv=%v",
		opts.SkipExisting, opts.SkipSystemEntities, opts.OverrideFromEnv)

	// Read file
	file, err := os.Open(inputFile)
	if err != nil {
		return fmt.Errorf("failed to open input file: %w", err)
	}
	defer file.Close()

	// Parse JSON
	data, err := parseJSONFile(file)
	if err != nil {
		return fmt.Errorf("failed to parse JSON: %w", err)
	}

	// Validate seed data before import
	if err := validateSeedData(data); err != nil {
		return fmt.Errorf("seed data validation failed: %w", err)
	}

	// Get and validate environment configuration if override is requested
	var envConfig initEnvConfig
	if opts.OverrideFromEnv {
		log.Println("Environment override requested - reading environment configuration...")
		envConfig = getInitConfigFromEnv()

		// Validate environment configuration
		if err := validateInitEnvConfig(envConfig); err != nil {
			return fmt.Errorf("environment configuration validation failed: %w", err)
		}
		log.Println("Environment configuration validated successfully")

		// Apply environment overrides to system entities before import
		for i := range data.Users {
			if isSystemUser(data.Users[i]) {
				log.Printf("Applying environment overrides for system user: %s", data.Users[i].DisplayName)
				overrideSuperAdminFromEnv(&data.Users[i], envConfig)
			}
		}

		for i := range data.Organizations {
			if isSystemOrganization(data.Organizations[i]) {
				log.Printf("Applying environment overrides for system organization: %s", data.Organizations[i].Name)
				overrideDefaultOrgFromEnv(&data.Organizations[i], envConfig)
			}
		}
	}

	ctx := context.Background()

	// Track import statistics
	var importStats struct {
		UsersProcessed       int
		UsersImported        int
		UsersSkipped         int
		SystemUsersSkipped   int
		OrgsProcessed        int
		OrgsImported         int
		OrgsSkipped          int
		SystemOrgsSkipped    int
		MembershipsProcessed int
		MembershipsImported  int
		MembershipsSkipped   int
	}

	// Use ExecTx for transactional operations
	err = s.store.ExecTx(ctx, func(txStore db.Querier) error {
		// Import users first
		userMap := make(map[string]uuid.UUID) // email/phone -> user ID
		for _, userSeed := range data.Users {
			importStats.UsersProcessed++
			if opts.SkipSystemEntities && isSystemUser(userSeed) {
				importStats.SystemUsersSkipped++
				importStats.UsersSkipped++
				continue
			}

			userID, err := s.importUser(txStore, userSeed, opts)
			if err != nil {
				return fmt.Errorf("failed to import user %s: %w", userSeed.DisplayName, err)
			}
			if userID != uuid.Nil {
				importStats.UsersImported++
				if userSeed.Email != "" {
					userMap[userSeed.Email] = userID
				}
				if userSeed.Phone != "" {
					userMap[userSeed.Phone] = userID
				}
			} else {
				importStats.UsersSkipped++
			}
		}

		// Import organizations
		orgMap := make(map[string]uuid.UUID) // org name -> org ID
		for _, orgSeed := range data.Organizations {
			importStats.OrgsProcessed++
			if opts.SkipSystemEntities && isSystemOrganization(orgSeed) {
				importStats.SystemOrgsSkipped++
				importStats.OrgsSkipped++
				continue
			}

			orgID, err := s.importOrganization(txStore, orgSeed, userMap, opts)
			if err != nil {
				return fmt.Errorf("failed to import organization %s: %w", orgSeed.Name, err)
			}
			if orgID != uuid.Nil {
				importStats.OrgsImported++
				orgMap[orgSeed.Name] = orgID
			} else {
				importStats.OrgsSkipped++
			}
		}

		// Import memberships
		for _, membershipSeed := range data.Memberships {
			importStats.MembershipsProcessed++

			// Check if user or org was skipped
			var userID uuid.UUID
			if membershipSeed.UserEmail != "" {
				userID = userMap[membershipSeed.UserEmail]
			} else if membershipSeed.UserPhone != "" {
				userID = userMap[membershipSeed.UserPhone]
			}

			_, orgExists := orgMap[membershipSeed.OrganizationName]

			// Skip if either user or org doesn't exist (likely due to being skipped)
			if userID == uuid.Nil || !orgExists {
				importStats.MembershipsSkipped++
				log.Printf("Skipping membership for %s in %s - user or organization not found (likely skipped)",
					membershipSeed.UserEmail+membershipSeed.UserPhone, membershipSeed.OrganizationName)
				continue
			}

			if err := s.importMembership(txStore, membershipSeed, userMap, orgMap, opts); err != nil {
				return fmt.Errorf("failed to import membership: %w", err)
			}
			importStats.MembershipsImported++
		}

		return nil
	})
	if err != nil {
		return err
	}

	// Log import summary
	log.Println("Import completed successfully!")
	log.Printf("Users: %d processed, %d imported, %d skipped (%d system users skipped)",
		importStats.UsersProcessed, importStats.UsersImported, importStats.UsersSkipped, importStats.SystemUsersSkipped)
	log.Printf("Organizations: %d processed, %d imported, %d skipped (%d system orgs skipped)",
		importStats.OrgsProcessed, importStats.OrgsImported, importStats.OrgsSkipped, importStats.SystemOrgsSkipped)
	log.Printf("Memberships: %d processed, %d imported, %d skipped",
		importStats.MembershipsProcessed, importStats.MembershipsImported, importStats.MembershipsSkipped)

	return nil
}

func (s *Seeder) importUser(store db.Querier, userSeed UserSeed, opts ImportOptions) (uuid.UUID, error) {
	// Skip system entities if requested
	if opts.SkipSystemEntities && isSystemUser(userSeed) {
		log.Printf("WARNING: Skipping system user (superadmin) %s - SkipSystemEntities is enabled", userSeed.DisplayName)
		return uuid.Nil, nil
	}

	// Check if user already exists
	ctx := context.Background()
	var existingUser db.User
	var err error

	if userSeed.Email != "" {
		existingUser, err = store.GetUserByEmail(ctx, &userSeed.Email)
	} else if userSeed.Phone != "" {
		existingUser, err = store.GetUserByPhone(ctx, &userSeed.Phone)
	}

	if err == nil && opts.SkipExisting {
		log.Printf("User %s already exists, skipping...", userSeed.DisplayName)
		return existingUser.ID, nil
	}

	// Hash password if provided
	var hashedPassword *string
	if userSeed.RawPassword != "" {
		hash, err := bcrypt.GenerateFromPassword([]byte(userSeed.RawPassword), bcrypt.DefaultCost)
		if err != nil {
			return uuid.Nil, fmt.Errorf("failed to hash password: %w", err)
		}
		hashedPasswordStr := string(hash)
		hashedPassword = &hashedPasswordStr
	}

	// Create user based on available data
	var user db.User

	// Determine platform role
	platformRole := userSeed.PlatformRole
	if platformRole == "" {
		// Backward compatibility
		if userSeed.IsSuperAdmin {
			platformRole = "super_admin"
		} else {
			platformRole = "user"
		}
	}
	
	if userSeed.Email != "" && (platformRole == "staff" || platformRole == "admin" || platformRole == "super_admin") {
		// Create staff user with email
		now := time.Now()
		params := db.CreateStaffUserWithEmailPasswordParams{
			DisplayName:              userSeed.DisplayName,
			Email:                    &userSeed.Email,
			EmailVerifiedAt:          &now,
			HashedPassword:           hashedPassword,
			PhoneOtpChannel:          "email",
			InterfaceLanguage:        "en",
			CommunicationLanguage:    "en",
			EnableAppNotifications:   true,
			EnableEmailNotifications: true,
		}

		user, err = store.CreateStaffUserWithEmailPassword(ctx, params)
		if err != nil {
			return uuid.Nil, err
		}

		// Update platform role if superadmin
		if userSeed.IsSuperAdmin {
			err = store.UpdateUserPlatformRole(ctx, db.UpdateUserPlatformRoleParams{
				ID:           user.ID,
				PlatformRole: "super_admin",
			})
			if err != nil {
				return uuid.Nil, fmt.Errorf("failed to update platform role: %w", err)
			}
		}
	} else if userSeed.Phone != "" {
		// Create user with phone
		now := time.Now()
		params := db.CreateUserWithPhoneParams{
			DisplayName:              userSeed.DisplayName,
			Phone:                    &userSeed.Phone,
			PhoneVerifiedAt:          &now,
			PhoneOtpChannel:          "sms",
			InterfaceLanguage:        "en",
			CommunicationLanguage:    "en",
			EnableAppNotifications:   true,
			EnableEmailNotifications: true,
		}

		user, err = store.CreateUserWithPhone(ctx, params)
		if err != nil {
			return uuid.Nil, err
		}
	} else {
		return uuid.Nil, fmt.Errorf("user must have either email or phone")
	}

	log.Printf("Created user: %s", userSeed.DisplayName)
	return user.ID, nil
}

func (s *Seeder) importOrganization(store db.Querier, orgSeed OrganizationSeed, userMap map[string]uuid.UUID, opts ImportOptions) (uuid.UUID, error) {
	// Check if organization already exists
	ctx := context.Background()
	orgs, err := store.GetOrganizationByName(ctx, orgSeed.Name)
	if err == nil && opts.SkipExisting {
		log.Printf("Organization %s already exists, skipping...", orgSeed.Name)
		return orgs.ID, nil
	}

	// If this is a system organization, handle special logic
	if isSystemOrganization(orgSeed) {
		// Skip system entities if requested
		if opts.SkipSystemEntities {
			log.Printf("WARNING: Skipping system organization (default) %s - SkipSystemEntities is enabled", orgSeed.Name)
			return uuid.Nil, nil
		}

		// Check if a default organization already exists
		existingDefault, err := store.GetDefaultOrganization(ctx)
		if err == nil && existingDefault.ID != uuid.Nil {
			if opts.SkipExisting {
				log.Printf("Default organization already exists (%s), skipping import of %s", existingDefault.Name, orgSeed.Name)
				return existingDefault.ID, nil
			}
			return uuid.Nil, fmt.Errorf("cannot import default organization %s: a default organization already exists (%s)", orgSeed.Name, existingDefault.Name)
		}
	}

	// Find owner user ID
	ownerID, ok := userMap[orgSeed.OwnerEmail]
	if !ok {
		return uuid.Nil, fmt.Errorf("owner user %s not found", orgSeed.OwnerEmail)
	}

	// Determine image URL
	imageURL := fmt.Sprintf("/files/organization-resources/%s", orgSeed.ImageAssetPath)

	// Create organization
	params := db.CreateOrganizationParams{
		Name:         orgSeed.Name,
		Description:  stringPtr(orgSeed.Description),
		OwnerUserID:  ownerID,
		IsDefaultOrg: orgSeed.IsDefault,
		ThemeColor:   stringPtr(orgSeed.ThemeColor),
		ImageUrl:     stringPtr(imageURL),
		Status:       "active",
	}

	org, err := store.CreateOrganization(ctx, params)
	if err != nil {
		return uuid.Nil, err
	}

	// Add owner as member
	membershipParams := db.AddUserToOrganizationParams{
		UserID:               ownerID,
		OrganizationID:       org.ID,
		Role:                 "owner",
		IsActive:             true,
		NotificationsEnabled: true,
	}

	_, err = store.AddUserToOrganization(ctx, membershipParams)
	if err != nil {
		return uuid.Nil, fmt.Errorf("failed to create owner membership: %w", err)
	}

	log.Printf("Created organization: %s", orgSeed.Name)
	return org.ID, nil
}

func (s *Seeder) importMembership(store db.Querier, membershipSeed MembershipSeed, userMap map[string]uuid.UUID, orgMap map[string]uuid.UUID, opts ImportOptions) error {
	// Find user ID
	var userID uuid.UUID
	if membershipSeed.UserEmail != "" {
		userID = userMap[membershipSeed.UserEmail]
	} else if membershipSeed.UserPhone != "" {
		userID = userMap[membershipSeed.UserPhone]
	}

	if userID == uuid.Nil {
		return fmt.Errorf("user not found for membership")
	}

	// Find organization ID
	orgID, ok := orgMap[membershipSeed.OrganizationName]
	if !ok {
		return fmt.Errorf("organization %s not found", membershipSeed.OrganizationName)
	}

	// Check if membership already exists
	ctx := context.Background()
	_, err := store.GetUserOrganizationMembership(ctx, db.GetUserOrganizationMembershipParams{
		UserID:         userID,
		OrganizationID: orgID,
	})
	if err == nil && opts.SkipExisting {
		log.Printf("Membership already exists for user in %s, skipping...", membershipSeed.OrganizationName)
		return nil
	}

	// Create membership
	params := db.AddUserToOrganizationParams{
		UserID:               userID,
		OrganizationID:       orgID,
		Role:                 membershipSeed.Role,
		IsActive:             true,
		NotificationsEnabled: true,
	}

	_, err = store.AddUserToOrganization(ctx, params)
	if err != nil {
		return err
	}

	log.Printf("Created membership: user -> %s (%s)", membershipSeed.OrganizationName, membershipSeed.Role)
	return nil
}

// validateSeedData validates the seed data before import
func validateSeedData(data *SeedData) error {
	// Validate default organization uniqueness
	defaultOrgCount := 0
	var defaultOrgNames []string

	for _, org := range data.Organizations {
		if org.IsDefault {
			defaultOrgCount++
			defaultOrgNames = append(defaultOrgNames, org.Name)
		}
	}

	if defaultOrgCount > 1 {
		return fmt.Errorf("seed data contains %d default organizations (%v), but only one is allowed", defaultOrgCount, defaultOrgNames)
	}

	// Validate that default organizations have owners
	for _, org := range data.Organizations {
		if org.IsDefault && org.OwnerEmail == "" {
			return fmt.Errorf("default organization '%s' must have an owner email", org.Name)
		}
	}

	return nil
}

// parseJSONFile parses the seed data JSON file
func parseJSONFile(r io.Reader) (*SeedData, error) {
	var data SeedData
	decoder := json.NewDecoder(r)
	if err := decoder.Decode(&data); err != nil {
		return nil, err
	}

	// Check if this is the default seed_data.json format
	if len(data.Users) == 0 && len(data.Organizations) == 0 {
		// Try to read from assets/init/seed_data.json
		defaultPath := filepath.Join("assets", "init", "seed_data.json")
		if file, err := os.Open(defaultPath); err == nil {
			defer file.Close()
			decoder := json.NewDecoder(file)
			if err := decoder.Decode(&data); err == nil {
				return &data, nil
			}
		}
	}

	return &data, nil
}

// Helper function for nullable strings
func stringPtr(s string) *string {
	if s == "" {
		return nil
	}
	return &s
}
