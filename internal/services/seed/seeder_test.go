package seed

import (
	"context"
	"encoding/json"
	"os"
	"path/filepath"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/config"
	"Membership-SAAS-System-Backend/internal/store"
	teststore "Membership-SAAS-System-Backend/internal/testutil/store"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewSeeder(t *testing.T) {
	// Skip if no test database
	if os.Getenv("TEST_USE_EXISTING_DB") != "true" && os.Getenv("DATABASE_URL") == "" {
		t.Skip("Skipping database test: no test database configured")
	}

	cfg := config.Load()

	seeder, err := NewSeeder(cfg)
	require.NoError(t, err)
	require.NotNil(t, seeder)

	// Clean up
	err = seeder.Close()
	require.NoError(t, err)
}

func TestExportImportRoundTrip(t *testing.T) {
	// Skip if no test database
	if os.Getenv("TEST_USE_EXISTING_DB") != "true" {
		testStore, cleanup := teststore.SetupTestDB(t)
		defer cleanup()
		_ = testStore // We use our own connection through seeder
	}

	cfg := config.Load()

	// Create seeder
	seeder, err := NewSeeder(cfg)
	require.NoError(t, err)
	defer seeder.Close()

	// Note: For this test to work properly, we would need some initial data
	// Since we separated initialization from seeding, we would need to:
	// 1. Initialize the app first (superadmin + default org)
	// 2. Add some actual data (events, posts, tags, etc.)
	// 3. Then test export/import

	// For now, just test that export/import functions exist and don't crash
	exportFile := t.TempDir() + "/test_export.json"
	err = seeder.ExportData(exportFile, ExportOptions{
		IncludeOrganizations: true,
		IncludeUsers:         true,
		IncludeMemberships:   true,
	})
	require.NoError(t, err)

	// Verify export file exists
	_, err = os.Stat(exportFile)
	require.NoError(t, err)

	// Test import
	err = seeder.ImportData(exportFile, ImportOptions{
		SkipExisting: false,
	})
	require.NoError(t, err)
}

func TestImportDataWithOptions(t *testing.T) {
	tests := []struct {
		name        string
		seedData    SeedData
		options     ImportOptions
		envSetup    map[string]string
		expectError bool
		verifyFunc  func(t *testing.T, store store.TransactionalStore)
	}{
		{
			name: "Import with SkipExisting option",
			seedData: SeedData{
				Users: []UserSeed{
					{
						Email:        "<EMAIL>",
						DisplayName:  "Existing User",
						RawPassword:  "password123",
						PlatformRole: "staff",
					},
				},
			},
			options: ImportOptions{
				SkipExisting: true,
			},
			verifyFunc: func(t *testing.T, store store.TransactionalStore) {
				ctx := context.Background()
				email := "<EMAIL>"
				users, err := store.GetUserByEmail(ctx, &email)
				require.NoError(t, err)
				assert.Equal(t, "Existing User", users.DisplayName)
			},
		},
		{
			name: "Import with SkipSystemEntities option",
			seedData: SeedData{
				Users: []UserSeed{
					{
						Email:        "<EMAIL>",
						DisplayName:  "Super Admin",
						RawPassword:  "admin123",
						IsSuperAdmin: true,
					},
					{
						Email:        "<EMAIL>",
						DisplayName:  "Regular User",
						RawPassword:  "user123",
						PlatformRole: "staff",
					},
				},
				Organizations: []OrganizationSeed{
					{
						Name:        "Default Org",
						Description: "Default organization",
						OwnerEmail:  "<EMAIL>",
						IsDefault:   true,
						ThemeColor:  "blue",
					},
					{
						Name:        "Regular Org",
						Description: "Regular organization",
						OwnerEmail:  "<EMAIL>",
						IsDefault:   false,
						ThemeColor:  "green",
					},
				},
			},
			options: ImportOptions{
				SkipSystemEntities: true,
			},
			verifyFunc: func(t *testing.T, store store.TransactionalStore) {
				ctx := context.Background()

				// Super admin should not exist
				superEmail := "<EMAIL>"
				_, err := store.GetUserByEmail(ctx, &superEmail)
				assert.Error(t, err)

				// Regular user should exist
				regularEmail := "<EMAIL>"
				user, err := store.GetUserByEmail(ctx, &regularEmail)
				require.NoError(t, err)
				assert.Equal(t, "Regular User", user.DisplayName)

				// Default org should not exist
				_, err = store.GetDefaultOrganization(ctx)
				assert.Error(t, err)

				// Regular org should exist
				org, err := store.GetOrganizationByName(ctx, "Regular Org")
				require.NoError(t, err)
				assert.Equal(t, "Regular Org", org.Name)
				assert.False(t, org.IsDefaultOrg)
			},
		},
		{
			name: "Import with OverrideFromEnv option",
			seedData: SeedData{
				Users: []UserSeed{
					{
						Email:        "<EMAIL>",
						DisplayName:  "Old Admin",
						RawPassword:  "oldpass",
						Phone:        "+1111111111",
						IsSuperAdmin: true,
					},
				},
				Organizations: []OrganizationSeed{
					{
						Name:           "Old Default Org",
						Description:    "Old description",
						OwnerEmail:     "<EMAIL>",
						IsDefault:      true,
						ThemeColor:     "red",
						ImageAssetPath: "old-logo.png",
					},
				},
			},
			options: ImportOptions{
				OverrideFromEnv: true,
			},
			envSetup: map[string]string{
				"SUPERADMIN_NAME":         "New Admin",
				"SUPERADMIN_EMAIL":        "<EMAIL>",
				"SUPERADMIN_PHONE":        "+2222222222",
				"SUPERADMIN_PASSWORD":     "newpass",
				"DEFAULT_ORG_NAME":        "New Default Org",
				"DEFAULT_ORG_DESCRIPTION": "New description",
				"DEFAULT_ORG_THEME_COLOR": "blue",
				"DEFAULT_ORG_IMAGE_PATH":  "new-logo.png",
			},
			verifyFunc: func(t *testing.T, store store.TransactionalStore) {
				ctx := context.Background()

				// Super admin should have new values
				newEmail := "<EMAIL>"
				user, err := store.GetUserByEmail(ctx, &newEmail)
				require.NoError(t, err)
				assert.Equal(t, "New Admin", user.DisplayName)
				assert.Equal(t, "+2222222222", *user.Phone)

				// Default org should have new values
				org, err := store.GetDefaultOrganization(ctx)
				require.NoError(t, err)
				assert.Equal(t, "New Default Org", org.Name)
				assert.Equal(t, "New description", *org.Description)
				assert.Equal(t, "blue", *org.ThemeColor)
			},
		},
		{
			name: "Import with all options combined",
			seedData: SeedData{
				Users: []UserSeed{
					{
						Email:        "<EMAIL>",
						DisplayName:  "Super Admin",
						RawPassword:  "admin123",
						IsSuperAdmin: true,
					},
					{
						Email:        "<EMAIL>",
						DisplayName:  "Should Skip",
						RawPassword:  "pass123",
						PlatformRole: "staff",
					},
				},
			},
			options: ImportOptions{
				SkipExisting:       true,
				SkipSystemEntities: true,
				OverrideFromEnv:    false,
			},
			verifyFunc: func(t *testing.T, store store.TransactionalStore) {
				ctx := context.Background()

				// Super admin should not exist (skipped as system entity)
				superEmail := "<EMAIL>"
				_, err := store.GetUserByEmail(ctx, &superEmail)
				assert.Error(t, err)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Skip if no test database
			if os.Getenv("TEST_USE_EXISTING_DB") != "true" {
				testStore, cleanup := teststore.SetupTestDB(t)
				defer cleanup()
				_ = testStore
			}

			// Save and set up environment variables
			if tt.envSetup != nil {
				for key, value := range tt.envSetup {
					oldValue := os.Getenv(key)
					os.Setenv(key, value)
					defer os.Setenv(key, oldValue)
				}
			}

			cfg := config.Load()
			seeder, err := NewSeeder(cfg)
			require.NoError(t, err)
			defer seeder.Close()

			// If testing SkipExisting, first create the user
			if tt.options.SkipExisting && len(tt.seedData.Users) > 0 {
				// Import first to create existing data
				tempFile := filepath.Join(t.TempDir(), "existing_data.json")
				data, _ := json.Marshal(tt.seedData)
				err := os.WriteFile(tempFile, data, 0644)
				require.NoError(t, err)

				err = seeder.ImportData(tempFile, ImportOptions{})
				require.NoError(t, err)
			}

			// Create test data file
			tempFile := filepath.Join(t.TempDir(), "test_import.json")
			data, err := json.Marshal(tt.seedData)
			require.NoError(t, err)
			err = os.WriteFile(tempFile, data, 0644)
			require.NoError(t, err)

			// Perform import
			err = seeder.ImportData(tempFile, tt.options)
			if tt.expectError {
				assert.Error(t, err)
			} else {
				require.NoError(t, err)
				if tt.verifyFunc != nil {
					tt.verifyFunc(t, seeder.store)
				}
			}
		})
	}
}

func TestImportDataWithExistingSystemEntities(t *testing.T) {
	// Skip if no test database
	if os.Getenv("TEST_USE_EXISTING_DB") != "true" {
		testStore, cleanup := teststore.SetupTestDB(t)
		defer cleanup()
		_ = testStore
	}

	cfg := config.Load()
	seeder, err := NewSeeder(cfg)
	require.NoError(t, err)
	defer seeder.Close()

	ctx := context.Background()

	// Create existing super admin
	existingAdminParams := db.CreateStaffUserWithEmailPasswordParams{
		DisplayName:              "Existing Super Admin",
		Email:                    stringPtr("<EMAIL>"),
		EmailVerifiedAt:          timePtr(),
		PhoneOtpChannel:          "email",
		InterfaceLanguage:        "en",
		CommunicationLanguage:    "en",
		EnableAppNotifications:   true,
		EnableEmailNotifications: true,
	}
	existingAdmin, err := seeder.store.CreateStaffUserWithEmailPassword(ctx, existingAdminParams)
	require.NoError(t, err)

	// Make it super admin
	err = seeder.store.UpdateUserPlatformRole(ctx, db.UpdateUserPlatformRoleParams{
		ID:           existingAdmin.ID,
		PlatformRole: "super_admin",
	})
	require.NoError(t, err)

	// Create existing default organization
	existingOrgParams := db.CreateOrganizationParams{
		Name:         "Existing Default Org",
		Description:  stringPtr("Existing default organization"),
		OwnerUserID:  existingAdmin.ID,
		IsDefaultOrg: true,
		ThemeColor:   stringPtr("red"),
		Status:       "active",
	}
	existingOrg, err := seeder.store.CreateOrganization(ctx, existingOrgParams)
	require.NoError(t, err)

	// Add owner membership
	_, err = seeder.store.AddUserToOrganization(ctx, db.AddUserToOrganizationParams{
		UserID:               existingAdmin.ID,
		OrganizationID:       existingOrg.ID,
		Role:                 "owner",
		IsActive:             true,
		NotificationsEnabled: true,
	})
	require.NoError(t, err)

	// Prepare seed data with new system entities
	seedData := SeedData{
		Users: []UserSeed{
			{
				Email:        "<EMAIL>",
				DisplayName:  "New Super Admin",
				RawPassword:  "newpass",
				IsSuperAdmin: true,
			},
		},
		Organizations: []OrganizationSeed{
			{
				Name:        "New Default Org",
				Description: "New default organization",
				OwnerEmail:  "<EMAIL>",
				IsDefault:   true,
				ThemeColor:  "blue",
			},
		},
	}

	// Create test data file
	tempFile := filepath.Join(t.TempDir(), "test_import.json")
	data, err := json.Marshal(seedData)
	require.NoError(t, err)
	err = os.WriteFile(tempFile, data, 0644)
	require.NoError(t, err)

	t.Run("Import fails when default org already exists", func(t *testing.T) {
		err = seeder.ImportData(tempFile, ImportOptions{
			SkipExisting: false,
		})
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "cannot import default organization")
	})

	t.Run("Import succeeds with SkipExisting", func(t *testing.T) {
		err = seeder.ImportData(tempFile, ImportOptions{
			SkipExisting: true,
		})
		require.NoError(t, err)

		// Verify existing entities were preserved
		defaultOrg, err := seeder.store.GetDefaultOrganization(ctx)
		require.NoError(t, err)
		assert.Equal(t, "Existing Default Org", defaultOrg.Name)
	})

	t.Run("Import succeeds with SkipSystemEntities", func(t *testing.T) {
		// Add a regular user to the seed data
		seedDataWithRegular := SeedData{
			Users: append(seedData.Users, UserSeed{
				Email:        "<EMAIL>",
				DisplayName:  "Regular User",
				RawPassword:  "pass123",
				PlatformRole: "staff",
			}),
			Organizations: seedData.Organizations,
		}

		tempFile2 := filepath.Join(t.TempDir(), "test_import2.json")
		data2, err := json.Marshal(seedDataWithRegular)
		require.NoError(t, err)
		err = os.WriteFile(tempFile2, data2, 0644)
		require.NoError(t, err)

		err = seeder.ImportData(tempFile2, ImportOptions{
			SkipSystemEntities: true,
		})
		require.NoError(t, err)

		// Regular user should be imported
		regularEmail := "<EMAIL>"
		regularUser, err := seeder.store.GetUserByEmail(ctx, &regularEmail)
		require.NoError(t, err)
		assert.Equal(t, "Regular User", regularUser.DisplayName)

		// System entities should still be the existing ones
		defaultOrg, err := seeder.store.GetDefaultOrganization(ctx)
		require.NoError(t, err)
		assert.Equal(t, "Existing Default Org", defaultOrg.Name)
	})
}

// Helper function for tests
func timePtr() *time.Time {
	t := time.Now()
	return &t
}
