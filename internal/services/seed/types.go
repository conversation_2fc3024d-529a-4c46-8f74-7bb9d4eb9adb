package seed

// SeedData represents the structure of seed data
type SeedData struct {
	Users         []UserSeed         `json:"users"`
	Organizations []OrganizationSeed `json:"organizations"`
	Memberships   []MembershipSeed   `json:"memberships"`
}

// UserSeed represents a user to be seeded
type UserSeed struct {
	Email          string `json:"email,omitempty"`
	DisplayName    string `json:"displayName"`
	RawPassword    string `json:"rawPassword,omitempty"`
	Phone          string `json:"phone,omitempty"`
	PlatformRole   string `json:"platformRole,omitempty"` // "user", "staff", "admin", "super_admin"
	IsSuperAdmin   bool   `json:"isSuperAdmin,omitempty"` // Deprecated, use PlatformRole
	HashedPassword string `json:"-"` // Internal use only
}

// OrganizationSeed represents an organization to be seeded
type OrganizationSeed struct {
	Name           string `json:"name"`
	Description    string `json:"description"`
	OwnerEmail     string `json:"ownerEmail"`
	ThemeColor     string `json:"themeColor"`
	ImageAssetPath string `json:"imageAssetPath"`
	IsDefault      bool   `json:"isDefault,omitempty"`
}

// MembershipSeed represents a membership to be seeded
type MembershipSeed struct {
	UserPhone        string `json:"userPhone,omitempty"`
	UserEmail        string `json:"userEmail,omitempty"`
	OrganizationName string `json:"organizationName"`
	Role             string `json:"role"`
}

// ExportOptions controls what data to export
type ExportOptions struct {
	IncludeOrganizations  bool
	IncludeUsers          bool
	IncludeMemberships    bool
	IncludeSystemEntities bool // Include super admin users and default organizations
}

// ImportOptions controls how data is imported
type ImportOptions struct {
	SkipExisting       bool // Skip records that already exist
	SkipSystemEntities bool // Skip system entities (superadmin, default org)
	OverrideFromEnv    bool // Override seed data values from environment variables
}
