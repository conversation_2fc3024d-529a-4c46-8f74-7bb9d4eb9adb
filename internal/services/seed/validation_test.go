package seed

import (
	"testing"
)

func TestValidateSeedData(t *testing.T) {
	tests := []struct {
		name      string
		data      *SeedData
		wantError bool
		errorMsg  string
	}{
		{
			name: "Valid data with one default organization",
			data: &SeedData{
				Organizations: []OrganizationSeed{
					{
						Name:       "Regular Org",
						IsDefault:  false,
						OwnerEmail: "<EMAIL>",
					},
					{
						Name:       "Default Org",
						IsDefault:  true,
						OwnerEmail: "<EMAIL>",
					},
				},
			},
			wantError: false,
		},
		{
			name: "Valid data with no default organization",
			data: &SeedData{
				Organizations: []OrganizationSeed{
					{
						Name:       "Regular Org 1",
						IsDefault:  false,
						OwnerEmail: "<EMAIL>",
					},
					{
						Name:       "Regular Org 2",
						IsDefault:  false,
						OwnerEmail: "<EMAIL>",
					},
				},
			},
			wantError: false,
		},
		{
			name: "Invalid data with multiple default organizations",
			data: &SeedData{
				Organizations: []OrganizationSeed{
					{
						Name:       "Default Org 1",
						IsDefault:  true,
						OwnerEmail: "<EMAIL>",
					},
					{
						Name:       "Default Org 2",
						IsDefault:  true,
						OwnerEmail: "<EMAIL>",
					},
				},
			},
			wantError: true,
			errorMsg:  "seed data contains 2 default organizations ([Default Org 1 Default Org 2]), but only one is allowed",
		},
		{
			name: "Invalid data with default organization missing owner email",
			data: &SeedData{
				Organizations: []OrganizationSeed{
					{
						Name:       "Default Org",
						IsDefault:  true,
						OwnerEmail: "",
					},
				},
			},
			wantError: true,
			errorMsg:  "default organization 'Default Org' must have an owner email",
		},
		{
			name: "Empty data",
			data: &SeedData{
				Organizations: []OrganizationSeed{},
			},
			wantError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateSeedData(tt.data)

			if tt.wantError {
				if err == nil {
					t.Errorf("validateSeedData() expected error but got none")
					return
				}
				if err.Error() != tt.errorMsg {
					t.Errorf("validateSeedData() error = %v, want %v", err.Error(), tt.errorMsg)
				}
			} else {
				if err != nil {
					t.Errorf("validateSeedData() unexpected error = %v", err)
				}
			}
		})
	}
}

func TestIsSystemUser(t *testing.T) {
	tests := []struct {
		name     string
		userSeed UserSeed
		want     bool
	}{
		{
			name: "Super admin user is system user",
			userSeed: UserSeed{
				Email:        "<EMAIL>",
				DisplayName:  "Super Admin",
				IsSuperAdmin: true,
			},
			want: true,
		},
		{
			name: "Staff user is not system user",
			userSeed: UserSeed{
				Email:        "<EMAIL>",
				DisplayName:  "Staff User",
				PlatformRole: "staff",
			},
			want: false,
		},
		{
			name: "Regular user is not system user",
			userSeed: UserSeed{
				Phone:       "+1234567890",
				DisplayName: "Regular User",
			},
			want: false,
		},
		{
			name: "Staff user who is also super admin is system user",
			userSeed: UserSeed{
				Email:        "<EMAIL>",
				DisplayName:  "Admin Staff",
				PlatformRole: "super_admin",
				IsSuperAdmin: true,
			},
			want: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isSystemUser(tt.userSeed); got != tt.want {
				t.Errorf("isSystemUser() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestIsSystemOrganization(t *testing.T) {
	tests := []struct {
		name    string
		orgSeed OrganizationSeed
		want    bool
	}{
		{
			name: "Default organization is system organization",
			orgSeed: OrganizationSeed{
				Name:       "Default Org",
				IsDefault:  true,
				OwnerEmail: "<EMAIL>",
			},
			want: true,
		},
		{
			name: "Regular organization is not system organization",
			orgSeed: OrganizationSeed{
				Name:       "Regular Org",
				IsDefault:  false,
				OwnerEmail: "<EMAIL>",
			},
			want: false,
		},
		{
			name: "Organization with empty IsDefault is not system organization",
			orgSeed: OrganizationSeed{
				Name:       "Another Org",
				OwnerEmail: "<EMAIL>",
			},
			want: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isSystemOrganization(tt.orgSeed); got != tt.want {
				t.Errorf("isSystemOrganization() = %v, want %v", got, tt.want)
			}
		})
	}
}
