package services

import (
	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/services/user"
	"Membership-SAAS-System-Backend/internal/utils/validation"
)

// UserService wraps domain services for backward compatibility
type UserService struct {
	Store          db.Store
	ProfileService user.ProfileService
	PhoneService   user.PhoneService
	AdminService   user.AdminService
	MediaService   user.MediaService
}

// NewUserService creates a new user service
func NewUserService(
	store db.Store,
	validator *validation.RequestValidator,
) *UserService {
	// For now, return a minimal service
	// The actual implementations would need to be instantiated here
	return &UserService{
		Store: store,
	}
}