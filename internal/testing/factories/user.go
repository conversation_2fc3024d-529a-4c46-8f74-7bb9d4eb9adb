package factories

import (
	"fmt"
	"time"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/locales"

	"github.com/google/uuid"
)

// UserOption is a functional option for customizing mock users
type UserOption func(*db.User)

// WithUserID sets a specific ID for the user
func WithUserID(id uuid.UUID) UserOption {
	return func(u *db.User) {
		u.ID = id
	}
}

// WithDisplayName sets a specific display name
func WithDisplayName(name string) UserOption {
	return func(u *db.User) {
		u.DisplayName = name
	}
}

// WithEmail sets a specific email
func WithEmail(email string) UserOption {
	return func(u *db.User) {
		u.Email = &email
		// Set email as verified by default
		now := time.Now()
		u.EmailVerifiedAt = &now
	}
}

// WithPhone sets a specific phone number
func WithPhone(phone string) UserOption {
	return func(u *db.User) {
		u.Phone = &phone
		// Set phone as verified by default
		now := time.Now()
		u.PhoneVerifiedAt = &now
	}
}

// WithPlatformRole sets the platform role
func WithPlatformRole(role db.PlatformRoleEnum) UserOption {
	return func(u *db.User) {
		u.PlatformRole = role
	}
}

// WithProfilePicture sets the profile picture URL
func WithProfilePicture(url string) UserOption {
	return func(u *db.User) {
		u.ProfilePictureUrl = &url
	}
}

// WithPassword sets a hashed password
func WithPassword(hashedPassword string) UserOption {
	return func(u *db.User) {
		u.HashedPassword = &hashedPassword
	}
}

// WithLanguageSettings sets interface and communication languages
func WithLanguageSettings(interfaceLang, commLang string) UserOption {
	return func(u *db.User) {
		u.InterfaceLanguage = interfaceLang
		u.CommunicationLanguage = commLang
	}
}

// WithNotificationSettings sets notification preferences
func WithNotificationSettings(app, whatsapp, sms, email bool) UserOption {
	return func(u *db.User) {
		u.EnableAppNotifications = app
		u.EnableWhatsappNotifications = whatsapp
		u.EnableSmsNotifications = sms
		u.EnableEmailNotifications = email
	}
}

// WithUnverifiedPhone sets phone as unverified
func WithUnverifiedPhone(phone string) UserOption {
	return func(u *db.User) {
		u.Phone = &phone
		u.PhoneVerifiedAt = nil
	}
}

// WithUnverifiedEmail sets email as unverified
func WithUnverifiedEmail(email string) UserOption {
	return func(u *db.User) {
		u.Email = &email
		u.EmailVerifiedAt = nil
	}
}

// NewMockUser creates a new mock user with sensible defaults
//
// Example:
//
//	user := factories.NewMockUser()
//
// Example with options:
//
//	user := factories.NewMockUser(
//	  factories.WithDisplayName("John Doe"),
//	  factories.WithEmail("<EMAIL>"),
//	  factories.WithPlatformRole("staff"),
//	)
func NewMockUser(opts ...UserOption) *db.User {
	now := time.Now()
	phone := fmt.Sprintf("+852987%05d", time.Now().UnixNano()%100000)
	email := fmt.Sprintf("<EMAIL>", time.Now().UnixNano()%1000000)

	user := &db.User{
		ID:                          uuid.New(),
		DisplayName:                 "Test User",
		Phone:                       &phone,
		PhoneVerifiedAt:             &now,
		Email:                       &email,
		EmailVerifiedAt:             &now,
		PhoneOtpChannel:             "sms",
		InterfaceLanguage:           locales.LangZHHK,
		CommunicationLanguage:       locales.LangZHHK,
		EnableAppNotifications:      true,
		EnableWhatsappNotifications: false,
		EnableSmsNotifications:      true,
		EnableEmailNotifications:    true,
		PlatformRole:                "user",
		CreatedAt:                   now,
		UpdatedAt:                   now,
	}

	// Apply options
	for _, opt := range opts {
		opt(user)
	}

	return user
}

// NewMockUsers creates multiple mock users
//
// Example:
//
//	users := factories.NewMockUsers(5)
//
// Example with custom options for each:
//
//	users := factories.NewMockUsers(3, func(i int) []factories.UserOption {
//	  if i == 0 {
//	    return []factories.UserOption{factories.WithPlatformRole("super_admin")}
//	  }
//	  return nil
//	})
func NewMockUsers(count int, optsFn ...func(int) []UserOption) []*db.User {
	users := make([]*db.User, count)
	for i := 0; i < count; i++ {
		var opts []UserOption
		if len(optsFn) > 0 && optsFn[0] != nil {
			opts = optsFn[0](i)
		}
		users[i] = NewMockUser(opts...)
	}
	return users
}

// Preset user factories for common scenarios

// NewOwnerUser creates a user with typical owner settings
func NewOwnerUser() *db.User {
	return NewMockUser(
		WithDisplayName("Organization Owner"),
		WithPlatformRole("user"),
	)
}

// NewAdminUser creates a user with typical admin settings
func NewAdminUser() *db.User {
	return NewMockUser(
		WithDisplayName("Organization Admin"),
		WithPlatformRole("user"),
	)
}

// NewManagerUser creates a user with typical manager settings
func NewManagerUser() *db.User {
	return NewMockUser(
		WithDisplayName("Organization Manager"),
		WithPlatformRole("user"),
	)
}

// NewMemberUser creates a basic member user
func NewMemberUser() *db.User {
	return NewMockUser(
		WithDisplayName("Organization Member"),
		WithPlatformRole("user"),
	)
}

// NewSuperAdminUser creates a super admin user
func NewSuperAdminUser() *db.User {
	return NewMockUser(
		WithDisplayName("Super Admin"),
		WithEmail("<EMAIL>"),
		WithPlatformRole("super_admin"),
	)
}

// NewStaffUser creates a staff user
func NewStaffUser() *db.User {
	return NewMockUser(
		WithDisplayName("Staff User"),
		WithEmail("<EMAIL>"),
		WithPlatformRole("staff"),
	)
}

// NewUnverifiedUser creates a user with unverified contact details
func NewUnverifiedUser() *db.User {
	phone := fmt.Sprintf("+852987%05d", time.Now().UnixNano()%100000)
	email := fmt.Sprintf("<EMAIL>", time.Now().UnixNano()%1000000)

	return NewMockUser(
		WithDisplayName("Unverified User"),
		WithUnverifiedPhone(phone),
		WithUnverifiedEmail(email),
	)
}

// NewUserWithFullProfile creates a user with all optional fields populated
func NewUserWithFullProfile() *db.User {
	profilePicUrl := fmt.Sprintf("/uploads/profile-pictures/user-%s.jpg", uuid.New().String())
	hashedPassword := "$2a$10$YourHashedPasswordHere" // Example bcrypt hash

	return NewMockUser(
		WithDisplayName("Complete Profile User"),
		WithProfilePicture(profilePicUrl),
		WithPassword(hashedPassword),
		WithNotificationSettings(true, true, true, true),
	)
}
