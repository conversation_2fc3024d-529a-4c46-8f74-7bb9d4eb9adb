package infrastructure

import (
	"context"
	"fmt"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/modules/postgres"
	tcredis "github.com/testcontainers/testcontainers-go/modules/redis"
	"github.com/testcontainers/testcontainers-go/wait"
)

// GetRedisURL returns a Redis connection URL for testing.
// It first checks the TEST_REDIS_URL environment variable.
// If not set, it automatically provisions a Redis container using TestContainers.
func GetRedisURL(t *testing.T) string {
	t.Helper()

	// Check environment variable first
	if url := os.Getenv("TEST_REDIS_URL"); url != "" {
		// Verify the connection works
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		client := redis.NewClient(&redis.Options{
			Addr: url,
		})
		defer client.Close()

		if err := client.Ping(ctx).Err(); err != nil {
			t.Fatalf("Failed to connect to Redis at %s: %v", url, err)
		}

		t.Logf("Using existing Redis from TEST_REDIS_URL: %s", url)
		return url
	}

	// Fall back to TestContainers
	ctx := context.Background()

	redisContainer, err := tcredis.Run(ctx,
		"redis:7-alpine",
		tcredis.WithSnapshotting(10, 1),
		tcredis.WithLogLevel(tcredis.LogLevelWarning),
		testcontainers.WithWaitStrategy(
			wait.ForListeningPort("6379/tcp").
				WithStartupTimeout(30*time.Second),
		),
	)
	if err != nil {
		t.Fatalf("Failed to start Redis container: %v", err)
	}

	// Register cleanup
	t.Cleanup(func() {
		if err := redisContainer.Terminate(context.Background()); err != nil {
			// Check if already terminated
			errMsg := err.Error()
			if errMsg != "container not found" &&
				!strings.Contains(errMsg, "No such container") &&
				!strings.Contains(errMsg, "container is not running") {
				t.Logf("Warning: Failed to terminate Redis container: %v", err)
			}
		}
	})

	// Get connection details
	host, err := redisContainer.Host(ctx)
	if err != nil {
		t.Fatalf("Failed to get Redis host: %v", err)
	}

	port, err := redisContainer.MappedPort(ctx, "6379")
	if err != nil {
		t.Fatalf("Failed to get Redis port: %v", err)
	}

	redisURL := fmt.Sprintf("%s:%s", host, port.Port())

	// Verify connection
	client := redis.NewClient(&redis.Options{
		Addr:         redisURL,
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
	})
	defer client.Close()

	if err := client.Ping(ctx).Err(); err != nil {
		t.Fatalf("Failed to connect to TestContainers Redis: %v", err)
	}

	t.Logf("Started Redis TestContainer at: %s", redisURL)
	return redisURL
}

// GetPostgreSQLURL returns a PostgreSQL connection URL for testing.
// It first checks the TEST_POSTGRESQL_URL environment variable.
// If not set, it automatically provisions a PostgreSQL container using TestContainers.
func GetPostgreSQLURL(t *testing.T) string {
	t.Helper()

	// Check environment variable first
	if url := os.Getenv("TEST_POSTGRESQL_URL"); url != "" {
		t.Logf("Using existing PostgreSQL from TEST_POSTGRESQL_URL")
		return url
	}

	// Fall back to TestContainers
	ctx := context.Background()

	pgContainer, err := postgres.Run(ctx,
		"postgres:16-alpine",
		postgres.WithDatabase("testdb"),
		postgres.WithUsername("testuser"),
		postgres.WithPassword("testpass"),
		testcontainers.WithWaitStrategy(
			wait.ForLog("database system is ready to accept connections").
				WithOccurrence(2).
				WithStartupTimeout(30*time.Second),
		),
	)
	if err != nil {
		t.Fatalf("Failed to start PostgreSQL container: %v", err)
	}

	// Register cleanup
	t.Cleanup(func() {
		if err := pgContainer.Terminate(context.Background()); err != nil {
			// Check if already terminated
			errMsg := err.Error()
			if errMsg != "container not found" &&
				!strings.Contains(errMsg, "No such container") &&
				!strings.Contains(errMsg, "container is not running") {
				t.Logf("Warning: Failed to terminate PostgreSQL container: %v", err)
			}
		}
	})

	// Get connection URL
	dbURL, err := pgContainer.ConnectionString(ctx, "sslmode=disable")
	if err != nil {
		t.Fatalf("Failed to get PostgreSQL connection string: %v", err)
	}

	t.Logf("Started PostgreSQL TestContainer with URL: %s", dbURL)
	return dbURL
}
