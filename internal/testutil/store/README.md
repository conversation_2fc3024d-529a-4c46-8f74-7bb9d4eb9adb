# Store Test Utilities

This package provides centralized test utilities for the Store pattern architecture, consolidating functionality from the former `testutils` package.

## Migration Notice

This package contains utilities migrated from `internal/testutils`. If you're updating existing code:

**Old Import:**
```go
import "Membership-SAAS-System-Backend/internal/testutils"
```

**New Import:**
```go
import "Membership-SAAS-System-Backend/internal/testutil/store"
```

## Components

### MockStoreProvider

Provides mocked Store instances for unit testing:

```go
provider := store.NewMockStoreProvider(t)
service := user.NewProfileService(provider.Store)

// Set expectations
provider.MockQuerier.On("UpdateUser", mock.Anything, mock.Anything).
    Return(expectedUser, nil)
```

### Database Setup

Provides PostgreSQL containers for integration testing:

```go
// Setup test database
store, cleanup := store.SetupTestDB(t)
defer cleanup()

// Use in tests
service := user.NewProfileService(store)
```

## Testing Strategy

| Test Type | Function | Purpose | Speed |
|-----------|----------|---------|-------|
| Unit Tests | `NewMockStoreProvider` | Business logic validation | ⚡ Fast |
| Integration Tests | `SetupTestDB` | Database validation | 🐌 Slower |

For detailed usage, see the original documentation in the package.