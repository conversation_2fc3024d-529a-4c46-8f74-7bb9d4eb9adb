package store

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/db"

	"github.com/golang-migrate/migrate/v4"
	_ "github.com/golang-migrate/migrate/v4/database/postgres"
	_ "github.com/golang-migrate/migrate/v4/source/file"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/modules/postgres"
	"github.com/testcontainers/testcontainers-go/wait"
)

// SetupTestDB creates an isolated test database using testcontainers for integration tests.
// This function provides a PostgreSQL database with the full schema loaded,
// ready for integration testing.
//
// Usage:
//
//	store, cleanup := store.SetupTestDB(t)
//	defer cleanup()
//
//	service := user.NewProfileService(store)
//	// Run integration tests with real database operations
//
// The returned cleanup function terminates the container and closes connections.
// It's designed to be called with defer for automatic cleanup.
func SetupTestDB(t *testing.T) (db.Store, func()) {
	t.Helper()

	ctx := context.Background()

	// Start PostgreSQL container with testcontainers
	pgContainer, err := postgres.Run(ctx,
		"postgres:16-alpine",
		postgres.WithDatabase("testdb"),
		postgres.WithUsername("testuser"),
		postgres.WithPassword("testpass"),
		testcontainers.WithWaitStrategy(
			wait.ForLog("database system is ready to accept connections").
				WithOccurrence(2).
				WithStartupTimeout(60*time.Second),
		),
	)
	if err != nil {
		t.Fatalf("Failed to start PostgreSQL container: %v", err)
	}

	// Get connection string from container
	connStr, err := pgContainer.ConnectionString(ctx, "sslmode=disable")
	if err != nil {
		pgContainer.Terminate(ctx)
		t.Fatalf("Failed to get connection string: %v", err)
	}

	// Create connection pool
	pool, err := pgxpool.New(ctx, connStr)
	if err != nil {
		pgContainer.Terminate(ctx)
		t.Fatalf("Failed to create connection pool: %v", err)
	}

	// Load database schema from migrations
	if err := loadSchema(connStr); err != nil {
		pool.Close()
		pgContainer.Terminate(ctx)
		t.Fatalf("Failed to load schema: %v", err)
	}

	// Create Store instance
	store := db.NewStore(pool)

	// Return cleanup function
	cleanup := func() {
		pool.Close()
		if err := pgContainer.Terminate(context.Background()); err != nil {
			// Check if container is already terminated - this is expected behavior
			errMsg := err.Error()
			if strings.Contains(errMsg, "container not found") ||
				strings.Contains(errMsg, "No such container") ||
				strings.Contains(errMsg, "container is not running") {
				// Container already terminated, no action needed
				return
			}
			t.Logf("Warning: Failed to terminate PostgreSQL container: %v", err)
		}
	}

	return store, cleanup
}

// getMigrationPath resolves the correct path to the migrations directory
func getMigrationPathTestutils() string {
	// Get the directory of the current file (database.go)
	_, filename, _, _ := runtime.Caller(0)
	dir := filepath.Dir(filename)

	// Go up to the project root and find db/migrations
	projectRoot := filepath.Join(dir, "..", "..", "..")
	migrationDir := filepath.Join(projectRoot, "db", "migrations")

	// Convert to file:// URL
	absPath, _ := filepath.Abs(migrationDir)
	return "file://" + absPath
}

// loadSchema loads the database schema from migration files
// This ensures the test database has the same structure as production
func loadSchema(databaseURL string) error {
	migrationPath := getMigrationPathTestutils()

	// Verify migration directory exists
	localPath := migrationPath[7:] // Remove "file://" prefix
	if _, err := os.Stat(localPath); os.IsNotExist(err) {
		return fmt.Errorf("migration directory does not exist: %s", localPath)
	}

	m, err := migrate.New(migrationPath, databaseURL)
	if err != nil {
		return fmt.Errorf("failed to create migrate instance: %w", err)
	}
	defer m.Close()

	if err := m.Up(); err != nil && err != migrate.ErrNoChange {
		return fmt.Errorf("failed to run migrations: %w", err)
	}

	return nil
}

// SetupSharedTestDB creates a shared database container for performance optimization.
// This is useful when running multiple test packages that need database access,
// as it reuses the same container and provides transaction-based isolation.
//
// Usage:
//
//	store, cleanup := store.SetupSharedTestDB(t)
//	defer cleanup() // This only closes the connection, not the container
//
//	// Use transaction-based isolation for test independence
//	err := store.ExecTx(ctx, func(q db.Querier) error {
//	    // Your test operations here
//	    return nil
//	})
//
// Note: This is an advanced pattern for performance optimization.
// Use SetupTestDB for most integration tests unless you specifically need
// container reuse across test packages.
func SetupSharedTestDB(t *testing.T) (db.Store, func()) {
	t.Helper()

	// This is a simplified version that creates a fresh container per test
	// In a production implementation, you would implement container sharing
	// using sync.Once or similar patterns, but that requires more complex
	// lifecycle management
	return SetupTestDB(t)
}

// TestDatabaseConfig provides configuration options for test database setup
type TestDatabaseConfig struct {
	// PostgresVersion specifies the PostgreSQL Docker image version
	PostgresVersion string

	// Database name for the test database
	Database string

	// Username for database connection
	Username string

	// Password for database connection
	Password string

	// MigrationPath specifies the path to migration files
	MigrationPath string

	// StartupTimeout specifies how long to wait for container startup
	StartupTimeout time.Duration
}

// DefaultTestDatabaseConfig returns the default configuration for test databases
func DefaultTestDatabaseConfig() *TestDatabaseConfig {
	return &TestDatabaseConfig{
		PostgresVersion: "postgres:16-alpine",
		Database:        "testdb",
		Username:        "testuser",
		Password:        "testpass",
		MigrationPath:   getMigrationPathTestutils(),
		StartupTimeout:  60 * time.Second,
	}
}

// SetupTestDBWithConfig creates a test database with custom configuration.
// This provides more control over the database setup for specific test requirements.
//
// Usage:
//
//	config := store.DefaultTestDatabaseConfig()
//	config.PostgresVersion = "postgres:15-alpine"
//	store, cleanup := store.SetupTestDBWithConfig(t, config)
//	defer cleanup()
func SetupTestDBWithConfig(t *testing.T, config *TestDatabaseConfig) (db.Store, func()) {
	t.Helper()

	ctx := context.Background()

	// Start PostgreSQL container with custom configuration
	pgContainer, err := postgres.Run(ctx,
		config.PostgresVersion,
		postgres.WithDatabase(config.Database),
		postgres.WithUsername(config.Username),
		postgres.WithPassword(config.Password),
		testcontainers.WithWaitStrategy(
			wait.ForLog("database system is ready to accept connections").
				WithOccurrence(2).
				WithStartupTimeout(config.StartupTimeout),
		),
	)
	if err != nil {
		t.Fatalf("Failed to start PostgreSQL container: %v", err)
	}

	// Get connection string from container
	connStr, err := pgContainer.ConnectionString(ctx, "sslmode=disable")
	if err != nil {
		pgContainer.Terminate(ctx)
		t.Fatalf("Failed to get connection string: %v", err)
	}

	// Create connection pool
	pool, err := pgxpool.New(ctx, connStr)
	if err != nil {
		pgContainer.Terminate(ctx)
		t.Fatalf("Failed to create connection pool: %v", err)
	}

	// Load database schema from migrations
	if err := loadSchemaWithPath(connStr, config.MigrationPath); err != nil {
		pool.Close()
		pgContainer.Terminate(ctx)
		t.Fatalf("Failed to load schema: %v", err)
	}

	// Create Store instance
	store := db.NewStore(pool)

	// Return cleanup function
	cleanup := func() {
		pool.Close()
		if err := pgContainer.Terminate(context.Background()); err != nil {
			// Check if container is already terminated - this is expected behavior
			errMsg := err.Error()
			if strings.Contains(errMsg, "container not found") ||
				strings.Contains(errMsg, "No such container") ||
				strings.Contains(errMsg, "container is not running") {
				// Container already terminated, no action needed
				return
			}
			t.Logf("Warning: Failed to terminate PostgreSQL container: %v", err)
		}
	}

	return store, cleanup
}

// loadSchemaWithPath loads the database schema from migration files at a specific path
func loadSchemaWithPath(databaseURL, migrationPath string) error {
	m, err := migrate.New(migrationPath, databaseURL)
	if err != nil {
		return fmt.Errorf("failed to create migrate instance: %w", err)
	}
	defer m.Close()

	if err := m.Up(); err != nil && err != migrate.ErrNoChange {
		return fmt.Errorf("failed to run migrations: %w", err)
	}

	return nil
}

// SetupTestDatabase is a wrapper that matches the test expectations
// It delegates to SetupTestDB which returns db.Store
func SetupTestDatabase(t *testing.T) (db.Store, func()) {
	return SetupTestDB(t)
}

// SetupTestDatabaseWithConfig is a wrapper that matches the test expectations
// It delegates to SetupTestDBWithConfig which returns db.Store
func SetupTestDatabaseWithConfig(t *testing.T, config *TestDatabaseConfig) (db.Store, func()) {
	return SetupTestDBWithConfig(t, config)
}
