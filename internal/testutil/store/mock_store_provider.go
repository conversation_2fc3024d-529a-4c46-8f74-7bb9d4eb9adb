package store

import (
	"Membership-SAAS-System-Backend/db"
	dbmocks "Membership-SAAS-System-Backend/internal/mocks/db"

	"github.com/stretchr/testify/mock"
)

// MockStoreProvider provides a mock implementation of store interfaces for testing
type MockStoreProvider struct {
	Store     db.Store
	MockStore *dbmocks.MockStore
}

// NewMockStoreProvider creates a new MockStoreProvider with all the necessary mocks
func NewMockStoreProvider(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockStoreProvider {
	mockStore := dbmocks.NewMockStore(t)

	return &MockStoreProvider{
		Store:     mockStore,
		MockStore: mockStore,
	}
}

// NewMockTransactionalStoreProvider creates a mock store provider with transactional capabilities
func NewMockTransactionalStoreProvider(t interface {
	mock.TestingT
	Cleanup(func())
}) *MockStoreProvider {
	// For now, we'll use the same implementation as NewMockStoreProvider
	// In the future, this could return a different mock with transaction-specific behavior
	return NewMockStoreProvider(t)
}