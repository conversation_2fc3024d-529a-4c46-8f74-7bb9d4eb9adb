package store

import (
	"context"
	"testing"

	"Membership-SAAS-System-Backend/db"
	dbmocks "Membership-SAAS-System-Backend/internal/mocks/db"

	"github.com/stretchr/testify/mock"
)

// TransactionTestHelper helps set up transaction-based tests
type TransactionTestHelper struct {
	t         *testing.T
	mockStore *dbmocks.MockStore
}

// NewTransactionTestHelper creates a new helper for transaction testing
func NewTransactionTestHelper(t *testing.T, mockStore *dbmocks.MockStore) *TransactionTestHelper {
	return &TransactionTestHelper{
		t:         t,
		mockStore: mockStore,
	}
}

// SetupTransactionTest sets up the ExecTx expectation to execute the callback
// This should be called at the beginning of any test that uses transactions
func (h *TransactionTestHelper) SetupTransactionTest() {
	h.mockStore.On("ExecTx", mock.Anything, mock.AnythingOfType("func(db.Querier) error")).
		Return(func(ctx context.Context, fn func(db.Querier) error) error {
			// Execute the callback function with the mock store as the Querier
			// and return any error from the callback
			if fn != nil {
				return fn(h.mockStore)
			}
			return nil
		}).
		Maybe() // Use Maybe() so it can be called multiple times
}

// SetupTransactionTestWithError sets up ExecTx to return an error
func (h *TransactionTestHelper) SetupTransactionTestWithError(err error) {
	h.mockStore.On("ExecTx", mock.Anything, mock.AnythingOfType("func(db.Querier) error")).
		Return(err).
		Once()
}

// SetupTransactionTestWithCallback allows custom handling of the transaction
func (h *TransactionTestHelper) SetupTransactionTestWithCallback(handler func(context.Context, func(db.Querier) error) error) {
	h.mockStore.On("ExecTx", mock.Anything, mock.AnythingOfType("func(db.Querier) error")).
		Run(func(args mock.Arguments) {
			ctx := args.Get(0).(context.Context)
			fn := args.Get(1).(func(db.Querier) error)
			_ = handler(ctx, fn)
		}).
		Return(nil).
		Maybe()
}
