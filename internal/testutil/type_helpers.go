package testutil

import (
	"fmt"
	"math"

	"Membership-SAAS-System-Backend/db"

	"github.com/google/uuid"
)

// Note: ToPtr and FromPtr are available in helpers.go to avoid duplication

// Pagination conversion helpers
type PaginationParams struct {
	Page     int
	PageSize int
}

func (p PaginationParams) ToLimitOffset() (limit, offset int32) {
	limit = int32(p.PageSize)
	offset = int32((p.Page - 1) * p.PageSize)
	return
}

func (p PaginationParams) ToLimitOffsetInt() (limit, offset int) {
	return p.PageSize, (p.Page - 1) * p.PageSize
}

// Status conversion helpers
type StatusConverter struct{}

func (StatusConverter) EventStatus(status string) db.EventStatusType {
	switch status {
	case "draft":
		return db.EventStatusTypeDraft
	case "published":
		return db.EventStatusTypePublished
	case "cancelled":
		return db.EventStatusTypeCancelled
	case "archived":
		return db.EventStatusTypeArchived
	case "deleted":
		return db.EventStatusTypeDeleted
	case "hidden":
		return db.EventStatusTypeHidden
	default:
		return db.EventStatusTypeDraft
	}
}

func (StatusConverter) ApplicationStatus(status string) db.ApplicationStatusEnum {
	switch status {
	case "pending", "pending_review":
		return db.ApplicationStatusEnumPending
	case "approved":
		return db.ApplicationStatusEnumApproved
	case "rejected":
		return db.ApplicationStatusEnumRejected
	case "withdrawn":
		return db.ApplicationStatusEnumWithdrawn
	default:
		return db.ApplicationStatusEnumPending
	}
}

func (StatusConverter) OrganizationStatus(status string) string {
	switch status {
	case "active", "pending_setup", "suspended":
		return status
	default:
		return "active"
	}
}

func (StatusConverter) EventRegistrationStatus(status string) db.EventRegistrationStatusType {
	switch status {
	case "registered":
		return db.EventRegistrationStatusTypeRegistered
	case "waitlisted":
		return db.EventRegistrationStatusTypeWaitlisted
	case "cancelled_by_user":
		return db.EventRegistrationStatusTypeCancelledByUser
	case "pending_approval":
		return db.EventRegistrationStatusTypePendingApproval
	case "rejected_approval":
		return db.EventRegistrationStatusTypeRejectedApproval
	case "attended":
		return db.EventRegistrationStatusTypeAttended
	case "absent":
		return db.EventRegistrationStatusTypeAbsent
	default:
		return db.EventRegistrationStatusTypeRegistered
	}
}

// Interface assertion helpers
func AssertInterface[T any](v interface{}) T {
	result, ok := v.(T)
	if !ok {
		panic(fmt.Sprintf("interface assertion failed: expected %T, got %T", *new(T), v))
	}
	return result
}

// Safe type conversion with validation
func SafeInt32(v int) (int32, error) {
	if v > math.MaxInt32 || v < math.MinInt32 {
		return 0, fmt.Errorf("value %d overflows int32", v)
	}
	return int32(v), nil
}

func SafeInt(v int32) int {
	return int(v)
}

// Batch conversion helpers
func IntSliceToInt32Slice(ints []int) []int32 {
	result := make([]int32, len(ints))
	for i, v := range ints {
		result[i] = int32(v)
	}
	return result
}

func Int32SliceToIntSlice(ints []int32) []int {
	result := make([]int, len(ints))
	for i, v := range ints {
		result[i] = int(v)
	}
	return result
}

// UUID helpers
func MustParseUUID(str string) uuid.UUID {
	id, err := uuid.Parse(str)
	if err != nil {
		panic(fmt.Sprintf("failed to parse UUID %s: %v", str, err))
	}
	return id
}

func UUIDOrNil(str string) *uuid.UUID {
	if str == "" {
		return nil
	}
	id, err := uuid.Parse(str)
	if err != nil {
		return nil
	}
	return &id
}

// JSON helpers for test data
func JSONBytes(content interface{}) []byte {
	switch v := content.(type) {
	case string:
		return []byte(v)
	case []byte:
		return v
	default:
		// For complex objects, return a default empty JSON
		return []byte(`{"type": "doc", "content": []}`)
	}
}

// Default test values
var (
	DefaultTestOrganizationID = uuid.MustParse("00000000-0000-0000-0000-000000000001")
	DefaultTestUserID         = uuid.MustParse("00000000-0000-0000-0000-000000000002")
	DefaultTestEventID        = uuid.MustParse("00000000-0000-0000-0000-000000000003")
	DefaultTestAdminUserID    = uuid.MustParse("00000000-0000-0000-0000-000000000004")
)

// Type conversion for common test scenarios
func ConvertToDbEventParams(event db.Event) db.CreateEventParams {
	return db.CreateEventParams{
		OrganizationID:                  event.OrganizationID,
		Title:                           event.Title,
		DescriptionContent:              event.DescriptionContent,
		LocationType:                    event.LocationType,
		LocationFullAddress:             event.LocationFullAddress,
		LocationOnlineUrl:               event.LocationOnlineUrl,
		StartTime:                       event.StartTime,
		EndTime:                         event.EndTime,
		Status:                          event.Status,
		ParticipantLimit:                event.ParticipantLimit,
		WaitlistLimit:                   event.WaitlistLimit,
		RequiresApprovalForRegistration: event.RequiresApprovalForRegistration,
		CreatedByUserID:                 event.CreatedByUserID,
		Price:                           event.Price,
		ContactEmail:                    event.ContactEmail,
		ContactPhone:                    event.ContactPhone,
		GovernmentFundingKeys:           event.GovernmentFundingKeys,
		PublishedAt:                     event.PublishedAt,
	}
}

func ConvertToDbUserWithPhoneParams(user db.User) db.CreateUserWithPhoneParams {
	// Note: This conversion may need adjustment based on actual field mappings
	return db.CreateUserWithPhoneParams{
		DisplayName:                 user.DisplayName,
		Phone:                       user.Phone,
		PhoneVerifiedAt:             user.PhoneVerifiedAt,
		InterfaceLanguage:           user.InterfaceLanguage,
		CommunicationLanguage:       user.CommunicationLanguage,
		EnableAppNotifications:      user.EnableAppNotifications,
		EnableWhatsappNotifications: user.EnableWhatsappNotifications,
		EnableSmsNotifications:      user.EnableSmsNotifications,
		EnableEmailNotifications:    user.EnableEmailNotifications,
	}
}

func ConvertToDbOrganizationParams(org db.Organization) db.CreateOrganizationParams {
	return db.CreateOrganizationParams{
		Name:         org.Name,
		Description:  org.Description,
		OwnerUserID:  org.OwnerUserID,
		IsDefaultOrg: org.IsDefaultOrg,
		ImageUrl:     org.ImageUrl,
		ThemeColor:   org.ThemeColor,
		Status:       org.Status,
	}
}

// Error conversion for consistent test assertions
type TestError struct {
	Code    string
	Message string
	Err     error
}

func (e TestError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("%s: %s (%v)", e.Code, e.Message, e.Err)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

func NewTestError(code, message string, err error) *TestError {
	return &TestError{
		Code:    code,
		Message: message,
		Err:     err,
	}
}

// Common test error types
var (
	ErrTestInvalidInput     = NewTestError("INVALID_INPUT", "invalid input provided", nil)
	ErrTestDatabaseError    = NewTestError("DATABASE_ERROR", "database operation failed", nil)
	ErrTestValidationFailed = NewTestError("VALIDATION_FAILED", "validation failed", nil)
	ErrTestPermissionDenied = NewTestError("PERMISSION_DENIED", "permission denied", nil)
	ErrTestNotFound         = NewTestError("NOT_FOUND", "resource not found", nil)
)
