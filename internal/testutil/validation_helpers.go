package testutil

import (
	"encoding/json"
	"fmt"
	"testing"

	"Membership-SAAS-System-Backend/db"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// AssertContainsError asserts that an error contains a specific message
func AssertContainsError(t *testing.T, err error, expectedMessage string, msgAndArgs ...interface{}) {
	require.Error(t, err, msgAndArgs...)
	assert.Contains(t, err.Error(), expectedMessage, msgAndArgs...)
}

// AssertNoError asserts no error and provides helpful context
func AssertNoError(t *testing.T, err error, operation string, msgAndArgs ...interface{}) {
	if err != nil {
		args := append([]interface{}{"Operation '%s' failed: %v"}, operation, err)
		args = append(args, msgAndArgs...)
		assert.Fail(t, "Unexpected error", args...)
	}
}

// AssertServiceResponse asserts service layer response structure
func AssertServiceResponse(t *testing.T, data interface{}, err error, expectError bool, expectedErrorMsg string) {
	if expectError {
		require.Error(t, err)
		if expectedErrorMsg != "" {
			assert.Contains(t, err.Error(), expectedErrorMsg)
		}
	} else {
		require.NoError(t, err)
		require.NotNil(t, data)
	}
}

// AssertMapContainsKey asserts that a map contains a specific key
func AssertMapContainsKey[K comparable, V any](t *testing.T, m map[K]V, key K, msgAndArgs ...interface{}) {
	_, exists := m[key]
	assert.True(t, exists, "Map should contain key %v", key)
}

// AssertDatabaseState asserts the state of entities in the database
func AssertDatabaseState(t *testing.T, queries *db.Queries, checks ...func(*testing.T, *db.Queries)) {
	for _, check := range checks {
		check(t, queries)
	}
}

// TestCase provides a helper for table-driven tests
type TestCase struct {
	Name     string
	Setup    func(*testing.T)
	Run      func(*testing.T)
	Teardown func(*testing.T)
}

// RunTableDrivenTests runs table-driven tests with setup and teardown
func RunTableDrivenTests(t *testing.T, testCases []TestCase) {
	for _, tc := range testCases {
		t.Run(tc.Name, func(t *testing.T) {
			if tc.Setup != nil {
				tc.Setup(t)
			}

			if tc.Teardown != nil {
				defer tc.Teardown(t)
			}

			tc.Run(t)
		})
	}
}

// FromPtr safely dereferences a pointer with fallback value
func FromPtr[T any](ptr *T, fallback T) T {
	if ptr == nil {
		return fallback
	}
	return *ptr
}

// CreateTestPagination creates test pagination parameters
func CreateTestPagination(page, limit int) map[string]string {
	return map[string]string{
		"page":  fmt.Sprintf("%d", page),
		"limit": fmt.Sprintf("%d", limit),
	}
}

// ValidateJSONStructure validates that a JSON string has expected structure
func ValidateJSONStructure(t *testing.T, jsonStr string, requiredFields []string) {
	var data map[string]interface{}
	err := json.Unmarshal([]byte(jsonStr), &data)
	require.NoError(t, err, "Failed to unmarshal JSON")

	for _, field := range requiredFields {
		assert.Contains(t, data, field, "JSON should contain required field: %s", field)
	}
}
