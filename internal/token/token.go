package token

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"errors"
	"fmt"
	"os"
	"sync"
	"time"

	"Membership-SAAS-System-Backend/db" // CORRECTED Path to your sqlc generated db package

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log" // Added for logging
)

// AllowedSigningMethods defines the allowed JWT signing algorithms
// Only HMAC algorithms are allowed to prevent algorithm confusion attacks
var AllowedSigningMethods = map[string]bool{
	"HS256": true,
	"HS384": true,
	"HS512": true,
}

// Maker is the interface for managing tokens
type Maker interface {
	CreateToken(userID uuid.UUID, duration time.Duration) (string, error)
	VerifyToken(token string) (*AppClaims, error)
}

// JWTMaker is a JWT maker
type JWTMaker struct {
	secretKey string
}

// NewJWTMaker creates a new JWTMaker
func NewJWTMaker(secretKey string) (Maker, error) {
	if len(secretKey) < 32 {
		return nil, fmt.Errorf("invalid key size: must be at least 32 characters")
	}
	return &JWTMaker{secretKey}, nil
}

// CreateToken creates a new token for a specific user and duration
func (maker *JWTMaker) CreateToken(userID uuid.UUID, duration time.Duration) (string, error) {
	claims := &AppClaims{
		UserID:       userID,
		PlatformRole: "user", // Default to user role for basic token creation
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(duration)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "MembershipSaas",
			Subject:   userID.String(),
		},
	}

	jwtToken := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return jwtToken.SignedString([]byte(maker.secretKey))
}

// VerifyToken checks if the token is valid or not
func (maker *JWTMaker) VerifyToken(tokenString string) (*AppClaims, error) {
	keyFunc := func(token *jwt.Token) (interface{}, error) {
		// Explicitly reject the "none" algorithm
		if token.Method == jwt.SigningMethodNone {
			log.Error().Str("algorithm", "none").Msg("Rejected JWT with 'none' algorithm - potential security attack")
			return nil, fmt.Errorf("JWT algorithm 'none' is not allowed")
		}

		// Check if the algorithm is in our allowed list
		alg := token.Method.Alg()
		if !AllowedSigningMethods[alg] {
			log.Error().Str("algorithm", alg).Msg("Rejected JWT with disallowed algorithm")
			return nil, fmt.Errorf("JWT algorithm '%s' is not allowed. Only HMAC algorithms (HS256, HS384, HS512) are permitted", alg)
		}

		// Verify it's specifically an HMAC signing method
		_, ok := token.Method.(*jwt.SigningMethodHMAC)
		if !ok {
			log.Error().Str("algorithm", alg).Msg("JWT algorithm is not HMAC despite being in allowed list")
			return nil, fmt.Errorf("unexpected signing method: %v. Only HMAC algorithms are allowed", token.Header["alg"])
		}
		return []byte(maker.secretKey), nil
	}

	jwtToken, err := jwt.ParseWithClaims(tokenString, &AppClaims{}, keyFunc)
	if err != nil {
		return nil, err
	}

	claims, ok := jwtToken.Claims.(*AppClaims)
	if !ok {
		return nil, fmt.Errorf("invalid token claims")
	}

	return claims, nil
}

// AppClaims defines the custom claims for our JWTs.
type AppClaims struct {
	UserID       uuid.UUID `json:"user_id"`
	PlatformRole string    `json:"platform_role"`
	jwt.RegisteredClaims
}

// OrgContext represents organization context in JWT
type OrgContext struct {
	ID   string `json:"id"`
	Role string `json:"role"`
}

// EnhancedClaims represents the new JWT structure with platform role and organization context
type EnhancedClaims struct {
	UserID       uuid.UUID         `json:"user_id"`
	PlatformRole string            `json:"platform_role"`
	OrgRoles     map[string]string `json:"org_roles,omitempty"`  // Organization ID -> Role mapping
	ActiveOrg    *OrgContext       `json:"active_org,omitempty"` // Current active organization context
	jwt.RegisteredClaims
}

// Alias Claims to EnhancedClaims for easier migration
type Claims = EnhancedClaims

type JWTConfig struct {
	AccessTokenSecret      []byte
	RefreshTokenSecret     []byte
	AccessTokenDuration    time.Duration
	RefreshTokenDuration   time.Duration
	RefreshSessionDuration time.Duration
}

var (
	jwtConfig      *JWTConfig
	jwtConfigError error
	once           sync.Once
)

// LoadJWTConfig loads configuration from environment variables once.
// It stores any error encountered during loading, to be returned by GetJWTConfig.
func LoadJWTConfig() {
	// Reset for testing - this allows tests to override config
	jwtConfig = nil
	jwtConfigError = nil
	once = sync.Once{}

	once.Do(func() {
		accessSecret := os.Getenv("ACCESS_TOKEN_SECRET")
		refreshSecret := os.Getenv("REFRESH_TOKEN_SECRET")

		if accessSecret == "" {
			jwtConfigError = errors.New("access token secret environment variable not set")
			return
		}
		if refreshSecret == "" {
			jwtConfigError = errors.New("refresh token secret environment variable not set")
			return
		}

		accessDurationStr := os.Getenv("ACCESS_TOKEN_DURATION")
		if accessDurationStr == "" {
			accessDurationStr = "15m" // Default duration
		}
		accessDur, err := time.ParseDuration(accessDurationStr)
		if err != nil {
			jwtConfigError = fmt.Errorf("failed to parse ACCESS_TOKEN_DURATION: %w", err)
			return
		}

		refreshDurationStr := os.Getenv("REFRESH_TOKEN_DURATION")
		if refreshDurationStr == "" {
			refreshDurationStr = "168h" // Default duration (7 days = 168 hours)
		}
		refreshDur, err := time.ParseDuration(refreshDurationStr)
		if err != nil {
			jwtConfigError = fmt.Errorf("failed to parse REFRESH_TOKEN_DURATION: %w", err)
			return
		}

		refreshSessionDurationStr := os.Getenv("REFRESH_SESSION_DURATION")
		if refreshSessionDurationStr == "" {
			refreshSessionDurationStr = "2160h" // Default session duration (90 days * 24 hours)
		}
		refreshSessDur, err := time.ParseDuration(refreshSessionDurationStr)
		if err != nil {
			jwtConfigError = fmt.Errorf("failed to parse REFRESH_SESSION_DURATION: %w", err)
			return
		}

		jwtConfig = &JWTConfig{
			AccessTokenSecret:      []byte(accessSecret),
			RefreshTokenSecret:     []byte(refreshSecret),
			AccessTokenDuration:    accessDur,
			RefreshTokenDuration:   refreshDur,
			RefreshSessionDuration: refreshSessDur,
		}
	})
}

// GetJWTConfig returns the loaded JWT configuration or an error if loading failed or not loaded.
func GetJWTConfig() (*JWTConfig, error) {
	// Call LoadJWTConfig to ensure it has been attempted.
	// once.Do inside LoadJWTConfig handles multiple calls safely.
	LoadJWTConfig()

	if jwtConfig == nil {
		if jwtConfigError != nil {
			return nil, fmt.Errorf("jwt configuration loading failed: %w", jwtConfigError)
		}
		// This case should ideally not be reached if LoadJWTConfig sets jwtConfigError on failure.
		return nil, errors.New("jwt configuration is nil after loading attempt without specific error")
	}
	return jwtConfig, nil
}

// GenerateAccessToken creates a new JWT access token for the given user ID.
func GenerateAccessToken(ctx context.Context, store db.Store, userID uuid.UUID) (string, error) {
	config, err := GetJWTConfig()
	if err != nil {
		return "", fmt.Errorf("failed to get JWT config for access token: %w", err)
	}
	if store == nil {
		return "", errors.New("database store instance is nil for GenerateAccessToken")
	}

	// Fetch user details to get platform role
	user, err := store.GetUserByID(ctx, userID)
	if err != nil {
		return "", fmt.Errorf("failed to retrieve user details: %w", err)
	}

	expiresAt := time.Now().Add(config.AccessTokenDuration)
	claims := AppClaims{
		UserID:       userID,
		PlatformRole: string(user.PlatformRole),
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "MembershipSaas",
			Subject:   userID.String(),
		},
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(config.AccessTokenSecret)
}

// GenerateAccessTokenWithCustomExpiry generates a new JWT access token for a given user ID with a custom expiry.
// This is primarily intended for testing purposes.
func GenerateAccessTokenWithCustomExpiry(ctx context.Context, store db.Store, userID uuid.UUID, expiryDuration time.Duration) (string, error) {
	config, err := GetJWTConfig()
	if err != nil {
		return "", fmt.Errorf("failed to get JWT config for custom expiry access token: %w", err)
	}
	if store == nil {
		return "", errors.New("database store instance is nil for GenerateAccessTokenWithCustomExpiry")
	}

	// Fetch user details to get platform role
	user, err := store.GetUserByID(ctx, userID)
	if err != nil {
		return "", fmt.Errorf("failed to retrieve user details: %w", err)
	}

	expiresAt := time.Now().Add(expiryDuration)
	claims := AppClaims{
		UserID:       userID,
		PlatformRole: string(user.PlatformRole),
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "MembershipSaaS-TestToken",
			Subject:   userID.String(),
		},
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(config.AccessTokenSecret)
}

// HashToken computes the SHA256 hash of a token string.
func HashToken(token string) string {
	hash := sha256.Sum256([]byte(token))
	return hex.EncodeToString(hash[:])
}

// GenerateRefreshToken creates a new JWT refresh token for the given user ID.
func GenerateRefreshToken(ctx context.Context, store db.Store, userID uuid.UUID) (string, error) {
	config, err := GetJWTConfig()
	if err != nil {
		return "", fmt.Errorf("failed to get JWT config for refresh token: %w", err)
	}
	if store == nil {
		return "", errors.New("database store instance is nil for GenerateRefreshToken")
	}

	// Fetch user details to get platform role
	user, err := store.GetUserByID(ctx, userID)
	if err != nil {
		return "", fmt.Errorf("failed to retrieve user details: %w", err)
	}

	expiryTime := time.Now().Add(config.RefreshTokenDuration)
	claims := RefreshTokenClaims{
		UserID:       userID,
		PlatformRole: string(user.PlatformRole),
		ActiveOrg:    nil, // Can be set during token refresh if needed
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiryTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "MembershipSaaS-Refresh",
			Subject:   userID.String(),
			ID:        uuid.NewString(),
		},
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	signedToken, err := token.SignedString(config.RefreshTokenSecret)
	if err != nil {
		return "", fmt.Errorf("failed to sign refresh token: %w", err)
	}

	hashedToken := HashToken(signedToken)

	_, err = store.CreateRefreshToken(ctx, db.CreateRefreshTokenParams{
		UserID:    userID,
		TokenHash: hashedToken,
		ExpiresAt: expiryTime,
	})
	if err != nil {
		return "", fmt.Errorf("failed to store refresh token: %w", err)
	}

	return signedToken, nil
}

// GenerateEnhancedAccessToken generates a new JWT access token with platform role and organization context
func GenerateEnhancedAccessToken(ctx context.Context, store db.Store, userID uuid.UUID) (string, error) {
	config, err := GetJWTConfig()
	if err != nil {
		return "", fmt.Errorf("failed to get JWT config: %w", err)
	}
	if store == nil {
		return "", errors.New("database store instance is nil")
	}

	// Fetch user details
	user, err := store.GetUserByID(ctx, userID)
	if err != nil {
		return "", fmt.Errorf("failed to retrieve user details: %w", err)
	}

	// Get user's organization memberships to populate OrgRoles map
	memberships, err := store.GetUserOrganizationMemberships(ctx, userID)
	if err != nil {
		// Log error but don't fail token generation - user might not have any org memberships
		log.Ctx(ctx).Warn().Err(err).Str("user_id", userID.String()).Msg("Failed to get user organization memberships for token generation")
	}

	// Build organization roles map
	orgRoles := make(map[string]string)
	for _, membership := range memberships {
		orgRoles[membership.OrganizationID.String()] = membership.Role
	}

	// Create enhanced claims
	claims := &EnhancedClaims{
		UserID:       userID,
		PlatformRole: string(user.PlatformRole),
		OrgRoles:     orgRoles,
		ActiveOrg:    nil, // Can be set later via organization switching
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(config.AccessTokenDuration)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "MembershipSaas",
			Subject:   userID.String(),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(config.AccessTokenSecret)
}

// ParseEnhancedToken validates the given token string and returns the enhanced claims
func ParseEnhancedToken(tokenString string, secret []byte) (*EnhancedClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &EnhancedClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Explicitly reject the "none" algorithm
		if token.Method == jwt.SigningMethodNone {
			log.Error().Str("algorithm", "none").Msg("Rejected JWT with 'none' algorithm in ParseEnhancedToken - potential security attack")
			return nil, fmt.Errorf("JWT algorithm 'none' is not allowed")
		}

		// Check if the algorithm is in our allowed list
		alg := token.Method.Alg()
		if !AllowedSigningMethods[alg] {
			log.Error().Str("algorithm", alg).Msg("Rejected JWT with disallowed algorithm in ParseEnhancedToken")
			return nil, fmt.Errorf("JWT algorithm '%s' is not allowed. Only HMAC algorithms (HS256, HS384, HS512) are permitted", alg)
		}

		// Verify it's specifically an HMAC signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			log.Error().Str("algorithm", alg).Msg("JWT algorithm is not HMAC despite being in allowed list in ParseEnhancedToken")
			return nil, fmt.Errorf("unexpected signing method: %v. Only HMAC algorithms are allowed", token.Header["alg"])
		}
		return secret, nil
	})
	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	claims, ok := token.Claims.(*EnhancedClaims)
	if !ok || !token.Valid {
		return nil, fmt.Errorf("invalid token claims")
	}

	return claims, nil
}

// ParseToken validates the given token string and returns the claims if valid.
func ParseToken(tokenString string, secret []byte) (*AppClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &AppClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Explicitly reject the "none" algorithm
		if token.Method == jwt.SigningMethodNone {
			log.Error().Str("algorithm", "none").Msg("Rejected JWT with 'none' algorithm in ParseToken - potential security attack")
			return nil, fmt.Errorf("JWT algorithm 'none' is not allowed")
		}

		// Check if the algorithm is in our allowed list
		alg := token.Method.Alg()
		if !AllowedSigningMethods[alg] {
			log.Error().Str("algorithm", alg).Msg("Rejected JWT with disallowed algorithm in ParseToken")
			return nil, fmt.Errorf("JWT algorithm '%s' is not allowed. Only HMAC algorithms (HS256, HS384, HS512) are permitted", alg)
		}

		// Verify it's specifically an HMAC signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			log.Error().Str("algorithm", alg).Msg("JWT algorithm is not HMAC despite being in allowed list in ParseToken")
			return nil, fmt.Errorf("unexpected signing method: %v. Only HMAC algorithms are allowed", token.Header["alg"])
		}
		return secret, nil
	})
	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	if claims, ok := token.Claims.(*AppClaims); ok && token.Valid {
		return claims, nil
	}
	return nil, fmt.Errorf("invalid token")
}

// ParseTokenWithBackwardCompatibility attempts to parse a token as either new format (EnhancedClaims)
// or legacy format (AppClaims) and returns it as EnhancedClaims for compatibility
func ParseTokenWithBackwardCompatibility(tokenString string, secret []byte) (*EnhancedClaims, error) {
	// First try to parse as new format (EnhancedClaims)
	enhancedClaims, err := ParseEnhancedToken(tokenString, secret)
	if err == nil {
		// Check if this is a real enhanced token (has platform_role) or a legacy token parsed as enhanced
		if enhancedClaims.PlatformRole != "" || len(enhancedClaims.OrgRoles) > 0 {
			return enhancedClaims, nil
		}
		// If platform_role is empty and no org roles, this might be a legacy token parsed as enhanced
		// Fall through to legacy parsing
	}

	// Try to parse as legacy format (AppClaims)
	legacyClaims, err := ParseToken(tokenString, secret)
	if err != nil {
		return nil, fmt.Errorf("failed to parse token as both enhanced and legacy format: %w", err)
	}

	// Convert legacy claims to enhanced format for backward compatibility
	platformRole := legacyClaims.PlatformRole
	if platformRole == "" {
		// Default to "user" for very old tokens without platform_role
		platformRole = "user"
	}

	enhancedClaims = &EnhancedClaims{
		UserID:           legacyClaims.UserID,
		PlatformRole:     platformRole,
		OrgRoles:         make(map[string]string), // Legacy tokens don't have org roles
		ActiveOrg:        nil,                     // Legacy tokens don't have org context
		RegisteredClaims: legacyClaims.RegisteredClaims,
	}

	return enhancedClaims, nil
}

// GetValidatedEnhancedClaims combines parsing and validation for enhanced claims
func GetValidatedEnhancedClaims(tokenString string, secret []byte) (*EnhancedClaims, error) {
	claims, err := ParseTokenWithBackwardCompatibility(tokenString, secret)
	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	// Additional validation can be added here
	if claims.UserID == uuid.Nil {
		return nil, fmt.Errorf("invalid user ID in token")
	}

	// Allow empty platform role for backward compatibility

	return claims, nil
}

// RefreshTokenClaims represents claims specific to refresh tokens
type RefreshTokenClaims struct {
	UserID       uuid.UUID   `json:"user_id"`
	PlatformRole string      `json:"platform_role"`
	ActiveOrg    *OrgContext `json:"active_org,omitempty"` // Preserved organization context
	jwt.RegisteredClaims
}

// GenerateEnhancedRefreshToken creates a new JWT refresh token with organization context
func GenerateEnhancedRefreshToken(ctx context.Context, store db.Store, userID uuid.UUID, activeOrg *OrgContext) (string, error) {
	config, err := GetJWTConfig()
	if err != nil {
		return "", fmt.Errorf("failed to get JWT config for enhanced refresh token: %w", err)
	}
	if store == nil {
		return "", errors.New("database store instance is nil for GenerateEnhancedRefreshToken")
	}

	// Fetch user details to get platform role
	user, err := store.GetUserByID(ctx, userID)
	if err != nil {
		return "", fmt.Errorf("failed to retrieve user details for refresh token: %w", err)
	}

	expiryTime := time.Now().Add(config.RefreshTokenDuration)
	claims := RefreshTokenClaims{
		UserID:       userID,
		PlatformRole: string(user.PlatformRole),
		ActiveOrg:    activeOrg, // Preserve organization context in refresh token
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiryTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "MembershipSaaS-Refresh",
			Subject:   userID.String(),
			ID:        uuid.NewString(),
		},
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	signedToken, err := token.SignedString(config.RefreshTokenSecret)
	if err != nil {
		return "", fmt.Errorf("failed to sign enhanced refresh token: %w", err)
	}

	hashedToken := HashToken(signedToken)

	_, err = store.CreateRefreshToken(ctx, db.CreateRefreshTokenParams{
		UserID:    userID,
		TokenHash: hashedToken,
		ExpiresAt: expiryTime,
	})
	if err != nil {
		return "", fmt.Errorf("failed to store enhanced refresh token: %w", err)
	}

	return signedToken, nil
}

// ParseRefreshTokenWithBackwardCompatibility parses refresh tokens in both new and legacy formats
func ParseRefreshTokenWithBackwardCompatibility(tokenString string, secret []byte) (*RefreshTokenClaims, error) {
	// First try to parse as new format (RefreshTokenClaims)
	refreshClaims := &RefreshTokenClaims{}
	token, err := jwt.ParseWithClaims(tokenString, refreshClaims, func(token *jwt.Token) (interface{}, error) {
		// Explicitly reject the "none" algorithm
		if token.Method == jwt.SigningMethodNone {
			log.Error().Str("algorithm", "none").Msg("Rejected JWT with 'none' algorithm in ParseRefreshTokenWithBackwardCompatibility - potential security attack")
			return nil, fmt.Errorf("JWT algorithm 'none' is not allowed")
		}

		// Check if the algorithm is in our allowed list
		alg := token.Method.Alg()
		if !AllowedSigningMethods[alg] {
			log.Error().Str("algorithm", alg).Msg("Rejected JWT with disallowed algorithm in ParseRefreshTokenWithBackwardCompatibility")
			return nil, fmt.Errorf("JWT algorithm '%s' is not allowed. Only HMAC algorithms (HS256, HS384, HS512) are permitted", alg)
		}

		// Verify it's specifically an HMAC signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			log.Error().Str("algorithm", alg).Msg("JWT algorithm is not HMAC despite being in allowed list in ParseRefreshTokenWithBackwardCompatibility")
			return nil, fmt.Errorf("unexpected signing method: %v. Only HMAC algorithms are allowed", token.Header["alg"])
		}
		return secret, nil
	})

	if err == nil && token.Valid {
		// Check if this is a real enhanced refresh token (has ActiveOrg) or a legacy token parsed as enhanced
		if refreshClaims.UserID != uuid.Nil {
			return refreshClaims, nil
		}
	}

	// Try to parse as legacy format (AppClaims)
	legacyClaims, err := ParseToken(tokenString, secret)
	if err != nil {
		return nil, fmt.Errorf("failed to parse refresh token as both enhanced and legacy format: %w", err)
	}

	// Convert legacy claims to refresh token format for backward compatibility
	platformRole := legacyClaims.PlatformRole
	if platformRole == "" {
		// Default to "user" for very old tokens without platform_role
		platformRole = "user"
	}
	
	refreshClaims = &RefreshTokenClaims{
		UserID:           legacyClaims.UserID,
		PlatformRole:     platformRole,
		ActiveOrg:        nil, // Legacy tokens don't have org context
		RegisteredClaims: legacyClaims.RegisteredClaims,
	}

	return refreshClaims, nil
}
