package token

import (
	"testing"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestRejectNoneAlgorithm(t *testing.T) {
	// Test configuration
	secretKey := "test-secret-key-that-is-at-least-32-chars-long"
	userID := uuid.New()

	// Create a valid maker
	maker, err := NewJWTMaker(secretKey)
	require.NoError(t, err)

	// Test Case 1: Create a token with "none" algorithm
	t.Run("ParseToken rejects none algorithm", func(t *testing.T) {
		// Create a token with "none" algorithm
		claims := &AppClaims{
			UserID:       userID,
			PlatformRole: "user",
			RegisteredClaims: jwt.RegisteredClaims{
				ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
				IssuedAt:  jwt.NewNumericDate(time.Now()),
				NotBefore: jwt.NewNumericDate(time.Now()),
				Issuer:    "MembershipSaas",
				Subject:   userID.String(),
			},
		}

		// Create token with "none" algorithm
		token := jwt.NewWithClaims(jwt.SigningMethodNone, claims)
		noneToken, err := token.SignedString(jwt.UnsafeAllowNoneSignatureType)
		require.NoError(t, err)

		// Try to parse the token - should fail
		_, err = ParseToken(noneToken, []byte(secretKey))
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "JWT algorithm 'none' is not allowed")
	})

	// Test Case 2: VerifyToken method rejects none algorithm
	t.Run("VerifyToken rejects none algorithm", func(t *testing.T) {
		// Create a token with "none" algorithm
		claims := &AppClaims{
			UserID:       userID,
			PlatformRole: "user",
			RegisteredClaims: jwt.RegisteredClaims{
				ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
				IssuedAt:  jwt.NewNumericDate(time.Now()),
				NotBefore: jwt.NewNumericDate(time.Now()),
				Issuer:    "MembershipSaas",
				Subject:   userID.String(),
			},
		}

		token := jwt.NewWithClaims(jwt.SigningMethodNone, claims)
		noneToken, err := token.SignedString(jwt.UnsafeAllowNoneSignatureType)
		require.NoError(t, err)

		// Try to verify the token - should fail
		_, err = maker.VerifyToken(noneToken)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "JWT algorithm 'none' is not allowed")
	})

	// Test Case 3: ParseEnhancedToken rejects none algorithm
	t.Run("ParseEnhancedToken rejects none algorithm", func(t *testing.T) {
		// Create an enhanced token with "none" algorithm
		claims := &EnhancedClaims{
			UserID:       userID,
			PlatformRole: "user",
			RegisteredClaims: jwt.RegisteredClaims{
				ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
				IssuedAt:  jwt.NewNumericDate(time.Now()),
				NotBefore: jwt.NewNumericDate(time.Now()),
				Issuer:    "MembershipSaas",
				Subject:   userID.String(),
			},
		}

		token := jwt.NewWithClaims(jwt.SigningMethodNone, claims)
		noneToken, err := token.SignedString(jwt.UnsafeAllowNoneSignatureType)
		require.NoError(t, err)

		// Try to parse the token - should fail
		_, err = ParseEnhancedToken(noneToken, []byte(secretKey))
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "JWT algorithm 'none' is not allowed")
	})
}

func TestRejectDisallowedAlgorithms(t *testing.T) {
	secretKey := "test-secret-key-that-is-at-least-32-chars-long"

	// Test Case 1: Reject RS256 (RSA algorithm)
	t.Run("ParseToken rejects RS256", func(t *testing.T) {
		// Create a fake token header that looks like RS256
		// Note: We can't actually create a valid RS256 token without private key
		// but we can test that the algorithm check happens before signature validation
		tokenString := "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiMTIzIn0.fake-signature"

		_, err := ParseToken(tokenString, []byte(secretKey))
		assert.Error(t, err)
		// The error might be about parsing or algorithm, both are acceptable
		// as long as the token is rejected
	})

	// Test Case 2: Reject ES256 (ECDSA algorithm)
	t.Run("ParseToken rejects ES256", func(t *testing.T) {
		tokenString := "eyJhbGciOiJFUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiMTIzIn0.fake-signature"

		_, err := ParseToken(tokenString, []byte(secretKey))
		assert.Error(t, err)
	})
}

func TestAllowedAlgorithms(t *testing.T) {
	secretKey := "test-secret-key-that-is-at-least-32-chars-long"
	userID := uuid.New()

	maker, err := NewJWTMaker(secretKey)
	require.NoError(t, err)

	// Test that all allowed algorithms work correctly
	allowedAlgs := []string{"HS256", "HS384", "HS512"}

	for _, alg := range allowedAlgs {
		t.Run("Allow "+alg, func(t *testing.T) {
			// For HS256, we can use the maker's CreateToken
			if alg == "HS256" {
				token, err := maker.CreateToken(userID, time.Hour)
				require.NoError(t, err)

				// Verify the token works
				claims, err := maker.VerifyToken(token)
				require.NoError(t, err)
				assert.Equal(t, userID, claims.UserID)
			}
			// For other algorithms, we'd need to create tokens manually
			// but that's beyond the scope of this security test
		})
	}
}

func TestAllowedSigningMethodsConstant(t *testing.T) {
	// Verify the AllowedSigningMethods map contains only HMAC algorithms
	expectedMethods := map[string]bool{
		"HS256": true,
		"HS384": true,
		"HS512": true,
	}

	assert.Equal(t, expectedMethods, AllowedSigningMethods)

	// Ensure no other algorithms are present
	for method := range AllowedSigningMethods {
		assert.True(t, method == "HS256" || method == "HS384" || method == "HS512",
			"Unexpected algorithm in AllowedSigningMethods: %s", method)
	}
}
