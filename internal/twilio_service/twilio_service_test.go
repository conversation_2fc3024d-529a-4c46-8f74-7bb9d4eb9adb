package twilio_service

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestDynamicOTPGeneration(t *testing.T) {
	// Set up mock mode
	os.Setenv("MOCK_TWILIO_OTP", "true")
	os.Setenv("MOCK_TWILIO_NOTIFICATIONS", "true")
	defer func() {
		os.Unsetenv("MOCK_TWILIO_OTP")
		os.Unsetenv("MOCK_TWILIO_NOTIFICATIONS")
	}()

	service, err := NewTwilioService()
	require.NoError(t, err)
	require.NotNil(t, service)

	// Test OTP generation
	t.Run("GenerateDifferentOTPs", func(t *testing.T) {
		otpMap := make(map[string]bool)

		// Generate 10 OTPs and verify they're different
		for i := 0; i < 10; i++ {
			sid, err := service.SendOTP("+1234567890", "sms")
			require.NoError(t, err)
			require.NotEmpty(t, sid)

			otp, exists := service.GetMockOTP(sid)
			require.True(t, exists)
			require.Len(t, otp, 6) // Should be 6 digits

			// Check if OTP is numeric
			for _, c := range otp {
				assert.True(t, c >= '0' && c <= '9', "OTP should contain only digits")
			}

			// Track OTPs to check for uniqueness (with high probability)
			otpMap[otp] = true
		}

		// With 10 generations, we should have at least 8 unique OTPs (allowing for some collisions)
		assert.GreaterOrEqual(t, len(otpMap), 8, "Should generate mostly unique OTPs")
	})

	t.Run("VerifyWithCorrectOTP", func(t *testing.T) {
		sid, err := service.SendOTP("+1234567890", "sms")
		require.NoError(t, err)

		otp, exists := service.GetMockOTP(sid)
		require.True(t, exists)

		// Verify with correct OTP
		valid, err := service.VerifyOTPWithSID(sid, otp)
		require.NoError(t, err)
		assert.True(t, valid)

		// OTP should be consumed after verification
		_, exists = service.GetMockOTP(sid)
		assert.False(t, exists, "OTP should be removed after successful verification")
	})

	t.Run("VerifyWithIncorrectOTP", func(t *testing.T) {
		sid, err := service.SendOTP("+1234567890", "sms")
		require.NoError(t, err)

		// Verify with incorrect OTP
		valid, err := service.VerifyOTPWithSID(sid, "999999")
		require.NoError(t, err)
		assert.False(t, valid)

		// OTP should still exist after failed verification
		_, exists := service.GetMockOTP(sid)
		assert.True(t, exists, "OTP should remain after failed verification")
	})

	t.Run("SecurityFix_NoHardcodedOTPFallback", func(t *testing.T) {
		// Test that hardcoded OTP fallback has been removed for security
		valid, err := service.VerifyOTPWithSID("non-existent-sid", MockOTPCode)
		require.NoError(t, err)
		assert.False(t, valid, "Hardcoded OTP fallback should be removed for security")
	})
}

func TestRealModeValidation(t *testing.T) {
	// Ensure mock mode is off
	os.Unsetenv("MOCK_TWILIO_OTP")
	os.Setenv("TWILIO_ACCOUNT_SID", "test-sid")
	os.Setenv("TWILIO_AUTH_TOKEN", "test-token")
	defer func() {
		os.Unsetenv("TWILIO_ACCOUNT_SID")
		os.Unsetenv("TWILIO_AUTH_TOKEN")
	}()

	service, err := NewTwilioService()
	require.NoError(t, err)
	require.NotNil(t, service)

	// GetMockOTP should return false in real mode
	otp, exists := service.GetMockOTP("any-sid")
	assert.Empty(t, otp)
	assert.False(t, exists)
}
