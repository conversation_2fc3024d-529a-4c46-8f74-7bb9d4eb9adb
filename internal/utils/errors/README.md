# Error Handling Utilities

This package provides standardized error handling utilities for consistent error management across the application. It builds upon the existing error infrastructure to provide simplified, reusable error handling patterns.

## Overview

The error handling utilities integrate with existing infrastructure:

- **`internal/utils/http/error_handler.go`** - Basic HTTP error handling
- **`internal/apierror/apierror.go`** - Structured API error types
- **`internal/domain/errors.go`** - Domain-specific error types
- **`internal/middleware/error_mapping.go`** - Domain-to-API error mapping
- **`internal/shared/errors/service_errors.go`** - Common service errors

## Components

### 1. Error<PERSON>andler (`handler.go`)

Provides standardized HTTP error handling for handlers.

```go
errHandler := errors.NewErrorHandler()

// Parse UUID parameters with automatic error handling
orgID, err := errHandler.ParseUUIDParam(c, "orgID", "organization ID")
if err != nil {
    return err // Error response already sent
}

// Handle service errors intelligently
result, err := service.GetEvent(ctx, eventID)
if err != nil {
    return errHandler.HandleServiceError(c, err, "Failed to get event")
}
```

**Key Methods:**
- `BadRequest(c, message, err)` - 400 Bad Request
- `Unauthorized(c, message, err)` - 401 Unauthorized
- `Forbidden(c, message, err)` - 403 Forbidden
- `NotFound(c, message, err)` - 404 Not Found
- `Conflict(c, message, err)` - 409 Conflict
- `InternalServerError(c, message, err)` - 500 Internal Server Error
- `ParseUUIDParam(c, param, field)` - Parse UUID with error handling
- `HandleServiceError(c, err, message)` - Intelligent service error handling

### 2. ServiceErrorHandler (`service.go`)

Helps services create domain-specific errors.

```go
svcErrHandler := errors.NewServiceErrorHandler()

// Create domain-specific errors
if user == nil {
    return svcErrHandler.UserNotFound(userID)
}

// Validate permissions
if err := svcErrHandler.CheckPermission(hasPermission, userID, "admin", "create_event"); err != nil {
    return err
}
```

**Key Methods:**
- `UserNotFound(userID)` - User not found error
- `EventNotFound(eventID)` - Event not found error
- `ValidationError(field, reason, value)` - Validation error
- `InsufficientPermissions(userID, role, action)` - Permission error
- `CheckPermission(hasPermission, userID, role, action)` - Permission validation

### 3. DatabaseErrorHandler (`database.go`)

Handles database errors and maps them to domain errors.

```go
dbErrHandler := errors.NewDatabaseErrorHandler()

user, err := queries.GetUser(ctx, userID)
if err != nil {
    return dbErrHandler.HandleDatabaseError(err, "get", "user", userID)
}
```

**Key Methods:**
- `HandleDatabaseError(err, operation, entityType, entityID)` - Main error handler
- `HandleTransactionError(err, operation)` - Transaction errors
- `HandleConnectionError(err)` - Connection errors
- `IsUniqueViolation(err)` - Check for unique constraint violations
- `IsNotFound(err)` - Check for not found errors

### 4. ValidationErrorHandler (`validation.go`)

Handles validation errors from go-playground/validator.

```go
valErrHandler := errors.NewValidationErrorHandler()

if err := validator.Struct(request); err != nil {
    return valErrHandler.HandleValidationError(c, err)
}
```

**Key Methods:**
- `HandleValidationError(c, err)` - Handle validator errors
- `FormatValidationErrors(err)` - Format field errors
- `RequiredFields(data, fields...)` - Check required fields
- `ValidateEnum(value, allowed, field)` - Validate enum values
- `ValidateRange(value, min, max, field)` - Validate numeric range

### 5. LoggingErrorHandler (`logging.go`)

Provides structured error logging with context.

```go
logErrHandler := errors.NewLoggingErrorHandler()

logCtx := logErrHandler.BuildLogContextFromEcho(c)
logCtx = logErrHandler.WithOperation(logCtx, "create_event")
logErrHandler.LogError(ctx, err, logCtx, "Failed to create event")
```

**Key Methods:**
- `LogError(ctx, err, logCtx, message)` - Error logging
- `LogWarn(ctx, err, logCtx, message)` - Warning logging
- `LogAuditEvent(ctx, action, logCtx, result, details)` - Audit logging
- `LogSecurityEvent(ctx, event, logCtx, details)` - Security logging
- `BuildLogContextFromEcho(c)` - Build context from Echo

### 6. ErrorHandlers (`errors.go`)

Convenience wrapper for all error handlers.

```go
errorHandlers := errors.NewErrorHandlers()

// Access all handlers
errorHandlers.HTTP.BadRequest(c, "Invalid input", err)
errorHandlers.Service.UserNotFound(userID)
errorHandlers.Database.HandleDatabaseError(err, "get", "user", userID)
errorHandlers.Validation.HandleValidationError(c, err)
errorHandlers.Logging.LogError(ctx, err, logCtx, "Operation failed")
```

## Usage Patterns

### HTTP Handler Pattern

```go
type EventHandler struct {
    errorHandlers *errors.ErrorHandlers
    eventService  event.ManagementService
    validator     *validation.RequestValidator
}

func (h *EventHandler) CreateEvent(c echo.Context) error {
    // Parse parameters
    orgID, err := h.errorHandlers.HTTP.ParseUUIDParam(c, "orgID", "organization ID")
    if err != nil {
        return err
    }
    
    // Validate request
    var req payloads.CreateEventRequest
    if err := c.Bind(&req); err != nil {
        return h.errorHandlers.HTTP.BadRequest(c, "Invalid request body", err)
    }
    
    if err := h.validator.ValidateStruct(&req); err != nil {
        return h.errorHandlers.Validation.HandleValidationError(c, err)
    }
    
    // Call service
    event, err := h.eventService.CreateEvent(c.Request().Context(), orgID, userID, req)
    if err != nil {
        return h.errorHandlers.HTTP.HandleServiceError(c, err, "Failed to create event")
    }
    
    return c.JSON(http.StatusCreated, event)
}
```

### Service Layer Pattern

```go
type EventService struct {
    store         db.Store
    errorHandlers *errors.ErrorHandlers
}

func (s *EventService) GetEvent(ctx context.Context, eventID uuid.UUID) (*Event, error) {
    // Validate input
    if err := s.errorHandlers.Service.RequireNonNilUUID(eventID, "event_id"); err != nil {
        return nil, err
    }
    
    // Database operation
    event, err := s.store.GetEvent(ctx, eventID)
    if err != nil {
        return nil, s.errorHandlers.Database.HandleDatabaseError(err, "get", "event", eventID)
    }
    
    return event, nil
}
```

### Database Error Handling

```go
// In service methods
user, err := queries.CreateUser(ctx, params)
if err != nil {
    // This will map unique constraint violations to DuplicateEmailError
    return s.errorHandlers.Database.HandleDatabaseError(err, "create", "user", uuid.Nil)
}
```

### Validation Error Handling

```go
// Custom validation
if err := h.errorHandlers.Validation.RequiredFields(data, "title", "description"); err != nil {
    return err
}

// Enum validation
allowedStatuses := []string{"draft", "published", "cancelled"}
if err := h.errorHandlers.Validation.ValidateEnum(status, allowedStatuses, "status"); err != nil {
    return err
}
```

### Structured Logging

```go
// Build log context
logCtx := h.errorHandlers.Logging.BuildLogContextFromEcho(c)
logCtx = h.errorHandlers.Logging.WithOperation(logCtx, "create_event")
logCtx = h.errorHandlers.Logging.WithUserID(logCtx, userID)

// Log error with context
h.errorHandlers.Logging.LogError(ctx, err, logCtx, "Failed to create event")

// Log audit events
h.errorHandlers.Logging.LogAuditEvent(ctx, "event_created", logCtx, "success", map[string]interface{}{
    "event_id": eventID.String(),
})
```

## Error Flow

1. **Handler Level**: Use `ErrorHandler` for HTTP-specific errors and parameter parsing
2. **Service Level**: Use `ServiceErrorHandler` to create domain errors
3. **Database Level**: Use `DatabaseErrorHandler` to map database errors to domain errors
4. **Middleware Level**: Error mapping middleware converts domain errors to API responses
5. **Logging**: Use `LoggingErrorHandler` for structured error logging throughout

## Benefits

1. **Consistency**: Standardized error handling patterns across the application
2. **Maintainability**: Centralized error handling logic reduces code duplication
3. **Observability**: Structured logging with proper context for debugging
4. **Type Safety**: Domain-specific errors with proper typing
5. **Security**: Proper error mapping prevents information leakage
6. **Developer Experience**: Simplified error handling with helper methods

## Integration with Existing Code

These utilities are designed to work alongside existing error handling:

- **No Breaking Changes**: Existing `httputil.HandleError` calls continue to work
- **Gradual Migration**: Can be adopted incrementally in new code
- **Complementary**: Works with existing error mapping middleware
- **Extensible**: Easy to add new error types and handling patterns

## Examples

See `examples_handler.go` and `examples_service.go` for comprehensive usage examples demonstrating:

- HTTP handler error handling patterns
- Service layer error creation and handling
- Database error mapping
- Validation error processing
- Structured logging with context
- Complex business logic error handling

## Best Practices

1. **Use Domain Errors**: Prefer creating domain errors in services over generic errors
2. **Let Middleware Handle Mapping**: Return domain errors and let error mapping middleware handle HTTP responses
3. **Add Context**: Include relevant context (user ID, organization ID, etc.) in logs
4. **Validate Early**: Use error handlers for input validation in both handlers and services
5. **Log Appropriately**: Use different log levels (Error, Warn, Info) based on severity
6. **Security First**: Don't expose sensitive information in error messages
7. **Audit Important Actions**: Log security-sensitive operations for compliance
