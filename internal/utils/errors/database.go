// Package errors provides database error handling utilities.
// These utilities help identify and handle common database errors
// with appropriate HTTP responses and logging.
package errors

import (
	"errors"
	"fmt"
	"strings"

	"Membership-SAAS-System-Backend/internal/apierror"
	"Membership-SAAS-System-Backend/internal/domain"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/rs/zerolog/log"
)

// DatabaseErrorHandler provides utilities for handling database errors.
type DatabaseErrorHandler struct{}

// NewDatabaseErrorHandler creates a new DatabaseErrorHandler instance.
func NewDatabaseErrorHandler() *DatabaseErrorHandler {
	return &DatabaseErrorHandler{}
}

// HandleDatabaseError processes database errors and returns appropriate domain errors.
func (h *DatabaseErrorHandler) HandleDatabaseError(err error, operation string, entityType string, entityID uuid.UUID) error {
	if err == nil {
		return nil
	}

	// Handle no rows found (404 cases)
	if errors.Is(err, pgx.ErrNoRows) {
		return h.createNotFoundError(entityType, entityID)
	}

	// Handle PostgreSQL-specific errors
	var pgErr *pgconn.PgError
	if errors.As(err, &pgErr) {
		return h.handlePostgreSQLError(pgErr, operation, entityType)
	}

	// Log unexpected database errors for debugging
	log.Error().
		Err(err).
		Str("operation", operation).
		Str("entity_type", entityType).
		Str("entity_id", entityID.String()).
		Msg("Unexpected database error")

	// Return generic database error
	return fmt.Errorf("database %s failed: %w", operation, err)
}

// handlePostgreSQLError processes PostgreSQL-specific error codes.
func (h *DatabaseErrorHandler) handlePostgreSQLError(pgErr *pgconn.PgError, operation, entityType string) error {
	switch pgErr.Code {
	case "23505": // unique_violation
		return h.handleUniqueViolation(pgErr, entityType)
	case "23503": // foreign_key_violation
		return h.handleForeignKeyViolation(pgErr, entityType)
	case "23514": // check_violation
		return h.handleCheckViolation(pgErr, entityType)
	case "23502": // not_null_violation
		return h.handleNotNullViolation(pgErr, entityType)
	case "42P01": // undefined_table
		return fmt.Errorf("database schema error: table does not exist")
	case "42703": // undefined_column
		return fmt.Errorf("database schema error: column does not exist")
	default:
		// Log the error code for debugging
		log.Warn().
			Str("pg_error_code", pgErr.Code).
			Str("pg_message", pgErr.Message).
			Str("operation", operation).
			Str("entity_type", entityType).
			Msg("Unhandled PostgreSQL error")

		return fmt.Errorf("database operation failed: %s", pgErr.Message)
	}
}

// handleUniqueViolation processes unique constraint violations.
func (h *DatabaseErrorHandler) handleUniqueViolation(pgErr *pgconn.PgError, entityType string) error {
	// Extract constraint name and field from error detail
	constraintName := pgErr.ConstraintName
	detail := pgErr.Detail

	// Map common constraint violations to domain errors
	switch {
	case strings.Contains(constraintName, "email") || strings.Contains(detail, "email"):
		email := h.extractValueFromDetail(detail, "email")
		return &domain.DuplicateEmailError{Email: email}

	case strings.Contains(constraintName, "phone") || strings.Contains(detail, "phone"):
		phone := h.extractValueFromDetail(detail, "phone")
		return &domain.DuplicatePhoneError{Phone: phone}

	case strings.Contains(constraintName, "username") || strings.Contains(detail, "username"):
		return apierror.NewConflict("Username already exists")

	case strings.Contains(constraintName, "organization") && strings.Contains(detail, "name"):
		return apierror.NewConflict("Organization name already exists")

	default:
		return apierror.NewConflict(fmt.Sprintf("%s already exists", entityType))
	}
}

// handleForeignKeyViolation processes foreign key constraint violations.
func (h *DatabaseErrorHandler) handleForeignKeyViolation(pgErr *pgconn.PgError, entityType string) error {
	detail := pgErr.Detail

	// Common foreign key violations
	switch {
	case strings.Contains(detail, "organization"):
		return apierror.NewBadRequest("Invalid organization reference")
	case strings.Contains(detail, "user"):
		return apierror.NewBadRequest("Invalid user reference")
	case strings.Contains(detail, "event"):
		return apierror.NewBadRequest("Invalid event reference")
	default:
		return apierror.NewBadRequest("Invalid reference in request")
	}
}

// handleCheckViolation processes check constraint violations.
func (h *DatabaseErrorHandler) handleCheckViolation(pgErr *pgconn.PgError, entityType string) error {
	constraintName := pgErr.ConstraintName

	// Map common check constraints to validation errors
	switch {
	case strings.Contains(constraintName, "email_format"):
		return apierror.NewBadRequest("Invalid email format")
	case strings.Contains(constraintName, "phone_format"):
		return apierror.NewBadRequest("Invalid phone number format")
	case strings.Contains(constraintName, "status"):
		return apierror.NewBadRequest("Invalid status value")
	case strings.Contains(constraintName, "capacity"):
		return apierror.NewBadRequest("Invalid capacity value")
	default:
		return apierror.NewBadRequest("Data constraint violation")
	}
}

// handleNotNullViolation processes not null constraint violations.
func (h *DatabaseErrorHandler) handleNotNullViolation(pgErr *pgconn.PgError, entityType string) error {
	columnName := pgErr.ColumnName
	if columnName != "" {
		return apierror.NewBadRequest(fmt.Sprintf("Required field '%s' cannot be null", columnName))
	}
	return apierror.NewBadRequest("Required field cannot be null")
}

// createNotFoundError creates appropriate not found domain errors based on entity type.
func (h *DatabaseErrorHandler) createNotFoundError(entityType string, entityID uuid.UUID) error {
	switch strings.ToLower(entityType) {
	case "user":
		return &domain.UserNotFoundError{UserID: entityID}
	case "organization":
		return &domain.OrganizationNotFoundError{OrgID: entityID}
	case "event":
		return &domain.EventNotFoundError{EventID: entityID}
	case "registration":
		return &domain.RegistrationNotFoundError{RegistrationID: entityID}
	case "volunteer_application":
		return &domain.VolunteerApplicationNotFoundError{ApplicationID: entityID}
	case "resource":
		return &domain.ResourceNotFoundError{ResourceID: entityID, Type: entityType}
	default:
		return apierror.NewNotFound(fmt.Sprintf("%s not found", entityType))
	}
}

// extractValueFromDetail extracts a field value from PostgreSQL error detail message.
func (h *DatabaseErrorHandler) extractValueFromDetail(detail, fieldName string) string {
	// PostgreSQL detail format: "Key (field)=(value) already exists."
	start := strings.Index(detail, fmt.Sprintf("(%s)=(", fieldName))
	if start == -1 {
		return ""
	}

	start += len(fmt.Sprintf("(%s)=(", fieldName))
	end := strings.Index(detail[start:], ")")
	if end == -1 {
		return ""
	}

	return detail[start : start+end]
}

// Transaction Error Handling

// HandleTransactionError processes transaction-related errors.
func (h *DatabaseErrorHandler) HandleTransactionError(err error, operation string) error {
	if err == nil {
		return nil
	}

	// Handle transaction-specific errors
	switch {
	case errors.Is(err, pgx.ErrTxClosed):
		log.Error().Err(err).Str("operation", operation).Msg("Transaction already closed")
		return fmt.Errorf("transaction error: %w", err)

	case errors.Is(err, pgx.ErrTxCommitRollback):
		log.Error().Err(err).Str("operation", operation).Msg("Transaction commit/rollback error")
		return fmt.Errorf("transaction commit failed: %w", err)

	default:
		// Handle as regular database error
		return h.HandleDatabaseError(err, operation, "transaction", uuid.Nil)
	}
}

// Connection Error Handling

// HandleConnectionError processes database connection-related errors.
func (h *DatabaseErrorHandler) HandleConnectionError(err error) error {
	if err == nil {
		return nil
	}

	// Log connection errors for monitoring
	log.Error().Err(err).Msg("Database connection error")

	// Return a generic error to avoid exposing internal details
	return apierror.ErrDatabaseError.WithError(err)
}

// Utility Methods

// IsUniqueViolation checks if an error is a PostgreSQL unique constraint violation.
func (h *DatabaseErrorHandler) IsUniqueViolation(err error) bool {
	var pgErr *pgconn.PgError
	return errors.As(err, &pgErr) && pgErr.Code == "23505"
}

// IsForeignKeyViolation checks if an error is a PostgreSQL foreign key constraint violation.
func (h *DatabaseErrorHandler) IsForeignKeyViolation(err error) bool {
	var pgErr *pgconn.PgError
	return errors.As(err, &pgErr) && pgErr.Code == "23503"
}

// IsNotFound checks if an error indicates a record was not found.
func (h *DatabaseErrorHandler) IsNotFound(err error) bool {
	return errors.Is(err, pgx.ErrNoRows)
}

// LogDatabaseError logs database errors with appropriate context.
func (h *DatabaseErrorHandler) LogDatabaseError(err error, operation, table string, params map[string]interface{}) {
	if err == nil {
		return
	}

	logEvent := log.Error().
		Err(err).
		Str("operation", operation).
		Str("table", table)

	// Add parameters to log event
	for key, value := range params {
		logEvent = logEvent.Interface(key, value)
	}

	logEvent.Msg("Database operation failed")
}
