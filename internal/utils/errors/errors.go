// Package errors provides standardized error handling utilities for the application.
// This package consolidates error handling patterns across HTTP handlers, service layer,
// validation, database operations, and logging to provide consistent error responses
// and proper error tracking throughout the application.
//
// The package integrates with existing error infrastructure:
//   - internal/utils/http/error_handler.go - Basic HTTP error handling
//   - internal/apierror/apierror.go - Structured API error types
//   - internal/domain/errors.go - Domain-specific error types
//   - internal/middleware/error_mapping.go - Domain-to-API error mapping
//   - internal/shared/errors/service_errors.go - Common service errors
//
// Usage Examples:
//
// HTTP Handler Error Handling:
//
//	errHandler := errors.NewErrorHandler()
//	// Parse UUID parameter with automatic error handling
//	orgID, err := errHandler.ParseUUIDParam(c, "orgID", "organization ID")
//	if err != nil {
//		return err // Error response already sent
//	}
//
//	// Handle service errors intelligently
//	result, err := service.GetEvent(ctx, eventID)
//	if err != nil {
//		return errHandler.HandleServiceError(c, err, "Failed to get event")
//	}
//
// Service Layer Error Creation:
//
//	svcErrHandler := errors.NewServiceErrorHandler()
//	// Create domain-specific errors
//	if user == nil {
//		return svcErrHandler.UserNotFound(userID)
//	}
//
//	// Validate permissions
//	if err := svcErrHandler.CheckPermission(hasPermission, userID, "admin", "create_event"); err != nil {
//		return err
//	}
//
// Database Error Handling:
//
//	dbErrHandler := errors.NewDatabaseErrorHandler()
//	user, err := queries.GetUser(ctx, userID)
//	if err != nil {
//		return dbErrHandler.HandleDatabaseError(err, "get", "user", userID)
//	}
//
// Validation Error Handling:
//
//	valErrHandler := errors.NewValidationErrorHandler()
//	if err := validator.Struct(request); err != nil {
//		return valErrHandler.HandleValidationError(c, err)
//	}
//
// Structured Logging:
//
//	logErrHandler := errors.NewLoggingErrorHandler()
//	logCtx := logErrHandler.BuildLogContextFromEcho(c)
//	logCtx = logErrHandler.WithOperation(logCtx, "create_event")
//	logErrHandler.LogError(ctx, err, logCtx, "Failed to create event")
package errors

// ErrorHandlers provides a convenient way to access all error handling utilities.
type ErrorHandlers struct {
	HTTP       *ErrorHandler
	Service    *ServiceErrorHandler
	Database   *DatabaseErrorHandler
	Validation *ValidationErrorHandler
	Logging    *LoggingErrorHandler
}

// NewErrorHandlers creates a new ErrorHandlers instance with all handlers initialized.
func NewErrorHandlers() *ErrorHandlers {
	return &ErrorHandlers{
		HTTP:       NewErrorHandler(),
		Service:    NewServiceErrorHandler(),
		Database:   NewDatabaseErrorHandler(),
		Validation: NewValidationErrorHandler(),
		Logging:    NewLoggingErrorHandler(),
	}
}

// Common error handling patterns and best practices:
//
// 1. Handler Pattern:
//    - Use ErrorHandler for consistent HTTP error responses
//    - Prefer ParseUUIDParam over manual UUID parsing
//    - Use HandleServiceError to leverage error mapping middleware
//
// 2. Service Pattern:
//    - Return domain errors using ServiceErrorHandler creators
//    - Use CheckPermission for authorization validation
//    - Wrap external service errors with appropriate context
//
// 3. Database Pattern:
//    - Always use HandleDatabaseError for database operations
//    - Let the handler map PostgreSQL errors to domain errors
//    - Log unexpected database errors for monitoring
//
// 4. Validation Pattern:
//    - Use HandleValidationError for go-playground/validator errors
//    - CreateValidationAPIError for custom validation logic
//    - Format field names consistently for API responses
//
// 5. Logging Pattern:
//    - Build LogContext from Echo context for HTTP requests
//    - Add operation and entity context to logs
//    - Use appropriate log levels (Error, Warn, Info, Debug)
//    - Include security and audit logging for sensitive operations
