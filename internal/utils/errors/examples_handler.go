// Package errors provides examples of how to use the standardized error handling utilities.
// These examples show best practices for using the error handlers in different scenarios.
package errors

import (
	"net/http"

	claims "Membership-SAAS-System-Backend/internal/auth/claims"
	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/services/event"
	"Membership-SAAS-System-Backend/internal/utils/validation"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
)

// ExampleEventHandler demonstrates how to use error handling utilities in HTTP handlers.
type ExampleEventHandler struct {
	errorHandlers *ErrorHandlers
	eventService  event.ManagementService
	validator     *validation.RequestValidator
}

// NewExampleEventHandler creates a new example handler with error handling utilities.
func NewExampleEventHandler(eventSvc event.ManagementService, val *validation.RequestValidator) *ExampleEventHandler {
	return &ExampleEventHandler{
		errorHandlers: NewErrorHandlers(),
		eventService:  eventSvc,
		validator:     val,
	}
}

// CreateEventExample demonstrates proper error handling in a create endpoint.
func (h *ExampleEventHandler) CreateEventExample(c echo.Context) error {
	// Example 1: Parse UUID parameters with automatic error handling
	orgID, err := h.errorHandlers.HTTP.ParseUUIDParam(c, "orgID", "organization ID")
	if err != nil {
		return err // Error response already sent
	}

	// Example 2: Extract user ID with proper error handling
	userID := claims.GetUserIDFromContext(c)
	if userID == uuid.Nil {
		return h.errorHandlers.HTTP.Unauthorized(c, "Authentication required", nil)
	}

	// Example 3: Parse and validate request body
	var req payloads.CreateEventRequest
	if err := c.Bind(&req); err != nil {
		return h.errorHandlers.HTTP.BadRequest(c, "Invalid request body", err)
	}

	// Example 4: Handle validation errors
	if err := h.validator.ValidateStruct(&req); err != nil {
		return h.errorHandlers.Validation.HandleValidationError(c, err)
	}

	// Example 5: Handle service errors intelligently
	// This will let domain errors be processed by error mapping middleware
	event, err := h.eventService.CreateEvent(c.Request().Context(), orgID, userID, req)
	if err != nil {
		return h.errorHandlers.HTTP.HandleServiceError(c, err, "Failed to create event")
	}

	// Example 6: Log successful operations
	logCtx := h.errorHandlers.Logging.BuildLogContextFromEcho(c)
	logCtx = h.errorHandlers.Logging.WithOperation(logCtx, "create_event")
	logCtx = h.errorHandlers.Logging.WithUserID(logCtx, userID)
	logCtx = h.errorHandlers.Logging.WithOrganizationID(logCtx, orgID)
	h.errorHandlers.Logging.LogInfo(c.Request().Context(), logCtx, "Event created successfully")

	return c.JSON(http.StatusCreated, event)
}

// GetEventExample demonstrates error handling in a get endpoint.
func (h *ExampleEventHandler) GetEventExample(c echo.Context) error {
	// Parse event ID parameter
	eventID, err := h.errorHandlers.HTTP.ParseUUIDParam(c, "eventID", "event ID")
	if err != nil {
		return err
	}

	// Extract user ID
	userID := claims.GetUserIDFromContext(c)
	if userID == uuid.Nil {
		return h.errorHandlers.HTTP.Unauthorized(c, "", nil)
	}

	// Get event with intelligent error handling
	event, err := h.eventService.GetEventByID(c.Request().Context(), eventID, userID, false)
	if err != nil {
		// This will handle domain errors like EventNotFoundError appropriately
		return h.errorHandlers.HTTP.HandleServiceError(c, err, "Failed to get event")
	}

	return c.JSON(http.StatusOK, event)
}

// UpdateEventExample demonstrates error handling in an update endpoint.
func (h *ExampleEventHandler) UpdateEventExample(c echo.Context) error {
	// Parse parameters
	eventID, err := h.errorHandlers.HTTP.ParseUUIDParam(c, "eventID", "event ID")
	if err != nil {
		return err
	}

	orgID, err := h.errorHandlers.HTTP.ParseUUIDParam(c, "orgID", "organization ID")
	if err != nil {
		return err
	}

	userID := claims.GetUserIDFromContext(c)
	if userID == uuid.Nil {
		return h.errorHandlers.HTTP.Unauthorized(c, "", nil)
	}

	// Parse and validate request
	var req payloads.UpdateEventRequest
	if err := c.Bind(&req); err != nil {
		return h.errorHandlers.HTTP.BadRequest(c, "Invalid request body", err)
	}

	if err := h.validator.ValidateStruct(&req); err != nil {
		return h.errorHandlers.Validation.HandleValidationError(c, err)
	}

	// Update event
	event, err := h.eventService.UpdateEventDetails(c.Request().Context(), eventID, orgID, userID, req)
	if err != nil {
		return h.errorHandlers.HTTP.HandleServiceError(c, err, "Failed to update event")
	}

	return c.JSON(http.StatusOK, event)
}

// DeleteEventExample demonstrates error handling in a delete endpoint.
func (h *ExampleEventHandler) DeleteEventExample(c echo.Context) error {
	// Parse parameters
	eventID, err := h.errorHandlers.HTTP.ParseUUIDParam(c, "eventID", "event ID")
	if err != nil {
		return err
	}

	orgID, err := h.errorHandlers.HTTP.ParseUUIDParam(c, "orgID", "organization ID")
	if err != nil {
		return err
	}

	userID := claims.GetUserIDFromContext(c)
	if userID == uuid.Nil {
		return h.errorHandlers.HTTP.Unauthorized(c, "", nil)
	}

	// Delete event
	err = h.eventService.DeleteEvent(c.Request().Context(), eventID, orgID, userID)
	if err != nil {
		return h.errorHandlers.HTTP.HandleServiceError(c, err, "Failed to delete event")
	}

	// Log audit event for deletion
	logCtx := h.errorHandlers.Logging.BuildLogContextFromEcho(c)
	logCtx = h.errorHandlers.Logging.WithOperation(logCtx, "delete_event")
	logCtx = h.errorHandlers.Logging.WithUserID(logCtx, userID)
	logCtx = h.errorHandlers.Logging.WithOrganizationID(logCtx, orgID)
	h.errorHandlers.Logging.LogAuditEvent(
		c.Request().Context(),
		"event_deleted",
		logCtx,
		"success",
		map[string]interface{}{
			"event_id": eventID.String(),
		},
	)

	return c.NoContent(http.StatusNoContent)
}

// ListEventsExample demonstrates error handling with query parameters.
func (h *ExampleEventHandler) ListEventsExample(c echo.Context) error {
	// Parse organization ID
	orgID, err := h.errorHandlers.HTTP.ParseUUIDParam(c, "orgID", "organization ID")
	if err != nil {
		return err
	}

	userID := claims.GetUserIDFromContext(c)
	if userID == uuid.Nil {
		return h.errorHandlers.HTTP.Unauthorized(c, "", nil)
	}

	// Parse query parameters with validation
	var filterParams payloads.ListOrganizationEventsRequest
	if err := c.Bind(&filterParams); err != nil {
		return h.errorHandlers.HTTP.BadRequest(c, "Invalid query parameters", err)
	}

	// Validate filter parameters
	if err := h.validator.ValidateStruct(&filterParams); err != nil {
		return h.errorHandlers.Validation.HandleValidationError(c, err)
	}

	// Get events
	events, total, err := h.eventService.ListEventsByOrganization(c.Request().Context(), orgID, userID, filterParams)
	if err != nil {
		return h.errorHandlers.HTTP.HandleServiceError(c, err, "Failed to list events")
	}

	// Return with pagination headers
	response := map[string]interface{}{
		"events": events,
		"total":  total,
	}

	return c.JSON(http.StatusOK, response)
}

// Advanced Error Handling Examples

// CustomValidationExample demonstrates custom validation with error handlers.
func (h *ExampleEventHandler) CustomValidationExample(c echo.Context) error {
	// Parse request
	var req map[string]interface{}
	if err := c.Bind(&req); err != nil {
		return h.errorHandlers.HTTP.BadRequest(c, "Invalid JSON", err)
	}

	// Custom validation using validation error handler
	if err := h.errorHandlers.Validation.RequiredFields(req, "title", "description", "start_time"); err != nil {
		return err
	}

	// Validate enum values
	if status, ok := req["status"].(string); ok {
		allowedStatuses := []string{"draft", "published", "cancelled"}
		if err := h.errorHandlers.Validation.ValidateEnum(status, allowedStatuses, "status"); err != nil {
			return err
		}
	}

	return c.JSON(http.StatusOK, map[string]string{"message": "Validation passed"})
}

// DatabaseErrorExample demonstrates handling database errors in a handler.
func (h *ExampleEventHandler) DatabaseErrorExample(c echo.Context) error {
	// This would typically be in a service, but shown here for example
	eventID, err := h.errorHandlers.HTTP.ParseUUIDParam(c, "eventID", "event ID")
	if err != nil {
		return err
	}

	// Example of database operation with error handling
	// In real code, this would be in the service layer
	/*
		event, err := queries.GetEvent(ctx, eventID)
		if err != nil {
			// Database error handler would map pgx.ErrNoRows to EventNotFoundError
			return h.errorHandlers.Database.HandleDatabaseError(err, "get", "event", eventID)
		}
	*/

	// For demonstration, simulate different error scenarios
	switch c.QueryParam("error_type") {
	case "not_found":
		return h.errorHandlers.Service.EventNotFound(eventID)
	case "permission":
		userID := claims.GetUserIDFromContext(c)
		return h.errorHandlers.Service.InsufficientPermissions(userID, "admin", "view_event")
	case "validation":
		return h.errorHandlers.Service.ValidationError("status", "invalid enum value", "invalid_status")
	default:
		return c.JSON(http.StatusOK, map[string]string{"message": "Success"})
	}
}
