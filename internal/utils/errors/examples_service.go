// Package errors provides examples of how to use error handling utilities in service layer.
// These examples demonstrate best practices for creating domain errors and handling
// business logic errors in services.
package errors

import (
	"context"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/payloads"

	"github.com/google/uuid"
)

// ExampleServiceErrorHandling demonstrates how to use error handling utilities in services.
type ExampleServiceErrorHandling struct {
	store         db.Store
	errorHandlers *ErrorHandlers
}

// NewExampleServiceErrorHandling creates a new example service with error handling utilities.
func NewExampleServiceErrorHandling(store db.Store) *ExampleServiceErrorHandling {
	return &ExampleServiceErrorHandling{
		store:         store,
		errorHandlers: NewErrorHandlers(),
	}
}

// ValidationExample demonstrates input validation using service error handlers.
func (s *ExampleServiceErrorHandling) ValidationExample(ctx context.Context, req payloads.CreateEventRequest) error {
	// Example 1: Validate required fields
	if err := s.errorHandlers.Service.RequireNonEmptyString(req.Title, "title"); err != nil {
		return err
	}

	// Example 2: Business logic validation
	if req.ParticipantLimit != nil && *req.ParticipantLimit < 1 {
		return s.errorHandlers.Service.ValidationError(
			"participant_limit",
			"must be greater than 0",
			*req.ParticipantLimit,
		)
	}

	return nil
}

// PermissionCheckExample demonstrates permission validation patterns.
func (s *ExampleServiceErrorHandling) PermissionCheckExample(ctx context.Context, userID uuid.UUID, action string) error {
	// Simulate permission check
	hasPermission := true // In real code, this would check user roles

	// Use error handler to validate permission
	if err := s.errorHandlers.Service.CheckPermission(hasPermission, userID, "admin", action); err != nil {
		return err
	}

	return nil
}

// DatabaseErrorExample demonstrates database error handling patterns.
func (s *ExampleServiceErrorHandling) DatabaseErrorExample(ctx context.Context, userID uuid.UUID) error {
	// Example of database operation with error handling
	// In real code, this would be actual database queries
	err := s.simulateDatabaseOperation(ctx, userID)
	if err != nil {
		// Database error handler maps database errors to domain errors
		return s.errorHandlers.Database.HandleDatabaseError(err, "get", "user", userID)
	}

	return nil
}

// TransactionExample demonstrates transaction error handling.
func (s *ExampleServiceErrorHandling) TransactionExample(ctx context.Context, userID uuid.UUID) error {
	err := s.store.ExecTx(ctx, func(q db.Querier) error {
		// Example transaction operations
		// In real code, these would be actual database operations

		// Simulate first operation
		if err := s.simulateOperation("first_operation"); err != nil {
			return s.errorHandlers.Database.HandleDatabaseError(err, "create", "entity", uuid.Nil)
		}

		// Simulate second operation
		if err := s.simulateOperation("second_operation"); err != nil {
			return s.errorHandlers.Database.HandleDatabaseError(err, "update", "entity", userID)
		}

		return nil
	})
	if err != nil {
		// Log the error with context
		logCtx := &LogContext{
			UserID:    userID,
			Operation: "transaction_example",
		}
		s.errorHandlers.Logging.LogError(ctx, err, logCtx, "Transaction failed")
		return err
	}

	return nil
}

// DomainErrorsExample demonstrates creating specific domain errors.
func (s *ExampleServiceErrorHandling) DomainErrorsExample(ctx context.Context, userID, eventID uuid.UUID) error {
	// User-related errors
	if userID == uuid.Nil {
		return s.errorHandlers.Service.UserNotFound(userID)
	}

	// Event-related errors
	if eventID == uuid.Nil {
		return s.errorHandlers.Service.EventNotFound(eventID)
	}

	// Permission errors
	return s.errorHandlers.Service.InsufficientPermissions(userID, "manager", "create_event")
}

// LoggingExample demonstrates structured logging with error handlers.
func (s *ExampleServiceErrorHandling) LoggingExample(ctx context.Context, userID uuid.UUID, operation string) {
	// Build log context
	logCtx := &LogContext{
		UserID:    userID,
		Operation: operation,
	}

	// Add custom fields
	logCtx = s.errorHandlers.Logging.WithCustomField(logCtx, "component", "example_service")

	// Log different types of events
	s.errorHandlers.Logging.LogInfo(ctx, logCtx, "Operation started")

	// Simulate error scenario
	err := s.simulateOperation(operation)
	if err != nil {
		s.errorHandlers.Logging.LogError(ctx, err, logCtx, "Operation failed")
		return
	}

	// Log successful business event
	s.errorHandlers.Logging.LogBusinessEvent(ctx, "operation_completed", logCtx, map[string]interface{}{
		"operation_type": operation,
		"success":        true,
	})

	// Log audit event
	s.errorHandlers.Logging.LogAuditEvent(ctx, operation, logCtx, "success", map[string]interface{}{
		"user_action": operation,
	})
}

// ErrorWrappingExample demonstrates error wrapping patterns.
func (s *ExampleServiceErrorHandling) ErrorWrappingExample(ctx context.Context) error {
	// Simulate external service call
	err := s.simulateExternalServiceCall()
	if err != nil {
		return s.errorHandlers.Service.WrapExternalServiceError(err, "notification_service")
	}

	// Simulate database operation
	err = s.simulateDatabaseOperation(ctx, uuid.New())
	if err != nil {
		return s.errorHandlers.Service.WrapDatabaseError(err, "user_lookup")
	}

	// Log and wrap error
	err = s.simulateOperation("complex_operation")
	if err != nil {
		context := map[string]interface{}{
			"operation": "complex_operation",
			"component": "example_service",
		}
		return s.errorHandlers.Service.LogAndWrapError(err, context, "Complex operation failed")
	}

	return nil
}

// Helper methods for simulation (in real code, these would be actual operations)

func (s *ExampleServiceErrorHandling) simulateDatabaseOperation(ctx context.Context, userID uuid.UUID) error {
	// Simulate different database error scenarios
	return nil
}

func (s *ExampleServiceErrorHandling) simulateOperation(operation string) error {
	// Simulate operation that might fail
	return nil
}

func (s *ExampleServiceErrorHandling) simulateExternalServiceCall() error {
	// Simulate external service call
	return nil
}
