// Package errors provides standardized error handling utilities for HTTP handlers.
// These utilities build upon the existing error infrastructure to provide consistent
// error responses, proper logging, and simplified error handling patterns.
package errors

import (
	"errors"
	"fmt"
	"net/http"

	"Membership-SAAS-System-Backend/internal/apierror"
	"Membership-SAAS-System-Backend/internal/domain"
	httputil "Membership-SAAS-System-Backend/internal/utils/http"

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/rs/zerolog/log"
)

// ErrorHandler provides standardized error handling methods for HTTP handlers.
// It wraps the existing httputil.HandleError function with convenient helper methods
// for common error scenarios.
type ErrorHandler struct{}

// NewErrorHandler creates a new ErrorHandler instance.
func NewErrorHandler() *ErrorHandler {
	return &ErrorHandler{}
}

// BadRequest handles 400 Bad Request errors with consistent formatting.
func (h *ErrorHandler) BadRequest(c echo.Context, message string, err error) error {
	return httputil.HandleError(c, http.StatusBadRequest, message, err)
}

// BadRequestf handles 400 Bad Request errors with formatted message.
func (h *ErrorHandler) BadRequestf(c echo.Context, format string, args ...interface{}) error {
	message := fmt.Sprintf(format, args...)
	return httputil.HandleError(c, http.StatusBadRequest, message, nil)
}

// Unauthorized handles 401 Unauthorized errors.
func (h *ErrorHandler) Unauthorized(c echo.Context, message string, err error) error {
	if message == "" {
		message = "Authentication required"
	}
	return httputil.HandleError(c, http.StatusUnauthorized, message, err)
}

// Forbidden handles 403 Forbidden errors.
func (h *ErrorHandler) Forbidden(c echo.Context, message string, err error) error {
	if message == "" {
		message = "Access denied"
	}
	return httputil.HandleError(c, http.StatusForbidden, message, err)
}

// NotFound handles 404 Not Found errors.
func (h *ErrorHandler) NotFound(c echo.Context, message string, err error) error {
	if message == "" {
		message = "Resource not found"
	}
	return httputil.HandleError(c, http.StatusNotFound, message, err)
}

// Conflict handles 409 Conflict errors.
func (h *ErrorHandler) Conflict(c echo.Context, message string, err error) error {
	if message == "" {
		message = "Resource conflict"
	}
	return httputil.HandleError(c, http.StatusConflict, message, err)
}

// InternalServerError handles 500 Internal Server Error responses.
func (h *ErrorHandler) InternalServerError(c echo.Context, message string, err error) error {
	if message == "" {
		message = "Internal server error"
	}
	return httputil.HandleError(c, http.StatusInternalServerError, message, err)
}

// ValidationError handles validation errors from go-playground/validator.
// It extracts field-specific validation errors and formats them consistently.
func (h *ErrorHandler) ValidationError(c echo.Context, err error) error {
	var validationErrs validator.ValidationErrors
	if errors.As(err, &validationErrs) {
		// Extract first validation error for simplified message
		if len(validationErrs) > 0 {
			firstErr := validationErrs[0]
			message := fmt.Sprintf("Validation failed for field '%s': %s", firstErr.Field(), firstErr.Tag())
			return httputil.HandleError(c, http.StatusBadRequest, message, err)
		}
	}
	return httputil.HandleError(c, http.StatusBadRequest, "Validation failed", err)
}

// InvalidUUID handles UUID parsing errors with standardized message.
func (h *ErrorHandler) InvalidUUID(c echo.Context, fieldName string, err error) error {
	message := fmt.Sprintf("Invalid %s format", fieldName)
	return httputil.HandleError(c, http.StatusBadRequest, message, err)
}

// ParseUUIDParam parses a UUID parameter and returns appropriate error if invalid.
func (h *ErrorHandler) ParseUUIDParam(c echo.Context, paramName, fieldName string) (uuid.UUID, error) {
	paramValue := c.Param(paramName)
	if paramValue == "" {
		err := h.BadRequestf(c, "Missing required parameter: %s", paramName)
		return uuid.Nil, err
	}

	parsedUUID, parseErr := uuid.Parse(paramValue)
	if parseErr != nil {
		err := h.InvalidUUID(c, fieldName, parseErr)
		return uuid.Nil, err
	}

	return parsedUUID, nil
}

// HandleServiceError intelligently handles service layer errors by checking for
// domain errors first, then falling back to generic error handling.
func (h *ErrorHandler) HandleServiceError(c echo.Context, err error, defaultMessage string) error {
	if err == nil {
		return nil
	}

	// Check for domain errors that should be handled by error mapping middleware
	if isDomainError(err) {
		// Return the error to let the error mapping middleware handle it
		return err
	}

	// Handle common service errors
	switch {
	case errors.Is(err, fmt.Errorf("not found")):
		return h.NotFound(c, defaultMessage, err)
	case errors.Is(err, fmt.Errorf("unauthorized")):
		return h.Unauthorized(c, defaultMessage, err)
	case errors.Is(err, fmt.Errorf("forbidden")):
		return h.Forbidden(c, defaultMessage, err)
	default:
		// Log unexpected errors for debugging
		log.Ctx(c.Request().Context()).Error().
			Err(err).
			Str("path", c.Request().URL.Path).
			Str("method", c.Request().Method).
			Msg("Unhandled service error")
		return h.InternalServerError(c, defaultMessage, err)
	}
}

// isDomainError checks if the error is a domain error that should be handled
// by the error mapping middleware instead of generic error handling.
func isDomainError(err error) bool {
	// Check for all domain error types
	var (
		userNotFound         *domain.UserNotFoundError
		duplicateEmail       *domain.DuplicateEmailError
		duplicatePhone       *domain.DuplicatePhoneError
		invalidCreds         *domain.InvalidCredentialsError
		orgNotFound          *domain.OrganizationNotFoundError
		notMember            *domain.NotMemberOfOrganizationError
		alreadyMember        *domain.AlreadyMemberOfOrganizationError
		eventNotFound        *domain.EventNotFoundError
		regClosed            *domain.EventRegistrationClosedError
		regFull              *domain.EventRegistrationFullError
		alreadyRegistered    *domain.AlreadyRegisteredError
		regNotFound          *domain.RegistrationNotFoundError
		volunteerAppNotFound *domain.VolunteerApplicationNotFoundError
		alreadyApplied       *domain.AlreadyAppliedToVolunteerError
		notQualified         *domain.NotQualifiedToVolunteerError
		validationErr        *domain.ValidationError
		insufficientPerms    *domain.InsufficientPermissionsError
		resourceNotFound     *domain.ResourceNotFoundError
		fileErr              *domain.FileProcessingError
		passwordNotSet       *domain.PasswordNotSetError
		weakPassword         *domain.WeakPasswordError
		samePassword         *domain.SamePasswordError
		rateLimitErr         *domain.RateLimitExceededError
	)

	return errors.As(err, &userNotFound) ||
		errors.As(err, &duplicateEmail) ||
		errors.As(err, &duplicatePhone) ||
		errors.As(err, &invalidCreds) ||
		errors.As(err, &orgNotFound) ||
		errors.As(err, &notMember) ||
		errors.As(err, &alreadyMember) ||
		errors.As(err, &eventNotFound) ||
		errors.As(err, &regClosed) ||
		errors.As(err, &regFull) ||
		errors.As(err, &alreadyRegistered) ||
		errors.As(err, &regNotFound) ||
		errors.As(err, &volunteerAppNotFound) ||
		errors.As(err, &alreadyApplied) ||
		errors.As(err, &notQualified) ||
		errors.As(err, &validationErr) ||
		errors.As(err, &insufficientPerms) ||
		errors.As(err, &resourceNotFound) ||
		errors.As(err, &fileErr) ||
		errors.As(err, &passwordNotSet) ||
		errors.As(err, &weakPassword) ||
		errors.As(err, &samePassword) ||
		errors.As(err, &rateLimitErr)
}

// ReturnAPIError returns an APIError directly, bypassing the error mapping middleware.
// Use this when you need fine-grained control over the exact API error response.
func (h *ErrorHandler) ReturnAPIError(c echo.Context, apiErr *apierror.APIError) error {
	// Log the error for debugging
	log.Ctx(c.Request().Context()).Error().
		Err(apiErr.Err).
		Int("status_code", apiErr.Status).
		Str("error_code", apiErr.Code).
		Str("path", c.Request().URL.Path).
		Msg("Returning API error")

	return c.JSON(apiErr.Status, apiErr)
}

// WrapError wraps a service error with additional context and returns it.
// This preserves the original error while adding contextual information.
func (h *ErrorHandler) WrapError(err error, context string) error {
	if err == nil {
		return nil
	}
	return fmt.Errorf("%s: %w", context, err)
}
