// Package errors provides logging utilities for error handling.
// These utilities provide consistent error logging patterns across the application
// with appropriate context and structured logging using zerolog.
package errors

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
)

// LoggingErrorHandler provides utilities for error logging.
type LoggingErrorHandler struct{}

// NewLoggingErrorHandler creates a new LoggingErrorHandler instance.
func NewLoggingErrorHandler() *LoggingErrorHandler {
	return &LoggingErrorHandler{}
}

// LogContext represents the context information for error logging.
type LogContext struct {
	UserID         uuid.UUID              `json:"user_id,omitempty"`
	OrganizationID uuid.UUID              `json:"organization_id,omitempty"`
	EventID        uuid.UUID              `json:"event_id,omitempty"`
	RequestID      string                 `json:"request_id,omitempty"`
	Operation      string                 `json:"operation,omitempty"`
	Path           string                 `json:"path,omitempty"`
	Method         string                 `json:"method,omitempty"`
	StatusCode     int                    `json:"status_code,omitempty"`
	UserAgent      string                 `json:"user_agent,omitempty"`
	RemoteIP       string                 `json:"remote_ip,omitempty"`
	Duration       time.Duration          `json:"duration,omitempty"`
	CustomFields   map[string]interface{} `json:"custom_fields,omitempty"`
}

// BuildLogContextFromEcho builds a LogContext from an Echo context.
func (h *LoggingErrorHandler) BuildLogContextFromEcho(c echo.Context) *LogContext {
	ctx := &LogContext{
		Path:      c.Request().URL.Path,
		Method:    c.Request().Method,
		UserAgent: c.Request().UserAgent(),
		RemoteIP:  c.RealIP(),
	}

	// Extract request ID if available
	if requestID := c.Response().Header().Get(echo.HeaderXRequestID); requestID != "" {
		ctx.RequestID = requestID
	}

	return ctx
}

// LogError logs an error with appropriate level and context.
func (h *LoggingErrorHandler) LogError(ctx context.Context, err error, logCtx *LogContext, message string) {
	if err == nil {
		return
	}

	// Build log event
	logEvent := log.Ctx(ctx).Error().Err(err)

	// Add context fields
	h.addLogContextFields(logEvent, logCtx)

	logEvent.Msg(message)
}

// LogWarn logs a warning with context.
func (h *LoggingErrorHandler) LogWarn(ctx context.Context, err error, logCtx *LogContext, message string) {
	logEvent := log.Ctx(ctx).Warn()

	if err != nil {
		logEvent = logEvent.Err(err)
	}

	// Add context fields
	h.addLogContextFields(logEvent, logCtx)

	logEvent.Msg(message)
}

// LogInfo logs an informational message with context.
func (h *LoggingErrorHandler) LogInfo(ctx context.Context, logCtx *LogContext, message string) {
	logEvent := log.Ctx(ctx).Info()

	// Add context fields
	h.addLogContextFields(logEvent, logCtx)

	logEvent.Msg(message)
}

// LogDebug logs a debug message with context.
func (h *LoggingErrorHandler) LogDebug(ctx context.Context, logCtx *LogContext, message string) {
	logEvent := log.Ctx(ctx).Debug()

	// Add context fields
	h.addLogContextFields(logEvent, logCtx)

	logEvent.Msg(message)
}

// addLogContextFields adds LogContext fields to a zerolog event.
func (h *LoggingErrorHandler) addLogContextFields(logEvent *zerolog.Event, logCtx *LogContext) {
	if logCtx == nil {
		return
	}

	if logCtx.UserID != uuid.Nil {
		logEvent.Str("user_id", logCtx.UserID.String())
	}
	if logCtx.OrganizationID != uuid.Nil {
		logEvent.Str("organization_id", logCtx.OrganizationID.String())
	}
	if logCtx.EventID != uuid.Nil {
		logEvent.Str("event_id", logCtx.EventID.String())
	}
	if logCtx.RequestID != "" {
		logEvent.Str("request_id", logCtx.RequestID)
	}
	if logCtx.Operation != "" {
		logEvent.Str("operation", logCtx.Operation)
	}
	if logCtx.Path != "" {
		logEvent.Str("path", logCtx.Path)
	}
	if logCtx.Method != "" {
		logEvent.Str("method", logCtx.Method)
	}
	if logCtx.StatusCode != 0 {
		logEvent.Int("status_code", logCtx.StatusCode)
	}
	if logCtx.UserAgent != "" {
		logEvent.Str("user_agent", logCtx.UserAgent)
	}
	if logCtx.RemoteIP != "" {
		logEvent.Str("remote_ip", logCtx.RemoteIP)
	}
	if logCtx.Duration > 0 {
		logEvent.Dur("duration", logCtx.Duration)
	}

	// Add custom fields
	for key, value := range logCtx.CustomFields {
		logEvent.Interface(key, value)
	}
}

// Security and Audit Logging

// LogSecurityEvent logs security-related events with enhanced context.
func (h *LoggingErrorHandler) LogSecurityEvent(ctx context.Context, event string, logCtx *LogContext, details map[string]interface{}) {
	logEvent := log.Ctx(ctx).Warn().
		Str("security_event", event)

	// Add standard context
	h.addLogContextFields(logEvent, logCtx)

	// Add security-specific details
	for key, value := range details {
		logEvent.Interface(key, value)
	}

	logEvent.Msg("Security event detected")
}

// LogAuditEvent logs audit events for compliance and tracking.
func (h *LoggingErrorHandler) LogAuditEvent(ctx context.Context, action string, logCtx *LogContext, result string, details map[string]interface{}) {
	logEvent := log.Ctx(ctx).Info().
		Str("audit_action", action).
		Str("audit_result", result)

	// Add standard context
	h.addLogContextFields(logEvent, logCtx)

	// Add audit-specific details
	for key, value := range details {
		logEvent.Interface(key, value)
	}

	logEvent.Msg("Audit event")
}

// Performance Logging

// LogSlowQuery logs database queries that exceed the threshold.
func (h *LoggingErrorHandler) LogSlowQuery(ctx context.Context, query string, duration time.Duration, logCtx *LogContext) {
	logEvent := log.Ctx(ctx).Warn().
		Str("slow_query", query).
		Dur("duration", duration)

	h.addLogContextFields(logEvent, logCtx)

	logEvent.Msg("Slow database query detected")
}

// LogPerformanceIssue logs performance-related issues.
func (h *LoggingErrorHandler) LogPerformanceIssue(ctx context.Context, issue string, metrics map[string]interface{}, logCtx *LogContext) {
	logEvent := log.Ctx(ctx).Warn().
		Str("performance_issue", issue)

	// Add metrics
	for key, value := range metrics {
		logEvent.Interface(key, value)
	}

	h.addLogContextFields(logEvent, logCtx)

	logEvent.Msg("Performance issue detected")
}

// Business Logic Logging

// LogBusinessEvent logs important business events.
func (h *LoggingErrorHandler) LogBusinessEvent(ctx context.Context, event string, logCtx *LogContext, data map[string]interface{}) {
	logEvent := log.Ctx(ctx).Info().
		Str("business_event", event)

	// Add business data
	for key, value := range data {
		logEvent.Interface(key, value)
	}

	h.addLogContextFields(logEvent, logCtx)

	logEvent.Msg("Business event")
}

// LogUserAction logs user actions for analytics and debugging.
func (h *LoggingErrorHandler) LogUserAction(ctx context.Context, action string, logCtx *LogContext, success bool, details map[string]interface{}) {
	var logEvent *zerolog.Event
	if success {
		logEvent = log.Ctx(ctx).Info()
	} else {
		logEvent = log.Ctx(ctx).Warn()
	}

	logEvent = logEvent.
		Str("user_action", action).
		Bool("success", success)

	// Add action details
	for key, value := range details {
		logEvent.Interface(key, value)
	}

	h.addLogContextFields(logEvent, logCtx)

	logEvent.Msg("User action")
}

// Utility Methods

// WithOperation adds operation context to LogContext.
func (h *LoggingErrorHandler) WithOperation(logCtx *LogContext, operation string) *LogContext {
	if logCtx == nil {
		logCtx = &LogContext{}
	}
	logCtx.Operation = operation
	return logCtx
}

// WithUserID adds user ID context to LogContext.
func (h *LoggingErrorHandler) WithUserID(logCtx *LogContext, userID uuid.UUID) *LogContext {
	if logCtx == nil {
		logCtx = &LogContext{}
	}
	logCtx.UserID = userID
	return logCtx
}

// WithOrganizationID adds organization ID context to LogContext.
func (h *LoggingErrorHandler) WithOrganizationID(logCtx *LogContext, orgID uuid.UUID) *LogContext {
	if logCtx == nil {
		logCtx = &LogContext{}
	}
	logCtx.OrganizationID = orgID
	return logCtx
}

// WithCustomField adds a custom field to LogContext.
func (h *LoggingErrorHandler) WithCustomField(logCtx *LogContext, key string, value interface{}) *LogContext {
	if logCtx == nil {
		logCtx = &LogContext{}
	}
	if logCtx.CustomFields == nil {
		logCtx.CustomFields = make(map[string]interface{})
	}
	logCtx.CustomFields[key] = value
	return logCtx
}

// IsErrorLoggingEnabled checks if error logging is enabled for the given context.
// This can be used to conditionally log errors based on configuration.
func (h *LoggingErrorHandler) IsErrorLoggingEnabled(ctx context.Context) bool {
	// For now, always return true. In the future, this could check
	// configuration settings or context values to determine if logging
	// should be performed.
	return true
}
