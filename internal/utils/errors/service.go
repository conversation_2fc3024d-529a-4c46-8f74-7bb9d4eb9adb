// Package errors provides service layer error handling utilities.
// These utilities help services create and return standardized errors
// that can be properly handled by the error mapping middleware.
package errors

import (
	"fmt"

	"Membership-SAAS-System-Backend/internal/apierror"
	"Membership-SAAS-System-Backend/internal/domain"

	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
)

// ServiceErrorHandler provides utilities for service layer error handling.
// It helps create domain errors and wrap business logic errors consistently.
type ServiceErrorHandler struct{}

// NewServiceErrorHandler creates a new ServiceErrorHandler instance.
func NewServiceErrorHandler() *ServiceErrorHandler {
	return &ServiceErrorHandler{}
}

// User Error Creators

// UserNotFound creates a UserNotFoundError for the specified user.
func (h *ServiceErrorHandler) UserNotFound(userID uuid.UUID) *domain.UserNotFoundError {
	return &domain.UserNotFoundError{UserID: userID}
}

// UserNotFoundByEmail creates a UserNotFoundError for the specified email.
func (h *ServiceErrorHandler) UserNotFoundByEmail(email string) *domain.UserNotFoundError {
	return &domain.UserNotFoundError{Email: email}
}

// UserNotFoundByPhone creates a UserNotFoundError for the specified phone.
func (h *ServiceErrorHandler) UserNotFoundByPhone(phone string) *domain.UserNotFoundError {
	return &domain.UserNotFoundError{Phone: phone}
}

// DuplicateEmail creates a DuplicateEmailError.
func (h *ServiceErrorHandler) DuplicateEmail(email string) *domain.DuplicateEmailError {
	return &domain.DuplicateEmailError{Email: email}
}

// DuplicatePhone creates a DuplicatePhoneError.
func (h *ServiceErrorHandler) DuplicatePhone(phone string) *domain.DuplicatePhoneError {
	return &domain.DuplicatePhoneError{Phone: phone}
}

// InvalidCredentials creates an InvalidCredentialsError.
func (h *ServiceErrorHandler) InvalidCredentials(identifier string) *domain.InvalidCredentialsError {
	return &domain.InvalidCredentialsError{Identifier: identifier}
}

// Organization Error Creators

// OrganizationNotFound creates an OrganizationNotFoundError.
func (h *ServiceErrorHandler) OrganizationNotFound(orgID uuid.UUID) *domain.OrganizationNotFoundError {
	return &domain.OrganizationNotFoundError{OrgID: orgID}
}

// NotMemberOfOrganization creates a NotMemberOfOrganizationError.
func (h *ServiceErrorHandler) NotMemberOfOrganization(userID, orgID uuid.UUID) *domain.NotMemberOfOrganizationError {
	return &domain.NotMemberOfOrganizationError{UserID: userID, OrgID: orgID}
}

// AlreadyMemberOfOrganization creates an AlreadyMemberOfOrganizationError.
func (h *ServiceErrorHandler) AlreadyMemberOfOrganization(userID, orgID uuid.UUID) *domain.AlreadyMemberOfOrganizationError {
	return &domain.AlreadyMemberOfOrganizationError{UserID: userID, OrgID: orgID}
}

// Event Error Creators

// EventNotFound creates an EventNotFoundError.
func (h *ServiceErrorHandler) EventNotFound(eventID uuid.UUID) *domain.EventNotFoundError {
	return &domain.EventNotFoundError{EventID: eventID}
}

// EventRegistrationClosed creates an EventRegistrationClosedError.
func (h *ServiceErrorHandler) EventRegistrationClosed(eventID uuid.UUID) *domain.EventRegistrationClosedError {
	return &domain.EventRegistrationClosedError{EventID: eventID}
}

// EventRegistrationFull creates an EventRegistrationFullError.
func (h *ServiceErrorHandler) EventRegistrationFull(eventID uuid.UUID, capacity int, waitlisted bool) *domain.EventRegistrationFullError {
	return &domain.EventRegistrationFullError{
		EventID:    eventID,
		Capacity:   capacity,
		Waitlisted: waitlisted,
	}
}

// AlreadyRegistered creates an AlreadyRegisteredError.
func (h *ServiceErrorHandler) AlreadyRegistered(userID, eventID uuid.UUID) *domain.AlreadyRegisteredError {
	return &domain.AlreadyRegisteredError{UserID: userID, EventID: eventID}
}

// RegistrationNotFound creates a RegistrationNotFoundError.
func (h *ServiceErrorHandler) RegistrationNotFound(registrationID uuid.UUID) *domain.RegistrationNotFoundError {
	return &domain.RegistrationNotFoundError{RegistrationID: registrationID}
}

// RegistrationNotFoundForUser creates a RegistrationNotFoundError for a specific user and event.
func (h *ServiceErrorHandler) RegistrationNotFoundForUser(userID, eventID uuid.UUID) *domain.RegistrationNotFoundError {
	return &domain.RegistrationNotFoundError{UserID: userID, EventID: eventID}
}

// Volunteer Error Creators

// VolunteerApplicationNotFound creates a VolunteerApplicationNotFoundError.
func (h *ServiceErrorHandler) VolunteerApplicationNotFound(applicationID uuid.UUID) *domain.VolunteerApplicationNotFoundError {
	return &domain.VolunteerApplicationNotFoundError{ApplicationID: applicationID}
}

// VolunteerApplicationNotFoundForUser creates a VolunteerApplicationNotFoundError for a specific user and event.
func (h *ServiceErrorHandler) VolunteerApplicationNotFoundForUser(userID, eventID uuid.UUID) *domain.VolunteerApplicationNotFoundError {
	return &domain.VolunteerApplicationNotFoundError{UserID: userID, EventID: eventID}
}

// AlreadyAppliedToVolunteer creates an AlreadyAppliedToVolunteerError.
func (h *ServiceErrorHandler) AlreadyAppliedToVolunteer(userID, eventID uuid.UUID) *domain.AlreadyAppliedToVolunteerError {
	return &domain.AlreadyAppliedToVolunteerError{UserID: userID, EventID: eventID}
}

// NotQualifiedToVolunteer creates a NotQualifiedToVolunteerError.
func (h *ServiceErrorHandler) NotQualifiedToVolunteer(userID, orgID uuid.UUID, reason string) *domain.NotQualifiedToVolunteerError {
	return &domain.NotQualifiedToVolunteerError{
		UserID: userID,
		OrgID:  orgID,
		Reason: reason,
	}
}

// Validation and Permission Error Creators

// ValidationError creates a ValidationError.
func (h *ServiceErrorHandler) ValidationError(field, reason string, value interface{}) *domain.ValidationError {
	return &domain.ValidationError{
		Field:  field,
		Reason: reason,
		Value:  value,
	}
}

// InsufficientPermissions creates an InsufficientPermissionsError.
func (h *ServiceErrorHandler) InsufficientPermissions(userID uuid.UUID, requiredRole, action string) *domain.InsufficientPermissionsError {
	return &domain.InsufficientPermissionsError{
		UserID:       userID,
		RequiredRole: requiredRole,
		Action:       action,
	}
}

// Resource Error Creators

// ResourceNotFound creates a ResourceNotFoundError.
func (h *ServiceErrorHandler) ResourceNotFound(resourceID uuid.UUID, resourceType string) *domain.ResourceNotFoundError {
	return &domain.ResourceNotFoundError{
		ResourceID: resourceID,
		Type:       resourceType,
	}
}

// FileProcessingError creates a FileProcessingError.
func (h *ServiceErrorHandler) FileProcessingError(filename, reason string) *domain.FileProcessingError {
	return &domain.FileProcessingError{
		Filename: filename,
		Reason:   reason,
	}
}

// Password Error Creators

// PasswordNotSet creates a PasswordNotSetError.
func (h *ServiceErrorHandler) PasswordNotSet(userID uuid.UUID) *domain.PasswordNotSetError {
	return &domain.PasswordNotSetError{UserID: userID}
}

// WeakPassword creates a WeakPasswordError.
func (h *ServiceErrorHandler) WeakPassword(reason string) *domain.WeakPasswordError {
	return &domain.WeakPasswordError{Reason: reason}
}

// SamePassword creates a SamePasswordError.
func (h *ServiceErrorHandler) SamePassword(userID uuid.UUID) *domain.SamePasswordError {
	return &domain.SamePasswordError{UserID: userID}
}

// RateLimitExceeded creates a RateLimitExceededError.
func (h *ServiceErrorHandler) RateLimitExceeded(userID uuid.UUID, remainingMinutes int) *domain.RateLimitExceededError {
	return &domain.RateLimitExceededError{
		UserID:           userID,
		RemainingMinutes: remainingMinutes,
	}
}

// Business Logic Error Wrappers

// WrapDatabaseError wraps database-related errors with additional context.
func (h *ServiceErrorHandler) WrapDatabaseError(err error, operation string) error {
	if err == nil {
		return nil
	}
	return fmt.Errorf("database %s failed: %w", operation, err)
}

// WrapExternalServiceError wraps external service errors with additional context.
func (h *ServiceErrorHandler) WrapExternalServiceError(err error, serviceName string) error {
	if err == nil {
		return nil
	}
	return fmt.Errorf("%s service error: %w", serviceName, err)
}

// LogAndWrapError logs an error with context and returns a wrapped version.
func (h *ServiceErrorHandler) LogAndWrapError(err error, context map[string]interface{}, message string) error {
	if err == nil {
		return nil
	}

	// Build log event with context
	logEvent := log.Error().Err(err)
	for key, value := range context {
		logEvent = logEvent.Interface(key, value)
	}
	logEvent.Msg(message)

	return fmt.Errorf("%s: %w", message, err)
}

// NewAPIError creates a new APIError for direct use when domain errors aren't sufficient.
func (h *ServiceErrorHandler) NewAPIError(status int, code, message string, err error) *apierror.APIError {
	apiErr := apierror.New(status, code, message)
	if err != nil {
		apiErr = apiErr.WithError(err)
	}
	return apiErr
}

// Helper methods for common patterns

// RequireNonNilUUID validates that a UUID is not nil and returns appropriate error.
func (h *ServiceErrorHandler) RequireNonNilUUID(id uuid.UUID, fieldName string) error {
	if id == uuid.Nil {
		return h.ValidationError(fieldName, "required", "nil UUID")
	}
	return nil
}

// RequireNonEmptyString validates that a string is not empty and returns appropriate error.
func (h *ServiceErrorHandler) RequireNonEmptyString(value, fieldName string) error {
	if value == "" {
		return h.ValidationError(fieldName, "required", "empty string")
	}
	return nil
}

// CheckPermission validates user permissions and returns appropriate error if insufficient.
func (h *ServiceErrorHandler) CheckPermission(hasPermission bool, userID uuid.UUID, requiredRole, action string) error {
	if !hasPermission {
		return h.InsufficientPermissions(userID, requiredRole, action)
	}
	return nil
}
