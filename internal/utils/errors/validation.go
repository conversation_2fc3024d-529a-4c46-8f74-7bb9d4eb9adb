// Package errors provides validation error handling utilities.
// These utilities help format and handle validation errors consistently
// across the application, integrating with go-playground/validator.
package errors

import (
	"errors"
	"fmt"
	"strings"

	"Membership-SAAS-System-Backend/internal/apierror"
	httputil "Membership-SAAS-System-Backend/internal/utils/http"

	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
)

// ValidationErrorHandler provides utilities for handling validation errors.
type ValidationErrorHandler struct{}

// NewValidationErrorHandler creates a new ValidationErrorHandler instance.
func NewValidationErrorHandler() *ValidationErrorHandler {
	return &ValidationErrorHandler{}
}

// FormatValidationErrors formats go-playground/validator errors into a user-friendly format.
func (h *ValidationErrorHandler) FormatValidationErrors(err error) []httputil.FieldError {
	var fieldErrors []httputil.FieldError

	var validationErrs validator.ValidationErrors
	if errors.As(err, &validationErrs) {
		for _, fieldErr := range validationErrs {
			fieldError := httputil.FieldError{
				Field:   h.formatFieldName(fieldErr.Field()),
				Message: h.formatValidationMessage(fieldErr),
				Code:    fieldErr.Tag(),
			}
			fieldErrors = append(fieldErrors, fieldError)
		}
	}

	return fieldErrors
}

// formatFieldName converts struct field names to user-friendly field names.
func (h *ValidationErrorHandler) formatFieldName(field string) string {
	// Convert PascalCase to snake_case for API consistency
	var result strings.Builder
	for i, char := range field {
		if i > 0 && char >= 'A' && char <= 'Z' {
			result.WriteRune('_')
		}
		result.WriteRune(char)
	}
	return strings.ToLower(result.String())
}

// formatValidationMessage creates user-friendly validation error messages.
func (h *ValidationErrorHandler) formatValidationMessage(fieldErr validator.FieldError) string {
	fieldName := h.formatFieldName(fieldErr.Field())

	switch fieldErr.Tag() {
	case "required":
		return fmt.Sprintf("%s is required", fieldName)
	case "email":
		return fmt.Sprintf("%s must be a valid email address", fieldName)
	case "min":
		return fmt.Sprintf("%s must be at least %s characters long", fieldName, fieldErr.Param())
	case "max":
		return fmt.Sprintf("%s must be no more than %s characters long", fieldName, fieldErr.Param())
	case "len":
		return fmt.Sprintf("%s must be exactly %s characters long", fieldName, fieldErr.Param())
	case "gt":
		return fmt.Sprintf("%s must be greater than %s", fieldName, fieldErr.Param())
	case "gte":
		return fmt.Sprintf("%s must be greater than or equal to %s", fieldName, fieldErr.Param())
	case "lt":
		return fmt.Sprintf("%s must be less than %s", fieldName, fieldErr.Param())
	case "lte":
		return fmt.Sprintf("%s must be less than or equal to %s", fieldName, fieldErr.Param())
	case "oneof":
		return fmt.Sprintf("%s must be one of: %s", fieldName, fieldErr.Param())
	case "uuid":
		return fmt.Sprintf("%s must be a valid UUID", fieldName)
	case "url":
		return fmt.Sprintf("%s must be a valid URL", fieldName)
	case "phone":
		return fmt.Sprintf("%s must be a valid phone number", fieldName)
	case "alphanum":
		return fmt.Sprintf("%s must contain only alphanumeric characters", fieldName)
	case "alpha":
		return fmt.Sprintf("%s must contain only alphabetic characters", fieldName)
	case "numeric":
		return fmt.Sprintf("%s must contain only numeric characters", fieldName)
	default:
		return fmt.Sprintf("%s is invalid", fieldName)
	}
}

// HandleValidationError processes validation errors and returns appropriate HTTP response.
func (h *ValidationErrorHandler) HandleValidationError(c echo.Context, err error) error {
	fieldErrors := h.FormatValidationErrors(err)

	if len(fieldErrors) > 0 {
		// Create legacy response for backward compatibility
		legacyResp := httputil.ErrorResponse{
			Error:   "Bad Request",
			Message: "Validation failed",
		}

		// TODO: In the future, can implement structured error response:
		// structuredResp := httputil.StructuredErrorResponse{
		//     Code:    "VALIDATION_ERROR",
		//     Message: "Request validation failed",
		//     Details: fieldErrors,
		// }

		// For now, return legacy format for compatibility
		return c.JSON(400, legacyResp)
	}

	// Fallback for non-validation errors
	return httputil.HandleError(c, 400, "Validation failed", err)
}

// ValidateStruct validates a struct and returns formatted validation errors.
func (h *ValidationErrorHandler) ValidateStruct(v *validator.Validate, data interface{}) error {
	err := v.Struct(data)
	if err != nil {
		// Return the validation error as-is to be handled by error handlers
		return err
	}
	return nil
}

// CreateValidationAPIError creates an APIError for validation failures.
func (h *ValidationErrorHandler) CreateValidationAPIError(err error) *apierror.APIError {
	fieldErrors := h.FormatValidationErrors(err)

	if len(fieldErrors) > 0 {
		// Create a detailed message from the first field error
		firstError := fieldErrors[0]
		message := firstError.Message

		apiErr := apierror.ErrValidation
		apiErr.Message = message
		return apiErr.WithError(err)
	}

	return apierror.ErrValidation.WithError(err)
}

// ValidateAndReturnError validates a struct and returns an appropriate error.
// This is a convenience method that combines validation and error creation.
func (h *ValidationErrorHandler) ValidateAndReturnError(v *validator.Validate, data interface{}) error {
	err := v.Struct(data)
	if err != nil {
		return h.CreateValidationAPIError(err)
	}
	return nil
}

// Common validation helper methods

// RequiredFields checks if required fields are present in a map[string]interface{}.
func (h *ValidationErrorHandler) RequiredFields(data map[string]interface{}, fields ...string) error {
	var missingFields []string

	for _, field := range fields {
		if value, exists := data[field]; !exists || value == nil || value == "" {
			missingFields = append(missingFields, field)
		}
	}

	if len(missingFields) > 0 {
		message := fmt.Sprintf("Missing required fields: %s", strings.Join(missingFields, ", "))
		return apierror.NewBadRequest(message)
	}

	return nil
}

// ValidateEnum checks if a value is within a set of allowed values.
func (h *ValidationErrorHandler) ValidateEnum(value string, allowed []string, fieldName string) error {
	for _, allowedValue := range allowed {
		if value == allowedValue {
			return nil
		}
	}

	message := fmt.Sprintf("%s must be one of: %s", fieldName, strings.Join(allowed, ", "))
	return apierror.NewBadRequest(message)
}

// ValidateUUIDString validates that a string is a valid UUID.
func (h *ValidationErrorHandler) ValidateUUIDString(value, fieldName string) error {
	if value == "" {
		return apierror.NewBadRequest(fmt.Sprintf("%s is required", fieldName))
	}

	// Use uuid.Parse to validate
	// Note: This requires importing github.com/google/uuid
	// We'll keep this simple for now and let the caller handle UUID parsing
	if len(value) != 36 {
		return apierror.NewBadRequest(fmt.Sprintf("%s must be a valid UUID", fieldName))
	}

	return nil
}

// ValidateRange validates that a numeric value is within a specified range.
func (h *ValidationErrorHandler) ValidateRange(value, minValue, maxValue int, fieldName string) error {
	if value < minValue {
		return apierror.NewBadRequest(fmt.Sprintf("%s must be at least %d", fieldName, minValue))
	}
	if value > maxValue {
		return apierror.NewBadRequest(fmt.Sprintf("%s must be no more than %d", fieldName, maxValue))
	}
	return nil
}
