package files

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"strconv"

	"github.com/labstack/echo/v4"
)

// GenerateRandomBytes returns securely generated random bytes.
// It will panic on error to ensure secure random data generation or failure.
func GenerateRandomBytes(n int) []byte {
	b := make([]byte, n)
	_, err := rand.Read(b)
	if err != nil {
		panic("failed to read random bytes: " + err.Error())
	}
	return b
}

// GenerateRandomState generates a random string of the specified length using hex encoding.
func GenerateRandomState(length int) string {
	// Each byte becomes two hex characters. So, we need length/2 bytes.
	numBytes := (length + 1) / 2
	randBytes := GenerateRandomBytes(numBytes)
	state := hex.EncodeToString(randBytes)
	return state[:length] // Trim to exact length if length is odd
}

// GenerateS256Challenge creates a SHA256 PKCE code challenge from a code verifier.
func GenerateS256Challenge(codeVerifier string) string {
	hash := sha256.Sum256([]byte(codeVerifier))
	return base64.RawURLEncoding.EncodeToString(hash[:])
}

// ParseQueryInt parses an integer from query parameters with a default value.
func ParseQueryInt(c echo.Context, paramName string, defaultValue int) int {
	valStr := c.QueryParam(paramName)
	if valStr == "" {
		return defaultValue
	}
	valInt, err := strconv.Atoi(valStr)
	if err != nil {
		return defaultValue
	}
	return valInt
}

// GetPaginationParamsEcho parses page and limit from query parameters with defaults.
func GetPaginationParamsEcho(c echo.Context) (page, limit int) {
	page = ParseQueryInt(c, "page", 1)
	if page < 1 {
		page = 1
	}
	limit = ParseQueryInt(c, "limit", 10)
	if limit < 1 {
		limit = 10
	} else if limit > 100 { // Max limit
		limit = 100
	}
	return page, limit
}

// GetOffset calculates the offset based on page and limit.
func GetOffset(page, limit int) int {
	return (page - 1) * limit
}
