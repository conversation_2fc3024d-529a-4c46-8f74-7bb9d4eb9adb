package httputil

import (
	"errors"
	"net/http"

	"Membership-SAAS-System-Backend/internal/apierror"

	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
	"github.com/rs/zerolog/log"
)

// ErrorResponse defines the structure for a JSON error response.
// @Description Represents a standard error format returned by the API.
type ErrorResponse struct {
	Error   string `json:"error" example:"Not Found"`                      // A short, general error message or code (often corresponds to HTTP status text).
	Message string `json:"message,omitempty" example:"Resource not found"` // A more detailed, human-readable message about the error.
	Details string `json:"details,omitempty" example:"error details here"` // Optional technical details or underlying error string (usually omitted for 5xx errors).
}

// FieldError contains validation error details for a single field
type FieldError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
	Code    string `json:"code"`
}

// StructuredErrorResponse represents our new structured error format
type StructuredErrorResponse struct {
	Code    string       `json:"code"`              // Machine-readable error code
	Message string       `json:"message"`           // User-facing message
	Details []FieldError `json:"details,omitempty"` // Field-specific errors for validation
}

// Predefined errors
var (
	ErrNotFound             = errors.New("resource not found")
	ErrUnauthorized         = errors.New("unauthorized")
	ErrForbidden            = errors.New("forbidden")
	ErrBadRequest           = errors.New("bad request")
	ErrConflict             = errors.New("resource conflict") // e.g., item already exists
	ErrInvalidRefreshToken  = errors.New("invalid refresh token")
	ErrInvalidPaymentStatus = errors.New("invalid payment status")
)

// HandleError logs the error and sends a JSON error response.
func HandleError(c echo.Context, statusCode int, publicMessage string, internalError error) error {
	response := ErrorResponse{
		Error: http.StatusText(statusCode),
	}

	if publicMessage != "" {
		response.Message = publicMessage
	}

	// Log the internal error with more details
	log.Ctx(c.Request().Context()).Error().
		Err(internalError).
		Int("status_code", statusCode).
		Str("public_message", publicMessage).
		Str("path", c.Request().URL.Path).
		Msg("API Error")

	// For client-facing errors (4xx), we might include more details if safe.
	// For server errors (5xx), we generally avoid sending internalError details to the client.
	if statusCode >= 400 && statusCode < 500 && internalError != nil {
		// Consider if internalError.Error() is safe to expose.
		// For validation errors, it often is, but be cautious with other errors.
		// response.Details = internalError.Error() // Potentially expose more detail for client errors
	}

	return c.JSON(statusCode, response)
}

// Note: Validation error building functions removed as they were unused.
// Validation errors are now handled directly in HandleError function.

// CustomHTTPErrorHandler is the centralized error handler for Echo
func CustomHTTPErrorHandler(err error, c echo.Context) {
	if c.Response().Committed {
		return
	}

	// Handle our custom APIError type
	var apiErr *apierror.APIError
	if errors.As(err, &apiErr) {
		// Also include legacy format for backward compatibility
		legacyResp := ErrorResponse{
			Error:   apiErr.Code,
			Message: apiErr.Message,
		}

		// Log the error
		log.Ctx(c.Request().Context()).Error().
			Err(apiErr.Err).
			Int("status_code", apiErr.Status).
			Str("error_code", apiErr.Code).
			Str("path", c.Request().URL.Path).
			Msg("API Error")

		// For now, return legacy format to maintain compatibility
		if err := c.JSON(apiErr.Status, legacyResp); err != nil {
			log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to send API error response")
		}
		return
	}

	// Handle validation errors
	var validationErrs validator.ValidationErrors
	if errors.As(err, &validationErrs) {
		// Legacy format for compatibility
		legacyResp := ErrorResponse{
			Error:   "Bad Request",
			Message: "Validation failed",
		}

		log.Ctx(c.Request().Context()).Warn().
			Err(err).
			Str("path", c.Request().URL.Path).
			Msg("Validation Error")

		if err := c.JSON(http.StatusBadRequest, legacyResp); err != nil {
			log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to send validation error response")
		}
		return
	}

	// Handle Echo's HTTPError
	var he *echo.HTTPError
	if errors.As(err, &he) {
		code := he.Code
		message := "Internal Server Error"

		if msg, ok := he.Message.(string); ok {
			message = msg

			// Special handling for JWT middleware errors
			// The echo-jwt middleware returns 400 for missing auth headers
			// but we want to return 401 for consistency
			if code == http.StatusBadRequest &&
				(message == "missing value in request header" ||
					message == "missing or malformed jwt") {
				code = http.StatusUnauthorized
				message = "Authentication required"
			}
		}

		// Map common Echo errors to our error codes
		errorCode := http.StatusText(code)
		if code == http.StatusUnauthorized {
			errorCode = "UNAUTHORIZED"
			// Use a consistent message for unauthorized errors
			message = "Authentication required"
		}

		resp := ErrorResponse{
			Error:   errorCode,
			Message: message,
		}

		log.Ctx(c.Request().Context()).Error().
			Err(he.Internal).
			Int("status_code", code).
			Str("path", c.Request().URL.Path).
			Msg("HTTP Error")

		if err := c.JSON(code, resp); err != nil {
			log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to send HTTP error response")
		}
		return
	}

	// Default error handling
	log.Ctx(c.Request().Context()).Error().
		Err(err).
		Str("path", c.Request().URL.Path).
		Msg("Unhandled Internal Server Error")

	resp := ErrorResponse{
		Error:   "INTERNAL_SERVER_ERROR",
		Message: "An unexpected error occurred",
	}

	if err := c.JSON(http.StatusInternalServerError, resp); err != nil {
		log.Ctx(c.Request().Context()).Error().Err(err).Msg("Failed to send internal error response")
	}
}
