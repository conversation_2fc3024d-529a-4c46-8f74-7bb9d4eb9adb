package httputil

import (
	"fmt"
	"math"
	"strconv"

	"github.com/labstack/echo/v4"
)

// ParseQueryInt parses an integer from query parameters with a default value.
func ParseQueryInt(c echo.Context, paramName string, defaultValue int) int {
	valStr := c.QueryParam(paramName)
	if valStr == "" {
		return defaultValue
	}
	valInt, err := strconv.Atoi(valStr)
	if err != nil {
		return defaultValue
	}
	return valInt
}

const (
	DefaultPageSize = 20
	MaxPageSize     = 100
	MinPageSize     = 1
	DefaultPage     = 1
)

// PaginationParams represents pagination parameters.
type PaginationParams struct {
	Page     int32
	PageSize int32
	Offset   int32
	Limit    int32
}

// PaginationMeta represents pagination metadata for responses.
type PaginationMeta struct {
	Page       int32 `json:"page"`
	PageSize   int32 `json:"pageSize"`
	TotalCount int64 `json:"totalCount"`
	TotalPages int64 `json:"totalPages"`
	HasNext    bool  `json:"hasNext"`
	HasPrev    bool  `json:"hasPrev"`
}

// ParsePaginationParams extracts and validates pagination parameters from request.
func ParsePaginationParams(c echo.Context) (*PaginationParams, error) {
	// Parse page parameter
	pageStr := c.QueryParam("page")
	page := int32(DefaultPage)
	if pageStr != "" {
		parsedPage, err := strconv.Atoi(pageStr)
		if err != nil {
			return nil, fmt.Errorf("invalid page parameter: %w", err)
		}
		if parsedPage < 1 {
			return nil, fmt.Errorf("page must be greater than 0")
		}
		if parsedPage > math.MaxInt32 {
			return nil, fmt.Errorf("page number too large")
		}
		page = int32(parsedPage)
	}

	// Parse pageSize parameter
	pageSizeStr := c.QueryParam("pageSize")
	pageSize := int32(DefaultPageSize)
	if pageSizeStr != "" {
		parsedPageSize, err := strconv.Atoi(pageSizeStr)
		if err != nil {
			return nil, fmt.Errorf("invalid pageSize parameter: %w", err)
		}
		if parsedPageSize < MinPageSize {
			return nil, fmt.Errorf("pageSize must be at least %d", MinPageSize)
		}
		if parsedPageSize > MaxPageSize {
			return nil, fmt.Errorf("pageSize cannot exceed %d", MaxPageSize)
		}
		if parsedPageSize > math.MaxInt32 {
			return nil, fmt.Errorf("pageSize too large")
		}
		pageSize = int32(parsedPageSize)
	}

	// Calculate offset and limit
	offset := (page - 1) * pageSize
	limit := pageSize

	return &PaginationParams{
		Page:     page,
		PageSize: pageSize,
		Offset:   offset,
		Limit:    limit,
	}, nil
}

// ParsePaginationParamsWithDefaults extracts pagination parameters with custom defaults.
func ParsePaginationParamsWithDefaults(c echo.Context, defaultPage, defaultPageSize, maxPageSize int32) (*PaginationParams, error) {
	// Parse page parameter
	pageStr := c.QueryParam("page")
	page := defaultPage
	if pageStr != "" {
		parsedPage, err := strconv.Atoi(pageStr)
		if err != nil {
			return nil, fmt.Errorf("invalid page parameter: %w", err)
		}
		if parsedPage < 1 {
			return nil, fmt.Errorf("page must be greater than 0")
		}
		if parsedPage > math.MaxInt32 {
			return nil, fmt.Errorf("page number too large")
		}
		page = int32(parsedPage) // #nosec G109 -- safe conversion, already checked against MaxInt32
	}

	// Parse pageSize parameter
	pageSizeStr := c.QueryParam("pageSize")
	pageSize := defaultPageSize
	if pageSizeStr != "" {
		parsedPageSize, err := strconv.Atoi(pageSizeStr)
		if err != nil {
			return nil, fmt.Errorf("invalid pageSize parameter: %w", err)
		}
		if parsedPageSize < MinPageSize {
			return nil, fmt.Errorf("pageSize must be at least %d", MinPageSize)
		}
		if parsedPageSize > int(maxPageSize) {
			return nil, fmt.Errorf("pageSize cannot exceed %d", maxPageSize)
		}
		if parsedPageSize > math.MaxInt32 {
			return nil, fmt.Errorf("pageSize too large")
		}
		pageSize = int32(parsedPageSize) // #nosec G109 -- safe conversion, already checked against MaxInt32
	}

	// Calculate offset and limit
	offset := (page - 1) * pageSize
	limit := pageSize

	return &PaginationParams{
		Page:     page,
		PageSize: pageSize,
		Offset:   offset,
		Limit:    limit,
	}, nil
}

// CalculatePaginationMeta calculates pagination metadata.
func CalculatePaginationMeta(params *PaginationParams, totalCount int64) *PaginationMeta {
	totalPages := (totalCount + int64(params.PageSize) - 1) / int64(params.PageSize)
	if totalPages == 0 {
		totalPages = 1
	}

	return &PaginationMeta{
		Page:       params.Page,
		PageSize:   params.PageSize,
		TotalCount: totalCount,
		TotalPages: totalPages,
		HasNext:    totalPages <= math.MaxInt32 && params.Page < int32(totalPages), // #nosec G115 -- safe conversion, already checked totalPages <= MaxInt32
		HasPrev:    params.Page > 1,
	}
}

// BuildPaginatedResponse creates a consistent paginated response structure.
func BuildPaginatedResponse(data interface{}, params *PaginationParams, totalCount int64) map[string]interface{} {
	meta := CalculatePaginationMeta(params, totalCount)

	return map[string]interface{}{
		"data":       data,
		"pagination": meta,
	}
}

// ValidatePageBounds ensures page is within valid bounds.
func ValidatePageBounds(page int32, totalCount int64, pageSize int32) error {
	if page < 1 {
		return fmt.Errorf("page must be greater than 0")
	}

	totalPages := (totalCount + int64(pageSize) - 1) / int64(pageSize)
	if totalPages == 0 {
		totalPages = 1
	}

	if totalPages > math.MaxInt32 || page > int32(totalPages) { // #nosec G115 -- safe conversion, checked totalPages > MaxInt32 first
		return fmt.Errorf("page %d exceeds total pages %d", page, totalPages)
	}

	return nil
}

// GetNextPageURL generates the URL for the next page.
func GetNextPageURL(c echo.Context, currentPage int32, totalPages int64) string {
	if totalPages > math.MaxInt32 || currentPage >= int32(totalPages) { // #nosec G115 -- safe conversion, checked totalPages > MaxInt32 first
		return ""
	}

	// Copy current query parameters
	values := c.Request().URL.Query()
	values.Set("page", strconv.Itoa(int(currentPage+1)))

	return c.Request().URL.Path + "?" + values.Encode()
}

// GetPrevPageURL generates the URL for the previous page.
func GetPrevPageURL(c echo.Context, currentPage int32) string {
	if currentPage <= 1 {
		return ""
	}

	// Copy current query parameters
	values := c.Request().URL.Query()
	values.Set("page", strconv.Itoa(int(currentPage-1)))

	return c.Request().URL.Path + "?" + values.Encode()
}

// PaginationLinks represents navigation links for pagination.
type PaginationLinks struct {
	First    string `json:"first,omitempty"`
	Previous string `json:"previous,omitempty"`
	Next     string `json:"next,omitempty"`
	Last     string `json:"last,omitempty"`
}

// GeneratePaginationLinks creates navigation links for pagination.
func GeneratePaginationLinks(c echo.Context, params *PaginationParams, totalCount int64) *PaginationLinks {
	totalPages := (totalCount + int64(params.PageSize) - 1) / int64(params.PageSize)
	if totalPages == 0 {
		totalPages = 1
	}

	links := &PaginationLinks{}
	baseURL := c.Request().URL.Path
	values := c.Request().URL.Query()

	// First page link
	if params.Page > 1 {
		firstValues := make(map[string]string)
		for k, v := range values {
			if len(v) > 0 {
				firstValues[k] = v[0]
			}
		}
		firstValues["page"] = "1"
		links.First = buildURL(baseURL, firstValues)
	}

	// Previous page link
	if params.Page > 1 {
		prevValues := make(map[string]string)
		for k, v := range values {
			if len(v) > 0 {
				prevValues[k] = v[0]
			}
		}
		prevValues["page"] = strconv.Itoa(int(params.Page - 1))
		links.Previous = buildURL(baseURL, prevValues)
	}

	// Next page link
	if totalPages <= math.MaxInt32 && params.Page < int32(totalPages) { // #nosec G115 -- safe conversion, already checked totalPages <= MaxInt32
		nextValues := make(map[string]string)
		for k, v := range values {
			if len(v) > 0 {
				nextValues[k] = v[0]
			}
		}
		nextValues["page"] = strconv.Itoa(int(params.Page + 1))
		links.Next = buildURL(baseURL, nextValues)
	}

	// Last page link
	if totalPages <= math.MaxInt32 && params.Page < int32(totalPages) { // #nosec G115 -- safe conversion, already checked totalPages <= MaxInt32
		lastValues := make(map[string]string)
		for k, v := range values {
			if len(v) > 0 {
				lastValues[k] = v[0]
			}
		}
		lastValues["page"] = strconv.Itoa(int(totalPages))
		links.Last = buildURL(baseURL, lastValues)
	}

	return links
}

// buildURL constructs a URL from base path and query parameters.
func buildURL(basePath string, params map[string]string) string {
	if len(params) == 0 {
		return basePath
	}

	query := ""
	first := true
	for k, v := range params {
		if !first {
			query += "&"
		}
		query += k + "=" + v
		first = false
	}

	return basePath + "?" + query
}

// PaginatedResult represents a generic paginated result.
type PaginatedResult struct {
	Data       interface{}      `json:"data"`
	Pagination *PaginationMeta  `json:"pagination"`
	Links      *PaginationLinks `json:"links,omitempty"`
}

// NewPaginatedResult creates a new paginated result with all metadata.
func NewPaginatedResult(
	data interface{},
	params *PaginationParams,
	totalCount int64,
	c echo.Context,
	includeLinks bool,
) *PaginatedResult {
	result := &PaginatedResult{
		Data:       data,
		Pagination: CalculatePaginationMeta(params, totalCount),
	}

	if includeLinks {
		result.Links = GeneratePaginationLinks(c, params, totalCount)
	}

	return result
}

// PageInfo represents simple page information.
type PageInfo struct {
	CurrentPage  int32 `json:"currentPage"`
	TotalPages   int64 `json:"totalPages"`
	TotalItems   int64 `json:"totalItems"`
	ItemsPerPage int32 `json:"itemsPerPage"`
}

// GetPageInfo extracts simple page information.
func GetPageInfo(params *PaginationParams, totalCount int64) *PageInfo {
	totalPages := (totalCount + int64(params.PageSize) - 1) / int64(params.PageSize)
	if totalPages == 0 {
		totalPages = 1
	}

	return &PageInfo{
		CurrentPage:  params.Page,
		TotalPages:   totalPages,
		TotalItems:   totalCount,
		ItemsPerPage: params.PageSize,
	}
}

// OffsetLimitParams represents simple offset/limit parameters for database queries.
type OffsetLimitParams struct {
	Offset int32 `json:"offset"`
	Limit  int32 `json:"limit"`
}

// ToOffsetLimit converts pagination parameters to offset/limit format.
func (p *PaginationParams) ToOffsetLimit() *OffsetLimitParams {
	return &OffsetLimitParams{
		Offset: p.Offset,
		Limit:  p.Limit,
	}
}

// FromPageSize creates pagination parameters from page and page size.
func FromPageSize(page, pageSize int32) *PaginationParams {
	if page < 1 {
		page = 1
	}
	if pageSize < MinPageSize {
		pageSize = DefaultPageSize
	}
	if pageSize > MaxPageSize {
		pageSize = MaxPageSize
	}

	offset := (page - 1) * pageSize

	return &PaginationParams{
		Page:     page,
		PageSize: pageSize,
		Offset:   offset,
		Limit:    pageSize,
	}
}
