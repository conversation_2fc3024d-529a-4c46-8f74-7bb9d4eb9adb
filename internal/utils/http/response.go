package httputil

import (
	"math"
	"net/url"
	"path/filepath"
	"strings"
	"time"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/locales"
	"Membership-SAAS-System-Backend/internal/payloads"
	"github.com/jackc/pgx/v5/pgtype"
)

// ResponseBuilder provides utilities for building consistent API responses.
type ResponseBuilder struct{}

// NewResponseBuilder creates a new response builder instance.
func NewResponseBuilder() *ResponseBuilder {
	return &ResponseBuilder{}
}

// convertNumericToString converts a pgtype.Numeric to a *string
func convertNumericToString(n pgtype.Numeric) *string {
	if !n.Valid {
		return nil
	}
	// Convert via Value() which returns the numeric as a string
	val, err := n.Value()
	if err != nil || val == nil {
		return nil
	}
	str := val.(string)
	return &str
}

// BuildMediaItemResponse constructs a MediaItemResponse from a database record.
func (r *ResponseBuilder) BuildMediaItemResponse(item db.EventMediaItem) payloads.MediaItemResponse {
	// Construct full URL from relative path
	fileURL := item.FilePath
	if !strings.HasPrefix(fileURL, "http") && !strings.HasPrefix(fileURL, "/") {
		fileURL = "/" + fileURL
	}

	// Parse URL to extract filename if not available
	fileName := item.FileName
	if fileName == "" {
		if parsedURL, err := url.Parse(fileURL); err == nil {
			fileName = filepath.Base(parsedURL.Path)
		}
	}

	return payloads.MediaItemResponse{
		ID:        item.ID,
		EventID:   &item.EventID,
		FileName:  fileName,
		FilePath:  fileURL,
		FileType:  &item.FileType,
		FileSize:  &item.FileSize,
		IsBanner:  item.IsBanner,
		CreatedAt: item.UploadedAt,
		UpdatedAt: item.UploadedAt,
	}
}

// BuildPostMediaItemResponse constructs a MediaItemResponse from a post media item.
func (r *ResponseBuilder) BuildPostMediaItemResponse(item db.PostMediaItem) payloads.MediaItemResponse {
	fileURL := item.FilePath
	if !strings.HasPrefix(fileURL, "http") && !strings.HasPrefix(fileURL, "/") {
		fileURL = "/" + fileURL
	}

	fileName := item.FileName
	if fileName == "" {
		if parsedURL, err := url.Parse(fileURL); err == nil {
			fileName = filepath.Base(parsedURL.Path)
		}
	}

	return payloads.MediaItemResponse{
		ID:        item.ID,
		PostID:    &item.PostID,
		FileName:  fileName,
		FilePath:  fileURL,
		FileType:  &item.FileType,
		FileSize:  &item.FileSize,
		IsBanner:  item.IsBanner,
		CreatedAt: item.UploadedAt,
		UpdatedAt: item.UploadedAt,
	}
}

// BuildTagResponse constructs a TagResponse from a database record.
func (r *ResponseBuilder) BuildTagResponse(tag db.Tag) payloads.TagResponse {
	// Build tag response with multi-language support
	return payloads.TagResponse{
		ID:                 tag.ID,
		TagName:            tag.NameEnUs, // Default to English name
		LanguageCode:       locales.LangEN,      // Default language
		Description:        tag.DescriptionEnUs,
		IsGloballyApproved: tag.OrganizationID == nil && tag.ApprovalStatus == db.TagApprovalStatusApproved,
	}
}

// BuildUserResponse constructs a UserResponse from a database record.
func (r *ResponseBuilder) BuildUserResponse(user db.User) payloads.UserResponse {
	// Split display name into first and last names (basic implementation)
	names := strings.Fields(user.DisplayName)
	firstName := user.DisplayName
	lastName := ""
	if len(names) > 1 {
		firstName = names[0]
		lastName = strings.Join(names[1:], " ")
	}

	return payloads.UserResponse{
		ID:        user.ID,
		Email:     user.Email,
		Phone:     user.Phone,
		FirstName: firstName,
		LastName:  lastName,
		Role:      "user", // Default role - this might need to be determined from user_organization_memberships
		Verified:  user.PhoneVerifiedAt != nil || user.EmailVerifiedAt != nil,
		CreatedAt: user.CreatedAt,
		UpdatedAt: user.UpdatedAt,
		// LastLoginAt: Not available in current User model
	}
}

// BuildOrganizationResponse constructs an OrganizationResponse from a database record.
func (r *ResponseBuilder) BuildOrganizationResponse(org db.Organization) payloads.OrganizationResponse {
	return payloads.OrganizationResponse{
		ID:          org.ID,
		Name:        org.Name,
		Description: org.Description,
		ImageURL:    org.ImageUrl,
		ThemeColor:  org.ThemeColor,
		Status:      org.Status,
		CreatedAt:   org.CreatedAt,
		UpdatedAt:   org.UpdatedAt,
	}
}

// BuildPaginatedResponse constructs a paginated response with metadata.
func (r *ResponseBuilder) BuildPaginatedResponse(items interface{}, totalCount int64, page, pageSize int32) payloads.PaginatedResponse {
	totalPages := (totalCount + int64(pageSize) - 1) / int64(pageSize)

	return payloads.PaginatedResponse{
		Data:       items,
		TotalItems: totalCount,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}
}

// BuildEventResponse constructs a comprehensive EventResponse from a database record.
func (r *ResponseBuilder) BuildEventResponse(
	event db.Event,
	organization *db.Organization,
	mediaItems []payloads.MediaItemResponse,
	tags []payloads.TagResponse,
	requiredVerificationTypes []string,
	userRegistrationStatus *payloads.UserEventStatusInfo,
	isAdminView bool,
) payloads.EventResponse {
	resp := payloads.EventResponse{
		ID:                              event.ID,
		OrganizationID:                  event.OrganizationID,
		Title:                           event.Title,
		JsonContent:                     event.DescriptionContent,
		LocationType:                    string(event.LocationType),
		LocationFullAddress:             event.LocationFullAddress,
		LocationOnlineURL:               event.LocationOnlineUrl,
		StartTime:                       event.StartTime,
		EndTime:                         event.EndTime,
		Status:                          string(event.Status),
		ParticipantLimit:                event.ParticipantLimit,
		WaitlistLimit:                   event.WaitlistLimit,
		RequiresApprovalForRegistration: event.RequiresApprovalForRegistration,
		CreatedByUserID:                 event.CreatedByUserID,
		CreatedAt:                       event.CreatedAt,
		UpdatedAt:                       event.UpdatedAt,
		PublishedAt:                     event.PublishedAt,
		GovernmentFundingKeys:           event.GovernmentFundingKeys,
		Price:                           convertNumericToString(event.Price),
		ContactEmail:                    event.ContactEmail,
		ContactPhone:                    event.ContactPhone,
		MediaItems:                      mediaItems,
		Tags:                            tags,
		RequiredVerificationTypeKeys:    requiredVerificationTypes,
	}

	// Add organization information if available
	if organization != nil {
		resp.OrganizationName = organization.Name
	}

	// Add user registration status if provided (note: field may not exist in current EventResponse)
	// This would need to be added to EventResponse struct if required

	// Include sensitive data only for admin view
	// Additional admin-specific fields would be added here when needed

	return resp
}

// BuildPublicEventResponse constructs a PublicEventResponse (no sensitive data).
func (r *ResponseBuilder) BuildPublicEventResponse(
	event db.Event,
	organization *db.Organization,
	mediaItems []payloads.MediaItemResponse,
	tags []payloads.TagResponse,
	userRegistrationStatus *payloads.UserEventStatusInfo,
) payloads.PublicEventResponse {
	resp := payloads.PublicEventResponse{
		ID:                  event.ID,
		OrganizationID:      event.OrganizationID,
		Title:               event.Title,
		JsonContent:         event.DescriptionContent,
		LocationType:        string(event.LocationType),
		LocationFullAddress: event.LocationFullAddress,
		LocationOnlineURL:   event.LocationOnlineUrl,
		StartTime:           event.StartTime,
		EndTime:             event.EndTime,
		ParticipantLimit:    event.ParticipantLimit,
		WaitlistLimit:       event.WaitlistLimit,
		Price:               convertNumericToString(event.Price),
		ContactEmail:        event.ContactEmail,
		ContactPhone:        event.ContactPhone,
		MediaItems:          mediaItems,
		Tags:                tags,
		PublishedAt:         event.PublishedAt,
	}

	// Add organization information if available
	if organization != nil {
		resp.OrganizationName = organization.Name
	}

	// Add user registration status if provided (for logged-in users)
	// Note: This field may need to be added to PublicEventResponse struct if required

	return resp
}

// BuildErrorResponse constructs a standardized error response.
func (r *ResponseBuilder) BuildErrorResponse(message string, details interface{}) payloads.ErrorResponse {
	return payloads.ErrorResponse{
		Error:     message,
		Details:   details,
		Timestamp: time.Now(),
	}
}

// BuildSuccessResponse constructs a standardized success response.
func (r *ResponseBuilder) BuildSuccessResponse(message string, data interface{}) payloads.SuccessResponse {
	return payloads.SuccessResponse{
		Message:   message,
		Data:      data,
		Timestamp: time.Now(),
	}
}

// BuildVolunteerApplicationResponse constructs a volunteer application response.
func (r *ResponseBuilder) BuildVolunteerApplicationResponse(
	app db.EventVolunteerApplication,
	user *db.User,
	event *db.Event,
) payloads.EventVolunteerApplicationResponse {
	resp := payloads.EventVolunteerApplicationResponse{
		EventVolunteerApplication: app, // Embed the full struct
	}

	// Add user information if available
	if user != nil {
		resp.UserDisplayName = user.DisplayName
		resp.UserEmail = user.Email
		resp.UserPhone = user.Phone
	}

	// Add event information if available
	if event != nil {
		resp.EventTitle = event.Title
		resp.EventStartTime = event.StartTime
		resp.EventEndTime = event.EndTime
	}

	return resp
}

// BuildUserEventStatusInfo constructs user event status information.
func (r *ResponseBuilder) BuildUserEventStatusInfo(
	registration *db.EventRegistration,
	volunteerApp *db.EventVolunteerApplication,
) *payloads.UserEventStatusInfo {
	status := &payloads.UserEventStatusInfo{
		IsRegistered:       registration != nil,
		IsVolunteerApplied: volunteerApp != nil,
	}

	if registration != nil {
		statusStr := string(registration.Status)
		status.RegistrationStatus = &statusStr
		status.RegistrationID = &registration.ID
		status.CheckedIn = registration.AttendedAt != nil
		if registration.AttendedAt != nil {
			status.CheckedInAt = registration.AttendedAt
		}
	}

	if volunteerApp != nil {
		volStatusStr := string(volunteerApp.Status)
		status.VolunteerApplicationStatus = &volStatusStr
		status.VolunteerApplicationID = &volunteerApp.ID
		status.VolunteerAttended = volunteerApp.AttendedAt != nil
		if volunteerApp.AttendedAt != nil {
			status.VolunteerAttendedAt = volunteerApp.AttendedAt
		}
	}

	return status
}

// BuildStatisticsBreakdown constructs a statistics breakdown map.
func (r *ResponseBuilder) BuildStatisticsBreakdown(items []interface{}) map[string]int64 {
	breakdown := make(map[string]int64)

	// This would be implemented based on the specific database query results
	// For now, return empty map as placeholder
	return breakdown
}

// BuildDateRange constructs a date range response.
func (r *ResponseBuilder) BuildDateRange(startDate, endDate *time.Time) *payloads.DateRange {
	if startDate == nil || endDate == nil {
		return nil
	}

	return &payloads.DateRange{
		StartDate: *startDate,
		EndDate:   *endDate,
	}
}

// BuildResourceFileResponse constructs a resource file response.
func (r *ResponseBuilder) BuildResourceFileResponse(file db.ResourceFile) payloads.ResourceFileResponse {
	fileURL := file.FilePath
	if !strings.HasPrefix(fileURL, "http") && !strings.HasPrefix(fileURL, "/") {
		fileURL = "/" + fileURL
	}

	description := ""
	if file.Description != nil {
		description = *file.Description
	}

	return payloads.ResourceFileResponse{
		ID:          file.ID,
		ResourceID:  file.ResourceID,
		FileName:    file.FileName,
		FilePath:    fileURL,
		FileType:    file.FileType,
		FileSize:    file.FileSize,
		Description: description,
		UploadedAt:  file.UploadedAt,
	}
}

// Utility functions for common response patterns

// WrapDataWithMeta wraps data with metadata for consistent API responses.
func WrapDataWithMeta(data interface{}, meta map[string]interface{}) map[string]interface{} {
	response := map[string]interface{}{
		"data": data,
	}

	if meta != nil {
		response["meta"] = meta
	}

	response["timestamp"] = time.Now()
	return response
}

// WrapPaginatedData wraps paginated data with pagination metadata.
func WrapPaginatedData(data interface{}, totalCount int64, page, pageSize int32) map[string]interface{} {
	totalPages := (totalCount + int64(pageSize) - 1) / int64(pageSize)

	return map[string]interface{}{
		"data": data,
		"pagination": map[string]interface{}{
			"totalCount": totalCount,
			"page":       page,
			"pageSize":   pageSize,
			"totalPages": totalPages,
			"hasNext":    totalPages <= math.MaxInt32 && page < int32(totalPages), // #nosec G115 -- safe conversion, already checked totalPages <= MaxInt32
			"hasPrev":    page > 1,
		},
		"timestamp": time.Now(),
	}
}

// BuildValidationErrorResponse constructs a validation error response with field details.
func BuildValidationErrorResponse(errors map[string]string) payloads.ErrorResponse {
	return payloads.ErrorResponse{
		Error:     "Validation failed",
		Details:   errors,
		Timestamp: time.Now(),
	}
}
