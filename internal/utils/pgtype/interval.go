package pgtype

import (
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

// IntervalToDuration converts a pgtype.Interval to time.Duration
// Note: This is an approximation as pgtype.Interval can represent months/years
// which don't have fixed durations. This function assumes 30 days per month
// and 365 days per year for approximation.
func IntervalToDuration(interval pgtype.Interval) time.Duration {
	if !interval.Valid {
		return 0
	}

	// Convert microseconds to duration
	dur := time.Duration(interval.Microseconds) * time.Microsecond

	// Add days (exact conversion)
	dur += time.Duration(interval.Days) * 24 * time.Hour

	// Add months (approximate: 30 days per month)
	dur += time.Duration(interval.Months) * 30 * 24 * time.Hour

	return dur
}

// DurationToInterval converts a time.Duration to pgtype.Interval
func DurationToInterval(duration time.Duration) pgtype.Interval {
	// Convert duration to microseconds
	microseconds := duration.Microseconds()

	return pgtype.Interval{
		Microseconds: microseconds,
		Days:         0,
		Months:       0,
		Valid:        true,
	}
}

// MustIntervalToDuration converts pgtype.Interval to time.Duration, panics if invalid
func MustIntervalToDuration(interval pgtype.Interval) time.Duration {
	if !interval.Valid {
		panic("invalid interval")
	}
	return IntervalToDuration(interval)
}
