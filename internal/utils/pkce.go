package utils

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
)

// GenerateRandomState generates a random string of the specified length.
// Used for OAuth2/PKCE state parameters.
func GenerateRandomState(length int) string {
	b := make([]byte, length)
	if _, err := rand.Read(b); err != nil {
		// This should never happen with crypto/rand
		panic(err)
	}
	return hex.EncodeToString(b)[:length]
}

// GenerateS256Challenge generates a SHA256 code challenge from a code verifier.
// Used for PKCE (Proof Key for Code Exchange) flows.
func GenerateS256Challenge(codeVerifier string) string {
	hash := sha256.Sum256([]byte(codeVerifier))
	return base64.RawURLEncoding.EncodeToString(hash[:])
}
