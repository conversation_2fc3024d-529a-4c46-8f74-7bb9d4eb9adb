package security

import (
	"bytes"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"path/filepath"
	"strings"
	"time"
)

// File validation errors
var (
	ErrFileTooLarge       = errors.New("file size exceeds maximum allowed")
	ErrFileEmpty          = errors.New("file size is empty")
	ErrInvalidFileType    = errors.New("file type not allowed")
	ErrInvalidFileName    = errors.New("invalid file name")
	ErrMaliciousContent   = errors.New("file contains potentially malicious content")
	ErrInvalidMagicNumber = errors.New("file content does not match extension")
)

// FileValidationConfig contains configuration for file validation
type FileValidationConfig struct {
	MaxFileSize         int64
	AllowedMimeTypes    []string
	AllowedExtensions   []string
	ValidateMagicNumber bool
}

// Common file validation configurations
var (
	ImageValidationConfig = FileValidationConfig{
		MaxFileSize:         10 * 1024 * 1024, // 10MB
		AllowedMimeTypes:    []string{"image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"},
		AllowedExtensions:   []string{".jpg", ".jpeg", ".png", ".gif", ".webp"},
		ValidateMagicNumber: true,
	}

	DocumentValidationConfig = FileValidationConfig{
		MaxFileSize:         50 * 1024 * 1024, // 50MB
		AllowedMimeTypes:    []string{"application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"},
		AllowedExtensions:   []string{".pdf", ".doc", ".docx"},
		ValidateMagicNumber: true,
	}

	MediaValidationConfig = FileValidationConfig{
		MaxFileSize:         100 * 1024 * 1024, // 100MB
		AllowedMimeTypes:    []string{"video/mp4", "video/mpeg", "audio/mpeg", "audio/mp3"},
		AllowedExtensions:   []string{".mp4", ".mpeg", ".mp3"},
		ValidateMagicNumber: true,
	}
)

// Magic numbers for common file types
var magicNumbers = map[string][]byte{
	".jpg":  {0xFF, 0xD8, 0xFF},
	".jpeg": {0xFF, 0xD8, 0xFF},
	".png":  {0x89, 0x50, 0x4E, 0x47},
	".gif":  {0x47, 0x49, 0x46},
	".pdf":  {0x25, 0x50, 0x44, 0x46},
	".zip":  {0x50, 0x4B, 0x03, 0x04},
}

// FileValidator provides secure file validation
type FileValidator struct {
	config FileValidationConfig
}

// NewFileValidator creates a new file validator with the given configuration
func NewFileValidator(config FileValidationConfig) *FileValidator {
	return &FileValidator{
		config: config,
	}
}

// ValidateFileUpload performs comprehensive validation on uploaded file
func (v *FileValidator) ValidateFileUpload(fileHeader *multipart.FileHeader) error {
	// Check file size
	if fileHeader.Size > v.config.MaxFileSize {
		return fmt.Errorf("%w: file size %d exceeds maximum %d", ErrFileTooLarge, fileHeader.Size, v.config.MaxFileSize)
	}

	// Check if file is empty
	if fileHeader.Size == 0 {
		return ErrFileEmpty
	}

	// Validate filename
	if err := v.ValidateFileName(fileHeader.Filename); err != nil {
		return err
	}

	// Check file extension
	ext := strings.ToLower(filepath.Ext(fileHeader.Filename))
	if !v.isAllowedExtension(ext) {
		return fmt.Errorf("%w: extension %s not allowed", ErrInvalidFileType, ext)
	}

	// Open file for content validation
	file, err := fileHeader.Open()
	if err != nil {
		return fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	// Read first 512 bytes for content type detection
	buffer := make([]byte, 512)
	n, err := file.Read(buffer)
	if err != nil && !errors.Is(err, io.EOF) {
		return fmt.Errorf("failed to read file: %w", err)
	}
	buffer = buffer[:n]

	// Reset file reader
	if _, err := file.Seek(0, 0); err != nil {
		return fmt.Errorf("failed to reset file reader: %w", err)
	}

	// Detect MIME type
	mimeType := http.DetectContentType(buffer)
	if !v.isAllowedMimeType(mimeType) {
		return fmt.Errorf("%w: detected MIME type %s not allowed", ErrInvalidFileType, mimeType)
	}

	// Validate magic number if configured
	if v.config.ValidateMagicNumber {
		if err := v.validateMagicNumber(ext, buffer); err != nil {
			return err
		}
	}

	// Check for malicious content patterns in the initial buffer
	if err := v.CheckMaliciousPatterns(buffer); err != nil {
		return err
	}

	// SECURITY FIX: Scan entire file content, not just first 512 bytes
	// This prevents malicious content from being hidden after byte 512
	if err := v.ScanFile(file); err != nil {
		return err
	}

	return nil
}

// ValidateFileName checks if filename is safe
func (v *FileValidator) ValidateFileName(filename string) error {
	// First check for empty filename before any processing
	if strings.TrimSpace(filename) == "" {
		return ErrInvalidFileName
	}

	// Check for encoded path traversal FIRST (before regular path traversal)
	if strings.Contains(filename, "%2e%2e") || strings.Contains(filename, "%2E%2E") ||
		strings.Contains(filename, "%252e%252e") || strings.Contains(filename, "..%2F") ||
		strings.Contains(filename, "..%5C") || strings.Contains(filename, "%2F") {
		return fmt.Errorf("%w: filename contains invalid characters", ErrInvalidFileName)
	}

	// Check for path traversal attempts
	if strings.Contains(filename, "..") || strings.Contains(filename, "../") || strings.Contains(filename, "..\\") {
		return fmt.Errorf("%w: path traversal detected", ErrInvalidFileName)
	}

	// Check for absolute paths
	if strings.HasPrefix(filename, "/") || strings.HasPrefix(filename, "\\") ||
		(len(filename) > 2 && filename[1] == ':') { // Windows drive letter
		return fmt.Errorf("%w: filename contains invalid characters", ErrInvalidFileName)
	}

	// Check for null bytes
	if strings.Contains(filename, "\x00") {
		return fmt.Errorf("%w: null byte detected", ErrInvalidFileName)
	}

	// Check for Unicode direction override characters
	for _, r := range filename {
		if r == 0x202E || r == 0x202D || r == 0x200E || r == 0x200F {
			return fmt.Errorf("%w: filename contains invalid characters", ErrInvalidFileName)
		}
	}

	// Remove any path components after validation
	baseFilename := filepath.Base(filename)

	// Check if Base returned "." or ".." which means invalid filename
	if baseFilename == "." || baseFilename == ".." || baseFilename == "/" || baseFilename == "" {
		return ErrInvalidFileName
	}

	// Sanitize filename and check if it changed
	sanitized := SanitizeFilename(baseFilename)
	if sanitized != baseFilename {
		return fmt.Errorf("%w: filename contains invalid characters", ErrInvalidFileName)
	}

	return nil
}

// isAllowedExtension checks if file extension is allowed
func (v *FileValidator) isAllowedExtension(ext string) bool {
	for _, allowed := range v.config.AllowedExtensions {
		if ext == allowed {
			return true
		}
	}
	return false
}

// isAllowedMimeType checks if MIME type is allowed
func (v *FileValidator) isAllowedMimeType(mimeType string) bool {
	// Extract base MIME type (remove parameters)
	if idx := strings.Index(mimeType, ";"); idx != -1 {
		mimeType = mimeType[:idx]
	}
	mimeType = strings.TrimSpace(mimeType)

	for _, allowed := range v.config.AllowedMimeTypes {
		if mimeType == allowed {
			return true
		}
	}
	return false
}

// validateMagicNumber checks if file content matches expected type
func (v *FileValidator) validateMagicNumber(ext string, content []byte) error {
	expectedMagic, exists := magicNumbers[ext]
	if !exists {
		// No magic number defined for this extension, skip validation
		return nil
	}

	if len(content) < len(expectedMagic) {
		return fmt.Errorf("%w: file too small to validate", ErrInvalidMagicNumber)
	}

	if !bytes.Equal(content[:len(expectedMagic)], expectedMagic) {
		return fmt.Errorf("%w: file content does not match %s format", ErrInvalidMagicNumber, ext)
	}

	return nil
}

// CheckMaliciousPatterns checks for known malicious patterns
func (v *FileValidator) CheckMaliciousPatterns(content []byte) error {
	// Convert to string for pattern matching
	contentStr := string(content)

	// Check for embedded scripts in images (common attack vector)
	maliciousPatterns := []string{
		"<script",
		"javascript:",
		"onerror=",
		"onload=",
		"<?php",
		"<%",
		"<jsp:",
	}

	for _, pattern := range maliciousPatterns {
		if strings.Contains(strings.ToLower(contentStr), pattern) {
			return fmt.Errorf("%w: suspicious pattern '%s' detected", ErrMaliciousContent, pattern)
		}
	}

	// Check for polyglot files (files that are valid in multiple formats)
	if v.IsPolyglot(content) {
		return fmt.Errorf("%w: file appears to be a polyglot", ErrMaliciousContent)
	}

	return nil
}

// IsPolyglot checks if file might be a polyglot (valid in multiple formats)
func (v *FileValidator) IsPolyglot(content []byte) bool {
	// Need at least some content to check
	if len(content) < 20 {
		return false
	}

	// Check if PDF header appears later in an image file
	if bytes.HasPrefix(content, []byte{0xFF, 0xD8, 0xFF}) { // JPEG
		// Look for PDF signature anywhere after JPEG header
		if bytes.Contains(content[3:], []byte("%PDF")) {
			return true
		}
		// Check for ZIP signature (PK)
		if bytes.Contains(content[3:], []byte("PK\x03\x04")) || bytes.Contains(content[3:], []byte("PK\x05\x06")) {
			return true
		}
	}

	// Check if HTML/script appears in what should be an image
	if bytes.HasPrefix(content, []byte{0x89, 0x50, 0x4E, 0x47}) { // PNG
		// Look for script/html content after PNG header
		contentStr := strings.ToLower(string(content[8:]))
		if strings.Contains(contentStr, "<script") ||
			strings.Contains(contentStr, "<html") ||
			strings.Contains(contentStr, "javascript:") ||
			strings.Contains(contentStr, "<iframe") {
			return true
		}
	}

	// Check for GIF with embedded content
	if bytes.HasPrefix(content, []byte{0x47, 0x49, 0x46}) { // GIF
		contentStr := strings.ToLower(string(content[6:]))
		if strings.Contains(contentStr, "<script") ||
			strings.Contains(contentStr, "<?php") ||
			strings.Contains(contentStr, "%PDF") {
			return true
		}
	}

	// Check for multiple file signatures in the same content
	signatures := [][]byte{
		{0xFF, 0xD8, 0xFF},       // JPEG
		{0x89, 0x50, 0x4E, 0x47}, // PNG
		{0x47, 0x49, 0x46},       // GIF
		[]byte("%PDF"),           // PDF
		[]byte("PK\x03\x04"),     // ZIP
		{0x4D, 0x5A},             // EXE
	}

	foundCount := 0
	for _, sig := range signatures {
		if bytes.Contains(content, sig) {
			foundCount++
			if foundCount > 1 {
				return true
			}
		}
	}

	return false
}

// ScanFile performs a deep scan of file content (can be extended with antivirus integration)
func (v *FileValidator) ScanFile(file multipart.File) error {
	// Reset file position
	if _, err := file.Seek(0, 0); err != nil {
		return fmt.Errorf("failed to reset file position: %w", err)
	}

	// This is where you could integrate with an antivirus scanner
	// For now, we'll do comprehensive content validation

	// Use larger buffer for better performance
	const chunkSize = 32 * 1024 // 32KB chunks
	buffer := make([]byte, chunkSize)

	// Keep a sliding window for pattern detection across chunk boundaries
	const overlapSize = 1024 // 1KB overlap to catch patterns at boundaries
	var previousTail []byte

	position := int64(0)
	for {
		n, err := file.Read(buffer)
		if errors.Is(err, io.EOF) {
			break
		}
		if err != nil {
			return fmt.Errorf("error reading file at position %d: %w", position, err)
		}

		// Combine previous tail with current chunk for boundary detection
		var scanBuffer []byte
		if previousTail != nil {
			scanBuffer = append(scanBuffer, previousTail...)
			scanBuffer = append(scanBuffer, buffer[:n]...)
		} else {
			scanBuffer = buffer[:n]
		}

		// Check chunk for malicious patterns
		// Use a temporary error to check if we should continue
		if err := v.CheckMaliciousPatterns(scanBuffer); err != nil {
			// If we're checking with overlap, only report if error is in the new content
			if previousTail != nil && len(scanBuffer) > len(previousTail) {
				// Check if the error is only in the overlap region
				if err2 := v.CheckMaliciousPatterns(buffer[:n]); err2 != nil {
					return fmt.Errorf("%w at byte position %d", err2, position)
				}
				// Error was only in overlap, which we already checked - continue
			} else {
				return fmt.Errorf("%w at byte position %d", err, position)
			}
		}

		// Check for additional patterns that might span chunks
		if err := v.checkDeepPatterns(scanBuffer, position); err != nil {
			return err
		}

		// Save tail of current chunk for next iteration
		if n > overlapSize {
			previousTail = make([]byte, overlapSize)
			copy(previousTail, buffer[n-overlapSize:n])
		} else {
			previousTail = buffer[:n]
		}

		position += int64(n)
	}

	// Reset file position for subsequent operations
	if _, err := file.Seek(0, 0); err != nil {
		return fmt.Errorf("failed to reset file position: %w", err)
	}

	return nil
}

// checkDeepPatterns performs additional security checks for patterns that might be obfuscated
func (v *FileValidator) checkDeepPatterns(content []byte, position int64) error {
	contentStr := string(content)

	// Check for hex-encoded script tags and other obfuscated patterns
	hexPatterns := []string{
		"3c7363726970743e",     // <script> in hex
		"3c73637269707420",     // <script  in hex
		"6a617661736372697074", // javascript in hex
		"3c696672616d65",       // <iframe in hex
	}

	for _, pattern := range hexPatterns {
		if strings.Contains(strings.ToLower(contentStr), pattern) {
			return fmt.Errorf("%w: hex-encoded malicious pattern detected at position %d", ErrMaliciousContent, position)
		}
	}

	// Check for base64-encoded patterns
	// Common base64 prefixes for malicious content
	base64Patterns := []string{
		"PHNjcmlwdD",     // <script in base64
		"PGlmcmFtZQ",     // <iframe in base64
		"amF2YXNjcmlwdA", // javascript in base64
	}

	for _, pattern := range base64Patterns {
		if strings.Contains(contentStr, pattern) {
			return fmt.Errorf("%w: base64-encoded malicious pattern detected at position %d", ErrMaliciousContent, position)
		}
	}

	// Check for null byte injection attempts
	if bytes.Contains(content, []byte{0x00}) {
		return fmt.Errorf("%w: null byte detected at position %d", ErrMaliciousContent, position)
	}

	return nil
}

// GetSafeFileName generates a safe filename while preserving extension
func GetSafeFileName(originalName string) string {
	// Get extension
	ext := filepath.Ext(originalName)

	// Get base name without extension
	baseName := strings.TrimSuffix(originalName, ext)

	// Sanitize base name
	safeName := SanitizeFilename(baseName)

	// Ensure it's not empty
	if safeName == "" {
		safeName = "file"
	}

	// Add timestamp to ensure uniqueness
	timestamp := fmt.Sprintf("%d", time.Now().Unix())

	return fmt.Sprintf("%s_%s%s", safeName, timestamp, ext)
}
