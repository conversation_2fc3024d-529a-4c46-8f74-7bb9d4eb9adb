package security

import (
	"html"
	"regexp"
	"strings"
)

// HTMLEncode encodes string for safe HTML output
func HTMLEncode(s string) string {
	return html.EscapeString(s)
}

// SanitizeInput removes potentially dangerous HTML/script content
func SanitizeInput(input string) string {
	// First, HTML encode the input
	input = html.EscapeString(input)

	// Remove any null bytes
	input = strings.ReplaceAll(input, "\x00", "")

	// Remove any non-printable characters except newlines and tabs
	input = removeNonPrintable(input)

	return input
}

// SanitizeJSON ensures JSON string values are safe
func SanitizeJSON(input string) string {
	// Escape quotes and backslashes
	input = strings.ReplaceAll(input, "\\", "\\\\")
	input = strings.ReplaceAll(input, "\"", "\\\"")
	input = strings.ReplaceAll(input, "\n", "\\n")
	input = strings.ReplaceAll(input, "\r", "\\r")
	input = strings.ReplaceAll(input, "\t", "\\t")

	// Remove any null bytes
	input = strings.ReplaceAll(input, "\x00", "")

	return input
}

// SanitizeMarkdown allows safe markdown but removes dangerous content
func SanitizeMarkdown(input string) string {
	// Remove script tags and their content
	scriptRegex := regexp.MustCompile(`(?i)<script[^>]*>.*?</script>`)
	input = scriptRegex.ReplaceAllString(input, "")

	// Remove style tags and their content
	styleRegex := regexp.MustCompile(`(?i)<style[^>]*>.*?</style>`)
	input = styleRegex.ReplaceAllString(input, "")

	// Remove iframe tags
	iframeRegex := regexp.MustCompile(`(?i)<iframe[^>]*>.*?</iframe>`)
	input = iframeRegex.ReplaceAllString(input, "")

	// Remove object/embed tags
	objectRegex := regexp.MustCompile(`(?i)<(object|embed)[^>]*>.*?</(object|embed)>`)
	input = objectRegex.ReplaceAllString(input, "")

	// Remove on* event handlers
	eventRegex := regexp.MustCompile(`(?i)\s*on\w+\s*=\s*["'][^"']*["']`)
	input = eventRegex.ReplaceAllString(input, "")

	// Remove javascript: protocols
	jsProtocolRegex := regexp.MustCompile(`(?i)javascript\s*:`)
	input = jsProtocolRegex.ReplaceAllString(input, "")

	// Remove data: protocols that could embed scripts
	dataProtocolRegex := regexp.MustCompile(`(?i)data\s*:\s*text/html`)
	input = dataProtocolRegex.ReplaceAllString(input, "")

	return input
}

// SanitizeFilename ensures filename is safe for filesystem operations
func SanitizeFilename(filename string) string {
	// Remove any path traversal attempts
	filename = strings.ReplaceAll(filename, "..", "")
	filename = strings.ReplaceAll(filename, "/", "_")
	filename = strings.ReplaceAll(filename, "\\", "_")

	// Remove null bytes
	filename = strings.ReplaceAll(filename, "\x00", "")

	// Remove other potentially dangerous characters
	dangerousChars := []string{"<", ">", ":", "\"", "|", "?", "*", "%", "!"}
	for _, char := range dangerousChars {
		filename = strings.ReplaceAll(filename, char, "_")
	}

	// Remove Unicode direction override and other control characters
	var result strings.Builder
	for _, r := range filename {
		// Only allow printable ASCII and safe Unicode characters
		if (r >= 32 && r < 127) || (r > 127 && r != 0x202E && r != 0x202D && r != 0x200E && r != 0x200F) {
			result.WriteRune(r)
		} else if r == ' ' {
			result.WriteRune('_')
		}
	}
	filename = result.String()

	// Limit length
	if len(filename) > 255 {
		filename = filename[:255]
	}

	// Ensure it's not empty
	filename = strings.TrimSpace(filename)
	if filename == "" {
		filename = "unnamed"
	}

	return filename
}

// SanitizeURL ensures URL is safe and well-formed
func SanitizeURL(url string) string {
	// Trim whitespace
	url = strings.TrimSpace(url)

	// Remove null bytes
	url = strings.ReplaceAll(url, "\x00", "")

	// Ensure it doesn't contain javascript: or data: protocols
	lowerURL := strings.ToLower(url)
	if strings.HasPrefix(lowerURL, "javascript:") ||
		strings.HasPrefix(lowerURL, "data:") ||
		strings.HasPrefix(lowerURL, "vbscript:") {
		return ""
	}

	// Remove any control characters
	url = removeControlCharacters(url)

	return url
}

// ValidateAndSanitizeEmail validates and sanitizes email addresses
func ValidateAndSanitizeEmail(email string) (string, bool) {
	// Basic email regex
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)

	// Trim and lowercase
	email = strings.TrimSpace(strings.ToLower(email))

	// Remove any dangerous characters
	email = SanitizeInput(email)

	// Validate
	if !emailRegex.MatchString(email) {
		return "", false
	}

	// Additional length check
	if len(email) > 254 { // RFC 5321
		return "", false
	}

	return email, true
}

// removeNonPrintable removes non-printable characters except newlines and tabs
func removeNonPrintable(s string) string {
	var result strings.Builder
	for _, r := range s {
		if r == '\n' || r == '\r' || r == '\t' || (r >= 32 && r < 127) || r > 127 {
			result.WriteRune(r)
		}
	}
	return result.String()
}

// removeControlCharacters removes control characters
func removeControlCharacters(s string) string {
	var result strings.Builder
	for _, r := range s {
		if r >= 32 || r == '\n' || r == '\r' || r == '\t' {
			result.WriteRune(r)
		}
	}
	return result.String()
}

// SanitizeStruct applies sanitization to all string fields in a struct
// This is a placeholder - in practice, you'd use reflection or code generation
type Sanitizer interface {
	Sanitize()
}

// Example implementation for common request types
type SanitizableString string

func (s *SanitizableString) Sanitize() {
	*s = SanitizableString(SanitizeInput(string(*s)))
}

// ContentSecurityPolicy helps build CSP headers
type ContentSecurityPolicy struct {
	directives map[string][]string
}

// NewContentSecurityPolicy creates a new CSP builder
func NewContentSecurityPolicy() *ContentSecurityPolicy {
	return &ContentSecurityPolicy{
		directives: make(map[string][]string),
	}
}

// DefaultSrc sets default-src directive
func (csp *ContentSecurityPolicy) DefaultSrc(sources ...string) *ContentSecurityPolicy {
	csp.directives["default-src"] = sources
	return csp
}

// ScriptSrc sets script-src directive
func (csp *ContentSecurityPolicy) ScriptSrc(sources ...string) *ContentSecurityPolicy {
	csp.directives["script-src"] = sources
	return csp
}

// StyleSrc sets style-src directive
func (csp *ContentSecurityPolicy) StyleSrc(sources ...string) *ContentSecurityPolicy {
	csp.directives["style-src"] = sources
	return csp
}

// ImgSrc sets img-src directive
func (csp *ContentSecurityPolicy) ImgSrc(sources ...string) *ContentSecurityPolicy {
	csp.directives["img-src"] = sources
	return csp
}

// Build creates the CSP header value
func (csp *ContentSecurityPolicy) Build() string {
	parts := make([]string, 0, len(csp.directives))
	for directive, sources := range csp.directives {
		parts = append(parts, directive+" "+strings.Join(sources, " "))
	}
	return strings.Join(parts, "; ")
}
