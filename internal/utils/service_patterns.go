package utils

import (
	"context"
	"errors"
	"fmt"
	"time"

	"Membership-SAAS-System-Backend/db"

	sharederrors "Membership-SAAS-System-Backend/internal/shared/errors"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog/log"
)

// Common service errors that all services can use
var (
	ErrInvalidInput       = errors.New("invalid input")
	ErrServiceUnavailable = errors.New("service unavailable")
)

// ServiceResult represents a standardized service operation result
type ServiceResult[T any] struct {
	Data    T
	Error   error
	Success bool
	Message string
}

// NewSuccessResult creates a successful service result
func NewSuccessResult[T any](data T, message string) ServiceResult[T] {
	return ServiceResult[T]{
		Data:    data,
		Error:   nil,
		Success: true,
		Message: message,
	}
}

// NewErrorResult creates an error service result
func NewErrorResult[T any](err error, message string) ServiceResult[T] {
	var zero T
	return ServiceResult[T]{
		Data:    zero,
		Error:   err,
		Success: false,
		Message: message,
	}
}

// TransactionWrapper provides a standardized way to handle database transactions
type TransactionWrapper struct {
	DB *pgxpool.Pool
}

// NewTransactionWrapper creates a new transaction wrapper
func NewTransactionWrapper(db *pgxpool.Pool) *TransactionWrapper {
	return &TransactionWrapper{DB: db}
}

// WithTransaction executes a function within a database transaction
func (tw *TransactionWrapper) WithTransaction(ctx context.Context, fn func(db.Querier) error) error {
	tx, err := tw.DB.Begin(ctx)
	if err != nil {
		log.Error().Err(err).Msg("Failed to begin transaction")
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer func() {
		if rollbackErr := tx.Rollback(ctx); rollbackErr != nil && rollbackErr.Error() != "tx is closed" {
			// Log rollback error but don't override the original error
			log.Ctx(ctx).Debug().Err(rollbackErr).Msg("Transaction rollback error")
		}
	}()

	// Create querier with transaction
	querier := db.New(tw.DB).WithTx(tx)

	// Execute the function
	err = fn(querier)
	if err != nil {
		return err
	}

	// Commit transaction
	if err = tx.Commit(ctx); err != nil {
		log.Error().Err(err).Msg("Failed to commit transaction")
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	return nil
}

// AuditInfo contains standard audit information for service operations
type AuditInfo struct {
	UserID       uuid.UUID
	Action       string
	ResourceType string
	ResourceID   uuid.UUID
	Timestamp    time.Time
	IPAddress    string
	UserAgent    string
}

// CreateAuditInfo creates audit information for an operation
func CreateAuditInfo(userID uuid.UUID, action, resourceType string, resourceID uuid.UUID) AuditInfo {
	return AuditInfo{
		UserID:       userID,
		Action:       action,
		ResourceType: resourceType,
		ResourceID:   resourceID,
		Timestamp:    time.Now(),
	}
}

// WithAudit wraps a service operation with audit logging
func WithAudit[T any](ctx context.Context, audit AuditInfo, operation func() (T, error)) (T, error) {
	start := time.Now()

	log.Info().
		Str("user_id", audit.UserID.String()).
		Str("action", audit.Action).
		Str("resource_type", audit.ResourceType).
		Str("resource_id", audit.ResourceID.String()).
		Msg("Starting operation")

	result, err := operation()

	duration := time.Since(start)

	if err != nil {
		log.Error().
			Err(err).
			Str("user_id", audit.UserID.String()).
			Str("action", audit.Action).
			Str("resource_type", audit.ResourceType).
			Str("resource_id", audit.ResourceID.String()).
			Dur("duration", duration).
			Msg("Operation failed")
	} else {
		log.Info().
			Str("user_id", audit.UserID.String()).
			Str("action", audit.Action).
			Str("resource_type", audit.ResourceType).
			Str("resource_id", audit.ResourceID.String()).
			Dur("duration", duration).
			Msg("Operation completed successfully")
	}

	return result, err
}

// ServiceConfig contains common configuration for services
type ServiceConfig struct {
	MaxRetries      int
	RetryDelay      time.Duration
	TimeoutDuration time.Duration
	EnableAudit     bool
	EnableMetrics   bool
}

// DefaultServiceConfig returns default service configuration
func DefaultServiceConfig() ServiceConfig {
	return ServiceConfig{
		MaxRetries:      3,
		RetryDelay:      time.Second,
		TimeoutDuration: 30 * time.Second,
		EnableAudit:     true,
		EnableMetrics:   true,
	}
}

// RetryableOperation represents an operation that can be retried
type RetryableOperation[T any] func() (T, error)

// WithRetry executes an operation with retry logic
func WithRetry[T any](config ServiceConfig, operation RetryableOperation[T]) (T, error) {
	var lastErr error
	var result T

	for attempt := 0; attempt <= config.MaxRetries; attempt++ {
		if attempt > 0 {
			log.Warn().
				Int("attempt", attempt).
				Dur("delay", config.RetryDelay).
				Msg("Retrying operation")
			time.Sleep(config.RetryDelay)
		}

		result, lastErr = operation()
		if lastErr == nil {
			return result, nil
		}

		// Don't retry certain types of errors
		if isNonRetryableError(lastErr) {
			break
		}
	}

	var zero T
	return zero, fmt.Errorf("operation failed after %d attempts: %w", config.MaxRetries+1, lastErr)
}

// isNonRetryableError determines if an error should not be retried
func isNonRetryableError(err error) bool {
	nonRetryableErrors := []error{
		sharederrors.ErrNotFound,
		sharederrors.ErrUnauthorized,
		ErrInvalidInput,
		sharederrors.ErrConflict,
	}

	for _, nonRetryable := range nonRetryableErrors {
		if errors.Is(err, nonRetryable) {
			return true
		}
	}

	return false
}

// Validator interface for input validation
type Validator[T any] interface {
	Validate(T) error
}

// WithValidation wraps a service operation with input validation
func WithValidation[T, R any](input T, validator Validator[T], operation func(T) (R, error)) (R, error) {
	if err := validator.Validate(input); err != nil {
		var zero R
		return zero, fmt.Errorf("validation failed: %w", err)
	}

	return operation(input)
}

// Permission checker interface
type PermissionChecker interface {
	CheckPermission(ctx context.Context, userID uuid.UUID, resource string, action string) error
}

// WithPermissionCheck wraps a service operation with permission checking
func WithPermissionCheck[T any](ctx context.Context, userID uuid.UUID, resource, action string, checker PermissionChecker, operation func() (T, error)) (T, error) {
	err := checker.CheckPermission(ctx, userID, resource, action)
	if err != nil {
		var zero T
		return zero, fmt.Errorf("permission check failed: %w", err)
	}

	return operation()
}

// CacheKey generates a consistent cache key for a resource
func CacheKey(resourceType string, parts ...string) string {
	key := resourceType
	for _, part := range parts {
		key += ":" + part
	}
	return key
}

// ParseUUIDSafely parses a UUID string safely, returning zero UUID on error
func ParseUUIDSafely(s string) uuid.UUID {
	if id, err := uuid.Parse(s); err == nil {
		return id
	}
	return uuid.Nil
}

// StringPtr creates a pointer to a string
func StringPtr(s string) *string {
	if s == "" {
		return nil
	}
	return &s
}

// TimePtr creates a pointer to a time.Time
func TimePtr(t time.Time) *time.Time {
	if t.IsZero() {
		return nil
	}
	return &t
}

// StringPtrValue safely gets the value from a string pointer
func StringPtrValue(ptr *string) string {
	if ptr == nil {
		return ""
	}
	return *ptr
}

// StringValueOrDefault returns the string value or a default if pointer is nil
func StringValueOrDefault(ptr *string, defaultValue string) string {
	if ptr == nil {
		return defaultValue
	}
	return *ptr
}
