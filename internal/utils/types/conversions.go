package types

import (
	"fmt"
	"math"
)

// SafeInt32 safely converts an int to int32, returning an error if out of range
func SafeInt32(val int) (int32, error) {
	if val > math.MaxInt32 || val < math.MinInt32 {
		return 0, fmt.Errorf("value %d out of int32 range", val)
	}
	return int32(val), nil
}

// SafeInt64ToInt32 safely converts an int64 to int32, returning an error if out of range
func SafeInt64ToInt32(val int64) (int32, error) {
	if val > math.MaxInt32 || val < math.MinInt32 {
		return 0, fmt.Errorf("value %d out of int32 range", val)
	}
	return int32(val), nil
}

// SafeUint64ToInt64 safely converts a uint64 to int64, returning an error if out of range
func SafeUint64ToInt64(val uint64) (int64, error) {
	if val > math.MaxInt64 {
		return 0, fmt.<PERSON><PERSON>rf("value %d out of int64 range", val)
	}
	return int64(val), nil
}

// SafeInt64ToUint64 safely converts an int64 to uint64, returning an error if negative
func SafeInt64ToUint64(val int64) (uint64, error) {
	if val < 0 {
		return 0, fmt.Errorf("cannot convert negative value %d to uint64", val)
	}
	return uint64(val), nil
}

// SafeIntToUint64 safely converts an int to uint64, returning an error if negative
func SafeIntToUint64(val int) (uint64, error) {
	if val < 0 {
		return 0, fmt.Errorf("cannot convert negative value %d to uint64", val)
	}
	return uint64(val), nil
}

// MustInt32 converts an int to int32, panicking if out of range (for test code only)
func MustInt32(val int) int32 {
	result, err := SafeInt32(val)
	if err != nil {
		panic(err)
	}
	return result
}

// DefaultInt32 converts an int to int32, returning defaultVal if out of range
func DefaultInt32(val int, defaultVal int32) int32 {
	result, err := SafeInt32(val)
	if err != nil {
		return defaultVal
	}
	return result
}
