package url

import (
	"fmt"
	"strings"
)

// BuildFullURL constructs a full URL from a base URL and relative path
func BuildFullURL(baseURL, relativePath string) string {
	// Return as-is if already a full URL
	if strings.HasPrefix(relativePath, "http://") || strings.HasPrefix(relativePath, "https://") {
		return relativePath
	}

	// Ensure base URL doesn't end with slash
	baseURL = strings.TrimSuffix(baseURL, "/")

	// Remove leading "./" from relative path
	relativePath = strings.TrimPrefix(relativePath, "./")

	// Remove leading "/" to avoid double slashes
	relativePath = strings.TrimPrefix(relativePath, "/")

	return fmt.Sprintf("%s/%s", baseURL, relativePath)
}

// FileURLResponse represents standardized file URL fields
type FileURLResponse struct {
	RelativePath string `json:"relative_path"` // Database value
	FullURL      string `json:"full_url"`      // Constructed URL
	FilePath     string `json:"file_path"`     // Backward compatibility
}

// BuildFileURLResponse creates standardized URL fields from a relative path
func BuildFileURLResponse(baseURL, relativePath string) FileURLResponse {
	fullURL := BuildFullURL(baseURL, relativePath)
	return FileURLResponse{
		RelativePath: relativePath,
		FullURL:      fullURL,
		FilePath:     fullURL, // Backward compatibility
	}
}
