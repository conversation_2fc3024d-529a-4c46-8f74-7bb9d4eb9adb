package url

import (
	"testing"
)

func TestBuildFullURL(t *testing.T) {
	tests := []struct {
		name         string
		baseURL      string
		relativePath string
		expected     string
	}{
		{
			name:         "simple relative path",
			baseURL:      "http://localhost:8080",
			relativePath: "uploads/file.pdf",
			expected:     "http://localhost:8080/uploads/file.pdf",
		},
		{
			name:         "relative path with leading slash",
			baseURL:      "http://localhost:8080",
			relativePath: "/uploads/file.pdf",
			expected:     "http://localhost:8080/uploads/file.pdf",
		},
		{
			name:         "relative path with ./ prefix",
			baseURL:      "http://localhost:8080",
			relativePath: "./uploads/file.pdf",
			expected:     "http://localhost:8080/uploads/file.pdf",
		},
		{
			name:         "base URL with trailing slash",
			baseURL:      "http://localhost:8080/",
			relativePath: "uploads/file.pdf",
			expected:     "http://localhost:8080/uploads/file.pdf",
		},
		{
			name:         "already full HTTP URL",
			baseURL:      "http://localhost:8080",
			relativePath: "http://example.com/file.pdf",
			expected:     "http://example.com/file.pdf",
		},
		{
			name:         "already full HTTPS URL",
			baseURL:      "http://localhost:8080",
			relativePath: "https://example.com/file.pdf",
			expected:     "https://example.com/file.pdf",
		},
		{
			name:         "empty relative path",
			baseURL:      "http://localhost:8080",
			relativePath: "",
			expected:     "http://localhost:8080/",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := BuildFullURL(tt.baseURL, tt.relativePath)
			if result != tt.expected {
				t.Errorf("BuildFullURL(%q, %q) = %q, want %q", tt.baseURL, tt.relativePath, result, tt.expected)
			}
		})
	}
}

func TestBuildFileURLResponse(t *testing.T) {
	tests := []struct {
		name         string
		baseURL      string
		relativePath string
		expected     FileURLResponse
	}{
		{
			name:         "standard file path",
			baseURL:      "http://localhost:8080",
			relativePath: "uploads/org/file.pdf",
			expected: FileURLResponse{
				RelativePath: "uploads/org/file.pdf",
				FullURL:      "http://localhost:8080/uploads/org/file.pdf",
				FilePath:     "http://localhost:8080/uploads/org/file.pdf",
			},
		},
		{
			name:         "already full URL",
			baseURL:      "http://localhost:8080",
			relativePath: "https://cdn.example.com/file.pdf",
			expected: FileURLResponse{
				RelativePath: "https://cdn.example.com/file.pdf",
				FullURL:      "https://cdn.example.com/file.pdf",
				FilePath:     "https://cdn.example.com/file.pdf",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := BuildFileURLResponse(tt.baseURL, tt.relativePath)
			if result.RelativePath != tt.expected.RelativePath {
				t.Errorf("RelativePath = %q, want %q", result.RelativePath, tt.expected.RelativePath)
			}
			if result.FullURL != tt.expected.FullURL {
				t.Errorf("FullURL = %q, want %q", result.FullURL, tt.expected.FullURL)
			}
			if result.FilePath != tt.expected.FilePath {
				t.Errorf("FilePath = %q, want %q", result.FilePath, tt.expected.FilePath)
			}
		})
	}
}
