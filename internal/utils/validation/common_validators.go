package validation

import (
	"fmt"
	"regexp"
	"strings"

	"Membership-SAAS-System-Backend/internal/auth/roles"

	"github.com/google/uuid"
)

// Common validation patterns used across the application

// UUID validation patterns
var (
	// UUIDPattern matches valid UUID v4 format
	UUIDPattern = regexp.MustCompile(`^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$`)
)

// ValidateUUID validates that a string is a valid UUID
func ValidateUUID(uuidStr string) error {
	if uuidStr == "" {
		return fmt.Errorf("UUID cannot be empty")
	}

	if _, err := uuid.Parse(uuidStr); err != nil {
		return fmt.Errorf("invalid UUID format: %s", uuidStr)
	}

	return nil
}

// ValidateUUIDs validates a slice of UUID strings
func ValidateUUIDs(uuidStrs []string) error {
	for i, uuidStr := range uuidStrs {
		if err := ValidateUUID(uuidStr); err != nil {
			return fmt.Errorf("invalid UUID at index %d: %w", i, err)
		}
	}
	return nil
}

// ParseAndValidateUUID validates and parses a UUID string
func ParseAndValidateUUID(uuidStr string) (uuid.UUID, error) {
	if err := ValidateUUID(uuidStr); err != nil {
		return uuid.Nil, err
	}

	return uuid.Parse(uuidStr)
}

// ParseAndValidateUUIDs validates and parses a slice of UUID strings
func ParseAndValidateUUIDs(uuidStrs []string) ([]uuid.UUID, error) {
	result := make([]uuid.UUID, len(uuidStrs))
	for i, uuidStr := range uuidStrs {
		parsedUUID, err := ParseAndValidateUUID(uuidStr)
		if err != nil {
			return nil, fmt.Errorf("invalid UUID at index %d: %w", i, err)
		}
		result[i] = parsedUUID
	}
	return result, nil
}

// Role validation functions

// ValidatePlatformRole validates platform role strings
func ValidatePlatformRole(role string) error {
	if role == "" {
		return fmt.Errorf("platform role cannot be empty")
	}

	validRoles := []string{"user", "staff", "super_admin"}
	for _, validRole := range validRoles {
		if role == validRole {
			return nil
		}
	}

	return fmt.Errorf("invalid platform role: %s. Valid roles are: %s", role, strings.Join(validRoles, ", "))
}

// ValidateOrganizationRole validates organization role strings
func ValidateOrganizationRole(role string) error {
	if role == "" {
		return fmt.Errorf("organization role cannot be empty")
	}

	validRoles := []string{"member", "manager", "admin", "owner"}
	for _, validRole := range validRoles {
		if role == validRole {
			return nil
		}
	}

	return fmt.Errorf("invalid organization role: %s. Valid roles are: %s", role, strings.Join(validRoles, ", "))
}

// ValidateRoleHierarchy validates that role1 has sufficient privileges for role2
func ValidateRoleHierarchy(userRole, requiredRole string, isOrgRole bool) error {
	if isOrgRole {
		if !roles.IsOrgRoleHigherOrEqual(userRole, requiredRole) {
			return fmt.Errorf("insufficient organization role: have %s, need %s or higher", userRole, requiredRole)
		}
	} else {
		if !roles.IsPlatformRoleHigherOrEqual(userRole, requiredRole) {
			return fmt.Errorf("insufficient platform role: have %s, need %s or higher", userRole, requiredRole)
		}
	}
	return nil
}

// Email validation patterns
var (
	// EmailPattern matches valid email format (basic pattern for custom validation)
	EmailPattern = regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
)

// ValidateEmail validates email format (supplementary to go-playground/validator)
func ValidateEmail(email string) error {
	if email == "" {
		return fmt.Errorf("email cannot be empty")
	}

	if len(email) > 254 {
		return fmt.Errorf("email too long: maximum 254 characters")
	}

	if !EmailPattern.MatchString(email) {
		return fmt.Errorf("invalid email format: %s", email)
	}

	return nil
}

// ValidateOptionalEmail validates email if provided (allows empty/nil)
func ValidateOptionalEmail(email *string) error {
	if email == nil || *email == "" {
		return nil
	}
	return ValidateEmail(*email)
}

// Phone validation patterns
var (
	// E164Pattern matches E.164 phone number format
	E164Pattern = regexp.MustCompile(`^\+[1-9]\d{1,14}$`)
)

// ValidatePhoneE164 validates phone number in E.164 format
func ValidatePhoneE164(phone string) error {
	if phone == "" {
		return fmt.Errorf("phone number cannot be empty")
	}

	if !E164Pattern.MatchString(phone) {
		return fmt.Errorf("invalid phone number format: must be E.164 format (e.g., +1234567890)")
	}

	return nil
}

// ValidateOptionalPhoneE164 validates phone number if provided (allows empty/nil)
func ValidateOptionalPhoneE164(phone *string) error {
	if phone == nil || *phone == "" {
		return nil
	}
	return ValidatePhoneE164(*phone)
}

// Event status validation
func ValidateEventStatus(status string) error {
	if status == "" {
		return fmt.Errorf("event status cannot be empty")
	}

	validStatuses := []string{"published", "archived", "deleted", "draft", "hidden", "cancelled"}
	for _, validStatus := range validStatuses {
		if status == validStatus {
			return nil
		}
	}

	return fmt.Errorf("invalid event status: %s. Valid statuses are: %s", status, strings.Join(validStatuses, ", "))
}

// ValidateOptionalEventStatus validates event status if provided (allows empty/nil)
func ValidateOptionalEventStatus(status *string) error {
	if status == nil || *status == "" {
		return nil
	}
	return ValidateEventStatus(*status)
}

// Event location type validation
func ValidateEventLocationType(locationType string) error {
	if locationType == "" {
		return fmt.Errorf("location type cannot be empty")
	}

	validTypes := []string{"online", "physical", "hybrid"}
	for _, validType := range validTypes {
		if locationType == validType {
			return nil
		}
	}

	return fmt.Errorf("invalid location type: %s. Valid types are: %s", locationType, strings.Join(validTypes, ", "))
}

// Registration status validation
func ValidateRegistrationStatus(status string) error {
	if status == "" {
		return fmt.Errorf("registration status cannot be empty")
	}

	validStatuses := []string{"registered", "waitlisted", "cancelled", "attended", "no_show"}
	for _, validStatus := range validStatuses {
		if status == validStatus {
			return nil
		}
	}

	return fmt.Errorf("invalid registration status: %s. Valid statuses are: %s", status, strings.Join(validStatuses, ", "))
}

// Verification type validation
func ValidateVerificationType(verificationType string) error {
	if verificationType == "" {
		return fmt.Errorf("verification type cannot be empty")
	}

	validTypes := []string{"hk_id_card", "passport", "student_id", "driver_license", "government_id"}
	for _, validType := range validTypes {
		if verificationType == validType {
			return nil
		}
	}

	return fmt.Errorf("invalid verification type: %s. Valid types are: %s", verificationType, strings.Join(validTypes, ", "))
}

// ValidateVerificationTypes validates a slice of verification types
func ValidateVerificationTypes(verificationTypes []string) error {
	for i, verificationType := range verificationTypes {
		if err := ValidateVerificationType(verificationType); err != nil {
			return fmt.Errorf("invalid verification type at index %d: %w", i, err)
		}
	}
	return nil
}

// URL validation patterns
var (
	// URLPattern matches basic URL format
	URLPattern = regexp.MustCompile(`^https?://[^\s/$.?#].\S*$`)
)

// ValidateURL validates URL format (supplementary to go-playground/validator)
func ValidateURL(url string) error {
	if url == "" {
		return fmt.Errorf("URL cannot be empty")
	}

	if len(url) > 2048 {
		return fmt.Errorf("URL too long: maximum 2048 characters")
	}

	if !URLPattern.MatchString(url) {
		return fmt.Errorf("invalid URL format: %s", url)
	}

	return nil
}

// ValidateOptionalURL validates URL if provided (allows empty/nil)
func ValidateOptionalURL(url *string) error {
	if url == nil || *url == "" {
		return nil
	}
	return ValidateURL(*url)
}

// ValidateURLs validates a slice of URLs
func ValidateURLs(urls []string) error {
	for i, url := range urls {
		if err := ValidateURL(url); err != nil {
			return fmt.Errorf("invalid URL at index %d: %w", i, err)
		}
	}
	return nil
}

// Pagination validation
func ValidatePaginationParams(limit, offset int) error {
	if limit < 0 {
		return fmt.Errorf("limit must be non-negative, got: %d", limit)
	}
	if limit > 100 {
		return fmt.Errorf("limit too large: maximum 100, got: %d", limit)
	}
	if offset < 0 {
		return fmt.Errorf("offset must be non-negative, got: %d", offset)
	}
	return nil
}

// String length validation helpers
func ValidateStringLength(value, fieldName string, minLength, maxLength int) error {
	if len(value) < minLength {
		return fmt.Errorf("%s too short: minimum %d characters, got %d", fieldName, minLength, len(value))
	}
	if len(value) > maxLength {
		return fmt.Errorf("%s too long: maximum %d characters, got %d", fieldName, maxLength, len(value))
	}
	return nil
}

// ValidateOptionalStringLength validates string length if provided (allows empty/nil)
func ValidateOptionalStringLength(value *string, fieldName string, minLength, maxLength int) error {
	if value == nil || *value == "" {
		return nil
	}
	return ValidateStringLength(*value, fieldName, minLength, maxLength)
}

// Numeric validation helpers
func ValidatePositiveInt32(value int32, fieldName string) error {
	if value <= 0 {
		return fmt.Errorf("%s must be positive, got: %d", fieldName, value)
	}
	return nil
}

func ValidateNonNegativeInt32(value int32, fieldName string) error {
	if value < 0 {
		return fmt.Errorf("%s must be non-negative, got: %d", fieldName, value)
	}
	return nil
}

func ValidateOptionalPositiveInt32(value *int32, fieldName string) error {
	if value == nil {
		return nil
	}
	return ValidatePositiveInt32(*value, fieldName)
}

func ValidateOptionalNonNegativeInt32(value *int32, fieldName string) error {
	if value == nil {
		return nil
	}
	return ValidateNonNegativeInt32(*value, fieldName)
}
