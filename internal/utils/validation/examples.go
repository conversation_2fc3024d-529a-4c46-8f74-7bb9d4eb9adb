package validation

import (
	"fmt"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
)

// Example usage patterns for the new validation utilities
// These examples show how to refactor existing handler validation logic

// ExampleHandlerWithOldPattern shows the old pattern with duplicate validation
func ExampleHandlerWithOldPattern(c echo.Context) error {
	// OLD PATTERN - this is what we're replacing

	// UUID validation (repeated in many handlers)
	orgIDStr := c.Param("orgID")
	if orgIDStr == "" {
		return fmt.Errorf("missing organization ID")
	}
	orgID, err := uuid.Parse(orgIDStr)
	if err != nil {
		return fmt.Errorf("invalid organization ID format")
	}

	// Event ID validation (repeated in many handlers)
	eventIDStr := c.Param("eventID")
	if eventIDStr == "" {
		return fmt.Errorf("missing event ID")
	}
	eventID, err := uuid.Parse(eventIDStr)
	if err != nil {
		return fmt.Errorf("invalid event ID format")
	}

	// Role validation (repeated in many handlers)
	role := c.Que<PERSON>("role")
	if role != "" {
		validRoles := []string{"member", "manager", "admin", "owner"}
		roleValid := false
		for _, validRole := range validRoles {
			if role == validRole {
				roleValid = true
				break
			}
		}
		if !roleValid {
			return fmt.Errorf("invalid role")
		}
	}

	// Pagination validation (repeated in many handlers)
	limitStr := c.QueryParam("limit")
	limit := 10 // default
	if limitStr != "" {
		// parsing and validation logic...
	}

	// Use extracted values
	fmt.Printf("OrgID: %s, EventID: %s, Role: %s, Limit: %d\n", orgID, eventID, role, limit)
	return nil
}

// ExampleHandlerWithNewPattern shows the new pattern using shared utilities
func ExampleHandlerWithNewPattern(c echo.Context) error {
	// NEW PATTERN - using shared validation utilities

	// UUID validation - single line, consistent error messages
	orgID, err := ExtractAndValidateUUID(c, "orgID")
	if err != nil {
		return err
	}

	eventID, err := ExtractAndValidateUUID(c, "eventID")
	if err != nil {
		return err
	}

	// Role validation - single line, consistent validation
	role, err := ExtractAndValidateRole(c, "role", true) // true = organization role
	if err != nil {
		return err
	}

	// Pagination validation - single line, built-in range checking
	limit, offset, err := ExtractPaginationParams(c)
	if err != nil {
		return err
	}

	// Use extracted values
	fmt.Printf("OrgID: %s, EventID: %s, Role: %s, Limit: %d, Offset: %d\n", orgID, eventID, role, limit, offset)
	return nil
}

// ExampleServiceWithOldPattern shows old service validation pattern
func ExampleServiceWithOldPattern(userID, orgID uuid.UUID, userRole string) error {
	// OLD PATTERN - validation logic duplicated across services

	if userID == uuid.Nil {
		return fmt.Errorf("user ID cannot be nil")
	}

	if orgID == uuid.Nil {
		return fmt.Errorf("organization ID cannot be nil")
	}

	if userRole == "" {
		return fmt.Errorf("user role cannot be empty")
	}

	// Role validation logic (repeated in many places)
	validRoles := []string{"member", "manager", "admin", "owner"}
	roleValid := false
	for _, validRole := range validRoles {
		if userRole == validRole {
			roleValid = true
			break
		}
	}
	if !roleValid {
		return fmt.Errorf("invalid user role")
	}

	// Permission validation logic (repeated in many places)
	if userRole != "admin" && userRole != "owner" {
		return fmt.Errorf("insufficient permissions")
	}

	return nil
}

// ExampleServiceWithNewPattern shows new service validation pattern
func ExampleServiceWithNewPattern(userID, orgID uuid.UUID, userRole string) error {
	// NEW PATTERN - using shared validation utilities

	// Organization membership validation - single function call
	params := OrganizationMembershipParams{
		UserID:         userID,
		OrganizationID: orgID,
		RequiredRole:   "admin", // minimum required role
		Action:         "manage_events",
	}

	if err := ValidateOrganizationMembership(params); err != nil {
		return err
	}

	// Role hierarchy validation - consistent across all services
	if err := ValidateRoleHierarchy(userRole, "admin", true); err != nil {
		return err
	}

	return nil
}

// ExampleRequestValidationWithOldPattern shows old request validation
type ExampleOldRequest struct {
	Title  string   `json:"title"`
	Email  string   `json:"email"`
	Phone  string   `json:"phone"`
	Status string   `json:"status"`
	TagIDs []string `json:"tag_ids"`
}

func (r *ExampleOldRequest) ValidateOldWay() error {
	// OLD PATTERN - manual validation in each struct

	if r.Title == "" {
		return fmt.Errorf("title is required")
	}
	if len(r.Title) > 255 {
		return fmt.Errorf("title too long")
	}

	// Email validation (repeated across many structs)
	if r.Email != "" {
		// Manual email regex or validation...
	}

	// Phone validation (repeated across many structs)
	if r.Phone != "" {
		// Manual phone validation...
	}

	// Status validation (repeated across many structs)
	validStatuses := []string{"active", "inactive", "pending"}
	// Manual status validation using validStatuses...
	_ = validStatuses // Avoid unused variable error

	// UUID validation for each tag (repeated across many structs)
	for _, tagID := range r.TagIDs {
		if _, err := uuid.Parse(tagID); err != nil {
			return fmt.Errorf("invalid tag ID")
		}
	}

	return nil
}

// ExampleRequestValidationWithNewPattern shows new request validation
type ExampleNewRequest struct {
	Title  string   `json:"title" validate:"required,min=1,max=255"`
	Email  string   `json:"email" validate:"omitempty,email"`
	Phone  string   `json:"phone" validate:"omitempty,e164"`
	Status string   `json:"status" validate:"omitempty,event_status"`
	TagIDs []string `json:"tag_ids" validate:"omitempty,dive,uuid_string"`
}

func ExampleValidateNewRequest(rv *RequestValidator, req *ExampleNewRequest) error {
	// NEW PATTERN - using struct tags + custom validators + shared validation functions

	// Basic struct validation using tags
	if err := rv.ValidateStruct(req); err != nil {
		return err
	}

	// Custom business logic validation using shared utilities
	if req.Email != "" {
		if err := ValidateEmail(req.Email); err != nil {
			return err
		}
	}

	if req.Phone != "" {
		if err := ValidatePhoneE164(req.Phone); err != nil {
			return err
		}
	}

	if len(req.TagIDs) > 0 {
		if err := ValidateUUIDs(req.TagIDs); err != nil {
			return err
		}
	}

	return nil
}

// ExamplePermissionValidationWithNewPattern shows permission validation patterns
func ExamplePermissionValidationWithNewPattern(userID, eventID uuid.UUID, action string) error {
	// Event permission validation
	params := EventPermissionParams{
		UserID:       userID,
		EventID:      eventID,
		Action:       action,
		UserRole:     "manager",
		IsEventOwner: false,
	}

	return ValidateEventPermission(params)
}

// ExampleAdminActionValidation shows admin action validation
func ExampleAdminActionValidation(actorUserID, targetUserID uuid.UUID) error {
	params := AdminActionParams{
		ActorUserID:       actorUserID,
		TargetUserID:      &targetUserID,
		Action:            "impersonate_user",
		ActorPlatformRole: "super_admin",
	}

	return ValidateAdminAction(params)
}
