package validation

import (
	"fmt"

	"github.com/google/uuid"
)

// Organization membership validation patterns

// OrganizationMembershipParams contains parameters for organization membership validation
type OrganizationMembershipParams struct {
	UserID         uuid.UUID
	OrganizationID uuid.UUID
	RequiredRole   string
	Action         string // e.g., "create_event", "manage_members"
}

// ValidateOrganizationMembership validates that a user has the required role in an organization
func ValidateOrganizationMembership(params OrganizationMembershipParams) error {
	if params.UserID == uuid.Nil {
		return fmt.Errorf("user ID cannot be nil")
	}

	if params.OrganizationID == uuid.Nil {
		return fmt.Errorf("organization ID cannot be nil")
	}

	if params.RequiredRole == "" {
		return fmt.Errorf("required role cannot be empty")
	}

	if err := ValidateOrganizationRole(params.RequiredRole); err != nil {
		return fmt.Errorf("invalid required role: %w", err)
	}

	return nil
}

// EventPermissionParams contains parameters for event permission validation
type EventPermissionParams struct {
	UserID       uuid.UUID
	EventID      uuid.UUID
	Action       string
	UserRole     string
	IsEventOwner bool
}

// ValidateEventPermission validates event-specific permissions
func ValidateEventPermission(params EventPermissionParams) error {
	if params.UserID == uuid.Nil {
		return fmt.Errorf("user ID cannot be nil for event permission validation")
	}

	if params.EventID == uuid.Nil {
		return fmt.Errorf("event ID cannot be nil for event permission validation")
	}

	if params.Action == "" {
		return fmt.Errorf("action cannot be empty for event permission validation")
	}

	// Validate action type
	validActions := []string{"view", "edit", "delete", "register", "manage_registrations", "check_in"}
	actionValid := false
	for _, validAction := range validActions {
		if params.Action == validAction {
			actionValid = true
			break
		}
	}

	if !actionValid {
		return fmt.Errorf("invalid action: %s. Valid actions are: view, edit, delete, register, manage_registrations, check_in", params.Action)
	}

	return nil
}

// VolunteerPermissionParams contains parameters for volunteer permission validation
type VolunteerPermissionParams struct {
	UserID              uuid.UUID
	OrganizationID      uuid.UUID
	EventID             *uuid.UUID
	Action              string
	IsOrgVolunteer      bool
	IsEventVolunteer    bool
	VolunteerIsApproved bool
}

// ValidateVolunteerPermission validates volunteer-specific permissions
func ValidateVolunteerPermission(params VolunteerPermissionParams) error {
	if params.UserID == uuid.Nil {
		return fmt.Errorf("user ID cannot be nil for volunteer permission validation")
	}

	if params.OrganizationID == uuid.Nil {
		return fmt.Errorf("organization ID cannot be nil for volunteer permission validation")
	}

	if params.Action == "" {
		return fmt.Errorf("action cannot be empty for volunteer permission validation")
	}

	// Validate action type
	validActions := []string{"apply", "check_in_participants", "manage_event", "view_volunteer_list"}
	actionValid := false
	for _, validAction := range validActions {
		if params.Action == validAction {
			actionValid = true
			break
		}
	}

	if !actionValid {
		return fmt.Errorf("invalid volunteer action: %s. Valid actions are: apply, check_in_participants, manage_event, view_volunteer_list", params.Action)
	}

	// For event-specific actions, event ID is required
	eventSpecificActions := []string{"check_in_participants", "manage_event"}
	for _, eventAction := range eventSpecificActions {
		if params.Action == eventAction && (params.EventID == nil || *params.EventID == uuid.Nil) {
			return fmt.Errorf("event ID is required for action: %s", params.Action)
		}
	}

	return nil
}

// ResourcePermissionParams contains parameters for resource permission validation
type ResourcePermissionParams struct {
	UserID          uuid.UUID
	ResourceID      uuid.UUID
	OrganizationID  uuid.UUID
	Action          string
	UserRole        string
	IsResourceOwner bool
}

// ValidateResourcePermission validates resource-specific permissions
func ValidateResourcePermission(params ResourcePermissionParams) error {
	if params.UserID == uuid.Nil {
		return fmt.Errorf("user ID cannot be nil for resource permission validation")
	}

	if params.ResourceID == uuid.Nil {
		return fmt.Errorf("resource ID cannot be nil for resource permission validation")
	}

	if params.OrganizationID == uuid.Nil {
		return fmt.Errorf("organization ID cannot be nil for resource permission validation")
	}

	if params.Action == "" {
		return fmt.Errorf("action cannot be empty for resource permission validation")
	}

	// Validate action type
	validActions := []string{"view", "download", "edit", "delete", "upload"}
	actionValid := false
	for _, validAction := range validActions {
		if params.Action == validAction {
			actionValid = true
			break
		}
	}

	if !actionValid {
		return fmt.Errorf("invalid resource action: %s. Valid actions are: view, download, edit, delete, upload", params.Action)
	}

	if params.UserRole != "" {
		if err := ValidateOrganizationRole(params.UserRole); err != nil {
			return fmt.Errorf("invalid user role in resource permission: %w", err)
		}
	}

	return nil
}

// MembershipChangeParams contains parameters for membership change validation
type MembershipChangeParams struct {
	TargetUserID   uuid.UUID
	OrganizationID uuid.UUID
	ActorUserID    uuid.UUID
	NewRole        string
	CurrentRole    string
	Action         string // "add", "remove", "change_role"
}

// ValidateMembershipChange validates organization membership changes
func ValidateMembershipChange(params MembershipChangeParams) error {
	if params.TargetUserID == uuid.Nil {
		return fmt.Errorf("target user ID cannot be nil")
	}

	if params.OrganizationID == uuid.Nil {
		return fmt.Errorf("organization ID cannot be nil")
	}

	if params.ActorUserID == uuid.Nil {
		return fmt.Errorf("actor user ID cannot be nil")
	}

	if params.Action == "" {
		return fmt.Errorf("action cannot be empty")
	}

	// Validate action type
	validActions := []string{"add", "remove", "change_role"}
	actionValid := false
	for _, validAction := range validActions {
		if params.Action == validAction {
			actionValid = true
			break
		}
	}

	if !actionValid {
		return fmt.Errorf("invalid membership action: %s. Valid actions are: add, remove, change_role", params.Action)
	}

	// Validate roles for role-based actions
	if params.Action == "add" || params.Action == "change_role" {
		if params.NewRole == "" {
			return fmt.Errorf("new role is required for action: %s", params.Action)
		}
		if err := ValidateOrganizationRole(params.NewRole); err != nil {
			return fmt.Errorf("invalid new role: %w", err)
		}
	}

	if params.Action == "change_role" || params.Action == "remove" {
		if params.CurrentRole == "" {
			return fmt.Errorf("current role is required for action: %s", params.Action)
		}
		if err := ValidateOrganizationRole(params.CurrentRole); err != nil {
			return fmt.Errorf("invalid current role: %w", err)
		}
	}

	return nil
}

// AdminActionParams contains parameters for admin action validation
type AdminActionParams struct {
	ActorUserID       uuid.UUID
	TargetUserID      *uuid.UUID
	OrganizationID    *uuid.UUID
	Action            string
	ActorPlatformRole string
	ActorOrgRole      *string
}

// ValidateAdminAction validates admin-level actions
func ValidateAdminAction(params AdminActionParams) error {
	if params.ActorUserID == uuid.Nil {
		return fmt.Errorf("actor user ID cannot be nil")
	}

	if params.Action == "" {
		return fmt.Errorf("action cannot be empty")
	}

	if params.ActorPlatformRole == "" {
		return fmt.Errorf("actor platform role cannot be empty")
	}

	if err := ValidatePlatformRole(params.ActorPlatformRole); err != nil {
		return fmt.Errorf("invalid actor platform role: %w", err)
	}

	// Validate action type
	validActions := []string{
		"view_audit_logs", "impersonate_user", "manage_platform_users",
		"access_all_organizations", "system_maintenance", "view_analytics",
	}
	actionValid := false
	for _, validAction := range validActions {
		if params.Action == validAction {
			actionValid = true
			break
		}
	}

	if !actionValid {
		return fmt.Errorf("invalid admin action: %s", params.Action)
	}

	// For organization-specific actions, organization ID is required
	orgSpecificActions := []string{"access_all_organizations", "view_analytics"}
	for _, orgAction := range orgSpecificActions {
		if params.Action == orgAction && (params.OrganizationID == nil || *params.OrganizationID == uuid.Nil) {
			return fmt.Errorf("organization ID is required for action: %s", params.Action)
		}
	}

	// For user-specific actions, target user ID is required
	userSpecificActions := []string{"impersonate_user", "manage_platform_users"}
	for _, userAction := range userSpecificActions {
		if params.Action == userAction && (params.TargetUserID == nil || *params.TargetUserID == uuid.Nil) {
			return fmt.Errorf("target user ID is required for action: %s", params.Action)
		}
	}

	return nil
}

// ValidateContextSwitchPermission validates organization context switching
func ValidateContextSwitchPermission(userID, targetOrgID uuid.UUID, userRole string) error {
	if userID == uuid.Nil {
		return fmt.Errorf("user ID cannot be nil for context switch")
	}

	if targetOrgID == uuid.Nil {
		return fmt.Errorf("target organization ID cannot be nil for context switch")
	}

	if userRole == "" {
		return fmt.Errorf("user role cannot be empty for context switch")
	}

	if err := ValidateOrganizationRole(userRole); err != nil {
		return fmt.Errorf("invalid user role for context switch: %w", err)
	}

	return nil
}
