package validation

import (
	"net/http"

	claims "Membership-SAAS-System-Backend/internal/auth/claims"
	"Membership-SAAS-System-Backend/internal/payloads"
	httputil "Membership-SAAS-System-Backend/internal/utils/http"

	"github.com/labstack/echo/v4"
	"github.com/rs/zerolog/log"
)

// This file demonstrates how to refactor existing handlers to use the new validation utilities

// OriginalCreateEventHandler shows the current pattern in event/management_handlers.go
func OriginalCreateEventHandler(c echo.Context, managementService interface{}, validator *RequestValidator) error {
	// ORIGINAL PATTERN - duplicated across many handlers

	// Extract organization ID from path (pattern repeated 20+ times)
	orgIDStr := c.Param("orgID")
	_, err := ParseAndValidateUUID(orgIDStr) // Using the actual function
	if err != nil {
		return httputil.HandleError(c, http.StatusBadRequest, "Invalid organization ID", err)
	}

	// Extract user ID from JWT claims (pattern repeated 30+ times)
	userID := claims.GetUserIDFromContext(c)
	if userID.String() == "" {
		return httputil.HandleError(c, http.StatusUnauthorized, "Unauthorized", nil)
	}

	// Parse request body (pattern repeated in all POST/PATCH handlers)
	var req payloads.CreateEventRequest
	if err := c.Bind(&req); err != nil {
		return httputil.HandleError(c, http.StatusBadRequest, "Invalid request body", err)
	}

	// Validate request (pattern repeated in all handlers with payloads)
	if err := validator.ValidateStruct(&req); err != nil {
		return httputil.HandleError(c, http.StatusBadRequest, "Validation failed", err)
	}

	// Service call and response (pattern varies)
	// event, err := managementService.CreateEvent(c.Request().Context(), orgID, userID, req)
	// ... error handling and response

	return nil
}

// RefactoredCreateEventHandler shows how to use the new validation utilities
func RefactoredCreateEventHandler(c echo.Context, managementService interface{}, validator *RequestValidator) error {
	// REFACTORED PATTERN - using shared validation utilities

	// Extract and validate organization ID - single line, consistent error messages
	_, err := ExtractAndValidateUUID(c, "orgID")
	if err != nil {
		return httputil.HandleError(c, http.StatusBadRequest, err.Error(), err)
	}

	// Extract user ID from JWT claims - unchanged (this is already clean)
	userID := claims.GetUserIDFromContext(c)
	if userID.String() == "" {
		return httputil.HandleError(c, http.StatusUnauthorized, "Unauthorized", nil)
	}

	// Parse and validate request in one step
	var req payloads.CreateEventRequest
	if err := c.Bind(&req); err != nil {
		return httputil.HandleError(c, http.StatusBadRequest, "Invalid request body", err)
	}

	// Enhanced validation with custom business rules
	validationResult := ValidateCreateEventRequest(validator, &req)
	if validationResult.HasErrors() {
		return httputil.HandleError(c, http.StatusBadRequest, "Validation failed", nil) // errors are in validationResult
	}

	// Service call and response (unchanged)
	// event, err := managementService.CreateEvent(c.Request().Context(), orgID, userID, req)
	// ... error handling and response

	return nil
}

// Example of how to refactor a complex handler with multiple validation patterns
func RefactorComplexEventListHandler(c echo.Context) error {
	// BEFORE: Multiple duplicate validation patterns

	// Organization ID validation
	orgID, err := ExtractAndValidateUUID(c, "orgID")
	if err != nil {
		return httputil.HandleError(c, http.StatusBadRequest, err.Error(), err)
	}

	// Optional event ID filter
	eventID, err := ExtractAndValidateOptionalUUID(c, "eventID")
	if err != nil {
		return httputil.HandleError(c, http.StatusBadRequest, err.Error(), err)
	}

	// Pagination parameters
	limit, offset, err := ExtractPaginationParams(c)
	if err != nil {
		return httputil.HandleError(c, http.StatusBadRequest, err.Error(), err)
	}

	// Date range filters
	startDate, err := ExtractAndValidateDate(c, "start_date")
	if err != nil {
		return httputil.HandleError(c, http.StatusBadRequest, err.Error(), err)
	}

	endDate, err := ExtractAndValidateDate(c, "end_date")
	if err != nil {
		return httputil.HandleError(c, http.StatusBadRequest, err.Error(), err)
	}

	// Status filter
	status, err := ExtractAndValidateEventStatus(c, "status")
	if err != nil {
		return httputil.HandleError(c, http.StatusBadRequest, err.Error(), err)
	}

	// Role filter
	role, err := ExtractAndValidateRole(c, "role", true) // true = organization role
	if err != nil {
		return httputil.HandleError(c, http.StatusBadRequest, err.Error(), err)
	}

	// Sort validation
	validSortOptions := []string{"date_asc", "date_desc", "title_asc", "title_desc", "created_asc", "created_desc"}
	sort, err := ExtractAndValidateSort(c, validSortOptions)
	if err != nil {
		return httputil.HandleError(c, http.StatusBadRequest, err.Error(), err)
	}

	// Organization ID filter (for admin views)
	organizationFilter, err := ExtractAndValidateQueryUUID(c, "organization_id")
	if err != nil {
		return httputil.HandleError(c, http.StatusBadRequest, err.Error(), err)
	}

	// Log the extracted and validated parameters
	log.Info().
		Str("orgID", orgID.String()).
		Interface("eventID", eventID).
		Int("limit", limit).
		Int("offset", offset).
		Interface("startDate", startDate).
		Interface("endDate", endDate).
		Str("status", status).
		Str("role", role).
		Str("sort", sort).
		Interface("organizationFilter", organizationFilter).
		Msg("Event list parameters validated")

	// Now all parameters are validated and can be used safely
	// Service call would use these validated parameters

	return c.JSON(http.StatusOK, map[string]interface{}{
		"message": "Parameters validated successfully",
		"params": map[string]interface{}{
			"orgID":              orgID,
			"eventID":            eventID,
			"limit":              limit,
			"offset":             offset,
			"startDate":          startDate,
			"endDate":            endDate,
			"status":             status,
			"role":               role,
			"sort":               sort,
			"organizationFilter": organizationFilter,
		},
	})
}

// Example of service-level validation refactoring
type EventService struct {
	// service dependencies
}

// OriginalServicePattern shows the old validation pattern in services
func (s *EventService) OriginalCreateEventPattern(orgIDStr, userIDStr, eventCreatorIDStr string) error {
	// ORIGINAL PATTERN - validation logic duplicated across services

	// UUID validations (repeated in every service method)
	// orgID, err := uuid.Parse(orgIDStr)
	// if err != nil { return fmt.Errorf("invalid organization ID") }
	// userID, err := uuid.Parse(userIDStr)
	// if err != nil { return fmt.Errorf("invalid user ID") }

	// Role and permission validations (repeated across many methods)
	// Check if user has permission to create events in organization
	// Check if user role is sufficient (manager, admin, or owner)
	// Check if organization exists and is active

	// Business rule validations (some repeated, some specific)
	// Check event date constraints
	// Check capacity limits
	// Check conflicting events

	return nil
}

// RefactoredServicePattern shows the new validation pattern using shared utilities
func (s *EventService) RefactoredCreateEventPattern(orgIDStr, userIDStr, eventCreatorIDStr string) error {
	// REFACTORED PATTERN - using shared validation utilities

	// Parse and validate UUIDs
	orgID, err := ParseAndValidateUUID(orgIDStr)
	if err != nil {
		return err
	}

	userID, err := ParseAndValidateUUID(userIDStr)
	if err != nil {
		return err
	}

	eventCreatorID, err := ParseAndValidateUUID(eventCreatorIDStr)
	if err != nil {
		return err
	}

	// Organization membership validation
	membershipParams := OrganizationMembershipParams{
		UserID:         userID,
		OrganizationID: orgID,
		RequiredRole:   "manager",
		Action:         "create_event",
	}

	if err := ValidateOrganizationMembership(membershipParams); err != nil {
		return err
	}

	// Event permission validation
	eventParams := EventPermissionParams{
		UserID:       userID,
		EventID:      eventCreatorID,
		Action:       "create",
		UserRole:     "manager", // This would come from actual user context
		IsEventOwner: true,      // User creating event becomes owner
	}

	if err := ValidateEventPermission(eventParams); err != nil {
		return err
	}

	// Business-specific validations can still be done here
	// but common patterns are handled by shared utilities

	return nil
}

// Example of refactoring admin-level validation
func RefactoredAdminUserManagement(actorUserIDStr, targetUserIDStr, action string) error {
	// Parse and validate UUIDs
	actorUserID, err := ParseAndValidateUUID(actorUserIDStr)
	if err != nil {
		return err
	}

	targetUserID, err := ParseAndValidateUUID(targetUserIDStr)
	if err != nil {
		return err
	}

	adminParams := AdminActionParams{
		ActorUserID:       actorUserID,
		TargetUserID:      &targetUserID,
		Action:            action,
		ActorPlatformRole: "super_admin", // This would come from JWT claims
	}

	return ValidateAdminAction(adminParams)
}

// Summary of benefits from using these validation utilities:
//
// 1. REDUCED DUPLICATION:
//    - UUID parsing: Reduced from 50+ identical implementations to 1 shared function
//    - Role validation: Reduced from 20+ implementations to 2 functions (platform/org roles)
//    - Pagination: Reduced from 15+ implementations to 1 shared function
//    - Permission checks: Reduced from scattered logic to structured validation functions
//
// 2. CONSISTENT ERROR MESSAGES:
//    - All UUID errors use the same format
//    - All role validation errors are consistent
//    - All permission errors follow the same pattern
//
// 3. EASIER MAINTENANCE:
//    - Change validation logic in one place
//    - Add new validation rules easily
//    - Fix validation bugs once, applied everywhere
//
// 4. BETTER TESTING:
//    - Test validation logic once in the utilities
//    - Handlers become simpler to test
//    - Reduced test duplication
//
// 5. IMPROVED SECURITY:
//    - Consistent validation reduces risk of bypassing checks
//    - Centralized permission logic is easier to audit
//    - Standard patterns reduce implementation errors
