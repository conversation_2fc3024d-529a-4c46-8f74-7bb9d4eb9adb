package validation

import (
	"fmt"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
)

// Request validation helpers for common handler patterns

// ExtractAndValidateUUID extracts and validates a UUID parameter from the request
func ExtractAndValidateUUID(c echo.Context, paramName string) (uuid.UUID, error) {
	paramValue := c.Param(paramName)
	if paramValue == "" {
		return uuid.Nil, fmt.Errorf("missing required parameter: %s", paramName)
	}

	parsedUUID, err := ParseAndValidateUUID(paramValue)
	if err != nil {
		return uuid.Nil, fmt.Errorf("invalid %s: %w", paramName, err)
	}

	return parsedUUID, nil
}

// ExtractAndValidateOptionalUUID extracts and validates an optional UUID parameter
func ExtractAndValidateOptionalUUID(c echo.Context, paramName string) (*uuid.UUID, error) {
	paramValue := c.Param(paramName)
	if paramValue == "" {
		return nil, nil
	}

	parsedUUID, err := ParseAndValidateUUID(paramValue)
	if err != nil {
		return nil, fmt.Errorf("invalid %s: %w", paramName, err)
	}

	return &parsedUUID, nil
}

// ExtractAndValidateQueryUUID extracts and validates a UUID from query parameters
func ExtractAndValidateQueryUUID(c echo.Context, paramName string) (*uuid.UUID, error) {
	paramValue := c.QueryParam(paramName)
	if paramValue == "" {
		return nil, nil
	}

	parsedUUID, err := ParseAndValidateUUID(paramValue)
	if err != nil {
		return nil, fmt.Errorf("invalid query parameter %s: %w", paramName, err)
	}

	return &parsedUUID, nil
}

// ExtractAndValidateInt extracts and validates an integer parameter
func ExtractAndValidateInt(c echo.Context, paramName string, defaultValue int) (int, error) {
	paramValue := c.QueryParam(paramName)
	if paramValue == "" {
		return defaultValue, nil
	}

	intValue, err := strconv.Atoi(paramValue)
	if err != nil {
		return 0, fmt.Errorf("invalid %s: must be an integer", paramName)
	}

	return intValue, nil
}

// ExtractAndValidateIntWithRange extracts and validates an integer parameter within a range
func ExtractAndValidateIntWithRange(c echo.Context, paramName string, defaultValue, minValue, maxValue int) (int, error) {
	intValue, err := ExtractAndValidateInt(c, paramName, defaultValue)
	if err != nil {
		return 0, err
	}

	if intValue < minValue || intValue > maxValue {
		return 0, fmt.Errorf("invalid %s: must be between %d and %d", paramName, minValue, maxValue)
	}

	return intValue, nil
}

// ExtractPaginationParams extracts and validates pagination parameters
func ExtractPaginationParams(c echo.Context) (limit, offset int, err error) {
	limit, err = ExtractAndValidateIntWithRange(c, "limit", 10, 1, 100)
	if err != nil {
		return 0, 0, err
	}

	offset, err = ExtractAndValidateIntWithRange(c, "offset", 0, 0, 1000000)
	if err != nil {
		return 0, 0, err
	}

	return limit, offset, nil
}

// ExtractAndValidateDate extracts and validates a date parameter
func ExtractAndValidateDate(c echo.Context, paramName string) (*time.Time, error) {
	paramValue := c.QueryParam(paramName)
	if paramValue == "" {
		return nil, nil
	}

	parsedTime, err := time.Parse(time.RFC3339, paramValue)
	if err != nil {
		return nil, fmt.Errorf("invalid %s: must be in RFC3339 format (e.g., 2023-12-31T23:59:59Z)", paramName)
	}

	return &parsedTime, nil
}

// ExtractAndValidateString extracts and validates a string parameter with length constraints
func ExtractAndValidateString(c echo.Context, paramName string, required bool, minLen, maxLen int) (string, error) {
	paramValue := c.QueryParam(paramName)

	if paramValue == "" && required {
		return "", fmt.Errorf("missing required parameter: %s", paramName)
	}

	if paramValue == "" {
		return "", nil
	}

	if err := ValidateStringLength(paramValue, paramName, minLen, maxLen); err != nil {
		return "", err
	}

	return paramValue, nil
}

// ExtractAndValidateRole extracts and validates a role parameter
func ExtractAndValidateRole(c echo.Context, paramName string, isOrgRole bool) (string, error) {
	role := c.QueryParam(paramName)
	if role == "" {
		return "", nil
	}

	if isOrgRole {
		if err := ValidateOrganizationRole(role); err != nil {
			return "", fmt.Errorf("invalid %s: %w", paramName, err)
		}
	} else {
		if err := ValidatePlatformRole(role); err != nil {
			return "", fmt.Errorf("invalid %s: %w", paramName, err)
		}
	}

	return role, nil
}

// ExtractAndValidateStatus extracts and validates a status parameter
func ExtractAndValidateStatus(c echo.Context, paramName string, validStatuses []string) (string, error) {
	status := c.QueryParam(paramName)
	if status == "" {
		return "", nil
	}

	for _, validStatus := range validStatuses {
		if status == validStatus {
			return status, nil
		}
	}

	return "", fmt.Errorf("invalid %s: must be one of %v", paramName, validStatuses)
}

// ExtractAndValidateEventStatus extracts and validates event status parameter
func ExtractAndValidateEventStatus(c echo.Context, paramName string) (string, error) {
	validStatuses := []string{"published", "archived", "deleted", "draft", "hidden", "cancelled"}
	return ExtractAndValidateStatus(c, paramName, validStatuses)
}

// ExtractAndValidateRegistrationStatus extracts and validates registration status parameter
func ExtractAndValidateRegistrationStatus(c echo.Context, paramName string) (string, error) {
	validStatuses := []string{"registered", "waitlisted", "cancelled", "attended", "no_show"}
	return ExtractAndValidateStatus(c, paramName, validStatuses)
}

// ExtractAndValidateSort extracts and validates sort parameter
func ExtractAndValidateSort(c echo.Context, validSortOptions []string) (string, error) {
	sort := c.QueryParam("sort")
	if sort == "" {
		return "", nil
	}

	for _, validSort := range validSortOptions {
		if sort == validSort {
			return sort, nil
		}
	}

	return "", fmt.Errorf("invalid sort option: must be one of %v", validSortOptions)
}

// RequestValidationResult holds validation results
type RequestValidationResult struct {
	Errors []string `json:"errors,omitempty"`
	Valid  bool     `json:"valid"`
}

// AddError adds an error to the validation result
func (r *RequestValidationResult) AddError(err error) {
	if err != nil {
		r.Errors = append(r.Errors, err.Error())
		r.Valid = false
	}
}

// AddErrorString adds an error string to the validation result
func (r *RequestValidationResult) AddErrorString(errStr string) {
	if errStr != "" {
		r.Errors = append(r.Errors, errStr)
		r.Valid = false
	}
}

// HasErrors returns true if there are validation errors
func (r *RequestValidationResult) HasErrors() bool {
	return len(r.Errors) > 0
}

// NewRequestValidationResult creates a new validation result
func NewRequestValidationResult() *RequestValidationResult {
	return &RequestValidationResult{
		Valid:  true,
		Errors: make([]string, 0),
	}
}

// ValidateRequestStruct validates a request struct using the validator and adds custom validation
func (rv *RequestValidator) ValidateRequestStruct(req interface{}, customValidations ...func() error) *RequestValidationResult {
	result := NewRequestValidationResult()

	// Run struct validation
	if err := rv.ValidateStruct(req); err != nil {
		result.AddError(err)
	}

	// Run custom validations
	for _, customValidation := range customValidations {
		if err := customValidation(); err != nil {
			result.AddError(err)
		}
	}

	return result
}

// Common request validation patterns

// ValidateCreateEventRequest validates event creation request with custom logic
func ValidateCreateEventRequest(rv *RequestValidator, req interface{}) *RequestValidationResult {
	result := rv.ValidateRequestStruct(req)

	// Add any custom event creation validation logic here
	// This can be extended based on business rules

	return result
}

// ValidateUpdateEventRequest validates event update request with custom logic
func ValidateUpdateEventRequest(rv *RequestValidator, req interface{}) *RequestValidationResult {
	result := rv.ValidateRequestStruct(req)

	// Add any custom event update validation logic here
	// This can be extended based on business rules

	return result
}

// ValidateUserRegistrationRequest validates user registration request
func ValidateUserRegistrationRequest(rv *RequestValidator, req interface{}) *RequestValidationResult {
	result := rv.ValidateRequestStruct(req)

	// Add any custom user registration validation logic here
	// This can be extended based on business rules

	return result
}

// ValidateOrganizationRequest validates organization-related requests
func ValidateOrganizationRequest(rv *RequestValidator, req interface{}) *RequestValidationResult {
	result := rv.ValidateRequestStruct(req)

	// Add any custom organization validation logic here
	// This can be extended based on business rules

	return result
}
