package validation

import (
	"regexp"

	"Membership-SAAS-System-Backend/internal/payloads"

	"github.com/go-playground/validator/v10"
)

// Regular expression for alphanumdash validation: allows letters, numbers, and dashes.
var alphanumdashRegex = regexp.MustCompile(`^[a-zA-Z0-9-]+$`)

// Regular expression for alphanumunderscore validation: allows letters, numbers, and underscores.
var alphanumUnderscoreRegex = regexp.MustCompile(`^[a-zA-Z0-9_]+$`)

// alphanumdash validation function
func validateAlphanumdash(fl validator.FieldLevel) bool {
	// Check if the field value is a string and matches the regex
	return alphanumdashRegex.MatchString(fl.Field().String())
}

// alphanumunderscore validation function
func validateAlphanumUnderscore(fl validator.FieldLevel) bool {
	// Check if the field value is a string and matches the regex
	return alphanumUnderscoreRegex.MatchString(fl.Field().String())
}

// Custom UUID validation function
func validateUUIDString(fl validator.FieldLevel) bool {
	return ValidateUUID(fl.Field().String()) == nil
}

// Custom platform role validation function
func validatePlatformRole(fl validator.FieldLevel) bool {
	return ValidatePlatformRole(fl.Field().String()) == nil
}

// Custom organization role validation function
func validateOrganizationRole(fl validator.FieldLevel) bool {
	return ValidateOrganizationRole(fl.Field().String()) == nil
}

// Custom event status validation function
func validateEventStatus(fl validator.FieldLevel) bool {
	return ValidateEventStatus(fl.Field().String()) == nil
}

// Custom event location type validation function
func validateEventLocationType(fl validator.FieldLevel) bool {
	return ValidateEventLocationType(fl.Field().String()) == nil
}

// Custom registration status validation function
func validateRegistrationStatus(fl validator.FieldLevel) bool {
	return ValidateRegistrationStatus(fl.Field().String()) == nil
}

// Custom verification type validation function
func validateVerificationType(fl validator.FieldLevel) bool {
	return ValidateVerificationType(fl.Field().String()) == nil
}

// RequestValidator wraps the validator instance.
type RequestValidator struct {
	validate *validator.Validate
}

// NewRequestValidator creates a new RequestValidator.
func NewRequestValidator() *RequestValidator {
	v := validator.New()

	// Register existing custom validations
	validators := map[string]validator.Func{
		"alphanumdash":             validateAlphanumdash,
		"alphanumunderscore":       validateAlphanumUnderscore,
		"verification_type_enum":   payloads.ValidateVerificationTypeEnum,
		"admin_review_status_enum": payloads.ValidateAdminReviewStatusEnum,
		// Register new common validators
		"uuid_string":         validateUUIDString,
		"platform_role":       validatePlatformRole,
		"organization_role":   validateOrganizationRole,
		"event_status":        validateEventStatus,
		"event_location_type": validateEventLocationType,
		"registration_status": validateRegistrationStatus,
		"verification_type":   validateVerificationType,
	}

	// Register all validators
	for tag, validatorFunc := range validators {
		if err := v.RegisterValidation(tag, validatorFunc); err != nil {
			println("FATAL: Failed to register", tag, "validator:", err.Error())
		}
	}

	return &RequestValidator{validate: v}
}

// ValidateStruct validates a struct using the underlying validator.
// It's a convenience method to be called by handlers.
func (rv *RequestValidator) ValidateStruct(s interface{}) error {
	return rv.validate.Struct(s)
}

// RegisterValidation can be used to register custom validation functions if needed.
func (rv *RequestValidator) RegisterValidation(tag string, fn validator.Func) error {
	return rv.validate.RegisterValidation(tag, fn)
}

// Validate implements the echo.Validator interface
func (rv *RequestValidator) Validate(i interface{}) error {
	return rv.validate.Struct(i)
}
