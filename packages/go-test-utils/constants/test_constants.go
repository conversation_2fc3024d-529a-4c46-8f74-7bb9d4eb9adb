package constants

// Test phone numbers
const (
	TestDefaultPhone = "+85291827364" // Non-sequential digits to avoid honeypot detection
	DefaultUserName  = "Test User"
)

// OAuth/PKCE test constants
const (
	OAuthState       = "test-state-12345"
	OAuthClientID    = "test-client"
	OAuthRedirectURI = "http://localhost:3000/callback"
	PKCEMethod       = "S256"
	PKCEVerifier     = "test-verifier"
	PKCEChallenge    = "JBbiqONGWPaAmwXk_8bT6UnlPfrn65D32eZlJS-zGG0" // SHA256 of "test-verifier"
)

// JWT test constants
const (
	JWTAccessTokenSecret    = "test-access-token-secret-32-chars-long"
	JWTRefreshTokenSecret   = "test-refresh-token-secret-32-chars-long"
	JWTAccessTokenDuration  = "15m"
	JWTRefreshTokenDuration = "168h" // 7 days
)

// Twilio mock constants
const (
	TwilioMockOTPSID = "mock_otp_sid_123456"
	ValidOTP         = "123456"
)
