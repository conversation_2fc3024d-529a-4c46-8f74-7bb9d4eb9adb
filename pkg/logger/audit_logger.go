// Package logger provides audit logging infrastructure for security-sensitive operations.
// It implements structured logging with GDPR compliance, retention policies, and monitoring integration.
package logger

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/google/uuid"
	"github.com/rs/zerolog"
	"gopkg.in/natefinch/lumberjack.v2"

	"Membership-SAAS-System-Backend/internal/services/audit"
)

// AuditLoggerConfig contains configuration for the audit logger
type AuditLoggerConfig struct {
	// File output configuration
	LogDir          string
	MaxSize         int           // megabytes
	MaxBackups      int           // number of backups
	MaxAge          int           // days
	RetentionPeriod time.Duration // for GDPR compliance
	Compress        bool

	// Structured logging configuration
	EnableJSON    bool
	EnableConsole bool
	ConsoleLevel  zerolog.Level
	FileLevel     zerolog.Level

	// Monitoring integration
	EnableMetrics      bool
	EnableTracing      bool
	EnableAlerts       bool
	AlertWebhookURL    string
	AlertSeverityLevel string

	// GDPR compliance
	EnablePII             bool
	AnonymizeAfterDays    int
	RedactSensitiveFields []string
}

// SecurityAuditLogger wraps the audit logger with security-specific functionality
type SecurityAuditLogger struct {
	config       AuditLoggerConfig
	logger       zerolog.Logger
	fileLogger   zerolog.Logger
	auditService audit.AuditLogger
	metrics      *AuditMetrics
}

// AuditMetrics tracks audit logging metrics
type AuditMetrics struct {
	TotalLogs            int64
	SecurityEvents       int64
	AuthenticationLogs   int64
	AuthorizationLogs    int64
	DataAccessLogs       int64
	ConfigChangeLogs     int64
	ErrorLogs            int64
	SuspiciousActivities int64
}

// DefaultAuditLoggerConfig returns a production-ready audit logger configuration
func DefaultAuditLoggerConfig() AuditLoggerConfig {
	return AuditLoggerConfig{
		LogDir:             "/var/log/the-moment/audit",
		MaxSize:            100, // 100MB per file
		MaxBackups:         30,  // Keep 30 backup files
		MaxAge:             90,  // Keep logs for 90 days
		RetentionPeriod:    90 * 24 * time.Hour,
		Compress:           true,
		EnableJSON:         true,
		EnableConsole:      true,
		ConsoleLevel:       zerolog.InfoLevel,
		FileLevel:          zerolog.DebugLevel,
		EnableMetrics:      true,
		EnableTracing:      true,
		EnableAlerts:       true,
		AlertSeverityLevel: "high",
		EnablePII:          false, // Disabled by default for GDPR
		AnonymizeAfterDays: 30,
		RedactSensitiveFields: []string{
			"password", "token", "secret", "api_key", "credit_card",
			"ssn", "id_number", "bank_account", "private_key",
		},
	}
}

// NewSecurityAuditLogger creates a new security audit logger
func NewSecurityAuditLogger(config AuditLoggerConfig, auditService audit.AuditLogger) (*SecurityAuditLogger, error) {
	// Ensure log directory exists
	if err := os.MkdirAll(config.LogDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create log directory: %w", err)
	}

	// Configure file rotation
	fileWriter := &lumberjack.Logger{
		Filename:   filepath.Join(config.LogDir, "security-audit.log"),
		MaxSize:    config.MaxSize,
		MaxBackups: config.MaxBackups,
		MaxAge:     config.MaxAge,
		Compress:   config.Compress,
	}

	// Create file logger
	fileLogger := zerolog.New(fileWriter).
		Level(config.FileLevel).
		With().
		Timestamp().
		Str("component", "security-audit").
		Logger()

	// Create console logger if enabled
	var mainLogger zerolog.Logger
	if config.EnableConsole {
		consoleWriter := zerolog.ConsoleWriter{
			Out:        os.Stdout,
			TimeFormat: time.RFC3339,
		}

		// Multi-level logger (both console and file)
		mainLogger = zerolog.New(zerolog.MultiLevelWriter(consoleWriter, fileWriter)).
			Level(config.FileLevel).
			With().
			Timestamp().
			Str("component", "security-audit").
			Logger()
	} else {
		mainLogger = fileLogger
	}

	return &SecurityAuditLogger{
		config:       config,
		logger:       mainLogger,
		fileLogger:   fileLogger,
		auditService: auditService,
		metrics:      &AuditMetrics{},
	}, nil
}

// LogSecurityEvent logs a security-related event with full context
func (l *SecurityAuditLogger) LogSecurityEvent(ctx context.Context, event SecurityEvent) {
	// Create structured log entry
	logEvent := l.logger.Info().
		Str("event_type", "security").
		Str("security_category", event.Category).
		Str("action", event.Action).
		Str("severity", event.Severity).
		Str("user_id", event.UserID.String()).
		Str("correlation_id", event.CorrelationID).
		Str("trace_id", event.TraceID)

	// Add organization context if present
	if event.OrganizationID != nil {
		logEvent = logEvent.Str("organization_id", event.OrganizationID.String())
	}

	// Add resource information
	if event.ResourceType != "" {
		logEvent = logEvent.
			Str("resource_type", event.ResourceType).
			Str("resource_id", event.ResourceID.String())
	}

	// Add request metadata
	if event.IPAddress != "" {
		logEvent = logEvent.Str("ip_address", l.sanitizeIP(event.IPAddress))
	}
	if event.UserAgent != "" {
		logEvent = logEvent.Str("user_agent", event.UserAgent)
	}

	// Add details with PII redaction
	if event.Details != nil {
		sanitizedDetails := l.sanitizeDetails(event.Details)
		logEvent = logEvent.Interface("details", sanitizedDetails)
	}

	// Add outcome
	logEvent = logEvent.
		Bool("success", event.Success).
		Str("result", event.Result)

	if event.Error != nil {
		logEvent = logEvent.Err(event.Error)
	}

	// Log the event
	logEvent.Msg(event.Message)

	// Update metrics
	l.updateMetrics(event)

	// Send to audit service for persistence
	if l.auditService != nil {
		l.persistAuditLog(ctx, event)
	}

	// Check if alert is needed
	if l.shouldAlert(event) {
		l.sendAlert(ctx, event)
	}
}

// LogAuthentication logs authentication attempts
func (l *SecurityAuditLogger) LogAuthentication(ctx context.Context, params AuthenticationLogParams) {
	event := SecurityEvent{
		Category:       "authentication",
		Action:         params.Action,
		Severity:       l.determineAuthSeverity(params),
		UserID:         params.UserID,
		OrganizationID: params.OrganizationID,
		IPAddress:      params.IPAddress,
		UserAgent:      params.UserAgent,
		Success:        params.Success,
		Result:         params.Result,
		Message:        fmt.Sprintf("Authentication %s: %s", params.Action, params.Result),
		Details: map[string]interface{}{
			"method":        params.Method,
			"provider":      params.Provider,
			"mfa_required":  params.MFARequired,
			"mfa_completed": params.MFACompleted,
		},
		CorrelationID: params.CorrelationID,
		TraceID:       params.TraceID,
	}

	l.LogSecurityEvent(ctx, event)
}

// LogAuthorization logs authorization decisions
func (l *SecurityAuditLogger) LogAuthorization(ctx context.Context, params AuthorizationLogParams) {
	event := SecurityEvent{
		Category:       "authorization",
		Action:         params.Action,
		Severity:       l.determineAuthzSeverity(params),
		UserID:         params.UserID,
		OrganizationID: params.OrganizationID,
		ResourceType:   params.ResourceType,
		ResourceID:     params.ResourceID,
		IPAddress:      params.IPAddress,
		UserAgent:      params.UserAgent,
		Success:        params.Allowed,
		Result:         l.formatAuthzResult(params),
		Message:        fmt.Sprintf("Authorization check for %s on %s", params.Action, params.ResourceType),
		Details: map[string]interface{}{
			"required_permission": params.RequiredPermission,
			"user_permissions":    params.UserPermissions,
			"reason":              params.Reason,
		},
		CorrelationID: params.CorrelationID,
		TraceID:       params.TraceID,
	}

	l.LogSecurityEvent(ctx, event)
}

// LogDataAccess logs sensitive data access
func (l *SecurityAuditLogger) LogDataAccess(ctx context.Context, params DataAccessLogParams) {
	event := SecurityEvent{
		Category:       "data_access",
		Action:         params.Operation,
		Severity:       "medium",
		UserID:         params.UserID,
		OrganizationID: params.OrganizationID,
		ResourceType:   params.DataType,
		ResourceID:     params.DataID,
		IPAddress:      params.IPAddress,
		UserAgent:      params.UserAgent,
		Success:        params.Success,
		Result:         params.Result,
		Message:        fmt.Sprintf("Data access: %s on %s", params.Operation, params.DataType),
		Details: map[string]interface{}{
			"fields_accessed": params.FieldsAccessed,
			"record_count":    params.RecordCount,
			"export_format":   params.ExportFormat,
			"purpose":         params.Purpose,
		},
		CorrelationID: params.CorrelationID,
		TraceID:       params.TraceID,
	}

	// Elevate severity for bulk operations
	if params.RecordCount > 100 {
		event.Severity = "high"
	}

	l.LogSecurityEvent(ctx, event)
}

// LogConfigurationChange logs system configuration changes
func (l *SecurityAuditLogger) LogConfigurationChange(ctx context.Context, params ConfigChangeLogParams) {
	event := SecurityEvent{
		Category:       "configuration",
		Action:         params.Action,
		Severity:       "high", // Config changes are always high severity
		UserID:         params.UserID,
		OrganizationID: params.OrganizationID,
		ResourceType:   "configuration",
		ResourceID:     uuid.MustParse(params.ConfigID),
		IPAddress:      params.IPAddress,
		UserAgent:      params.UserAgent,
		Success:        params.Success,
		Result:         params.Result,
		Message:        fmt.Sprintf("Configuration change: %s", params.ConfigName),
		Details: map[string]interface{}{
			"config_type":   params.ConfigType,
			"old_value":     l.redactIfSensitive(params.ConfigName, params.OldValue),
			"new_value":     l.redactIfSensitive(params.ConfigName, params.NewValue),
			"change_reason": params.ChangeReason,
		},
		CorrelationID: params.CorrelationID,
		TraceID:       params.TraceID,
	}

	l.LogSecurityEvent(ctx, event)
}

// LogSuspiciousActivity logs potentially malicious activity
func (l *SecurityAuditLogger) LogSuspiciousActivity(ctx context.Context, params SuspiciousActivityParams) {
	event := SecurityEvent{
		Category:       "suspicious_activity",
		Action:         params.ActivityType,
		Severity:       params.Severity,
		UserID:         params.UserID,
		OrganizationID: params.OrganizationID,
		IPAddress:      params.IPAddress,
		UserAgent:      params.UserAgent,
		Success:        false,
		Result:         "detected",
		Message:        fmt.Sprintf("Suspicious activity detected: %s", params.Description),
		Details: map[string]interface{}{
			"indicators":      params.Indicators,
			"risk_score":      params.RiskScore,
			"action_taken":    params.ActionTaken,
			"pattern_matched": params.PatternMatched,
		},
		CorrelationID: params.CorrelationID,
		TraceID:       params.TraceID,
	}

	l.LogSecurityEvent(ctx, event)

	// Always alert on suspicious activity
	l.sendAlert(ctx, event)
}

// Helper methods

func (l *SecurityAuditLogger) sanitizeIP(ip string) string {
	if !l.config.EnablePII {
		// Anonymize IP by removing last octet for IPv4 or last segment for IPv6
		// This is a simplified implementation
		return "xxx.xxx.xxx.xxx"
	}
	return ip
}

func (l *SecurityAuditLogger) sanitizeDetails(details map[string]interface{}) map[string]interface{} {
	sanitized := make(map[string]interface{})
	for key, value := range details {
		if l.isSensitiveField(key) {
			sanitized[key] = "[REDACTED]"
		} else {
			sanitized[key] = value
		}
	}
	return sanitized
}

func (l *SecurityAuditLogger) isSensitiveField(field string) bool {
	for _, sensitive := range l.config.RedactSensitiveFields {
		if field == sensitive {
			return true
		}
	}
	return false
}

func (l *SecurityAuditLogger) redactIfSensitive(configName string, value interface{}) interface{} {
	if l.isSensitiveField(configName) {
		return "[REDACTED]"
	}
	return value
}

func (l *SecurityAuditLogger) updateMetrics(event SecurityEvent) {
	l.metrics.TotalLogs++

	switch event.Category {
	case "authentication":
		l.metrics.AuthenticationLogs++
	case "authorization":
		l.metrics.AuthorizationLogs++
	case "data_access":
		l.metrics.DataAccessLogs++
	case "configuration":
		l.metrics.ConfigChangeLogs++
	case "suspicious_activity":
		l.metrics.SuspiciousActivities++
	}

	if event.Severity == "critical" || event.Severity == "high" {
		l.metrics.SecurityEvents++
	}

	if event.Error != nil {
		l.metrics.ErrorLogs++
	}
}

func (l *SecurityAuditLogger) persistAuditLog(ctx context.Context, event SecurityEvent) {
	params := audit.LogActionParams{
		UserID:         event.UserID,
		OrganizationID: event.OrganizationID,
		Action:         fmt.Sprintf("%s.%s", event.Category, event.Action),
		ResourceType:   event.ResourceType,
		ResourceID:     &event.ResourceID,
		Details:        event.Details,
		IPAddress:      event.IPAddress,
		UserAgent:      event.UserAgent,
		CorrelationID:  event.CorrelationID,
		TraceID:        event.TraceID,
	}

	if err := l.auditService.LogAction(ctx, params); err != nil {
		l.logger.Error().
			Err(err).
			Str("event_category", event.Category).
			Str("event_action", event.Action).
			Msg("Failed to persist audit log")
	}
}

func (l *SecurityAuditLogger) shouldAlert(event SecurityEvent) bool {
	if !l.config.EnableAlerts {
		return false
	}

	// Alert on critical/high severity events
	if event.Severity == "critical" || event.Severity == "high" {
		return true
	}

	// Alert on suspicious activities
	if event.Category == "suspicious_activity" {
		return true
	}

	// Alert on repeated failures
	if !event.Success && event.Category == "authentication" {
		// This would check for repeated failures in practice
		return true
	}

	return false
}

func (l *SecurityAuditLogger) sendAlert(ctx context.Context, event SecurityEvent) {
	// Implementation would send to webhook, monitoring system, etc.
	l.logger.Warn().
		Str("event_category", event.Category).
		Str("event_action", event.Action).
		Str("severity", event.Severity).
		Msg("Security alert triggered")
}

func (l *SecurityAuditLogger) determineAuthSeverity(params AuthenticationLogParams) string {
	if !params.Success {
		return "medium"
	}
	if params.Action == "login" && params.Success {
		return "low"
	}
	if params.Action == "password_reset" {
		return "medium"
	}
	return "low"
}

func (l *SecurityAuditLogger) determineAuthzSeverity(params AuthorizationLogParams) string {
	if !params.Allowed && params.RequiredPermission == "admin" {
		return "high"
	}
	if !params.Allowed {
		return "medium"
	}
	return "low"
}

func (l *SecurityAuditLogger) formatAuthzResult(params AuthorizationLogParams) string {
	if params.Allowed {
		return "allowed"
	}
	return fmt.Sprintf("denied: %s", params.Reason)
}

// GetMetrics returns current audit logging metrics
func (l *SecurityAuditLogger) GetMetrics() AuditMetrics {
	return *l.metrics
}

// Close gracefully shuts down the audit logger
func (l *SecurityAuditLogger) Close() error {
	// Flush any pending logs
	if l.auditService != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		return l.auditService.Flush(ctx)
	}
	return nil
}
