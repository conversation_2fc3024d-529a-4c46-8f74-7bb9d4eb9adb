package logger

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
)

const (
	anonymizedValue = "[ANONYMIZED]"
)

// GDPRCompliance handles GDPR-compliant data handling for audit logs
type GDPRCompliance struct {
	// Configuration
	EnablePIIRedaction  bool
	EnableAnonymization bool
	AnonymizationDelay  time.Duration
	PIIPatterns         []*regexp.Regexp
	SensitiveFields     map[string]bool

	// Data subject rights
	EnableRightToAccess      bool
	EnableRightToErasure     bool
	EnableRightToPortability bool

	// Consent tracking
	ConsentRequired      bool
	DefaultConsentPeriod time.Duration
}

// DefaultGDPRCompliance returns a GDPR-compliant configuration
func DefaultGDPRCompliance() *GDPRCompliance {
	return &GDPRCompliance{
		EnablePIIRedaction:  true,
		EnableAnonymization: true,
		AnonymizationDelay:  30 * 24 * time.Hour, // 30 days
		PIIPatterns: []*regexp.Regexp{
			// IP addresses (simplified pattern)
			regexp.MustCompile(`\b(?:\d{1,3}\.){3}\d{1,3}\b`),
			// Email addresses
			regexp.MustCompile(`[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}`),
			// Phone numbers (various formats)
			regexp.MustCompile(`(?:\+\d{1,3}\s?)?(?:\d{3,4}[\s.-]?){2,3}\d{3,4}`),
			// Credit card numbers (simplified)
			regexp.MustCompile(`\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b`),
			// Social Security Numbers (US format)
			regexp.MustCompile(`\b\d{3}-\d{2}-\d{4}\b`),
		},
		SensitiveFields: map[string]bool{
			"password":       true,
			"token":          true,
			"secret":         true,
			"api_key":        true,
			"private_key":    true,
			"credit_card":    true,
			"ssn":            true,
			"id_number":      true,
			"bank_account":   true,
			"medical_record": true,
			"biometric_data": true,
		},
		EnableRightToAccess:      true,
		EnableRightToErasure:     true,
		EnableRightToPortability: true,
		ConsentRequired:          true,
		DefaultConsentPeriod:     365 * 24 * time.Hour, // 1 year
	}
}

// RedactPII redacts personally identifiable information from text
func (g *GDPRCompliance) RedactPII(text string) string {
	if !g.EnablePIIRedaction {
		return text
	}

	redacted := text
	for _, pattern := range g.PIIPatterns {
		redacted = pattern.ReplaceAllString(redacted, "[REDACTED]")
	}

	return redacted
}

// RedactSensitiveFields redacts sensitive fields from a map
func (g *GDPRCompliance) RedactSensitiveFields(data map[string]interface{}) map[string]interface{} {
	if !g.EnablePIIRedaction {
		return data
	}

	redacted := make(map[string]interface{})
	for key, value := range data {
		if g.IsSensitiveField(key) {
			redacted[key] = "[REDACTED]"
		} else {
			// Recursively check nested maps
			if nestedMap, ok := value.(map[string]interface{}); ok {
				redacted[key] = g.RedactSensitiveFields(nestedMap)
			} else if str, ok := value.(string); ok {
				// Check for PII patterns in string values
				redacted[key] = g.RedactPII(str)
			} else {
				redacted[key] = value
			}
		}
	}

	return redacted
}

// IsSensitiveField checks if a field name indicates sensitive data
func (g *GDPRCompliance) IsSensitiveField(fieldName string) bool {
	lowerField := strings.ToLower(fieldName)

	// Check exact matches
	if g.SensitiveFields[lowerField] {
		return true
	}

	// Check if field contains sensitive keywords
	sensitiveKeywords := []string{
		"password", "secret", "token", "key", "credential",
		"ssn", "social_security", "credit_card", "bank",
		"medical", "health", "biometric",
	}

	for _, keyword := range sensitiveKeywords {
		if strings.Contains(lowerField, keyword) {
			return true
		}
	}

	return false
}

// AnonymizeUserData anonymizes user data while preserving structure for analytics
func (g *GDPRCompliance) AnonymizeUserData(userID uuid.UUID, data map[string]interface{}) map[string]interface{} {
	if !g.EnableAnonymization {
		return data
	}

	// Generate deterministic anonymous ID from user ID
	anonymousID := g.generateAnonymousID(userID)

	anonymized := make(map[string]interface{})
	for key, value := range data {
		switch key {
		case "user_id", "userid", "user":
			anonymized[key] = anonymousID
		case "email", "email_address":
			anonymized[key] = g.anonymizeEmail(value)
		case "ip_address", "ip":
			anonymized[key] = g.anonymizeIP(value)
		case "user_agent":
			anonymized[key] = g.anonymizeUserAgent(value)
		default:
			if g.IsSensitiveField(key) {
				anonymized[key] = anonymizedValue
			} else {
				anonymized[key] = value
			}
		}
	}

	return anonymized
}

// generateAnonymousID generates a consistent anonymous ID for a user
func (g *GDPRCompliance) generateAnonymousID(userID uuid.UUID) string {
	// Use a deterministic method so the same user always gets the same anonymous ID
	// In production, this should use a proper hashing method with a secret salt
	return fmt.Sprintf("anon_%s", hex.EncodeToString([]byte(userID.String())[:8]))
}

// anonymizeEmail anonymizes an email address while preserving domain for analytics
func (g *GDPRCompliance) anonymizeEmail(value interface{}) string {
	email, ok := value.(string)
	if !ok {
		return "[ANONYMIZED]"
	}

	parts := strings.Split(email, "@")
	if len(parts) != 2 {
		return "[ANONYMIZED]"
	}

	// Keep domain for analytics, anonymize local part
	return fmt.Sprintf("user_%s@%s", generateRandomString(8), parts[1])
}

// anonymizeIP anonymizes an IP address while preserving general location
func (g *GDPRCompliance) anonymizeIP(value interface{}) string {
	ip, ok := value.(string)
	if !ok {
		return "0.0.0.0"
	}

	// For IPv4, zero out last octet
	parts := strings.Split(ip, ".")
	if len(parts) == 4 {
		return fmt.Sprintf("%s.%s.%s.0", parts[0], parts[1], parts[2])
	}

	// For IPv6 or invalid IPs
	return "0.0.0.0"
}

// anonymizeUserAgent anonymizes user agent while preserving browser/OS info
func (g *GDPRCompliance) anonymizeUserAgent(value interface{}) string {
	ua, ok := value.(string)
	if !ok {
		return "[ANONYMIZED]"
	}

	// Simple anonymization - in production, use a proper UA parser
	if strings.Contains(ua, "Chrome") {
		return "Chrome/Generic"
	} else if strings.Contains(ua, "Firefox") {
		return "Firefox/Generic"
	} else if strings.Contains(ua, "Safari") {
		return "Safari/Generic"
	}

	return "Generic/Browser"
}

// DataSubjectRequest represents a GDPR data subject request
type DataSubjectRequest struct {
	RequestID      uuid.UUID
	UserID         uuid.UUID
	RequestType    string // access, erasure, portability, rectification
	RequestedAt    time.Time
	ProcessedAt    *time.Time
	Status         string // pending, processing, completed, rejected
	ProcessedBy    *uuid.UUID
	ResponseURL    string
	AdditionalInfo map[string]interface{}
}

// ProcessDataSubjectRequest processes a GDPR data subject request
func (g *GDPRCompliance) ProcessDataSubjectRequest(ctx context.Context, request DataSubjectRequest) error {
	log.Info().
		Str("request_id", request.RequestID.String()).
		Str("user_id", request.UserID.String()).
		Str("request_type", request.RequestType).
		Msg("Processing data subject request")

	switch request.RequestType {
	case "access":
		if !g.EnableRightToAccess {
			return fmt.Errorf("right to access is not enabled")
		}
		return g.processAccessRequest(ctx, request)

	case "erasure":
		if !g.EnableRightToErasure {
			return fmt.Errorf("right to erasure is not enabled")
		}
		return g.processErasureRequest(ctx, request)

	case "portability":
		if !g.EnableRightToPortability {
			return fmt.Errorf("right to portability is not enabled")
		}
		return g.processPortabilityRequest(ctx, request)

	case "rectification":
		return g.processRectificationRequest(ctx, request)

	default:
		return fmt.Errorf("unknown request type: %s", request.RequestType)
	}
}

// processAccessRequest handles right to access requests
func (g *GDPRCompliance) processAccessRequest(ctx context.Context, request DataSubjectRequest) error {
	// Implementation would:
	// 1. Gather all data related to the user
	// 2. Format it in a human-readable way
	// 3. Provide it to the user securely

	log.Info().
		Str("request_id", request.RequestID.String()).
		Msg("Processing right to access request")

	return nil
}

// processErasureRequest handles right to erasure (right to be forgotten) requests
func (g *GDPRCompliance) processErasureRequest(ctx context.Context, request DataSubjectRequest) error {
	// Implementation would:
	// 1. Check if there are legal reasons to retain the data
	// 2. If not, anonymize or delete all personal data
	// 3. Keep audit trail of the deletion request itself

	log.Info().
		Str("request_id", request.RequestID.String()).
		Msg("Processing right to erasure request")

	return nil
}

// processPortabilityRequest handles data portability requests
func (g *GDPRCompliance) processPortabilityRequest(ctx context.Context, request DataSubjectRequest) error {
	// Implementation would:
	// 1. Export user data in a machine-readable format (JSON/CSV)
	// 2. Include only data provided by the user or generated about them
	// 3. Exclude inferred data or proprietary analytics

	log.Info().
		Str("request_id", request.RequestID.String()).
		Msg("Processing data portability request")

	return nil
}

// processRectificationRequest handles data rectification requests
func (g *GDPRCompliance) processRectificationRequest(ctx context.Context, request DataSubjectRequest) error {
	// Implementation would:
	// 1. Verify the requested corrections
	// 2. Update the data as requested
	// 3. Log the changes for audit purposes

	log.Info().
		Str("request_id", request.RequestID.String()).
		Msg("Processing rectification request")

	return nil
}

// ConsentRecord represents a user's consent for data processing
type ConsentRecord struct {
	UserID      uuid.UUID
	ConsentID   uuid.UUID
	Purpose     string   // What the data will be used for
	DataTypes   []string // Types of data being processed
	GrantedAt   time.Time
	ExpiresAt   time.Time
	WithdrawnAt *time.Time
	IPAddress   string
	UserAgent   string
	ConsentText string // The exact text the user agreed to
	Version     string // Version of the consent form
}

// RecordConsent records a user's consent for data processing
func (g *GDPRCompliance) RecordConsent(ctx context.Context, consent ConsentRecord) error {
	if !g.ConsentRequired {
		return nil
	}

	// Set default expiration if not provided
	if consent.ExpiresAt.IsZero() {
		consent.ExpiresAt = consent.GrantedAt.Add(g.DefaultConsentPeriod)
	}

	log.Info().
		Str("user_id", consent.UserID.String()).
		Str("consent_id", consent.ConsentID.String()).
		Str("purpose", consent.Purpose).
		Time("expires_at", consent.ExpiresAt).
		Msg("Recording user consent")

	// In a real implementation, this would save to database
	return nil
}

// WithdrawConsent withdraws a user's consent
func (g *GDPRCompliance) WithdrawConsent(ctx context.Context, userID uuid.UUID, consentID uuid.UUID) error {
	log.Info().
		Str("user_id", userID.String()).
		Str("consent_id", consentID.String()).
		Msg("Withdrawing user consent")

	// In a real implementation, this would update the consent record
	return nil
}

// CheckConsent checks if a user has valid consent for a specific purpose
func (g *GDPRCompliance) CheckConsent(ctx context.Context, userID uuid.UUID, purpose string) (bool, error) {
	if !g.ConsentRequired {
		return true, nil
	}

	// In a real implementation, this would check the database for valid consent
	// For now, return true for demonstration
	return true, nil
}

// generateRandomString generates a random string of specified length
func generateRandomString(length int) string {
	bytes := make([]byte, length)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)[:length]
}

// LegalBasisType represents the legal basis for processing personal data
type LegalBasisType string

const (
	LegalBasisConsent             LegalBasisType = "consent"
	LegalBasisContract            LegalBasisType = "contract"
	LegalBasisLegalObligation     LegalBasisType = "legal_obligation"
	LegalBasisVitalInterests      LegalBasisType = "vital_interests"
	LegalBasisPublicTask          LegalBasisType = "public_task"
	LegalBasisLegitimateInterests LegalBasisType = "legitimate_interests"
)

// DataProcessingRecord documents the legal basis for data processing
type DataProcessingRecord struct {
	ProcessingID   uuid.UUID
	UserID         uuid.UUID
	LegalBasis     LegalBasisType
	Purpose        string
	DataCategories []string
	Recipients     []string // Who the data may be shared with
	RetentionDays  int
	CreatedAt      time.Time
}

// RecordDataProcessing records the legal basis for processing personal data
func (g *GDPRCompliance) RecordDataProcessing(ctx context.Context, record DataProcessingRecord) error {
	log.Info().
		Str("processing_id", record.ProcessingID.String()).
		Str("user_id", record.UserID.String()).
		Str("legal_basis", string(record.LegalBasis)).
		Str("purpose", record.Purpose).
		Msg("Recording data processing activity")

	// Validate legal basis
	switch record.LegalBasis {
	case LegalBasisConsent:
		// Check if we have valid consent
		hasConsent, err := g.CheckConsent(ctx, record.UserID, record.Purpose)
		if err != nil {
			return fmt.Errorf("failed to check consent: %w", err)
		}
		if !hasConsent {
			return fmt.Errorf("no valid consent for purpose: %s", record.Purpose)
		}
	case LegalBasisContract, LegalBasisLegalObligation,
		LegalBasisVitalInterests, LegalBasisPublicTask,
		LegalBasisLegitimateInterests:
		// These are valid legal bases
	default:
		return fmt.Errorf("invalid legal basis: %s", record.LegalBasis)
	}

	// In a real implementation, this would save to database
	return nil
}
