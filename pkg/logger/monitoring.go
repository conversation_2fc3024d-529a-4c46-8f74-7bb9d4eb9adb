package logger

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/metric"
	"go.opentelemetry.io/otel/trace"
)

// MonitoringIntegration provides integration with monitoring systems
type MonitoringIntegration struct {
	// Prometheus metrics
	auditLogCounter           *prometheus.CounterVec
	auditLogDuration          *prometheus.HistogramVec
	securityEventCounter      *prometheus.CounterVec
	suspiciousActivityCounter *prometheus.CounterVec

	// OpenTelemetry
	tracer trace.Tracer
	meter  metric.Meter

	// Alert configuration
	alertWebhook string
	alertClient  *http.Client

	// Rate limiting for alerts
	alertRateLimiter *AlertRateLimiter
}

// AlertRateLimiter prevents alert spam
type AlertRateLimiter struct {
	mu          sync.Mutex
	alertCounts map[string]int
	lastReset   time.Time
	maxPerHour  int
	windowSize  time.Duration
}

// NewMonitoringIntegration creates a new monitoring integration
func NewMonitoringIntegration(alertWebhook string) *MonitoringIntegration {
	// Initialize Prometheus metrics
	auditLogCounter := promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "audit_logs_total",
			Help: "Total number of audit logs by category and action",
		},
		[]string{"category", "action", "severity", "success"},
	)

	auditLogDuration := promauto.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    "audit_log_processing_duration_seconds",
			Help:    "Duration of audit log processing",
			Buckets: prometheus.DefBuckets,
		},
		[]string{"category"},
	)

	securityEventCounter := promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "security_events_total",
			Help: "Total number of security events by type and severity",
		},
		[]string{"event_type", "severity", "outcome"},
	)

	suspiciousActivityCounter := promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "suspicious_activities_total",
			Help: "Total number of suspicious activities detected",
		},
		[]string{"activity_type", "risk_level", "action_taken"},
	)

	// Initialize OpenTelemetry
	tracer := otel.Tracer("audit-logger")
	meter := otel.Meter("audit-logger")

	return &MonitoringIntegration{
		auditLogCounter:           auditLogCounter,
		auditLogDuration:          auditLogDuration,
		securityEventCounter:      securityEventCounter,
		suspiciousActivityCounter: suspiciousActivityCounter,
		tracer:                    tracer,
		meter:                     meter,
		alertWebhook:              alertWebhook,
		alertClient: &http.Client{
			Timeout: 10 * time.Second,
		},
		alertRateLimiter: &AlertRateLimiter{
			alertCounts: make(map[string]int),
			lastReset:   time.Now(),
			maxPerHour:  100,
			windowSize:  time.Hour,
		},
	}
}

// RecordAuditLog records audit log metrics
func (m *MonitoringIntegration) RecordAuditLog(ctx context.Context, event SecurityEvent) {
	// Start timer for duration metric
	start := time.Now()

	// Start OpenTelemetry span
	ctx, span := m.tracer.Start(ctx, "audit.log",
		trace.WithAttributes(
			attribute.String("audit.category", event.Category),
			attribute.String("audit.action", event.Action),
			attribute.String("audit.severity", event.Severity),
			attribute.Bool("audit.success", event.Success),
		),
	)
	defer span.End()

	// Record Prometheus metrics
	successStr := "false"
	if event.Success {
		successStr = "true"
	}

	m.auditLogCounter.WithLabelValues(
		event.Category,
		event.Action,
		event.Severity,
		successStr,
	).Inc()

	// Record duration when done
	defer func() {
		duration := time.Since(start).Seconds()
		m.auditLogDuration.WithLabelValues(event.Category).Observe(duration)
	}()

	// Record security event if applicable
	if event.Severity == "high" || event.Severity == "critical" {
		m.recordSecurityEvent(ctx, event)
	}

	// Record suspicious activity if applicable
	if event.Category == "suspicious_activity" {
		m.recordSuspiciousActivity(ctx, event)
	}
}

// recordSecurityEvent records high-severity security events
func (m *MonitoringIntegration) recordSecurityEvent(ctx context.Context, event SecurityEvent) {
	outcome := "success"
	if !event.Success {
		outcome = "failure"
	}

	m.securityEventCounter.WithLabelValues(
		event.Category,
		event.Severity,
		outcome,
	).Inc()

	// Add span event
	trace.SpanFromContext(ctx).AddEvent("security_event",
		trace.WithAttributes(
			attribute.String("event_type", event.Category),
			attribute.String("severity", event.Severity),
		),
	)
}

// recordSuspiciousActivity records suspicious activity metrics
func (m *MonitoringIntegration) recordSuspiciousActivity(ctx context.Context, event SecurityEvent) {
	// Extract activity details from event
	activityType := event.Action
	riskLevel := event.Severity
	actionTaken := "logged"

	if details, ok := event.Details["action_taken"].(string); ok {
		actionTaken = details
	}

	m.suspiciousActivityCounter.WithLabelValues(
		activityType,
		riskLevel,
		actionTaken,
	).Inc()
}

// SendAlert sends an alert to the configured webhook
func (m *MonitoringIntegration) SendAlert(ctx context.Context, alert SecurityAlert) error {
	if m.alertWebhook == "" {
		return nil
	}

	// Check rate limit
	if !m.alertRateLimiter.AllowAlert(alert.Type) {
		return fmt.Errorf("alert rate limit exceeded for type: %s", alert.Type)
	}

	// Create span for alert sending
	ctx, span := m.tracer.Start(ctx, "audit.alert.send",
		trace.WithAttributes(
			attribute.String("alert.type", alert.Type),
			attribute.String("alert.severity", alert.Severity),
		),
	)
	defer span.End()

	// Prepare alert payload
	payload := AlertPayload{
		Type:        alert.Type,
		Severity:    alert.Severity,
		Title:       alert.Title,
		Description: alert.Description,
		Details:     alert.Details,
		Timestamp:   alert.Timestamp,
		Source:      "audit-logger",
		Environment: getEnvironment(),
	}

	// Marshal payload
	data, err := json.Marshal(payload)
	if err != nil {
		span.RecordError(err)
		return fmt.Errorf("failed to marshal alert payload: %w", err)
	}

	// Create request
	req, err := http.NewRequestWithContext(ctx, "POST", m.alertWebhook,
		bytes.NewReader(data))
	if err != nil {
		span.RecordError(err)
		return fmt.Errorf("failed to create alert request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	// Send request
	resp, err := m.alertClient.Do(req)
	if err != nil {
		span.RecordError(err)
		return fmt.Errorf("failed to send alert: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 400 {
		err := fmt.Errorf("alert webhook returned status %d", resp.StatusCode)
		span.RecordError(err)
		return err
	}

	span.SetAttributes(attribute.Int("alert.response.status", resp.StatusCode))
	return nil
}

// SecurityAlert represents a security alert
type SecurityAlert struct {
	Type        string                 `json:"type"`
	Severity    string                 `json:"severity"`
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Details     map[string]interface{} `json:"details"`
	Timestamp   time.Time              `json:"timestamp"`
}

// AlertPayload represents the payload sent to alert webhook
type AlertPayload struct {
	Type        string                 `json:"type"`
	Severity    string                 `json:"severity"`
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Details     map[string]interface{} `json:"details"`
	Timestamp   time.Time              `json:"timestamp"`
	Source      string                 `json:"source"`
	Environment string                 `json:"environment"`
}

// AllowAlert checks if an alert is allowed based on rate limiting
func (r *AlertRateLimiter) AllowAlert(alertType string) bool {
	r.mu.Lock()
	defer r.mu.Unlock()

	// Reset counter if window has passed
	if time.Since(r.lastReset) > r.windowSize {
		r.alertCounts = make(map[string]int)
		r.lastReset = time.Now()
	}

	// Check current count
	count := r.alertCounts[alertType]
	if count >= r.maxPerHour {
		return false
	}

	// Increment count
	r.alertCounts[alertType]++
	return true
}

// CreateDashboardQueries returns Prometheus queries for audit log dashboards
func CreateDashboardQueries() map[string]string {
	return map[string]string{
		"audit_logs_rate": `rate(audit_logs_total[5m])`,

		"audit_logs_by_category": `sum by (category) (rate(audit_logs_total[5m]))`,

		"failed_operations": `sum by (category, action) (rate(audit_logs_total{success="false"}[5m]))`,

		"security_events_rate": `rate(security_events_total[5m])`,

		"critical_events": `sum by (event_type) (rate(security_events_total{severity="critical"}[5m]))`,

		"suspicious_activities": `sum by (activity_type, risk_level) (rate(suspicious_activities_total[5m]))`,

		"audit_log_processing_p99": `histogram_quantile(0.99, rate(audit_log_processing_duration_seconds_bucket[5m]))`,

		"authentication_failures": `sum(rate(audit_logs_total{category="authentication",success="false"}[5m]))`,

		"authorization_denials": `sum(rate(audit_logs_total{category="authorization",success="false"}[5m]))`,

		"data_access_exports": `sum(rate(audit_logs_total{category="data_access",action="export"}[5m]))`,

		"configuration_changes": `sum(rate(audit_logs_total{category="configuration"}[5m]))`,
	}
}

// CreateAlertRules returns Prometheus alert rules for audit monitoring
func CreateAlertRules() []AlertRule {
	return []AlertRule{
		{
			Name:       "HighAuthenticationFailureRate",
			Expression: `rate(audit_logs_total{category="authentication",success="false"}[5m]) > 10`,
			Duration:   "5m",
			Labels: map[string]string{
				"severity": "warning",
				"team":     "security",
			},
			Annotations: map[string]string{
				"summary":     "High authentication failure rate detected",
				"description": "Authentication failures are above threshold ({{ $value }} failures/sec)",
			},
		},
		{
			Name:       "SuspiciousActivityDetected",
			Expression: `rate(suspicious_activities_total[5m]) > 0`,
			Duration:   "1m",
			Labels: map[string]string{
				"severity": "critical",
				"team":     "security",
			},
			Annotations: map[string]string{
				"summary":     "Suspicious activity detected",
				"description": "Suspicious activity of type {{ $labels.activity_type }} detected",
			},
		},
		{
			Name:       "CriticalSecurityEvent",
			Expression: `rate(security_events_total{severity="critical"}[1m]) > 0`,
			Duration:   "1m",
			Labels: map[string]string{
				"severity": "critical",
				"team":     "security",
			},
			Annotations: map[string]string{
				"summary":     "Critical security event occurred",
				"description": "Critical security event of type {{ $labels.event_type }} detected",
			},
		},
		{
			Name:       "UnusualDataExport",
			Expression: `rate(audit_logs_total{category="data_access",action="export"}[5m]) > 1`,
			Duration:   "5m",
			Labels: map[string]string{
				"severity": "warning",
				"team":     "security",
			},
			Annotations: map[string]string{
				"summary":     "Unusual data export activity",
				"description": "High rate of data exports detected ({{ $value }} exports/sec)",
			},
		},
		{
			Name:       "ConfigurationChangeSpike",
			Expression: `rate(audit_logs_total{category="configuration"}[5m]) > 5`,
			Duration:   "5m",
			Labels: map[string]string{
				"severity": "warning",
				"team":     "operations",
			},
			Annotations: map[string]string{
				"summary":     "High rate of configuration changes",
				"description": "Configuration changes above normal threshold ({{ $value }} changes/sec)",
			},
		},
	}
}

// AlertRule represents a Prometheus alert rule
type AlertRule struct {
	Name        string            `yaml:"alert"`
	Expression  string            `yaml:"expr"`
	Duration    string            `yaml:"for"`
	Labels      map[string]string `yaml:"labels"`
	Annotations map[string]string `yaml:"annotations"`
}

// getEnvironment returns the current environment
func getEnvironment() string {
	env := os.Getenv("GO_ENV")
	if env == "" {
		env = "development"
	}
	return env
}
