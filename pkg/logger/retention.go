package logger

import (
	"archive/tar"
	"compress/gzip"
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"

	"Membership-SAAS-System-Backend/db"

	"github.com/robfig/cron/v3"
	"github.com/rs/zerolog/log"
)

// RetentionPolicy defines the retention policy for audit logs
type RetentionPolicy struct {
	// Standard retention periods
	AuditLogRetention    time.Duration // How long to keep audit logs
	SecurityLogRetention time.Duration // How long to keep security logs
	AccessLogRetention   time.Duration // How long to keep access logs

	// GDPR compliance
	PersonalDataRetention time.Duration // How long before anonymizing personal data
	AnonymizationEnabled  bool
	DeletionEnabled       bool

	// Archive settings
	ArchiveEnabled     bool
	ArchiveLocation    string
	ArchiveCompression bool
	ArchiveEncryption  bool
	ArchiveRetention   time.Duration

	// Cleanup schedule
	CleanupSchedule string // Cron expression
}

// LogRetentionService manages log retention and cleanup
type LogRetentionService struct {
	policy     RetentionPolicy
	logDir     string
	archiveDir string
	cron       *cron.Cron
	logger     *SecurityAuditLogger
	store      db.Store
}

// DefaultRetentionPolicy returns a GDPR-compliant retention policy
func DefaultRetentionPolicy() RetentionPolicy {
	return RetentionPolicy{
		AuditLogRetention:     90 * 24 * time.Hour,  // 90 days
		SecurityLogRetention:  180 * 24 * time.Hour, // 180 days for security logs
		AccessLogRetention:    30 * 24 * time.Hour,  // 30 days for access logs
		PersonalDataRetention: 30 * 24 * time.Hour,  // Anonymize personal data after 30 days
		AnonymizationEnabled:  true,
		DeletionEnabled:       true,
		ArchiveEnabled:        true,
		ArchiveLocation:       "/var/log/the-moment/archive",
		ArchiveCompression:    true,
		ArchiveEncryption:     false,                // Would be enabled in production with proper key management
		ArchiveRetention:      365 * 24 * time.Hour, // 1 year
		CleanupSchedule:       "0 2 * * *",          // Run at 2 AM daily
	}
}

// NewLogRetentionService creates a new log retention service
func NewLogRetentionService(policy RetentionPolicy, logDir string, logger *SecurityAuditLogger, store db.Store) (*LogRetentionService, error) {
	archiveDir := policy.ArchiveLocation
	if archiveDir == "" {
		archiveDir = filepath.Join(logDir, "archive")
	}

	// Ensure directories exist
	if err := os.MkdirAll(logDir, 0o755); err != nil {
		return nil, fmt.Errorf("failed to create log directory: %w", err)
	}
	if policy.ArchiveEnabled {
		if err := os.MkdirAll(archiveDir, 0o755); err != nil {
			return nil, fmt.Errorf("failed to create archive directory: %w", err)
		}
	}

	service := &LogRetentionService{
		policy:     policy,
		logDir:     logDir,
		archiveDir: archiveDir,
		cron:       cron.New(),
		logger:     logger,
		store:      store,
	}

	// Schedule cleanup job
	if _, err := service.cron.AddFunc(policy.CleanupSchedule, service.performCleanup); err != nil {
		return nil, fmt.Errorf("failed to schedule cleanup job: %w", err)
	}

	// Start cron scheduler
	service.cron.Start()

	return service, nil
}

// performCleanup runs the scheduled cleanup tasks
func (s *LogRetentionService) performCleanup() {
	ctx := context.Background()
	startTime := time.Now()

	log.Info().Msg("Starting log retention cleanup")

	// Archive old logs
	if s.policy.ArchiveEnabled {
		if err := s.archiveOldLogs(ctx); err != nil {
			log.Error().Err(err).Msg("Failed to archive old logs")
		}
	}

	// Anonymize personal data
	if s.policy.AnonymizationEnabled {
		if err := s.anonymizePersonalData(ctx); err != nil {
			log.Error().Err(err).Msg("Failed to anonymize personal data")
		}
	}

	// Delete expired logs
	if s.policy.DeletionEnabled {
		if err := s.deleteExpiredLogs(ctx); err != nil {
			log.Error().Err(err).Msg("Failed to delete expired logs")
		}
	}

	// Clean up old archives
	if s.policy.ArchiveEnabled {
		if err := s.cleanupOldArchives(ctx); err != nil {
			log.Error().Err(err).Msg("Failed to cleanup old archives")
		}
	}

	duration := time.Since(startTime)
	log.Info().Dur("duration", duration).Msg("Log retention cleanup completed")
}

// archiveOldLogs archives logs that are older than retention period
func (s *LogRetentionService) archiveOldLogs(ctx context.Context) error {
	cutoffTime := time.Now().Add(-s.policy.AuditLogRetention)

	files, err := s.findLogsToArchive(cutoffTime)
	if err != nil {
		return fmt.Errorf("failed to find logs to archive: %w", err)
	}

	if len(files) == 0 {
		return nil
	}

	// Create archive for this batch
	archiveName := fmt.Sprintf("audit-logs-%s.tar.gz", time.Now().Format("2006-01-02"))
	archivePath := filepath.Join(s.archiveDir, archiveName)

	if err := s.createArchive(archivePath, files); err != nil {
		return fmt.Errorf("failed to create archive: %w", err)
	}

	// Delete archived files
	for _, file := range files {
		if err := os.Remove(file); err != nil {
			log.Error().Err(err).Str("file", file).Msg("Failed to remove archived file")
		}
	}

	log.Info().
		Int("files_archived", len(files)).
		Str("archive", archiveName).
		Msg("Logs archived successfully")

	return nil
}

// findLogsToArchive finds log files older than the cutoff time
func (s *LogRetentionService) findLogsToArchive(cutoffTime time.Time) ([]string, error) {
	var files []string

	err := filepath.Walk(s.logDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Skip directories and non-log files
		if info.IsDir() || !strings.HasSuffix(info.Name(), ".log") {
			return nil
		}

		// Skip current log files (without date suffix)
		if !strings.Contains(info.Name(), "-20") {
			return nil
		}

		// Check if file is older than cutoff
		if info.ModTime().Before(cutoffTime) {
			files = append(files, path)
		}

		return nil
	})

	return files, err
}

// createArchive creates a compressed tar archive of the specified files
func (s *LogRetentionService) createArchive(archivePath string, files []string) error {
	file, err := os.Create(archivePath)
	if err != nil {
		return err
	}
	defer file.Close()

	var writer io.WriteCloser = file

	// Add compression if enabled
	if s.policy.ArchiveCompression {
		gzWriter := gzip.NewWriter(file)
		defer gzWriter.Close()
		writer = gzWriter
	}

	tarWriter := tar.NewWriter(writer)
	defer tarWriter.Close()

	for _, filePath := range files {
		if err := s.addFileToArchive(tarWriter, filePath); err != nil {
			return fmt.Errorf("failed to add file %s to archive: %w", filePath, err)
		}
	}

	return nil
}

// addFileToArchive adds a single file to the tar archive
func (s *LogRetentionService) addFileToArchive(tw *tar.Writer, filePath string) error {
	file, err := os.Open(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	info, err := file.Stat()
	if err != nil {
		return err
	}

	header, err := tar.FileInfoHeader(info, info.Name())
	if err != nil {
		return err
	}

	// Use relative path in archive
	header.Name = filepath.Base(filePath)

	if err := tw.WriteHeader(header); err != nil {
		return err
	}

	_, err = io.Copy(tw, file)
	return err
}

// anonymizePersonalData anonymizes personal data in logs older than retention period
func (s *LogRetentionService) anonymizePersonalData(ctx context.Context) error {
	log.Info().Msg("Starting anonymization of personal data in old audit logs")

	// Note: We can't get a precise count without adding a custom SQLC query
	// For now, we'll proceed without the count and let the anonymization happen
	// The SQLC method will handle the actual anonymization

	// Use SQLC generated method to anonymize old audit logs
	// This will:
	// - Update IP addresses to '0.0.0.0'
	// - Replace user agents with '[ANONYMIZED]'
	// - Remove PII fields from JSONB details
	// - Set anonymized_at timestamp
	// - Only affect logs older than 30 days with contains_pii=true
	if err := s.store.AnonymizeOldAuditLogs(ctx); err != nil {
		log.Error().Err(err).Msg("Failed to anonymize old audit logs")
		return fmt.Errorf("failed to anonymize personal data: %w", err)
	}

	log.Info().Msg("Successfully anonymized personal data in old audit logs")

	return nil
}

// deleteExpiredLogs deletes logs that have exceeded all retention periods
func (s *LogRetentionService) deleteExpiredLogs(ctx context.Context) error {
	log.Info().Msg("Starting deletion of expired audit logs")

	// Note: We can't get a precise count without adding a custom SQLC query
	// For now, we'll proceed without the count and let the deletion happen
	// The SQLC method will handle the actual deletion

	// Use SQLC generated method to delete expired audit logs
	// This will delete all logs where retention_expires_at < CURRENT_TIMESTAMP
	if err := s.store.DeleteExpiredAuditLogs(ctx); err != nil {
		log.Error().Err(err).Msg("Failed to delete expired audit logs")
		return fmt.Errorf("failed to delete expired logs: %w", err)
	}

	log.Info().Msg("Successfully deleted expired audit logs")

	// Also clean up any dangling references in related tables if necessary
	// This is handled by CASCADE deletes in the database schema

	return nil
}

// cleanupOldArchives removes archives that exceed archive retention period
func (s *LogRetentionService) cleanupOldArchives(ctx context.Context) error {
	if !s.policy.ArchiveEnabled || s.policy.ArchiveRetention == 0 {
		return nil
	}

	cutoffTime := time.Now().Add(-s.policy.ArchiveRetention)
	var deletedCount int

	err := filepath.Walk(s.archiveDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Skip directories
		if info.IsDir() {
			return nil
		}

		// Check if archive is older than retention period
		if info.ModTime().Before(cutoffTime) {
			if err := os.Remove(path); err != nil {
				log.Error().Err(err).Str("archive", path).Msg("Failed to delete old archive")
			} else {
				deletedCount++
			}
		}

		return nil
	})
	if err != nil {
		return fmt.Errorf("failed to cleanup old archives: %w", err)
	}

	if deletedCount > 0 {
		log.Info().
			Int("archives_deleted", deletedCount).
			Msg("Old archives cleaned up")
	}

	return nil
}

// ExportLogsForCompliance exports logs for compliance purposes
func (s *LogRetentionService) ExportLogsForCompliance(ctx context.Context, params AuditLogExportParams) (string, error) {
	// Create export directory
	exportDir := filepath.Join(s.logDir, "exports")
	if err := os.MkdirAll(exportDir, 0o755); err != nil {
		return "", fmt.Errorf("failed to create export directory: %w", err)
	}

	// Generate export filename
	exportFile := filepath.Join(exportDir, fmt.Sprintf("audit-export-%s-%s.%s",
		params.ExportID.String(),
		time.Now().Format("20060102-150405"),
		params.Format,
	))

	// Log the export request
	s.logger.LogDataAccess(ctx, DataAccessLogParams{
		Operation:    "export",
		UserID:       params.ApprovedBy,
		DataType:     "audit_logs",
		DataID:       params.ExportID,
		ExportFormat: params.Format,
		Purpose:      params.Reason,
		Success:      true,
		Result:       "export_initiated",
	})

	// In a real implementation, this would:
	// 1. Query logs based on params.Query
	// 2. Format the data according to params.Format
	// 3. Apply PII redaction if !params.IncludePII
	// 4. Write to exportFile
	// 5. Send notification when complete

	return exportFile, nil
}

// GetRetentionStatistics returns statistics about log retention
func (s *LogRetentionService) GetRetentionStatistics() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Count log files
	var logCount int
	var totalSize int64

	err := filepath.Walk(s.logDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		if !info.IsDir() && strings.HasSuffix(info.Name(), ".log") {
			logCount++
			totalSize += info.Size()
		}
		return nil
	})
	if err != nil {
		return nil, err
	}

	// Count archives
	var archiveCount int
	var archiveSize int64

	if s.policy.ArchiveEnabled {
		err = filepath.Walk(s.archiveDir, func(path string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}
			if !info.IsDir() {
				archiveCount++
				archiveSize += info.Size()
			}
			return nil
		})
		if err != nil {
			return nil, err
		}
	}

	stats["active_logs"] = logCount
	stats["active_logs_size_mb"] = totalSize / 1024 / 1024
	stats["archived_logs"] = archiveCount
	stats["archive_size_mb"] = archiveSize / 1024 / 1024
	stats["retention_days"] = int(s.policy.AuditLogRetention.Hours() / 24)
	stats["archive_retention_days"] = int(s.policy.ArchiveRetention.Hours() / 24)
	stats["anonymization_enabled"] = s.policy.AnonymizationEnabled
	stats["next_cleanup"] = s.cron.Entries()[0].Next

	return stats, nil
}

// Stop gracefully shuts down the retention service
func (s *LogRetentionService) Stop() {
	s.cron.Stop()
	log.Info().Msg("Log retention service stopped")
}
