package logger

import (
	"context"
	"errors"
	"testing"
	"time"

	dbmocks "Membership-SAAS-System-Backend/internal/mocks/db"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestAnonymizePersonalData(t *testing.T) {
	tests := []struct {
		name          string
		setupMocks    func(*dbmocks.MockStore)
		expectedError bool
		errorMessage  string
	}{
		{
			name: "successful anonymization",
			setupMocks: func(mockStore *dbmocks.MockStore) {
				// Mock anonymization
				mockStore.On("AnonymizeOldAuditLogs", mock.Anything).Return(nil)
			},
			expectedError: false,
		},
		{
			name: "anonymization fails",
			setupMocks: func(mockStore *dbmocks.MockStore) {
				// Mock anonymization failure
				mockStore.On("AnonymizeOldAuditLogs", mock.Anything).Return(errors.New("database error"))
			},
			expectedError: true,
			errorMessage:  "failed to anonymize personal data: database error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockStore := dbmocks.NewMockStore(t)

			service := &LogRetentionService{
				store:  mockStore,
				policy: DefaultRetentionPolicy(),
			}

			tt.setupMocks(mockStore)

			err := service.anonymizePersonalData(context.Background())

			if tt.expectedError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMessage)
			} else {
				assert.NoError(t, err)
			}

			mockStore.AssertExpectations(t)
		})
	}
}

func TestDeleteExpiredLogs(t *testing.T) {
	tests := []struct {
		name          string
		setupMocks    func(*dbmocks.MockStore)
		expectedError bool
		errorMessage  string
	}{
		{
			name: "successful deletion",
			setupMocks: func(mockStore *dbmocks.MockStore) {
				// Mock deletion
				mockStore.On("DeleteExpiredAuditLogs", mock.Anything).Return(nil)
			},
			expectedError: false,
		},
		{
			name: "deletion fails",
			setupMocks: func(mockStore *dbmocks.MockStore) {
				// Mock deletion failure
				mockStore.On("DeleteExpiredAuditLogs", mock.Anything).Return(errors.New("database error"))
			},
			expectedError: true,
			errorMessage:  "failed to delete expired logs: database error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockStore := dbmocks.NewMockStore(t)

			service := &LogRetentionService{
				store:  mockStore,
				policy: DefaultRetentionPolicy(),
			}

			tt.setupMocks(mockStore)

			err := service.deleteExpiredLogs(context.Background())

			if tt.expectedError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMessage)
			} else {
				assert.NoError(t, err)
			}

			mockStore.AssertExpectations(t)
		})
	}
}

func TestNewLogRetentionService(t *testing.T) {
	tests := []struct {
		name          string
		policy        RetentionPolicy
		logDir        string
		expectedError bool
		errorMessage  string
	}{
		{
			name: "successful creation",
			policy: RetentionPolicy{
				AuditLogRetention:     90 * 24 * time.Hour,
				SecurityLogRetention:  180 * 24 * time.Hour,
				AccessLogRetention:    30 * 24 * time.Hour,
				PersonalDataRetention: 30 * 24 * time.Hour,
				AnonymizationEnabled:  true,
				DeletionEnabled:       true,
				ArchiveEnabled:        true,
				ArchiveLocation:       "/tmp/test-archive",
				ArchiveCompression:    true,
				ArchiveEncryption:     false,
				ArchiveRetention:      365 * 24 * time.Hour,
				CleanupSchedule:       "0 2 * * *",
			},
			logDir:        "/tmp/test-logs",
			expectedError: false,
		},
		{
			name: "with custom archive location",
			policy: RetentionPolicy{
				AuditLogRetention: 90 * 24 * time.Hour,
				ArchiveEnabled:    true,
				ArchiveLocation:   "/tmp/custom-archive",
				CleanupSchedule:   "0 2 * * *",
			},
			logDir:        "/tmp/test-logs",
			expectedError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockStore := dbmocks.NewMockStore(t)

			service, err := NewLogRetentionService(tt.policy, tt.logDir, nil, mockStore)

			if tt.expectedError {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMessage)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, service)
				assert.Equal(t, mockStore, service.store)
				assert.Equal(t, tt.policy, service.policy)

				// Clean up
				service.Stop()
			}
		})
	}
}
