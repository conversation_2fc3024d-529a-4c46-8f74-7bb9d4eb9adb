package logger

import (
	"github.com/google/uuid"
)

// SecurityEvent represents a security-related event to be logged
type SecurityEvent struct {
	// Event categorization
	Category string // authentication, authorization, data_access, configuration, suspicious_activity
	Action   string // Specific action within the category
	Severity string // critical, high, medium, low

	// Context
	UserID         uuid.UUID
	OrganizationID *uuid.UUID
	ResourceType   string
	ResourceID     uuid.UUID

	// Request metadata
	IPAddress string
	UserAgent string

	// Event details
	Success bool
	Result  string
	Message string
	Details map[string]interface{}
	Error   error

	// Correlation
	CorrelationID string
	TraceID       string
}

// AuthenticationLogParams contains parameters for authentication logging
type AuthenticationLogParams struct {
	Action         string // login, logout, password_reset, mfa_setup, mfa_verify
	UserID         uuid.UUID
	OrganizationID *uuid.UUID
	Method         string // password, oauth, sso, magic_link
	Provider       string // local, google, microsoft, etc.
	Success        bool
	Result         string // success, invalid_credentials, account_locked, mfa_required
	MFARequired    bool
	MFACompleted   bool
	IPAddress      string
	UserAgent      string
	CorrelationID  string
	TraceID        string
}

// AuthorizationLogParams contains parameters for authorization logging
type AuthorizationLogParams struct {
	Action             string // view, create, update, delete, admin_action
	UserID             uuid.UUID
	OrganizationID     *uuid.UUID
	ResourceType       string
	ResourceID         uuid.UUID
	RequiredPermission string
	UserPermissions    []string
	Allowed            bool
	Reason             string // Reason for denial if not allowed
	IPAddress          string
	UserAgent          string
	CorrelationID      string
	TraceID            string
}

// DataAccessLogParams contains parameters for data access logging
type DataAccessLogParams struct {
	Operation      string // read, write, export, bulk_read, bulk_write
	UserID         uuid.UUID
	OrganizationID *uuid.UUID
	DataType       string // users, events, personal_data, financial_data
	DataID         uuid.UUID
	FieldsAccessed []string
	RecordCount    int
	ExportFormat   string // csv, json, pdf
	Purpose        string // business_operation, compliance, analytics
	Success        bool
	Result         string
	IPAddress      string
	UserAgent      string
	CorrelationID  string
	TraceID        string
}

// ConfigChangeLogParams contains parameters for configuration change logging
type ConfigChangeLogParams struct {
	Action         string // create, update, delete
	UserID         uuid.UUID
	OrganizationID *uuid.UUID
	ConfigType     string // system, organization, security, feature_flag
	ConfigID       string
	ConfigName     string
	OldValue       interface{}
	NewValue       interface{}
	ChangeReason   string
	Success        bool
	Result         string
	IPAddress      string
	UserAgent      string
	CorrelationID  string
	TraceID        string
}

// SuspiciousActivityParams contains parameters for suspicious activity logging
type SuspiciousActivityParams struct {
	ActivityType   string // brute_force, sql_injection, privilege_escalation, data_exfiltration
	Severity       string // critical, high, medium
	UserID         uuid.UUID
	OrganizationID *uuid.UUID
	Description    string
	Indicators     []string // Specific indicators that triggered the detection
	RiskScore      int      // 0-100
	ActionTaken    string   // blocked, rate_limited, flagged_for_review
	PatternMatched string   // Name of the pattern or rule that was matched
	IPAddress      string
	UserAgent      string
	CorrelationID  string
	TraceID        string
}

// AuditLogQuery represents query parameters for retrieving audit logs
type AuditLogQuery struct {
	// Filters
	UserID         *uuid.UUID
	OrganizationID *uuid.UUID
	Category       string
	Action         string
	ResourceType   string
	ResourceID     *uuid.UUID
	Severity       string
	Success        *bool

	// Time range
	StartTime string
	EndTime   string

	// Pagination
	Limit  int
	Offset int

	// Sorting
	SortBy    string // created_at, severity, category
	SortOrder string // asc, desc
}

// AuditLogExportParams contains parameters for exporting audit logs
type AuditLogExportParams struct {
	Query        AuditLogQuery
	Format       string // csv, json, pdf
	IncludePII   bool   // Whether to include PII (requires special permission)
	Reason       string // Reason for export (compliance requirement)
	ApprovedBy   uuid.UUID
	ExportID     uuid.UUID
	Notification string // Email/webhook to notify when export is ready
}
