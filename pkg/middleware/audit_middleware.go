package middleware

import (
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"

	"Membership-SAAS-System-Backend/pkg/logger"
)

// AuditMiddlewareConfig defines the config for audit middleware
type AuditMiddlewareConfig struct {
	// <PERSON><PERSON> defines a function to skip middleware
	Skipper middleware.Skipper

	// Logger is the security audit logger instance
	Logger *logger.SecurityAuditLogger

	// ExcludePaths are paths to exclude from audit logging
	ExcludePaths []string

	// IncludeRequestBody determines if request body should be logged
	IncludeRequestBody bool

	// IncludeResponseBody determines if response body should be logged
	IncludeResponseBody bool

	// SensitivePaths are paths that contain sensitive data
	SensitivePaths []string

	// HighRiskPaths are paths that should trigger alerts
	HighRiskPaths []string
}

// DefaultAuditMiddlewareConfig returns a default config
func DefaultAuditMiddlewareConfig() AuditMiddlewareConfig {
	return AuditMiddlewareConfig{
		Skipper: middleware.DefaultSkipper,
		ExcludePaths: []string{
			"/health",
			"/metrics",
			"/favicon.ico",
		},
		IncludeRequestBody:  false, // Disabled by default for performance
		IncludeResponseBody: false,
		SensitivePaths: []string{
			"/api/v1/auth/login",
			"/api/v1/auth/register",
			"/api/v1/users/password",
		},
		HighRiskPaths: []string{
			"/api/v1/admin",
			"/api/v1/system/config",
			"/api/v1/users/export",
		},
	}
}

// AuditMiddleware returns an audit logging middleware
func AuditMiddleware(config AuditMiddlewareConfig) echo.MiddlewareFunc {
	// Defaults
	if config.Skipper == nil {
		config.Skipper = DefaultAuditMiddlewareConfig().Skipper
	}
	if config.ExcludePaths == nil {
		config.ExcludePaths = DefaultAuditMiddlewareConfig().ExcludePaths
	}

	// Create maps for faster lookups
	excludeMap := make(map[string]bool)
	for _, path := range config.ExcludePaths {
		excludeMap[path] = true
	}

	sensitiveMap := make(map[string]bool)
	for _, path := range config.SensitivePaths {
		sensitiveMap[path] = true
	}

	highRiskMap := make(map[string]bool)
	for _, path := range config.HighRiskPaths {
		highRiskMap[path] = true
	}

	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			if config.Skipper(c) {
				return next(c)
			}

			// Check if path should be excluded
			path := c.Request().URL.Path
			if excludeMap[path] {
				return next(c)
			}

			// Start timing
			start := time.Now()

			// Extract request information
			req := c.Request()
			res := c.Response()

			// Get user information from context
			userID := getUserIDFromContext(c)
			orgID := getOrgIDFromContext(c)

			// Generate correlation ID if not present
			correlationID := c.Request().Header.Get("X-Correlation-ID")
			if correlationID == "" {
				correlationID = uuid.New().String()
				c.Request().Header.Set("X-Correlation-ID", correlationID)
			}

			// Get trace ID from OpenTelemetry if available
			traceID := getTraceIDFromContext(c)

			// Determine if this is a sensitive path
			isSensitive := sensitiveMap[path]
			isHighRisk := highRiskMap[path]

			// Process the request
			err := next(c)

			// Calculate duration
			duration := time.Since(start)

			// Log based on the type of operation
			logRequest(c, config.Logger, auditLogParams{
				UserID:         userID,
				OrganizationID: orgID,
				Path:           path,
				Method:         req.Method,
				StatusCode:     res.Status,
				Duration:       duration,
				IPAddress:      c.RealIP(),
				UserAgent:      req.UserAgent(),
				CorrelationID:  correlationID,
				TraceID:        traceID,
				Error:          err,
				IsSensitive:    isSensitive,
				IsHighRisk:     isHighRisk,
			})

			return err
		}
	}
}

// auditLogParams contains parameters for audit logging
type auditLogParams struct {
	UserID         uuid.UUID
	OrganizationID *uuid.UUID
	Path           string
	Method         string
	StatusCode     int
	Duration       time.Duration
	IPAddress      string
	UserAgent      string
	CorrelationID  string
	TraceID        string
	Error          error
	IsSensitive    bool
	IsHighRisk     bool
}

// logRequest logs the HTTP request based on its characteristics
func logRequest(c echo.Context, auditLogger *logger.SecurityAuditLogger, params auditLogParams) {
	ctx := c.Request().Context()

	// Determine the type of operation based on path and method
	category, action := categorizeRequest(params.Path, params.Method)

	// Check for suspicious patterns
	if suspicious, indicators := checkSuspiciousPatterns(c, params); suspicious {
		auditLogger.LogSuspiciousActivity(ctx, logger.SuspiciousActivityParams{
			ActivityType:   "suspicious_request",
			Severity:       determineSuspicionSeverity(indicators),
			UserID:         params.UserID,
			OrganizationID: params.OrganizationID,
			Description:    fmt.Sprintf("Suspicious request pattern detected on %s", params.Path),
			Indicators:     indicators,
			RiskScore:      calculateRiskScore(indicators),
			ActionTaken:    determineAction(params.StatusCode),
			PatternMatched: "http_request_analysis",
			IPAddress:      params.IPAddress,
			UserAgent:      params.UserAgent,
			CorrelationID:  params.CorrelationID,
			TraceID:        params.TraceID,
		})
		return
	}

	// Log authentication attempts
	if category == "authentication" {
		success := params.StatusCode >= 200 && params.StatusCode < 300
		result := "success"
		if !success {
			switch params.StatusCode {
			case 401:
				result = "invalid_credentials"
			case 403:
				result = "forbidden"
			case 429:
				result = "rate_limited"
			default:
				result = fmt.Sprintf("failed_%d", params.StatusCode)
			}
		}

		auditLogger.LogAuthentication(ctx, logger.AuthenticationLogParams{
			Action:         action,
			UserID:         params.UserID,
			OrganizationID: params.OrganizationID,
			Method:         "api",
			Provider:       "local",
			Success:        success,
			Result:         result,
			IPAddress:      params.IPAddress,
			UserAgent:      params.UserAgent,
			CorrelationID:  params.CorrelationID,
			TraceID:        params.TraceID,
		})
		return
	}

	// Log authorization checks for protected resources
	if params.StatusCode == 403 {
		auditLogger.LogAuthorization(ctx, logger.AuthorizationLogParams{
			Action:         action,
			UserID:         params.UserID,
			OrganizationID: params.OrganizationID,
			ResourceType:   category,
			ResourceID:     extractResourceID(c),
			Allowed:        false,
			Reason:         "insufficient_permissions",
			IPAddress:      params.IPAddress,
			UserAgent:      params.UserAgent,
			CorrelationID:  params.CorrelationID,
			TraceID:        params.TraceID,
		})
		return
	}

	// Log data access for export/download operations
	if action == "export" || action == "download" {
		auditLogger.LogDataAccess(ctx, logger.DataAccessLogParams{
			Operation:      action,
			UserID:         params.UserID,
			OrganizationID: params.OrganizationID,
			DataType:       category,
			DataID:         extractResourceID(c),
			Success:        params.StatusCode >= 200 && params.StatusCode < 300,
			Result:         fmt.Sprintf("status_%d", params.StatusCode),
			IPAddress:      params.IPAddress,
			UserAgent:      params.UserAgent,
			CorrelationID:  params.CorrelationID,
			TraceID:        params.TraceID,
		})
		return
	}

	// Log high-risk operations
	if params.IsHighRisk {
		severity := "medium"
		if params.StatusCode >= 200 && params.StatusCode < 300 {
			severity = "high"
		}

		event := logger.SecurityEvent{
			Category:       category,
			Action:         action,
			Severity:       severity,
			UserID:         params.UserID,
			OrganizationID: params.OrganizationID,
			ResourceType:   category,
			ResourceID:     extractResourceID(c),
			IPAddress:      params.IPAddress,
			UserAgent:      params.UserAgent,
			Success:        params.StatusCode >= 200 && params.StatusCode < 300,
			Result:         fmt.Sprintf("status_%d", params.StatusCode),
			Message:        fmt.Sprintf("High-risk operation: %s %s", params.Method, params.Path),
			Details: map[string]interface{}{
				"duration_ms": params.Duration.Milliseconds(),
				"path":        params.Path,
				"method":      params.Method,
			},
			CorrelationID: params.CorrelationID,
			TraceID:       params.TraceID,
		}

		if params.Error != nil {
			event.Error = params.Error
		}

		auditLogger.LogSecurityEvent(ctx, event)
	}
}

// categorizeRequest determines the category and action from the request
func categorizeRequest(path string, method string) (category string, action string) {
	// Authentication endpoints
	if contains(path, "/auth/") {
		category = "authentication"
		if contains(path, "/login") {
			action = "login"
		} else if contains(path, "/logout") {
			action = "logout"
		} else if contains(path, "/register") {
			action = "register"
		} else if contains(path, "/refresh") {
			action = "token_refresh"
		} else if contains(path, "/password") {
			action = "password_change"
		} else {
			action = method
		}
		return
	}

	// User management
	if contains(path, "/users") {
		category = "user_management"
	} else if contains(path, "/organizations") {
		category = "organization_management"
	} else if contains(path, "/events") {
		category = "event_management"
	} else if contains(path, "/posts") || contains(path, "/resources") {
		category = "content_management"
	} else if contains(path, "/admin") || contains(path, "/system") {
		category = "system_administration"
	} else {
		category = "general"
	}

	// Determine action based on method
	switch method {
	case "GET":
		if contains(path, "/export") {
			action = "export"
		} else if contains(path, "/download") {
			action = "download"
		} else {
			action = "view"
		}
	case "POST":
		action = "create"
	case "PUT", "PATCH":
		action = "update"
	case "DELETE":
		action = "delete"
	default:
		action = strings.ToLower(method)
	}

	return
}

// checkSuspiciousPatterns checks for suspicious request patterns
func checkSuspiciousPatterns(c echo.Context, params auditLogParams) (bool, []string) {
	var indicators []string

	// Check for SQL injection patterns
	if containsSQLInjection(c.QueryString()) {
		indicators = append(indicators, "sql_injection_attempt")
	}

	// Check for path traversal
	if contains(params.Path, "../") || contains(params.Path, "..\\") {
		indicators = append(indicators, "path_traversal_attempt")
	}

	// Check for unusual user agents
	if isUnusualUserAgent(params.UserAgent) {
		indicators = append(indicators, "unusual_user_agent")
	}

	// Check for rapid requests (potential brute force)
	if params.StatusCode == 429 {
		indicators = append(indicators, "rate_limit_exceeded")
	}

	// Check for repeated authentication failures
	if params.StatusCode == 401 && contains(params.Path, "/auth/") {
		indicators = append(indicators, "authentication_failure")
	}

	// Check for access to sensitive paths without authentication
	if params.UserID == uuid.Nil && isSensitivePath(params.Path) {
		indicators = append(indicators, "unauthenticated_sensitive_access")
	}

	return len(indicators) > 0, indicators
}

// Helper functions

func getUserIDFromContext(c echo.Context) uuid.UUID {
	if userID, ok := c.Get("user_id").(uuid.UUID); ok {
		return userID
	}
	if userIDStr, ok := c.Get("user_id").(string); ok {
		if id, err := uuid.Parse(userIDStr); err == nil {
			return id
		}
	}
	return uuid.Nil
}

func getOrgIDFromContext(c echo.Context) *uuid.UUID {
	if orgID, ok := c.Get("organization_id").(uuid.UUID); ok {
		return &orgID
	}
	if orgIDStr, ok := c.Get("organization_id").(string); ok {
		if id, err := uuid.Parse(orgIDStr); err == nil {
			return &id
		}
	}
	return nil
}

func getTraceIDFromContext(c echo.Context) string {
	if traceID, ok := c.Get("trace_id").(string); ok {
		return traceID
	}
	return ""
}

func extractResourceID(c echo.Context) uuid.UUID {
	// Try common parameter names
	for _, param := range []string{"id", "resource_id", "user_id", "org_id", "event_id"} {
		if idStr := c.Param(param); idStr != "" {
			if id, err := uuid.Parse(idStr); err == nil {
				return id
			}
		}
	}
	return uuid.Nil
}

func contains(s, substr string) bool {
	return strings.Contains(strings.ToLower(s), strings.ToLower(substr))
}

func containsSQLInjection(s string) bool {
	// Simple SQL injection patterns - in production, use a more comprehensive check
	patterns := []string{
		"' or '",
		"' or 1=1",
		"'; drop table",
		"union select",
		"exec(",
		"execute(",
	}

	lower := strings.ToLower(s)
	for _, pattern := range patterns {
		if strings.Contains(lower, pattern) {
			return true
		}
	}
	return false
}

func isUnusualUserAgent(ua string) bool {
	// Check for common scanning tools
	scanners := []string{
		"sqlmap", "nikto", "nmap", "masscan",
		"metasploit", "burp", "zap", "acunetix",
	}

	lower := strings.ToLower(ua)
	for _, scanner := range scanners {
		if strings.Contains(lower, scanner) {
			return true
		}
	}

	// Check for missing or very short user agents
	return len(ua) < 10
}

func isSensitivePath(path string) bool {
	sensitivePaths := []string{
		"/admin", "/api/admin",
		"/system", "/config",
		"/users", "/export",
	}

	for _, sensitive := range sensitivePaths {
		if contains(path, sensitive) {
			return true
		}
	}
	return false
}

func determineSuspicionSeverity(indicators []string) string {
	if len(indicators) >= 3 {
		return "critical"
	} else if len(indicators) >= 2 {
		return "high"
	}
	return "medium"
}

func calculateRiskScore(indicators []string) int {
	// Simple risk scoring - in production, use a more sophisticated model
	score := len(indicators) * 20
	if score > 100 {
		score = 100
	}
	return score
}

func determineAction(statusCode int) string {
	switch {
	case statusCode == 429:
		return "rate_limited"
	case statusCode >= 400 && statusCode < 500:
		return "blocked"
	default:
		return "logged"
	}
}
