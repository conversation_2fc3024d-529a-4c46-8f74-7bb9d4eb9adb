#!/bin/bash
set -e

# check-coverage.sh - Enforce test coverage gates for CI/CD
# This implements the "Tollbooth Enforcement" pattern to prevent tech debt accumulation

COVERAGE_THRESHOLD=${COVERAGE_THRESHOLD:-80}
COVERAGE_FILE="coverage.out"
COVERAGE_HTML="coverage.html"

echo "🧪 Running test coverage analysis..."
echo "Coverage threshold: ${COVERAGE_THRESHOLD}%"

# Run tests with coverage
go test -v -coverprofile=${COVERAGE_FILE} ./... || {
    echo "❌ Tests failed - cannot proceed with coverage check"
    exit 1
}

# Generate coverage report
go tool cover -html=${COVERAGE_FILE} -o ${COVERAGE_HTML}

# Extract coverage percentage
COVERAGE_PERCENT=$(go tool cover -func=${COVERAGE_FILE} | grep total | awk '{print $3}' | sed 's/%//')

echo "📊 Current test coverage: ${COVERAGE_PERCENT}%"

# Check if coverage meets threshold
if (( $(echo "${COVERAGE_PERCENT} >= ${COVERAGE_THRESHOLD}" | bc -l) )); then
    echo "✅ Coverage check passed (${COVERAGE_PERCENT}% >= ${COVERAGE_THRESHOLD}%)"
    echo "📄 HTML coverage report generated: ${COVERAGE_HTML}"
    exit 0
else
    echo "❌ Coverage check failed (${COVERAGE_PERCENT}% < ${COVERAGE_THRESHOLD}%)"
    echo "📄 HTML coverage report generated: ${COVERAGE_HTML}"
    echo ""
    echo "🔍 Low coverage areas:"
    go tool cover -func=${COVERAGE_FILE} | grep -v "100.0%" | head -10
    echo ""
    echo "💡 To improve coverage:"
    echo "   1. Add unit tests for uncovered functions"
    echo "   2. Use testutils.MockStoreProvider for database operations"
    echo "   3. Follow the Golden Path pattern (see internal/services/user/profile_service_test.go)"
    echo "   4. Consider integration tests for complex scenarios"
    echo ""
    echo "⚠️  If you need to temporarily bypass this check, create a tech debt waiver:"
    echo "   ./scripts/create-tech-debt-waiver.sh coverage 'Reason for bypass'"
    exit 1
fi