#!/bin/bash
set -e

# check-waivers.sh - Monitor and manage tech debt waivers
# Provides visibility into outstanding technical debt and waiver status

WAIVERS_DIR="tech-debt-waivers"
CURRENT_DATE=$(date +%Y-%m-%d)

echo "📋 Tech Debt Waiver Status Report"
echo "=================================="
echo "Generated: $(date)"
echo ""

if [ ! -d "${WAIVERS_DIR}" ] || [ -z "$(ls -A "${WAIVERS_DIR}" 2>/dev/null)" ]; then
    echo "✅ No tech debt waivers found - clean slate!"
    exit 0
fi

# Count waivers by status
TOTAL_WAIVERS=0
EXPIRED_WAIVERS=0
ACTIVE_WAIVERS=0

echo "📊 Summary"
echo "----------"

for waiver_file in "${WAIVERS_DIR}"/*.md; do
    if [ -f "$waiver_file" ]; then
        TOTAL_WAIVERS=$((TOTAL_WAIVERS + 1))
        
        # Extract expiry date from waiver
        EXPIRY_DATE=$(grep "Expiry Date:" "$waiver_file" | cut -d' ' -f3 || echo "")
        
        if [ -n "$EXPIRY_DATE" ] && [[ "$CURRENT_DATE" > "$EXPIRY_DATE" ]]; then
            EXPIRED_WAIVERS=$((EXPIRED_WAIVERS + 1))
        else
            ACTIVE_WAIVERS=$((ACTIVE_WAIVERS + 1))
        fi
    fi
done

echo "Total Waivers: $TOTAL_WAIVERS"
echo "Active Waivers: $ACTIVE_WAIVERS"
echo "Expired Waivers: $EXPIRED_WAIVERS"
echo ""

if [ $EXPIRED_WAIVERS -gt 0 ]; then
    echo "⚠️  EXPIRED WAIVERS (Immediate Attention Required)"
    echo "================================================="
    
    for waiver_file in "${WAIVERS_DIR}"/*.md; do
        if [ -f "$waiver_file" ]; then
            EXPIRY_DATE=$(grep "Expiry Date:" "$waiver_file" | cut -d' ' -f3 || echo "")
            
            if [ -n "$EXPIRY_DATE" ] && [[ "$CURRENT_DATE" > "$EXPIRY_DATE" ]]; then
                WAIVER_TYPE=$(grep "Type:" "$waiver_file" | cut -d' ' -f2 || echo "unknown")
                AUTHOR=$(grep "Author:" "$waiver_file" | cut -d' ' -f2- || echo "unknown")
                REASON=$(grep "Reason:" "$waiver_file" | cut -d' ' -f2- || echo "")
                
                echo "❌ $(basename "$waiver_file")"
                echo "   Type: $WAIVER_TYPE"
                echo "   Author: $AUTHOR"
                echo "   Expired: $EXPIRY_DATE"
                echo "   Reason: $REASON"
                echo ""
            fi
        fi
    done
fi

if [ $ACTIVE_WAIVERS -gt 0 ]; then
    echo "🟡 ACTIVE WAIVERS (Monitoring Required)"
    echo "======================================"
    
    for waiver_file in "${WAIVERS_DIR}"/*.md; do
        if [ -f "$waiver_file" ]; then
            EXPIRY_DATE=$(grep "Expiry Date:" "$waiver_file" | cut -d' ' -f3 || echo "")
            
            if [ -z "$EXPIRY_DATE" ] || [[ "$CURRENT_DATE" <= "$EXPIRY_DATE" ]]; then
                WAIVER_TYPE=$(grep "Type:" "$waiver_file" | cut -d' ' -f2 || echo "unknown")
                AUTHOR=$(grep "Author:" "$waiver_file" | cut -d' ' -f2- || echo "unknown")
                REASON=$(grep "Reason:" "$waiver_file" | cut -d' ' -f2- || echo "")
                
                # Calculate days until expiry
                if [ -n "$EXPIRY_DATE" ]; then
                    DAYS_UNTIL_EXPIRY=$(( ( $(date -d "$EXPIRY_DATE" +%s) - $(date -d "$CURRENT_DATE" +%s) ) / 86400 ))
                    if [ $DAYS_UNTIL_EXPIRY -le 7 ]; then
                        URGENCY="⚠️  EXPIRES SOON"
                    elif [ $DAYS_UNTIL_EXPIRY -le 14 ]; then
                        URGENCY="🟡 Approaching expiry"
                    else
                        URGENCY="🟢 Active"
                    fi
                else
                    URGENCY="❓ No expiry date"
                    DAYS_UNTIL_EXPIRY="unknown"
                fi
                
                echo "$URGENCY $(basename "$waiver_file")"
                echo "   Type: $WAIVER_TYPE"
                echo "   Author: $AUTHOR"
                echo "   Expires: $EXPIRY_DATE ($DAYS_UNTIL_EXPIRY days)"
                echo "   Reason: $REASON"
                echo ""
            fi
        fi
    done
fi

echo "🔧 Recommended Actions"
echo "======================"

if [ $EXPIRED_WAIVERS -gt 0 ]; then
    echo "❗ URGENT: $EXPIRED_WAIVERS waiver(s) have expired"
    echo "   1. Review expired waivers immediately"
    echo "   2. Either fix the technical debt or renew the waiver"
    echo "   3. Update CI/CD pipeline to block builds if unresolved"
fi

if [ $ACTIVE_WAIVERS -gt 0 ]; then
    echo "📅 Plan tech debt remediation for $ACTIVE_WAIVERS active waiver(s)"
    echo "   1. Schedule dedicated time for technical debt in upcoming sprints"
    echo "   2. Track progress on remediation plans"
    echo "   3. Set up alerts for waivers expiring soon"
fi

echo ""
echo "📈 Tech Debt Health Metrics"
echo "============================"
echo "Waiver Velocity: $TOTAL_WAIVERS total waivers created"
echo "Cleanup Rate: Consider tracking resolved vs new waivers"
echo "Quality Gate Effectiveness: Monitor waiver reasons for patterns"
echo ""
echo "💡 To resolve technical debt:"
echo "   1. ./scripts/check-coverage.sh (run tests and coverage)"
echo "   2. make lint (check code quality)"
echo "   3. Update waiver files with progress"
echo "   4. Remove waiver files when debt is resolved"