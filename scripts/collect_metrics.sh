#!/bin/bash
# Collect and export test metrics
# Usage: ./collect_metrics.sh <test_suite> <test_duration> <tests_passed> <tests_failed> <coverage_percent>

set -euo pipefail

# Parse arguments
TEST_SUITE="${1:-unknown}"
TEST_DURATION="${2:-0}"
TESTS_PASSED="${3:-0}"
TESTS_FAILED="${4:-0}"
COVERAGE_PERCENT="${5:-0}"

# Generate timestamp
TIMESTAMP=$(date -u +%Y%m%d%H%M%S)
METRICS_DIR="${METRICS_DIR:-./metrics}"

# Create metrics directory if it doesn't exist
mkdir -p "$METRICS_DIR"

# Define output files
JSON_FILE="$METRICS_DIR/metrics-${TIMESTAMP}.json"
PROM_FILE="$METRICS_DIR/metrics-${TIMESTAMP}.prom"

# Collect additional system metrics
LOAD_AVG=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | tr -d ',')
MEM_USED=$(free -m | awk 'NR==2{printf "%.2f", $3/$2*100}')
CPU_COUNT=$(nproc)

# Calculate derived metrics
TOTAL_TESTS=$((TESTS_PASSED + TESTS_FAILED))
SUCCESS_RATE=0
if [ "$TOTAL_TESTS" -gt 0 ]; then
    SUCCESS_RATE=$(awk "BEGIN {printf \"%.2f\", ($TESTS_PASSED / $TOTAL_TESTS) * 100}")
fi

# Generate JSON metrics
jq -n \
  --arg suite "$TEST_SUITE" \
  --arg duration "$TEST_DURATION" \
  --arg passed "$TESTS_PASSED" \
  --arg failed "$TESTS_FAILED" \
  --arg coverage "$COVERAGE_PERCENT" \
  --arg timestamp "$TIMESTAMP" \
  --arg load "$LOAD_AVG" \
  --arg memory "$MEM_USED" \
  --arg success_rate "$SUCCESS_RATE" \
  '{
    timestamp: $timestamp,
    suite: $suite,
    duration: ($duration | tonumber),
    tests: {
      total: (($passed | tonumber) + ($failed | tonumber)),
      passed: ($passed | tonumber),
      failed: ($failed | tonumber),
      success_rate: ($success_rate | tonumber)
    },
    coverage: ($coverage | tonumber),
    system: {
      load_average: ($load | tonumber),
      memory_percent: ($memory | tonumber)
    }
  }' > "$JSON_FILE"

# Generate Prometheus metrics
cat <<EOF > "$PROM_FILE"
# HELP test_duration_seconds Test suite execution time
# TYPE test_duration_seconds gauge
test_duration_seconds{suite="${TEST_SUITE}"} ${TEST_DURATION}

# HELP test_coverage_percent Test coverage percentage
# TYPE test_coverage_percent gauge
test_coverage_percent{suite="${TEST_SUITE}"} ${COVERAGE_PERCENT}

# HELP test_total_count Total number of tests
# TYPE test_total_count gauge
test_total_count{suite="${TEST_SUITE}"} ${TOTAL_TESTS}

# HELP test_passed_count Number of passed tests
# TYPE test_passed_count gauge
test_passed_count{suite="${TEST_SUITE}"} ${TESTS_PASSED}

# HELP test_failed_count Number of failed tests
# TYPE test_failed_count gauge
test_failed_count{suite="${TEST_SUITE}"} ${TESTS_FAILED}

# HELP test_success_rate_percent Test success rate percentage
# TYPE test_success_rate_percent gauge
test_success_rate_percent{suite="${TEST_SUITE}"} ${SUCCESS_RATE}

# HELP system_load_average System load average
# TYPE system_load_average gauge
system_load_average ${LOAD_AVG}

# HELP system_memory_usage_percent System memory usage percentage
# TYPE system_memory_usage_percent gauge
system_memory_usage_percent ${MEM_USED}

# HELP test_timestamp_seconds Unix timestamp of test execution
# TYPE test_timestamp_seconds gauge
test_timestamp_seconds{suite="${TEST_SUITE}"} $(date +%s)
EOF

# Output file locations
echo "Metrics collected successfully:"
echo "  JSON: $JSON_FILE"
echo "  Prometheus: $PROM_FILE"

# Optionally push to monitoring system
if [ -n "${PROMETHEUS_PUSHGATEWAY_URL:-}" ]; then
    echo "Pushing metrics to Prometheus Pushgateway..."
    curl -s -X POST "${PROMETHEUS_PUSHGATEWAY_URL}/metrics/job/test_metrics/instance/${HOSTNAME}" \
        -H "Content-Type: text/plain" \
        --data-binary "@$PROM_FILE"
    echo "Metrics pushed to Prometheus Pushgateway"
fi

# Archive old metrics (keep last 7 days)
find "$METRICS_DIR" -name "metrics-*.json" -mtime +7 -delete 2>/dev/null || true
find "$METRICS_DIR" -name "metrics-*.prom" -mtime +7 -delete 2>/dev/null || true