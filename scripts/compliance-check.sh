#!/bin/bash

# Compliance Check Script for CI/CD
# This script runs automated compliance checks and generates reports

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
REPORT_DIR="${PROJECT_ROOT}/compliance-reports"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Create report directory
mkdir -p "${REPORT_DIR}"

echo "🔍 Running Compliance Checks..."

# Function to check exit status
check_status() {
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ $1 passed${NC}"
        return 0
    else
        echo -e "${RED}✗ $1 failed${NC}"
        return 1
    fi
}

# Initialize compliance score
TOTAL_CHECKS=0
PASSED_CHECKS=0
COMPLIANCE_ISSUES=""

# 1. Security Headers Check
echo "📋 Checking Security Headers..."
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
if grep -q "SecurityHeaders" "${PROJECT_ROOT}/internal/middleware/security_headers.go"; then
    HEADERS_FOUND=0
    for header in "X-Content-Type-Options" "X-Frame-Options" "X-XSS-Protection" "Strict-Transport-Security" "Content-Security-Policy"; do
        if grep -q "$header" "${PROJECT_ROOT}/internal/middleware/security_headers.go"; then
            HEADERS_FOUND=$((HEADERS_FOUND + 1))
        fi
    done
    if [ $HEADERS_FOUND -ge 5 ]; then
        echo -e "${GREEN}✓ Security headers properly configured${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        echo -e "${YELLOW}⚠ Missing some security headers${NC}"
        COMPLIANCE_ISSUES="${COMPLIANCE_ISSUES}\n- Missing security headers"
    fi
else
    echo -e "${RED}✗ Security headers middleware not found${NC}"
    COMPLIANCE_ISSUES="${COMPLIANCE_ISSUES}\n- Security headers middleware missing"
fi

# 2. Encryption Check
echo "📋 Checking Encryption Implementation..."
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
if [ -f "${PROJECT_ROOT}/internal/infrastructure/crypto/encryption.go" ]; then
    echo -e "${GREEN}✓ Encryption utilities implemented${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    echo -e "${RED}✗ Encryption utilities not found${NC}"
    COMPLIANCE_ISSUES="${COMPLIANCE_ISSUES}\n- Encryption utilities missing"
fi

# 3. Authentication Security
echo "📋 Checking Authentication Security..."
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
AUTH_SECURE=1
# Check for rate limiting
if ! grep -q "RateLimiter" "${PROJECT_ROOT}/internal/authn/phone_rate_limit.go" 2>/dev/null; then
    AUTH_SECURE=0
    COMPLIANCE_ISSUES="${COMPLIANCE_ISSUES}\n- Rate limiting not implemented"
fi
# Check for MFA/OTP
if ! grep -q "OTP" "${PROJECT_ROOT}/internal/authn/phone_handlers.go" 2>/dev/null; then
    AUTH_SECURE=0
    COMPLIANCE_ISSUES="${COMPLIANCE_ISSUES}\n- OTP authentication not found"
fi
if [ $AUTH_SECURE -eq 1 ]; then
    echo -e "${GREEN}✓ Authentication security measures in place${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    echo -e "${YELLOW}⚠ Authentication security issues found${NC}"
fi

# 4. Data Privacy Compliance
echo "📋 Checking Data Privacy Implementation..."
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
PRIVACY_COMPLIANT=1
# Check for data minimization
if ! grep -q "phone" "${PROJECT_ROOT}/internal/authn/registration_handlers.go" 2>/dev/null; then
    PRIVACY_COMPLIANT=0
    COMPLIANCE_ISSUES="${COMPLIANCE_ISSUES}\n- Data minimization not verified"
fi
# Check for audit logging
if [ ! -d "${PROJECT_ROOT}/internal/services/audit" ]; then
    PRIVACY_COMPLIANT=0
    COMPLIANCE_ISSUES="${COMPLIANCE_ISSUES}\n- Audit logging not implemented"
fi
if [ $PRIVACY_COMPLIANT -eq 1 ]; then
    echo -e "${GREEN}✓ Data privacy measures implemented${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    echo -e "${YELLOW}⚠ Data privacy issues found${NC}"
fi

# 5. Access Control Check
echo "📋 Checking Access Control..."
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
if [ -f "${PROJECT_ROOT}/internal/authorization/can.go" ]; then
    echo -e "${GREEN}✓ RBAC implementation found${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    echo -e "${RED}✗ Access control implementation missing${NC}"
    COMPLIANCE_ISSUES="${COMPLIANCE_ISSUES}\n- RBAC not implemented"
fi

# 6. Input Validation
echo "📋 Checking Input Validation..."
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
if [ -d "${PROJECT_ROOT}/internal/utils/validation" ]; then
    echo -e "${GREEN}✓ Input validation framework found${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    echo -e "${RED}✗ Input validation framework missing${NC}"
    COMPLIANCE_ISSUES="${COMPLIANCE_ISSUES}\n- Input validation framework missing"
fi

# 7. Security Testing
echo "📋 Checking Security Tests..."
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
if [ -d "${PROJECT_ROOT}/tests/security/owasp" ]; then
    # Count OWASP test files
    OWASP_TESTS=$(find "${PROJECT_ROOT}/tests/security/owasp" -name "*_test.go" | wc -l)
    if [ $OWASP_TESTS -ge 5 ]; then
        echo -e "${GREEN}✓ Comprehensive security tests found (${OWASP_TESTS} test files)${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    else
        echo -e "${YELLOW}⚠ Limited security test coverage${NC}"
        COMPLIANCE_ISSUES="${COMPLIANCE_ISSUES}\n- Insufficient security test coverage"
    fi
else
    echo -e "${RED}✗ Security tests not found${NC}"
    COMPLIANCE_ISSUES="${COMPLIANCE_ISSUES}\n- Security tests missing"
fi

# 8. Vulnerability Scanning
echo "📋 Running Vulnerability Scan..."
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
if command -v gosec &> /dev/null; then
    cd "${PROJECT_ROOT}"
    gosec -fmt json -out "${REPORT_DIR}/gosec_${TIMESTAMP}.json" ./... 2>/dev/null || true
    
    # Check if high severity issues exist
    if [ -f "${REPORT_DIR}/gosec_${TIMESTAMP}.json" ]; then
        HIGH_ISSUES=$(jq '[.Issues[] | select(.severity == "HIGH")] | length' "${REPORT_DIR}/gosec_${TIMESTAMP}.json" 2>/dev/null || echo "0")
        if [ "$HIGH_ISSUES" = "0" ]; then
            echo -e "${GREEN}✓ No high-severity vulnerabilities found${NC}"
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
        else
            echo -e "${RED}✗ Found ${HIGH_ISSUES} high-severity vulnerabilities${NC}"
            COMPLIANCE_ISSUES="${COMPLIANCE_ISSUES}\n- ${HIGH_ISSUES} high-severity vulnerabilities"
        fi
    fi
else
    echo -e "${YELLOW}⚠ gosec not installed, skipping vulnerability scan${NC}"
fi

# 9. Dependency Check
echo "📋 Checking Dependencies..."
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
if command -v nancy &> /dev/null; then
    cd "${PROJECT_ROOT}"
    go list -json -deps ./... | nancy sleuth > "${REPORT_DIR}/nancy_${TIMESTAMP}.txt" 2>&1 || true
    
    if grep -q "Vulnerable" "${REPORT_DIR}/nancy_${TIMESTAMP}.txt"; then
        VULN_COUNT=$(grep -c "Vulnerable" "${REPORT_DIR}/nancy_${TIMESTAMP}.txt")
        echo -e "${RED}✗ Found ${VULN_COUNT} vulnerable dependencies${NC}"
        COMPLIANCE_ISSUES="${COMPLIANCE_ISSUES}\n- ${VULN_COUNT} vulnerable dependencies"
    else
        echo -e "${GREEN}✓ No vulnerable dependencies found${NC}"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
    fi
else
    echo -e "${YELLOW}⚠ nancy not installed, skipping dependency check${NC}"
fi

# 10. Data Retention Policy
echo "📋 Checking Data Retention Implementation..."
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
if grep -r "deleted_at" "${PROJECT_ROOT}/db/migrations/" &>/dev/null; then
    echo -e "${GREEN}✓ Soft delete implementation found${NC}"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    echo -e "${YELLOW}⚠ Data retention implementation needs review${NC}"
    COMPLIANCE_ISSUES="${COMPLIANCE_ISSUES}\n- Data retention policy implementation unclear"
fi

# Calculate compliance score
COMPLIANCE_SCORE=$(awk "BEGIN {printf \"%.1f\", ($PASSED_CHECKS/$TOTAL_CHECKS)*100}")

# Generate compliance report
REPORT_FILE="${REPORT_DIR}/compliance_report_${TIMESTAMP}.txt"
cat > "${REPORT_FILE}" << EOF
================================================================================
                           COMPLIANCE CHECK REPORT
================================================================================
Generated: $(date)
Project: The Moment Backend

SUMMARY
-------
Total Checks: ${TOTAL_CHECKS}
Passed: ${PASSED_CHECKS}
Failed: $((TOTAL_CHECKS - PASSED_CHECKS))
Compliance Score: ${COMPLIANCE_SCORE}%

COMPLIANCE FRAMEWORKS
--------------------
- SOC2 Type II
- GDPR
- OWASP Top 10

DETAILED RESULTS
---------------
EOF

# Add issues if any
if [ -n "${COMPLIANCE_ISSUES}" ]; then
    echo -e "\nCOMPLIANCE ISSUES FOUND:" >> "${REPORT_FILE}"
    echo -e "${COMPLIANCE_ISSUES}" >> "${REPORT_FILE}"
else
    echo -e "\nNo compliance issues found. All checks passed!" >> "${REPORT_FILE}"
fi

# Add recommendations
cat >> "${REPORT_FILE}" << EOF

RECOMMENDATIONS
--------------
1. Run this compliance check as part of every CI/CD pipeline
2. Address any HIGH severity vulnerabilities immediately
3. Review and update security policies quarterly
4. Conduct annual third-party security audits
5. Maintain compliance documentation up-to-date

NEXT STEPS
----------
EOF

if [ "${COMPLIANCE_SCORE%.*}" -lt 80 ]; then
    echo "⚠️  Compliance score below 80%. Immediate action required!" >> "${REPORT_FILE}"
    echo "1. Review and fix all identified issues" >> "${REPORT_FILE}"
    echo "2. Re-run compliance checks after fixes" >> "${REPORT_FILE}"
    echo "3. Schedule security review meeting" >> "${REPORT_FILE}"
elif [ "${COMPLIANCE_SCORE%.*}" -lt 95 ]; then
    echo "📋 Good compliance level. Some improvements needed." >> "${REPORT_FILE}"
    echo "1. Address remaining compliance gaps" >> "${REPORT_FILE}"
    echo "2. Plan for achieving 95%+ compliance" >> "${REPORT_FILE}"
else
    echo "✅ Excellent compliance level!" >> "${REPORT_FILE}"
    echo "1. Maintain current security practices" >> "${REPORT_FILE}"
    echo "2. Continue regular compliance monitoring" >> "${REPORT_FILE}"
fi

echo "" >> "${REPORT_FILE}"
echo "Report saved to: ${REPORT_FILE}" >> "${REPORT_FILE}"

# Display summary
echo ""
echo "=================================================================================="
echo "COMPLIANCE CHECK COMPLETE"
echo "=================================================================================="
echo "Compliance Score: ${COMPLIANCE_SCORE}%"
echo "Report saved to: ${REPORT_FILE}"

# Exit with appropriate code for CI/CD
if [ "${COMPLIANCE_SCORE%.*}" -lt 80 ]; then
    echo -e "${RED}❌ Compliance check failed (score < 80%)${NC}"
    exit 1
else
    echo -e "${GREEN}✅ Compliance check passed${NC}"
    exit 0
fi