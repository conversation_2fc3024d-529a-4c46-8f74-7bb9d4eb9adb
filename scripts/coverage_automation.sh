#!/bin/bash

# Coverage Automation with Makefile Integration and PR Diff Reporting
# Comprehensive automation system for seamless coverage integration

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
COVERAGE_DIR="coverage-data"
AUTOMATION_DIR="$COVERAGE_DIR/automation"
PR_DIR="$AUTOMATION_DIR/pr-diffs"
INTEGRATION_DIR="$AUTOMATION_DIR/integration"
HOOKS_DIR="$AUTOMATION_DIR/hooks"
MAKEFILE_BACKUP="Makefile.backup"

# Automation types
AUTO_MAKEFILE="makefile"
AUTO_PR_DIFF="pr-diff"
AUTO_HOOKS="hooks"
AUTO_CI="ci"
AUTO_BADGES="badges"

# Timestamp for this run
TIMESTAMP=$(date '+%Y-%m-%d_%H-%M-%S')
ISO_TIMESTAMP=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to show usage
usage() {
    cat << EOF
Usage: $(basename "$0") [OPTIONS]

Coverage automation with Makefile integration and PR diff reporting.

OPTIONS:
    --integrate TYPE        Integration type (makefile|pr-diff|hooks|ci|badges|all)
    --pr-number NUMBER      PR number for diff analysis
    --base-branch BRANCH    Base branch for comparison (default: main)
    --target-branch BRANCH  Target branch for comparison (default: current)
    --generate-hooks        Generate Git hooks for coverage
    --update-makefile       Update Makefile with coverage targets
    --create-badges         Create and update coverage badges
    --pr-comment           Generate PR comment with coverage analysis
    --ci-integration        Generate CI/CD integration files
    --watch                 Enable file watching for automatic updates
    --dry-run              Show what would be done without making changes
    --help                  Show this help message

INTEGRATION TYPES:
    makefile    - Integrate with Makefile targets
    pr-diff     - Generate PR coverage diff reports
    hooks       - Set up Git hooks for coverage checking
    ci          - Generate CI/CD integration files
    badges      - Create and update coverage badges
    all         - All integration types

PR DIFF ANALYSIS:
    --pr-number NUMBER      Analyze specific PR
    --diff-format FORMAT    Output format (text|json|markdown|html)
    --include-files         Include file-level diff analysis
    --threshold-check       Check coverage thresholds for changed files

EXAMPLES:
    $(basename "$0") --integrate all                           # Full integration
    $(basename "$0") --pr-number 123 --pr-comment             # Analyze PR and comment
    $(basename "$0") --integrate makefile --update-makefile   # Update Makefile
    $(basename "$0") --watch                                   # Enable auto-updates

EOF
    exit 0
}

# Setup directories
setup_directories() {
    mkdir -p "$COVERAGE_DIR"
    mkdir -p "$AUTOMATION_DIR"
    mkdir -p "$PR_DIR"
    mkdir -p "$INTEGRATION_DIR"
    mkdir -p "$HOOKS_DIR"
}

# Update Makefile with coverage automation targets
integrate_makefile() {
    print_color "$BLUE" "🔧 Integrating with Makefile..."
    
    # Backup existing Makefile
    if [ -f "Makefile" ]; then
        cp "Makefile" "$MAKEFILE_BACKUP"
        print_color "$GREEN" "✓ Backed up existing Makefile to $MAKEFILE_BACKUP"
    fi
    
    # Check if our targets already exist
    if grep -q "# Agent C Coverage Automation" Makefile 2>/dev/null; then
        print_color "$YELLOW" "⚠️  Coverage automation targets already exist in Makefile"
        return
    fi
    
    # Add our coverage automation targets
    cat >> Makefile << 'EOF'

# ============================================================================
# Agent C Coverage Automation Targets
# ============================================================================

# Enhanced Coverage Collection
.PHONY: coverage-collect coverage-collect-full coverage-packages coverage-trends
coverage-collect: ## Collect basic coverage data
	@echo "📊 Collecting coverage data..."
	@./scripts/coverage_collect.sh

coverage-collect-full: ## Collect full coverage data with packages and trends
	@echo "📊 Collecting comprehensive coverage data..."
	@./scripts/coverage_collect.sh --packages --trends --json --html

coverage-packages: ## Analyze package-level coverage
	@echo "📦 Analyzing package coverage..."
	@./scripts/coverage_collect.sh --packages --json

coverage-trends: ## Generate coverage trends
	@echo "📈 Generating coverage trends..."
	@./scripts/coverage_collect.sh --trends

# Coverage Quality Gates
.PHONY: coverage-gates coverage-gates-all coverage-gates-strict coverage-regression-check
coverage-gates: ## Run basic coverage gates
	@echo "🚦 Running coverage gates..."
	@./scripts/coverage_gates.sh --gate overall

coverage-gates-all: ## Run all coverage gates
	@echo "🚦 Running all coverage gates..."
	@./scripts/coverage_gates.sh --gate all

coverage-gates-strict: ## Run coverage gates in strict mode
	@echo "🚦 Running coverage gates (strict mode)..."
	@./scripts/coverage_gates.sh --gate all --strict

coverage-regression-check: ## Check for coverage regressions
	@echo "📉 Checking for coverage regressions..."
	@./scripts/coverage_gates.sh --gate regression

# Coverage Reporting
.PHONY: coverage-dashboard coverage-reports coverage-serve coverage-watch
coverage-dashboard: ## Generate interactive coverage dashboard
	@echo "🌐 Generating coverage dashboard..."
	@./scripts/coverage_reporting.sh --type dashboard --interactive

coverage-reports: ## Generate all coverage reports
	@echo "📊 Generating all coverage reports..."
	@./scripts/coverage_reporting.sh --type all --interactive

coverage-serve: ## Start coverage report server
	@echo "🚀 Starting coverage report server..."
	@./scripts/coverage_reporting.sh --serve --watch --port 8080

coverage-watch: ## Watch for coverage changes and auto-update reports
	@echo "👀 Watching for coverage changes..."
	@./scripts/coverage_reporting.sh --watch

# Coverage Quality Management
.PHONY: coverage-quality coverage-quality-check coverage-waivers coverage-thresholds
coverage-quality: ## Run quality checks
	@echo "🎯 Running coverage quality checks..."
	@./scripts/coverage_quality.sh --check

coverage-quality-check: ## Run quality checks with strict enforcement
	@echo "🎯 Running coverage quality checks (strict)..."
	@./scripts/coverage_quality.sh --check --strict

coverage-waivers: ## List active coverage waivers
	@echo "📋 Listing active coverage waivers..."
	@./scripts/coverage_quality.sh --list-waivers

coverage-thresholds: ## List coverage thresholds
	@echo "🎯 Listing coverage thresholds..."
	@./scripts/coverage_quality.sh --list-thresholds

# Coverage Automation
.PHONY: coverage-pr-diff coverage-badges coverage-hooks coverage-ci
coverage-pr-diff: ## Generate PR coverage diff (requires PR_NUMBER)
	@if [ -z "$(PR_NUMBER)" ]; then \
		echo "Usage: make coverage-pr-diff PR_NUMBER=123"; \
		exit 1; \
	fi
	@echo "🔍 Generating PR coverage diff..."
	@./scripts/coverage_automation.sh --pr-number $(PR_NUMBER) --pr-comment

coverage-badges: ## Generate coverage badges
	@echo "🏷️ Generating coverage badges..."
	@./scripts/coverage_automation.sh --integrate badges

coverage-hooks: ## Set up Git hooks for coverage
	@echo "🪝 Setting up Git hooks..."
	@./scripts/coverage_automation.sh --integrate hooks

coverage-ci: ## Generate CI/CD integration files
	@echo "🔄 Generating CI/CD integration..."
	@./scripts/coverage_automation.sh --integrate ci

# Comprehensive Coverage Workflow
.PHONY: coverage-full coverage-ci-check coverage-pr-check
coverage-full: ## Full coverage workflow (collect, check, report)
	@echo "🚀 Running full coverage workflow..."
	@$(MAKE) coverage-collect-full
	@$(MAKE) coverage-gates-all
	@$(MAKE) coverage-quality-check
	@$(MAKE) coverage-dashboard

coverage-ci-check: ## CI/CD coverage check (for automated builds)
	@echo "🤖 Running CI coverage checks..."
	@./scripts/coverage_collect.sh --packages --json
	@./scripts/coverage_gates.sh --gate all --output-format json
	@./scripts/coverage_quality.sh --check --output-format json

coverage-pr-check: ## PR coverage check (requires PR_NUMBER)
	@if [ -z "$(PR_NUMBER)" ]; then \
		echo "Usage: make coverage-pr-check PR_NUMBER=123"; \
		exit 1; \
	fi
	@echo "🔍 Running PR coverage check..."
	@./scripts/coverage_automation.sh --pr-number $(PR_NUMBER) --pr-comment --threshold-check

# Coverage Maintenance
.PHONY: coverage-clean coverage-reset coverage-backup
coverage-clean: ## Clean coverage data and reports
	@echo "🧹 Cleaning coverage data..."
	@rm -rf coverage-data/reports/*
	@rm -f coverage.out coverage.html

coverage-reset: ## Reset all coverage data and configuration
	@echo "🔄 Resetting coverage data..."
	@rm -rf coverage-data/
	@rm -f coverage.out coverage.html coverage-config.yaml

coverage-backup: ## Backup coverage data
	@echo "💾 Backing up coverage data..."
	@tar -czf coverage-backup-$(shell date +%Y%m%d-%H%M%S).tar.gz coverage-data/ coverage.out coverage-config.yaml 2>/dev/null || true

# Help for coverage targets
coverage-help: ## Show coverage automation help
	@echo "Coverage Automation Targets:"
	@echo "============================"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | grep coverage | awk 'BEGIN {FS = ":.*?## "}; {printf "  %-25s %s\n", $$1, $$2}'

EOF
    
    print_color "$GREEN" "✓ Added coverage automation targets to Makefile"
    print_color "$CYAN" "  Run 'make coverage-help' to see all available targets"
}

# Generate PR coverage diff analysis
generate_pr_diff() {
    local pr_number=$1
    local base_branch=${2:-main}
    local target_branch=${3:-HEAD}
    local format=${4:-markdown}
    local include_files=${5:-true}
    
    print_color "$BLUE" "🔍 Generating PR coverage diff for PR #$pr_number..."
    
    # Ensure we have coverage data
    if [ ! -f "coverage.out" ]; then
        print_color "$YELLOW" "⚠️  No coverage data found, generating..."
        go test -timeout 30m -coverprofile=coverage.out -covermode=atomic ./...
    fi
    
    local pr_diff_file="$PR_DIR/pr-${pr_number}-diff-${TIMESTAMP}.${format}"
    local current_coverage=$(go tool cover -func=coverage.out | grep "total:" | awk '{print $3}' | sed 's/%//')
    
    # Get changed files (simulate for demo - would use git/GitHub API)
    local changed_files="
internal/auth/handlers.go
internal/user/service.go
internal/event/repository.go
"
    
    case $format in
        markdown)
            cat > "$pr_diff_file" << EOF
# Coverage Analysis for PR #$pr_number

## 📊 Overview
- **Current Coverage**: ${current_coverage}%
- **Base Branch**: $base_branch
- **Target Branch**: $target_branch
- **Analysis Date**: $ISO_TIMESTAMP

## 🔍 Changed Files Analysis

EOF
            
            if [ "$include_files" = true ]; then
                echo "$changed_files" | while read -r file; do
                    if [ -n "$file" ]; then
                        # Get file coverage (simplified)
                        local file_coverage=$(go tool cover -func=coverage.out | grep "$file" | head -1 | awk '{print $3}' || echo "N/A")
                        cat >> "$pr_diff_file" << EOF
### \`$file\`
- **Coverage**: $file_coverage
- **Status**: $([ "$file_coverage" != "N/A" ] && echo "✅ Covered" || echo "⚠️ No coverage data")

EOF
                    fi
                done
            fi
            
            cat >> "$pr_diff_file" << EOF
## 🎯 Quality Gates
- **Overall Threshold**: 85%
- **Status**: $([ $(echo "$current_coverage >= 85" | bc -l) -eq 1 ] && echo "✅ PASS" || echo "❌ FAIL")

## 📈 Recommendations
- Ensure new code has adequate test coverage
- Consider adding integration tests for complex features
- Review any files with coverage below 75%

---
*Generated by Agent C Coverage Automation*
EOF
            ;;
            
        json)
            jq -n \
                --arg pr "$pr_number" \
                --arg coverage "$current_coverage" \
                --arg base "$base_branch" \
                --arg target "$target_branch" \
                --arg timestamp "$ISO_TIMESTAMP" \
                '{
                    pr_number: $pr,
                    analysis: {
                        current_coverage: ($coverage | tonumber),
                        base_branch: $base,
                        target_branch: $target,
                        timestamp: $timestamp
                    },
                    changed_files: [],
                    quality_gates: {
                        overall_threshold: 85,
                        status: (if ($coverage | tonumber) >= 85 then "pass" else "fail" end)
                    }
                }' > "$pr_diff_file"
            ;;
            
        html)
            cat > "$pr_diff_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Coverage Analysis - PR #$pr_number</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background: #e9ecef; border-radius: 4px; }
        .pass { color: #28a745; }
        .fail { color: #dc3545; }
        .file { margin: 10px 0; padding: 10px; border-left: 4px solid #007bff; background: #f8f9fa; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 Coverage Analysis for PR #$pr_number</h1>
        <div class="metric">Current Coverage: <strong>${current_coverage}%</strong></div>
        <div class="metric">Base Branch: <strong>$base_branch</strong></div>
        <div class="metric">Target Branch: <strong>$target_branch</strong></div>
    </div>
    
    <h2>🔍 Quality Gates</h2>
    <div class="metric $([ $(echo "$current_coverage >= 85" | bc -l) -eq 1 ] && echo "pass" || echo "fail")">
        Overall Threshold: $([ $(echo "$current_coverage >= 85" | bc -l) -eq 1 ] && echo "✅ PASS" || echo "❌ FAIL")
    </div>
    
    <p><em>Generated by Agent C Coverage Automation on $ISO_TIMESTAMP</em></p>
</body>
</html>
EOF
            ;;
    esac
    
    print_color "$GREEN" "✓ PR diff analysis saved: $pr_diff_file"
    echo "$pr_diff_file"
}

# Generate PR comment for GitHub
generate_pr_comment() {
    local pr_number=$1
    local diff_file=$2
    
    print_color "$BLUE" "💬 Generating PR comment..."
    
    local comment_file="$PR_DIR/pr-${pr_number}-comment-${TIMESTAMP}.md"
    
    cat > "$comment_file" << EOF
## 📊 Coverage Report

EOF
    
    # Include the diff analysis
    if [ -f "$diff_file" ] && [[ "$diff_file" == *.md ]]; then
        cat "$diff_file" >> "$comment_file"
    fi
    
    cat >> "$comment_file" << EOF

<details>
<summary>📋 Coverage Commands</summary>

\`\`\`bash
# Generate coverage report
make coverage-full

# Check coverage gates
make coverage-gates-all

# View coverage dashboard
make coverage-serve
\`\`\`

</details>

---
🤖 This comment was automatically generated by Agent C Coverage Automation
EOF
    
    print_color "$GREEN" "✓ PR comment saved: $comment_file"
    
    # If gh CLI is available, post the comment
    if command -v gh >/dev/null 2>&1; then
        print_color "$BLUE" "📤 Posting comment to PR #$pr_number..."
        gh pr comment "$pr_number" --body-file "$comment_file" || {
            print_color "$YELLOW" "⚠️  Failed to post comment automatically"
            print_color "$CYAN" "   Manual command: gh pr comment $pr_number --body-file $comment_file"
        }
    else
        print_color "$CYAN" "💡 To post comment manually:"
        print_color "$CYAN" "   gh pr comment $pr_number --body-file $comment_file"
    fi
}

# Set up Git hooks for coverage
setup_git_hooks() {
    print_color "$BLUE" "🪝 Setting up Git hooks..."
    
    local git_hooks_dir=".git/hooks"
    
    if [ ! -d "$git_hooks_dir" ]; then
        print_color "$YELLOW" "⚠️  Not in a Git repository, skipping hooks setup"
        return
    fi
    
    # Pre-commit hook for coverage check
    cat > "$git_hooks_dir/pre-commit" << 'EOF'
#!/bin/bash
# Agent C Coverage Pre-commit Hook

echo "🧪 Running coverage checks before commit..."

# Run tests and collect coverage
if ! make coverage-collect; then
    echo "❌ Coverage collection failed"
    exit 1
fi

# Run basic quality checks
if ! make coverage-quality; then
    echo "❌ Coverage quality checks failed"
    echo "💡 Use 'make coverage-waivers' to create temporary waivers if needed"
    exit 1
fi

echo "✅ Coverage checks passed"
exit 0
EOF
    
    # Pre-push hook for comprehensive coverage check
    cat > "$git_hooks_dir/pre-push" << 'EOF'
#!/bin/bash
# Agent C Coverage Pre-push Hook

echo "🚀 Running comprehensive coverage checks before push..."

# Run all coverage gates
if ! make coverage-gates-all; then
    echo "❌ Coverage gates failed"
    echo "💡 Use 'make coverage-gates' for basic checks or create waivers"
    exit 1
fi

echo "✅ All coverage gates passed"
exit 0
EOF
    
    # Make hooks executable
    chmod +x "$git_hooks_dir/pre-commit"
    chmod +x "$git_hooks_dir/pre-push"
    
    # Save hook configurations
    cat > "$HOOKS_DIR/hook-config.json" << EOF
{
    "hooks": {
        "pre-commit": {
            "enabled": true,
            "commands": ["make coverage-collect", "make coverage-quality"],
            "description": "Basic coverage checks before commit"
        },
        "pre-push": {
            "enabled": true,
            "commands": ["make coverage-gates-all"],
            "description": "Comprehensive coverage gates before push"
        }
    },
    "installed": "$ISO_TIMESTAMP"
}
EOF
    
    print_color "$GREEN" "✓ Git hooks installed:"
    print_color "$CYAN" "  pre-commit: Basic coverage checks"
    print_color "$CYAN" "  pre-push: Comprehensive coverage gates"
}

# Generate CI/CD integration files
generate_ci_integration() {
    print_color "$BLUE" "🔄 Generating CI/CD integration files..."
    
    # GitHub Actions workflow for coverage
    local github_dir=".github/workflows"
    mkdir -p "$github_dir"
    
    cat > "$github_dir/coverage-automation.yml" << 'EOF'
name: Coverage Automation

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  GO_VERSION: '1.23'

jobs:
  coverage-analysis:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0  # Full history for trend analysis
    
    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: ${{ env.GO_VERSION }}
    
    - name: Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y bc jq
    
    - name: Generate test coverage
      run: make coverage-collect-full
    
    - name: Run coverage gates
      run: make coverage-gates-all
    
    - name: Quality checks
      run: make coverage-quality-check
    
    - name: Generate reports
      run: make coverage-dashboard
    
    - name: PR Coverage Analysis
      if: github.event_name == 'pull_request'
      run: |
        make coverage-pr-diff PR_NUMBER=${{ github.event.number }}
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Upload coverage reports
      uses: actions/upload-artifact@v4
      with:
        name: coverage-reports
        path: |
          coverage-data/reports/
          coverage-data/badges/
          coverage.out
    
    - name: Update coverage badge
      if: github.ref == 'refs/heads/main'
      run: |
        make coverage-badges
        # Commit badge updates if needed
EOF
    
    # Makefile integration for Docker
    cat > "$INTEGRATION_DIR/docker-coverage.mk" << 'EOF'
# Docker-based coverage automation
# Include this in your main Makefile: include coverage-data/automation/integration/docker-coverage.mk

.PHONY: coverage-docker coverage-docker-report
coverage-docker: ## Run coverage in Docker container
	@echo "🐳 Running coverage in Docker..."
	@docker run --rm -v $(PWD):/app -w /app golang:1.23-alpine sh -c "\
		apk add --no-cache make git bc jq && \
		make coverage-full"

coverage-docker-report: ## Generate coverage report in Docker and serve
	@echo "🐳 Generating coverage report in Docker..."
	@docker run --rm -v $(PWD):/app -w /app -p 8080:8080 golang:1.23-alpine sh -c "\
		apk add --no-cache make git bc jq python3 && \
		make coverage-reports && \
		make coverage-serve"
EOF
    
    # Jenkins pipeline
    cat > "$INTEGRATION_DIR/Jenkinsfile.coverage" << 'EOF'
pipeline {
    agent any
    
    environment {
        GO_VERSION = '1.23'
    }
    
    stages {
        stage('Coverage Collection') {
            steps {
                script {
                    sh 'make coverage-collect-full'
                }
            }
        }
        
        stage('Coverage Gates') {
            steps {
                script {
                    sh 'make coverage-gates-all'
                }
            }
        }
        
        stage('Quality Checks') {
            steps {
                script {
                    sh 'make coverage-quality-check'
                }
            }
        }
        
        stage('Generate Reports') {
            steps {
                script {
                    sh 'make coverage-reports'
                }
            }
            post {
                always {
                    publishHTML([
                        allowMissing: false,
                        alwaysLinkToLastBuild: true,
                        keepAll: true,
                        reportDir: 'coverage-data/reports',
                        reportFiles: 'index.html',
                        reportName: 'Coverage Report'
                    ])
                }
            }
        }
    }
    
    post {
        always {
            archiveArtifacts artifacts: 'coverage-data/**/*', fingerprint: true
        }
    }
}
EOF
    
    print_color "$GREEN" "✓ CI/CD integration files generated:"
    print_color "$CYAN" "  GitHub Actions: $github_dir/coverage-automation.yml"
    print_color "$CYAN" "  Docker: $INTEGRATION_DIR/docker-coverage.mk"
    print_color "$CYAN" "  Jenkins: $INTEGRATION_DIR/Jenkinsfile.coverage"
}

# Create and update coverage badges
create_coverage_badges() {
    print_color "$BLUE" "🏷️ Creating coverage badges..."
    
    # Ensure coverage data
    if [ ! -f "coverage.out" ]; then
        print_color "$YELLOW" "⚠️  No coverage data found, generating..."
        go test -timeout 30m -coverprofile=coverage.out -covermode=atomic ./...
    fi
    
    local current_coverage=$(go tool cover -func=coverage.out | grep "total:" | awk '{print $3}' | sed 's/%//')
    local badges_dir="$COVERAGE_DIR/badges"
    mkdir -p "$badges_dir"
    
    # Determine badge color
    local color
    if (( $(echo "$current_coverage >= 90" | bc -l) )); then
        color="brightgreen"
    elif (( $(echo "$current_coverage >= 80" | bc -l) )); then
        color="green"
    elif (( $(echo "$current_coverage >= 70" | bc -l) )); then
        color="yellow"
    elif (( $(echo "$current_coverage >= 60" | bc -l) )); then
        color="orange"
    else
        color="red"
    fi
    
    # Generate SVG badge
    cat > "$badges_dir/coverage.svg" << EOF
<svg xmlns="http://www.w3.org/2000/svg" width="104" height="20">
    <linearGradient id="b" x2="0" y2="100%">
        <stop offset="0" stop-color="#bbb" stop-opacity=".1"/>
        <stop offset="1" stop-opacity=".1"/>
    </linearGradient>
    <mask id="a">
        <rect width="104" height="20" rx="3" fill="#fff"/>
    </mask>
    <g mask="url(#a)">
        <path fill="#555" d="M0 0h63v20H0z"/>
        <path fill="$(case $color in brightgreen) echo "#4c1";; green) echo "#97CA00";; yellow) echo "#dfb317";; orange) echo "#fe7d37";; red) echo "#e05d44";; esac)" d="M63 0h41v20H63z"/>
        <path fill="url(#b)" d="M0 0h104v20H0z"/>
    </g>
    <g fill="#fff" text-anchor="middle" font-family="DejaVu Sans,Verdana,Geneva,sans-serif" font-size="11">
        <text x="31.5" y="15" fill="#010101" fill-opacity=".3">coverage</text>
        <text x="31.5" y="14">coverage</text>
        <text x="83.5" y="15" fill="#010101" fill-opacity=".3">${current_coverage}%</text>
        <text x="83.5" y="14">${current_coverage}%</text>
    </g>
</svg>
EOF
    
    # Generate README badge markdown
    cat > "$badges_dir/badge.md" << EOF
[![Coverage](./coverage-data/badges/coverage.svg)](./coverage-data/reports/index.html)
EOF
    
    # Generate shields.io URL
    echo "https://img.shields.io/badge/coverage-${current_coverage}%25-${color}" > "$badges_dir/shields-url.txt"
    
    print_color "$GREEN" "✓ Coverage badges created:"
    print_color "$CYAN" "  SVG Badge: $badges_dir/coverage.svg"
    print_color "$CYAN" "  Markdown: $badges_dir/badge.md"
    print_color "$CYAN" "  Shields URL: $badges_dir/shields-url.txt"
}

# Watch for file changes and trigger automation
watch_automation() {
    print_color "$BLUE" "👀 Starting automation watcher..."
    
    if ! command -v fswatch >/dev/null 2>&1; then
        print_color "$YELLOW" "⚠️  fswatch not installed, using simple polling"
        
        while true; do
            sleep 60
            if [ -f "coverage.out" ]; then
                local mtime=$(stat -c %Y "coverage.out" 2>/dev/null || stat -f %m "coverage.out" 2>/dev/null)
                local now=$(date +%s)
                local age=$((now - mtime))
                
                if [ $age -lt 120 ]; then  # Updated in last 2 minutes
                    print_color "$GREEN" "🔄 Coverage updated, running automation..."
                    make coverage-dashboard
                    make coverage-badges
                fi
            fi
        done
    else
        fswatch -o . --exclude=coverage-data/ --include='\.go$' | while read -r event; do
            print_color "$GREEN" "🔄 Go files changed, updating coverage..."
            sleep 5  # Brief delay to allow compilation
            make coverage-collect
            make coverage-dashboard
            make coverage-badges
        done
    fi
}

# Main execution
main() {
    local integration_type=""
    local pr_number=""
    local base_branch="main"
    local target_branch="HEAD"
    local diff_format="markdown"
    local include_files=true
    local generate_hooks=false
    local update_makefile=false
    local create_badges=false
    local pr_comment=false
    local ci_integration=false
    local watch=false
    local dry_run=false
    local threshold_check=false
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --integrate)
                integration_type="$2"
                shift 2
                ;;
            --pr-number)
                pr_number="$2"
                shift 2
                ;;
            --base-branch)
                base_branch="$2"
                shift 2
                ;;
            --target-branch)
                target_branch="$2"
                shift 2
                ;;
            --diff-format)
                diff_format="$2"
                shift 2
                ;;
            --include-files)
                include_files=true
                shift
                ;;
            --generate-hooks)
                generate_hooks=true
                shift
                ;;
            --update-makefile)
                update_makefile=true
                shift
                ;;
            --create-badges)
                create_badges=true
                shift
                ;;
            --pr-comment)
                pr_comment=true
                shift
                ;;
            --ci-integration)
                ci_integration=true
                shift
                ;;
            --threshold-check)
                threshold_check=true
                shift
                ;;
            --watch)
                watch=true
                shift
                ;;
            --dry-run)
                dry_run=true
                shift
                ;;
            --help)
                usage
                ;;
            *)
                print_color "$RED" "Unknown option: $1"
                usage
                ;;
        esac
    done
    
    print_color "$BLUE" "🤖 Coverage Automation - Agent C"
    print_color "$BLUE" "================================"
    echo
    
    setup_directories
    
    # Handle integration types
    case $integration_type in
        makefile|all)
            integrate_makefile
            ;;
    esac
    
    case $integration_type in
        hooks|all)
            setup_git_hooks
            ;;
    esac
    
    case $integration_type in
        ci|all)
            generate_ci_integration
            ;;
    esac
    
    case $integration_type in
        badges|all)
            create_coverage_badges
            ;;
    esac
    
    # Handle individual flags
    if [ "$update_makefile" = true ]; then
        integrate_makefile
    fi
    
    if [ "$generate_hooks" = true ]; then
        setup_git_hooks
    fi
    
    if [ "$ci_integration" = true ]; then
        generate_ci_integration
    fi
    
    if [ "$create_badges" = true ]; then
        create_coverage_badges
    fi
    
    # Handle PR analysis
    if [ -n "$pr_number" ]; then
        local diff_file=$(generate_pr_diff "$pr_number" "$base_branch" "$target_branch" "$diff_format" "$include_files")
        
        if [ "$pr_comment" = true ]; then
            generate_pr_comment "$pr_number" "$diff_file"
        fi
        
        if [ "$threshold_check" = true ]; then
            print_color "$BLUE" "🎯 Running threshold checks for PR..."
            ./scripts/coverage_quality.sh --check
        fi
    fi
    
    # Start watching if requested
    if [ "$watch" = true ]; then
        watch_automation
    fi
    
    print_color "$GREEN" "✅ Coverage automation complete"
    
    # Show integration summary
    echo
    print_color "$BLUE" "📋 Integration Summary:"
    if [ -f "Makefile" ] && grep -q "Agent C Coverage Automation" Makefile; then
        print_color "$GREEN" "  ✓ Makefile integration active"
    fi
    if [ -f ".git/hooks/pre-commit" ] && grep -q "Agent C" .git/hooks/pre-commit; then
        print_color "$GREEN" "  ✓ Git hooks installed"
    fi
    if [ -f ".github/workflows/coverage-automation.yml" ]; then
        print_color "$GREEN" "  ✓ GitHub Actions workflow available"
    fi
    if [ -f "$COVERAGE_DIR/badges/coverage.svg" ]; then
        print_color "$GREEN" "  ✓ Coverage badges generated"
    fi
}

# Run main function with all arguments
main "$@"