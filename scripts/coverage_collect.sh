#!/bin/bash

# Enhanced Coverage Collection Infrastructure
# Comprehensive coverage tracking with package-level analysis and historical trending

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
COVERAGE_DIR="coverage-data"
HISTORICAL_DIR="$COVERAGE_DIR/historical"
PACKAGE_ANALYSIS_DIR="$COVERAGE_DIR/packages"
TRENDS_DIR="$COVERAGE_DIR/trends"
COVERAGE_FILE="coverage.out"
PACKAGE_COVERAGE_FILE="$COVERAGE_DIR/package-coverage.json"
HISTORICAL_DATA_FILE="$HISTORICAL_DIR/coverage-history.json"
CONFIG_FILE="coverage-config.yaml"

# Default thresholds
DEFAULT_OVERALL_THRESHOLD=85
DEFAULT_PACKAGE_THRESHOLD=75
DEFAULT_REGRESSION_THRESHOLD=5

# Timestamp for this run
TIMESTAMP=$(date '+%Y-%m-%d_%H-%M-%S')
ISO_TIMESTAMP=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to show usage
usage() {
    cat << EOF
Usage: $(basename "$0") [OPTIONS]

Enhanced coverage collection with package-level tracking and historical analysis.

OPTIONS:
    -o, --output DIR        Output directory (default: $COVERAGE_DIR)
    -c, --config FILE       Configuration file (default: $CONFIG_FILE)
    -p, --packages          Run package-level analysis
    -t, --trends           Generate trend analysis
    -j, --json             Output JSON format
    -h, --html             Generate HTML reports
    --regression-check     Check for coverage regressions
    --baseline             Set current coverage as baseline
    --compare-branch BRANCH Compare against specific branch
    --help                 Show this help message

EXAMPLES:
    $(basename "$0")                    # Basic coverage collection
    $(basename "$0") --packages --trends # Full analysis with trends
    $(basename "$0") --baseline         # Set baseline for regression tracking
    $(basename "$0") --regression-check # Check for regressions

EOF
    exit 0
}

# Initialize configuration
init_config() {
    if [ ! -f "$CONFIG_FILE" ]; then
        cat > "$CONFIG_FILE" << EOF
# Coverage Collection Configuration

# Overall thresholds
overall_threshold: $DEFAULT_OVERALL_THRESHOLD
package_threshold: $DEFAULT_PACKAGE_THRESHOLD
regression_threshold: $DEFAULT_REGRESSION_THRESHOLD

# Package-specific thresholds
package_thresholds:
  internal/auth: 90
  internal/user: 88
  internal/organization: 85
  internal/event: 85
  internal/handlers: 80
  internal/middleware: 85

# Exclusions
exclude_packages:
  - internal/mocks
  - internal/testing
  - cmd/

# Trend analysis settings
trend_analysis:
  window_days: 30
  min_data_points: 5

# Reporting settings
reports:
  generate_html: true
  generate_json: true
  include_trends: true
  package_details: true

EOF
        print_color "$GREEN" "✓ Created default configuration: $CONFIG_FILE"
    fi
}

# Load configuration
load_config() {
    if [ -f "$CONFIG_FILE" ]; then
        # Simple YAML parser for our needs
        OVERALL_THRESHOLD=$(grep "overall_threshold:" "$CONFIG_FILE" | awk '{print $2}' || echo "$DEFAULT_OVERALL_THRESHOLD")
        PACKAGE_THRESHOLD=$(grep "package_threshold:" "$CONFIG_FILE" | awk '{print $2}' || echo "$DEFAULT_PACKAGE_THRESHOLD")
        REGRESSION_THRESHOLD=$(grep "regression_threshold:" "$CONFIG_FILE" | awk '{print $2}' || echo "$DEFAULT_REGRESSION_THRESHOLD")
    else
        OVERALL_THRESHOLD=$DEFAULT_OVERALL_THRESHOLD
        PACKAGE_THRESHOLD=$DEFAULT_PACKAGE_THRESHOLD
        REGRESSION_THRESHOLD=$DEFAULT_REGRESSION_THRESHOLD
    fi
}

# Setup directory structure
setup_directories() {
    mkdir -p "$COVERAGE_DIR"
    mkdir -p "$HISTORICAL_DIR"
    mkdir -p "$PACKAGE_ANALYSIS_DIR"
    mkdir -p "$TRENDS_DIR"
    mkdir -p "$COVERAGE_DIR/reports"
    mkdir -p "$COVERAGE_DIR/badges"
}

# Collect basic coverage data
collect_coverage() {
    print_color "$BLUE" "📊 Collecting coverage data..."
    
    # Run tests with coverage
    go test -timeout 30m -coverprofile="$COVERAGE_FILE" -covermode=atomic ./... || {
        print_color "$RED" "❌ Tests failed - cannot collect coverage"
        exit 1
    }
    
    if [ ! -f "$COVERAGE_FILE" ]; then
        print_color "$RED" "❌ Coverage file not generated"
        exit 1
    fi
    
    # Copy to historical directory with timestamp
    cp "$COVERAGE_FILE" "$HISTORICAL_DIR/coverage-${TIMESTAMP}.out"
    
    print_color "$GREEN" "✓ Coverage data collected"
}

# Analyze package-level coverage
analyze_packages() {
    print_color "$BLUE" "📦 Analyzing package-level coverage..."
    
    local package_data="$PACKAGE_ANALYSIS_DIR/packages-${TIMESTAMP}.json"
    
    # Extract package coverage with Go
    cat > /tmp/package_analyzer.go << 'EOF'
package main

import (
    "bufio"
    "encoding/json"
    "fmt"
    "os"
    "strconv"
    "strings"
    "time"
)

type PackageCoverage struct {
    Package    string  `json:"package"`
    Coverage   float64 `json:"coverage"`
    Functions  int     `json:"functions"`
    Covered    int     `json:"covered"`
    Uncovered  int     `json:"uncovered"`
}

type CoverageReport struct {
    Timestamp string            `json:"timestamp"`
    Overall   float64           `json:"overall"`
    Packages  []PackageCoverage `json:"packages"`
}

func main() {
    file, err := os.Open(os.Args[1])
    if err != nil {
        fmt.Fprintf(os.Stderr, "Error opening file: %v\n", err)
        os.Exit(1)
    }
    defer file.Close()

    packages := make(map[string]*PackageCoverage)
    scanner := bufio.NewScanner(file)
    
    for scanner.Scan() {
        line := scanner.Text()
        if !strings.HasPrefix(line, "mode:") {
            parts := strings.Fields(line)
            if len(parts) >= 3 {
                // Extract package from file path
                filePath := parts[0]
                pkgPath := strings.Split(filePath, ":")[0]
                
                // Get package name
                pathParts := strings.Split(pkgPath, "/")
                var pkg string
                for i, part := range pathParts {
                    if part == "internal" && i < len(pathParts)-1 {
                        pkg = "internal/" + pathParts[i+1]
                        break
                    } else if strings.HasPrefix(part, "cmd") {
                        pkg = part
                        break
                    }
                }
                
                if pkg == "" {
                    continue
                }
                
                if packages[pkg] == nil {
                    packages[pkg] = &PackageCoverage{
                        Package: pkg,
                    }
                }
                
                packages[pkg].Functions++
                
                // Parse coverage data
                count, _ := strconv.Atoi(parts[2])
                if count > 0 {
                    packages[pkg].Covered++
                } else {
                    packages[pkg].Uncovered++
                }
            }
        }
    }
    
    // Calculate coverage percentages
    var totalFunctions, totalCovered int
    var packageList []PackageCoverage
    
    for _, pkg := range packages {
        if pkg.Functions > 0 {
            pkg.Coverage = float64(pkg.Covered) / float64(pkg.Functions) * 100
            packageList = append(packageList, *pkg)
            totalFunctions += pkg.Functions
            totalCovered += pkg.Covered
        }
    }
    
    overall := float64(totalCovered) / float64(totalFunctions) * 100
    
    report := CoverageReport{
        Timestamp: time.Now().UTC().Format(time.RFC3339),
        Overall:   overall,
        Packages:  packageList,
    }
    
    json, _ := json.MarshalIndent(report, "", "  ")
    fmt.Println(string(json))
}
EOF

    # Run package analyzer
    go run /tmp/package_analyzer.go "$COVERAGE_FILE" > "$package_data"
    rm /tmp/package_analyzer.go
    
    # Update current package coverage
    cp "$package_data" "$PACKAGE_COVERAGE_FILE"
    
    print_color "$GREEN" "✓ Package analysis complete: $package_data"
}

# Update historical data
update_historical_data() {
    print_color "$BLUE" "📈 Updating historical data..."
    
    local current_overall=$(go tool cover -func="$COVERAGE_FILE" | grep "total:" | awk '{print $3}' | sed 's/%//')
    
    # Initialize historical data if it doesn't exist
    if [ ! -f "$HISTORICAL_DATA_FILE" ]; then
        echo '{"history": []}' > "$HISTORICAL_DATA_FILE"
    fi
    
    # Add current data point
    local temp_file=$(mktemp)
    jq --arg timestamp "$ISO_TIMESTAMP" \
       --arg coverage "$current_overall" \
       --arg commit "$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')" \
       --arg branch "$(git branch --show-current 2>/dev/null || echo 'unknown')" \
       '.history += [{
           timestamp: $timestamp,
           coverage: ($coverage | tonumber),
           commit: $commit,
           branch: $branch
       }]' "$HISTORICAL_DATA_FILE" > "$temp_file"
    
    mv "$temp_file" "$HISTORICAL_DATA_FILE"
    
    print_color "$GREEN" "✓ Historical data updated"
}

# Generate trend analysis
generate_trends() {
    print_color "$BLUE" "📊 Generating trend analysis..."
    
    if [ ! -f "$HISTORICAL_DATA_FILE" ]; then
        print_color "$YELLOW" "⚠️  No historical data available for trend analysis"
        return
    fi
    
    local trend_file="$TRENDS_DIR/trends-${TIMESTAMP}.json"
    
    # Simple trend analysis
    jq -r '
        .history[-30:] | 
        if length < 2 then
            {
                "status": "insufficient_data",
                "message": "Need at least 2 data points for trend analysis",
                "data_points": length
            }
        else
            {
                "status": "success",
                "data_points": length,
                "latest_coverage": .[-1].coverage,
                "previous_coverage": .[-2].coverage,
                "change": (.[-1].coverage - .[-2].coverage),
                "trend": (
                    if (.[-1].coverage - .[-2].coverage) > 1 then "improving"
                    elif (.[-1].coverage - .[-2].coverage) < -1 then "declining"
                    else "stable"
                    end
                ),
                "average_last_10": ([.[-10:][].coverage] | add / length),
                "max_coverage": ([.[].coverage] | max),
                "min_coverage": ([.[].coverage] | min)
            }
        end
    ' "$HISTORICAL_DATA_FILE" > "$trend_file"
    
    print_color "$GREEN" "✓ Trend analysis complete: $trend_file"
}

# Check for regressions
check_regressions() {
    print_color "$BLUE" "🔍 Checking for coverage regressions..."
    
    if [ ! -f "$HISTORICAL_DATA_FILE" ]; then
        print_color "$YELLOW" "⚠️  No historical data available for regression checking"
        return 0
    fi
    
    local current_coverage=$(go tool cover -func="$COVERAGE_FILE" | grep "total:" | awk '{print $3}' | sed 's/%//')
    local baseline_coverage=$(jq -r '.history[-2].coverage // 0' "$HISTORICAL_DATA_FILE")
    
    if [ "$baseline_coverage" = "0" ] || [ "$baseline_coverage" = "null" ]; then
        print_color "$YELLOW" "⚠️  No baseline coverage available"
        return 0
    fi
    
    local regression=$(echo "$baseline_coverage - $current_coverage" | bc -l)
    
    if (( $(echo "$regression > $REGRESSION_THRESHOLD" | bc -l) )); then
        print_color "$RED" "❌ Coverage regression detected!"
        print_color "$RED" "   Baseline: ${baseline_coverage}%"
        print_color "$RED" "   Current:  ${current_coverage}%"
        print_color "$RED" "   Drop:     ${regression}%"
        
        # Generate regression report
        local regression_report="$COVERAGE_DIR/regression-${TIMESTAMP}.json"
        jq -n --arg timestamp "$ISO_TIMESTAMP" \
              --arg baseline "$baseline_coverage" \
              --arg current "$current_coverage" \
              --arg regression "$regression" \
              --arg threshold "$REGRESSION_THRESHOLD" \
              '{
                  timestamp: $timestamp,
                  baseline_coverage: ($baseline | tonumber),
                  current_coverage: ($current | tonumber),
                  regression: ($regression | tonumber),
                  threshold: ($threshold | tonumber),
                  status: "regression_detected"
              }' > "$regression_report"
        
        return 1
    else
        print_color "$GREEN" "✅ No significant coverage regression"
        return 0
    fi
}

# Generate enhanced HTML report
generate_html_report() {
    print_color "$BLUE" "🌐 Generating enhanced HTML report..."
    
    local html_dir="$COVERAGE_DIR/reports"
    local index_file="$html_dir/index.html"
    
    # Get current data
    local current_coverage=$(go tool cover -func="$COVERAGE_FILE" | grep "total:" | awk '{print $3}' | sed 's/%//')
    
    # Generate main coverage HTML
    go tool cover -html="$COVERAGE_FILE" -o "$html_dir/coverage-detail.html"
    
    # Create enhanced index page
    cat > "$index_file" << EOF
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coverage Dashboard - The Moment Backend</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .dashboard {
            padding: 40px;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin-bottom: 40px;
        }
        
        .metric-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 24px;
            text-align: center;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
        
        .metric-value {
            font-size: 3rem;
            font-weight: 800;
            margin: 12px 0;
            line-height: 1;
        }
        
        .metric-label {
            color: #64748b;
            font-size: 0.9rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .metric-trend {
            font-size: 0.85rem;
            margin-top: 8px;
            padding: 4px 8px;
            border-radius: 20px;
            display: inline-block;
        }
        
        .passing { color: #059669; }
        .failing { color: #dc2626; }
        .warning { color: #d97706; }
        
        .trend-up { background: #dcfce7; color: #166534; }
        .trend-down { background: #fecaca; color: #991b1b; }
        .trend-stable { background: #e0e7ff; color: #3730a3; }
        
        .chart-section {
            margin: 40px 0;
        }
        
        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #1e293b;
        }
        
        .chart-container {
            background: #f8fafc;
            border-radius: 12px;
            padding: 20px;
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #e2e8f0;
        }
        
        .package-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 16px;
            margin-top: 20px;
        }
        
        .package-card {
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            font-size: 0.9rem;
        }
        
        .package-name {
            font-weight: 600;
            margin-bottom: 8px;
            color: #1e293b;
        }
        
        .package-coverage {
            font-size: 1.2rem;
            font-weight: 700;
        }
        
        .actions {
            display: flex;
            gap: 16px;
            margin-top: 40px;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: transform 0.2s, box-shadow 0.2s;
            display: inline-block;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
        }
        
        .timestamp {
            text-align: center;
            color: #64748b;
            font-size: 0.9rem;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
        }
        
        @media (max-width: 768px) {
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            .actions {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Coverage Dashboard</h1>
            <p>The Moment Backend - Comprehensive Test Coverage Analysis</p>
        </div>
        
        <div class="dashboard">
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-label">Overall Coverage</div>
                    <div class="metric-value passing">${current_coverage}%</div>
                    <div class="metric-trend trend-stable">Stable</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-label">Target Threshold</div>
                    <div class="metric-value">${OVERALL_THRESHOLD}%</div>
                    <div class="metric-trend trend-stable">Required</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-label">Status</div>
                    <div class="metric-value passing">✅ PASS</div>
                    <div class="metric-trend trend-up">Meets Target</div>
                </div>
                
                <div class="metric-card">
                    <div class="metric-label">Last Updated</div>
                    <div class="metric-value" style="font-size: 1.5rem;">${TIMESTAMP}</div>
                    <div class="metric-trend trend-stable">Latest Run</div>
                </div>
            </div>
            
            <div class="chart-section">
                <h2 class="section-title">📈 Coverage Trends</h2>
                <div class="chart-container">
                    <p style="color: #64748b;">Trend visualization will be implemented with historical data</p>
                </div>
            </div>
            
            <div class="chart-section">
                <h2 class="section-title">📦 Package Coverage</h2>
                <div class="package-grid" id="package-grid">
                    <!-- Package cards will be populated by JavaScript -->
                </div>
            </div>
            
            <div class="actions">
                <a href="coverage-detail.html" class="btn">View Detailed Coverage</a>
                <a href="../package-coverage.json" class="btn btn-secondary">Download JSON Data</a>
                <a href="../badges/coverage-badge.svg" class="btn btn-secondary">Coverage Badge</a>
            </div>
            
            <div class="timestamp">
                Report generated on ${ISO_TIMESTAMP}
            </div>
        </div>
    </div>
    
    <script>
        // Load and display package data
        fetch('../package-coverage.json')
            .then(response => response.json())
            .then(data => {
                const grid = document.getElementById('package-grid');
                data.packages.forEach(pkg => {
                    const card = document.createElement('div');
                    card.className = 'package-card';
                    
                    const statusClass = pkg.coverage >= ${PACKAGE_THRESHOLD} ? 'passing' : 'failing';
                    
                    card.innerHTML = \`
                        <div class="package-name">\${pkg.package}</div>
                        <div class="package-coverage \${statusClass}">\${pkg.coverage.toFixed(1)}%</div>
                        <div style="font-size: 0.8rem; color: #64748b; margin-top: 4px;">
                            \${pkg.covered}/\${pkg.functions} functions
                        </div>
                    \`;
                    
                    grid.appendChild(card);
                });
            })
            .catch(error => {
                console.error('Error loading package data:', error);
                document.getElementById('package-grid').innerHTML = 
                    '<p style="color: #dc2626;">Error loading package coverage data</p>';
            });
    </script>
</body>
</html>
EOF
    
    print_color "$GREEN" "✓ Enhanced HTML report generated: $index_file"
}

# Generate coverage badge
generate_badge() {
    print_color "$BLUE" "🏷️  Generating coverage badge..."
    
    local badge_dir="$COVERAGE_DIR/badges"
    local current_coverage=$(go tool cover -func="$COVERAGE_FILE" | grep "total:" | awk '{print $3}' | sed 's/%//')
    
    # Determine badge color
    local color
    if (( $(echo "$current_coverage >= 90" | bc -l) )); then
        color="#4c1"
    elif (( $(echo "$current_coverage >= 80" | bc -l) )); then
        color="#97CA00"
    elif (( $(echo "$current_coverage >= 70" | bc -l) )); then
        color="#dfb317"
    elif (( $(echo "$current_coverage >= 60" | bc -l) )); then
        color="#fe7d37"
    else
        color="#e05d44"
    fi
    
    cat > "$badge_dir/coverage-badge.svg" << EOF
<svg xmlns="http://www.w3.org/2000/svg" width="104" height="20">
    <linearGradient id="b" x2="0" y2="100%">
        <stop offset="0" stop-color="#bbb" stop-opacity=".1"/>
        <stop offset="1" stop-opacity=".1"/>
    </linearGradient>
    <mask id="a">
        <rect width="104" height="20" rx="3" fill="#fff"/>
    </mask>
    <g mask="url(#a)">
        <path fill="#555" d="M0 0h63v20H0z"/>
        <path fill="$color" d="M63 0h41v20H63z"/>
        <path fill="url(#b)" d="M0 0h104v20H0z"/>
    </g>
    <g fill="#fff" text-anchor="middle" font-family="DejaVu Sans,Verdana,Geneva,sans-serif" font-size="11">
        <text x="31.5" y="15" fill="#010101" fill-opacity=".3">coverage</text>
        <text x="31.5" y="14">coverage</text>
        <text x="83.5" y="15" fill="#010101" fill-opacity=".3">${current_coverage}%</text>
        <text x="83.5" y="14">${current_coverage}%</text>
    </g>
</svg>
EOF
    
    print_color "$GREEN" "✓ Coverage badge generated: $badge_dir/coverage-badge.svg"
}

# Main execution flow
main() {
    local run_packages=false
    local run_trends=false
    local run_json=false
    local run_html=false
    local run_regression_check=false
    local set_baseline=false
    local compare_branch=""
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -o|--output)
                COVERAGE_DIR="$2"
                shift 2
                ;;
            -c|--config)
                CONFIG_FILE="$2"
                shift 2
                ;;
            -p|--packages)
                run_packages=true
                shift
                ;;
            -t|--trends)
                run_trends=true
                shift
                ;;
            -j|--json)
                run_json=true
                shift
                ;;
            -h|--html)
                run_html=true
                shift
                ;;
            --regression-check)
                run_regression_check=true
                shift
                ;;
            --baseline)
                set_baseline=true
                shift
                ;;
            --compare-branch)
                compare_branch="$2"
                shift 2
                ;;
            --help)
                usage
                ;;
            *)
                print_color "$RED" "Unknown option: $1"
                usage
                ;;
        esac
    done
    
    print_color "$BLUE" "🚀 Enhanced Coverage Collection Agent C"
    print_color "$BLUE" "========================================"
    echo
    
    # Initialize
    init_config
    load_config
    setup_directories
    
    # Collect basic coverage
    collect_coverage
    
    # Update historical data
    update_historical_data
    
    # Run optional analyses
    if [ "$run_packages" = true ] || [ "$run_json" = true ]; then
        analyze_packages
    fi
    
    if [ "$run_trends" = true ]; then
        generate_trends
    fi
    
    if [ "$run_regression_check" = true ]; then
        if ! check_regressions; then
            exit 1
        fi
    fi
    
    if [ "$run_html" = true ]; then
        generate_html_report
        generate_badge
    fi
    
    # Generate summary
    local current_coverage=$(go tool cover -func="$COVERAGE_FILE" | grep "total:" | awk '{print $3}' | sed 's/%//')
    
    echo
    print_color "$BLUE" "════════════════════════════════════════"
    print_color "$BLUE" "         COLLECTION SUMMARY            "
    print_color "$BLUE" "════════════════════════════════════════"
    print_color "$CYAN" "  Coverage: ${current_coverage}%"
    print_color "$CYAN" "  Target: ${OVERALL_THRESHOLD}%"
    
    if (( $(echo "$current_coverage >= $OVERALL_THRESHOLD" | bc -l) )); then
        print_color "$GREEN" "  Status: ✅ PASSING"
    else
        print_color "$RED" "  Status: ❌ FAILING"
    fi
    
    print_color "$CYAN" "  Data Directory: $COVERAGE_DIR"
    print_color "$BLUE" "════════════════════════════════════════"
    
    # Exit with appropriate code
    if (( $(echo "$current_coverage >= $OVERALL_THRESHOLD" | bc -l) )); then
        exit 0
    else
        exit 1
    fi
}

# Run main function with all arguments
main "$@"