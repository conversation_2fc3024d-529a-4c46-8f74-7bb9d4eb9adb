#!/bin/bash

# CI/CD Coverage Gates with Threshold Enforcement and Regression Detection
# Implements tollbooth pattern for preventing coverage regression

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
COVERAGE_DIR="coverage-data"
HISTORICAL_DIR="$COVERAGE_DIR/historical"
GATES_DIR="$COVERAGE_DIR/gates"
REPORTS_DIR="$COVERAGE_DIR/reports"
COVERAGE_FILE="coverage.out"
CONFIG_FILE="coverage-config.yaml"

# Default gate configurations
DEFAULT_OVERALL_THRESHOLD=85
DEFAULT_PACKAGE_THRESHOLD=75
DEFAULT_REGRESSION_THRESHOLD=5
DEFAULT_STRICT_MODE=false

# Gate types
GATE_OVERALL="overall"
GATE_PACKAGE="package"
GATE_REGRESSION="regression"
GATE_TREND="trend"

# Exit codes
EXIT_SUCCESS=0
EXIT_THRESHOLD_FAIL=1
EXIT_REGRESSION_FAIL=2
EXIT_PACKAGE_FAIL=3
EXIT_TREND_FAIL=4
EXIT_CONFIG_ERROR=5

# Timestamp for this run
TIMESTAMP=$(date '+%Y-%m-%d_%H-%M-%S')
ISO_TIMESTAMP=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to show usage
usage() {
    cat << EOF
Usage: $(basename "$0") [OPTIONS]

CI/CD Coverage Gates with comprehensive threshold enforcement and regression detection.

OPTIONS:
    --gate TYPE             Run specific gate (overall|package|regression|trend|all)
    --threshold PERCENT     Override overall threshold (default: $DEFAULT_OVERALL_THRESHOLD)
    --strict                Enable strict mode (all gates must pass)
    --config FILE           Configuration file (default: $CONFIG_FILE)
    --baseline COMMIT       Set baseline for regression comparison
    --branch BRANCH         Compare against specific branch
    --output-format FORMAT  Output format (text|json|junit|github)
    --fail-fast            Exit on first gate failure
    --dry-run              Show what would be checked without failing
    --generate-report      Generate detailed gate report
    --help                 Show this help message

GATE TYPES:
    overall     - Check overall coverage threshold
    package     - Check package-level thresholds
    regression  - Check for coverage regressions
    trend       - Check coverage trends
    all         - Run all gates (default)

OUTPUT FORMATS:
    text        - Human-readable text output (default)
    json        - JSON format for CI/CD integration
    junit       - JUnit XML format
    github      - GitHub Actions format

EXAMPLES:
    $(basename "$0")                           # Run all gates with defaults
    $(basename "$0") --gate overall --threshold 90   # Check overall with custom threshold
    $(basename "$0") --strict --fail-fast     # Strict mode, exit on first failure
    $(basename "$0") --output-format json     # JSON output for CI/CD

EOF
    exit 0
}

# Load configuration
load_config() {
    if [ -f "$CONFIG_FILE" ]; then
        OVERALL_THRESHOLD=$(grep "overall_threshold:" "$CONFIG_FILE" | awk '{print $2}' 2>/dev/null || echo "$DEFAULT_OVERALL_THRESHOLD")
        PACKAGE_THRESHOLD=$(grep "package_threshold:" "$CONFIG_FILE" | awk '{print $2}' 2>/dev/null || echo "$DEFAULT_PACKAGE_THRESHOLD")
        REGRESSION_THRESHOLD=$(grep "regression_threshold:" "$CONFIG_FILE" | awk '{print $2}' 2>/dev/null || echo "$DEFAULT_REGRESSION_THRESHOLD")
    else
        OVERALL_THRESHOLD=$DEFAULT_OVERALL_THRESHOLD
        PACKAGE_THRESHOLD=$DEFAULT_PACKAGE_THRESHOLD
        REGRESSION_THRESHOLD=$DEFAULT_REGRESSION_THRESHOLD
    fi
}

# Setup directories
setup_directories() {
    mkdir -p "$COVERAGE_DIR"
    mkdir -p "$HISTORICAL_DIR"
    mkdir -p "$GATES_DIR"
    mkdir -p "$REPORTS_DIR"
}

# Generate coverage if not exists
ensure_coverage() {
    if [ ! -f "$COVERAGE_FILE" ]; then
        print_color "$YELLOW" "⚠️  Coverage file not found, generating..."
        go test -timeout 30m -coverprofile="$COVERAGE_FILE" -covermode=atomic ./... || {
            print_color "$RED" "❌ Failed to generate coverage data"
            exit $EXIT_CONFIG_ERROR
        }
    fi
}

# Get current coverage percentage
get_current_coverage() {
    go tool cover -func="$COVERAGE_FILE" | grep "total:" | awk '{print $3}' | sed 's/%//'
}

# Overall coverage gate
gate_overall() {
    local threshold=${1:-$OVERALL_THRESHOLD}
    local current_coverage=$(get_current_coverage)
    
    print_color "$BLUE" "🚪 Overall Coverage Gate"
    print_color "$BLUE" "========================"
    
    print_color "$CYAN" "  Current Coverage: ${current_coverage}%"
    print_color "$CYAN" "  Required Threshold: ${threshold}%"
    
    if (( $(echo "$current_coverage >= $threshold" | bc -l) )); then
        local margin=$(echo "$current_coverage - $threshold" | bc)
        print_color "$GREEN" "  ✅ PASSED (margin: +${margin}%)"
        
        # Save gate result
        local gate_result="{
            \"gate\": \"overall\",
            \"status\": \"passed\",
            \"current_coverage\": $current_coverage,
            \"threshold\": $threshold,
            \"margin\": $margin,
            \"timestamp\": \"$ISO_TIMESTAMP\"
        }"
        echo "$gate_result" > "$GATES_DIR/overall-${TIMESTAMP}.json"
        
        return $EXIT_SUCCESS
    else
        local gap=$(echo "$threshold - $current_coverage" | bc)
        print_color "$RED" "  ❌ FAILED (gap: -${gap}%)"
        
        # Show top uncovered areas
        print_color "$YELLOW" "\n🔍 Areas needing attention:"
        go tool cover -func="$COVERAGE_FILE" | grep -v "total:" | sort -k3 -n | head -10 | while read -r line; do
            local coverage=$(echo "$line" | awk '{print $3}' | sed 's/%//')
            if (( $(echo "$coverage < $threshold" | bc -l) )); then
                local file=$(echo "$line" | awk '{print $1}')
                printf "     ${RED}%-60s %6s%%${NC}\\n" "$file" "$coverage"
            fi
        done
        
        # Save gate result
        local gate_result="{
            \"gate\": \"overall\",
            \"status\": \"failed\",
            \"current_coverage\": $current_coverage,
            \"threshold\": $threshold,
            \"gap\": $gap,
            \"timestamp\": \"$ISO_TIMESTAMP\"
        }"
        echo "$gate_result" > "$GATES_DIR/overall-${TIMESTAMP}.json"
        
        return $EXIT_THRESHOLD_FAIL
    fi
}

# Package-level coverage gate
gate_package() {
    local threshold=${1:-$PACKAGE_THRESHOLD}
    
    print_color "$BLUE" "\n📦 Package Coverage Gate"
    print_color "$BLUE" "========================"
    
    local failed_packages=0
    local total_packages=0
    local package_results="["
    
    # Analyze package coverage
    while read -r line; do
        if [[ "$line" =~ ^[[:space:]]*$ ]] || [[ "$line" == *"total:"* ]]; then
            continue
        fi
        
        local file=$(echo "$line" | awk '{print $1}')
        local func=$(echo "$line" | awk '{print $2}')
        local coverage=$(echo "$line" | awk '{print $3}' | sed 's/%//')
        
        # Extract package name
        local package=""
        if [[ "$file" =~ internal/([^/]+) ]]; then
            package="internal/${BASH_REMATCH[1]}"
        elif [[ "$file" =~ cmd/([^/]+) ]]; then
            package="cmd/${BASH_REMATCH[1]}"
        else
            continue
        fi
        
        # Check if we've seen this package
        if ! echo "$package_results" | grep -q "\"package\":\"$package\""; then
            total_packages=$((total_packages + 1))
            
            # Calculate package average coverage
            local package_coverage=$(go tool cover -func="$COVERAGE_FILE" | grep "$package" | grep -v "total:" | awk '{sum+=$3; count++} END {if(count>0) print sum/count; else print 0}' | sed 's/%//')
            
            if [ "$package_coverage" = "" ]; then
                package_coverage="0"
            fi
            
            local status="passed"
            if (( $(echo "$package_coverage < $threshold" | bc -l) )); then
                status="failed"
                failed_packages=$((failed_packages + 1))
                print_color "$RED" "  ❌ $package: ${package_coverage}% (< ${threshold}%)"
            else
                print_color "$GREEN" "  ✅ $package: ${package_coverage}%"
            fi
            
            # Add to results
            if [ "$package_results" != "[" ]; then
                package_results="$package_results,"
            fi
            package_results="$package_results{\"package\":\"$package\",\"coverage\":$package_coverage,\"threshold\":$threshold,\"status\":\"$status\"}"
        fi
    done < <(go tool cover -func="$COVERAGE_FILE" | grep -v "total:")
    
    package_results="$package_results]"
    
    print_color "$CYAN" "\nPackage Summary:"
    print_color "$CYAN" "  Total Packages: $total_packages"
    print_color "$CYAN" "  Failed Packages: $failed_packages"
    print_color "$CYAN" "  Success Rate: $(echo "scale=1; ($total_packages - $failed_packages) * 100 / $total_packages" | bc)%"
    
    # Save gate result
    local gate_result="{
        \"gate\": \"package\",
        \"status\": $([ $failed_packages -eq 0 ] && echo '\"passed\"' || echo '\"failed\"'),
        \"total_packages\": $total_packages,
        \"failed_packages\": $failed_packages,
        \"threshold\": $threshold,
        \"packages\": $package_results,
        \"timestamp\": \"$ISO_TIMESTAMP\"
    }"
    echo "$gate_result" > "$GATES_DIR/package-${TIMESTAMP}.json"
    
    if [ $failed_packages -eq 0 ]; then
        print_color "$GREEN" "  ✅ All packages meet threshold"
        return $EXIT_SUCCESS
    else
        print_color "$RED" "  ❌ $failed_packages packages below threshold"
        return $EXIT_PACKAGE_FAIL
    fi
}

# Regression detection gate
gate_regression() {
    local threshold=${1:-$REGRESSION_THRESHOLD}
    
    print_color "$BLUE" "\n📉 Regression Detection Gate"
    print_color "$BLUE" "============================="
    
    local historical_file="$HISTORICAL_DIR/coverage-history.json"
    
    if [ ! -f "$historical_file" ]; then
        print_color "$YELLOW" "⚠️  No historical data available, skipping regression check"
        return $EXIT_SUCCESS
    fi
    
    local current_coverage=$(get_current_coverage)
    local baseline_coverage=$(jq -r '.history[-2].coverage // null' "$historical_file" 2>/dev/null || echo "null")
    
    if [ "$baseline_coverage" = "null" ] || [ "$baseline_coverage" = "" ]; then
        print_color "$YELLOW" "⚠️  No baseline coverage available, skipping regression check"
        return $EXIT_SUCCESS
    fi
    
    local regression=$(echo "$baseline_coverage - $current_coverage" | bc -l)
    
    print_color "$CYAN" "  Baseline Coverage: ${baseline_coverage}%"
    print_color "$CYAN" "  Current Coverage: ${current_coverage}%"
    print_color "$CYAN" "  Change: $(echo "$current_coverage - $baseline_coverage" | bc)%"
    print_color "$CYAN" "  Regression Threshold: ${threshold}%"
    
    if (( $(echo "$regression > $threshold" | bc -l) )); then
        print_color "$RED" "  ❌ REGRESSION DETECTED (-${regression}%)"
        
        # Analyze which packages regressed
        print_color "$YELLOW" "\n🔍 Regression Analysis:"
        print_color "$YELLOW" "  Significant coverage drop detected"
        print_color "$YELLOW" "  Consider reviewing recent changes"
        
        # Save gate result
        local gate_result="{
            \"gate\": \"regression\",
            \"status\": \"failed\",
            \"baseline_coverage\": $baseline_coverage,
            \"current_coverage\": $current_coverage,
            \"regression\": $regression,
            \"threshold\": $threshold,
            \"timestamp\": \"$ISO_TIMESTAMP\"
        }"
        echo "$gate_result" > "$GATES_DIR/regression-${TIMESTAMP}.json"
        
        return $EXIT_REGRESSION_FAIL
    else
        if (( $(echo "$regression < 0" | bc -l) )); then
            local improvement=$(echo "-1 * $regression" | bc)
            print_color "$GREEN" "  ✅ IMPROVEMENT (+${improvement}%)"
        else
            print_color "$GREEN" "  ✅ NO SIGNIFICANT REGRESSION"
        fi
        
        # Save gate result
        local gate_result="{
            \"gate\": \"regression\",
            \"status\": \"passed\",
            \"baseline_coverage\": $baseline_coverage,
            \"current_coverage\": $current_coverage,
            \"change\": $(echo "$current_coverage - $baseline_coverage" | bc),
            \"threshold\": $threshold,
            \"timestamp\": \"$ISO_TIMESTAMP\"
        }"
        echo "$gate_result" > "$GATES_DIR/regression-${TIMESTAMP}.json"
        
        return $EXIT_SUCCESS
    fi
}

# Trend analysis gate
gate_trend() {
    print_color "$BLUE" "\n📈 Trend Analysis Gate"
    print_color "$BLUE" "======================"
    
    local historical_file="$HISTORICAL_DIR/coverage-history.json"
    
    if [ ! -f "$historical_file" ]; then
        print_color "$YELLOW" "⚠️  No historical data available for trend analysis"
        return $EXIT_SUCCESS
    fi
    
    local trend_data=$(jq -r '
        .history[-10:] | 
        if length < 3 then
            {"status": "insufficient_data", "data_points": length}
        else
            {
                "status": "success",
                "data_points": length,
                "latest": .[-1].coverage,
                "trend": (
                    if (.[-1].coverage - .[-3].coverage) > 2 then "improving"
                    elif (.[-1].coverage - .[-3].coverage) < -2 then "declining"
                    else "stable"
                    end
                ),
                "average": ([.[].coverage] | add / length),
                "volatility": (([.[].coverage] | max) - ([.[].coverage] | min))
            }
        end
    ' "$historical_file" 2>/dev/null || echo '{"status":"error"}')
    
    local status=$(echo "$trend_data" | jq -r '.status')
    
    if [ "$status" = "insufficient_data" ]; then
        print_color "$YELLOW" "⚠️  Insufficient data for trend analysis"
        return $EXIT_SUCCESS
    elif [ "$status" = "error" ]; then
        print_color "$YELLOW" "⚠️  Error analyzing trend data"
        return $EXIT_SUCCESS
    fi
    
    local trend=$(echo "$trend_data" | jq -r '.trend')
    local average=$(echo "$trend_data" | jq -r '.average')
    local volatility=$(echo "$trend_data" | jq -r '.volatility')
    local latest=$(echo "$trend_data" | jq -r '.latest')
    
    print_color "$CYAN" "  Trend Direction: $trend"
    print_color "$CYAN" "  Average Coverage: ${average}%"
    print_color "$CYAN" "  Volatility: ${volatility}%"
    print_color "$CYAN" "  Latest: ${latest}%"
    
    # Determine trend health
    local trend_status="passed"
    if [ "$trend" = "declining" ] && (( $(echo "$volatility > 10" | bc -l) )); then
        print_color "$RED" "  ❌ UNSTABLE DECLINING TREND"
        trend_status="failed"
    elif [ "$trend" = "declining" ]; then
        print_color "$YELLOW" "  ⚠️  DECLINING TREND (stable)"
        trend_status="warning"
    elif [ "$trend" = "improving" ]; then
        print_color "$GREEN" "  ✅ IMPROVING TREND"
    else
        print_color "$GREEN" "  ✅ STABLE TREND"
    fi
    
    # Save gate result
    local gate_result="{
        \"gate\": \"trend\",
        \"status\": \"$trend_status\",
        \"trend\": \"$trend\",
        \"average\": $average,
        \"volatility\": $volatility,
        \"latest\": $latest,
        \"timestamp\": \"$ISO_TIMESTAMP\"
    }"
    echo "$gate_result" > "$GATES_DIR/trend-${TIMESTAMP}.json"
    
    if [ "$trend_status" = "failed" ]; then
        return $EXIT_TREND_FAIL
    else
        return $EXIT_SUCCESS
    fi
}

# Output results in specified format
output_results() {
    local format=$1
    local gates_passed=$2
    local gates_failed=$3
    local exit_code=$4
    
    case $format in
        json)
            # Combine all gate results
            local all_results=$(find "$GATES_DIR" -name "*-${TIMESTAMP}.json" -type f | xargs cat | jq -s '.')
            jq -n --argjson gates "$all_results" \
                  --arg timestamp "$ISO_TIMESTAMP" \
                  --arg status "$([ $exit_code -eq 0 ] && echo 'passed' || echo 'failed')" \
                  --arg passed "$gates_passed" \
                  --arg failed "$gates_failed" \
                  '{
                      timestamp: $timestamp,
                      overall_status: $status,
                      gates_passed: ($passed | tonumber),
                      gates_failed: ($failed | tonumber),
                      gates: $gates
                  }'
            ;;
        junit)
            cat << EOF
<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="Coverage Gates" tests="$((gates_passed + gates_failed))" failures="$gates_failed" errors="0" time="0">
    <testcase classname="CoverageGates" name="Overall" time="0">
        $([ $exit_code -ne $EXIT_THRESHOLD_FAIL ] || echo '<failure message="Overall coverage threshold not met"/>')
    </testcase>
    <testcase classname="CoverageGates" name="Package" time="0">
        $([ $exit_code -ne $EXIT_PACKAGE_FAIL ] || echo '<failure message="Package coverage thresholds not met"/>')
    </testcase>
    <testcase classname="CoverageGates" name="Regression" time="0">
        $([ $exit_code -ne $EXIT_REGRESSION_FAIL ] || echo '<failure message="Coverage regression detected"/>')
    </testcase>
    <testcase classname="CoverageGates" name="Trend" time="0">
        $([ $exit_code -ne $EXIT_TREND_FAIL ] || echo '<failure message="Negative coverage trend detected"/>')
    </testcase>
</testsuite>
EOF
            ;;
        github)
            if [ $exit_code -eq 0 ]; then
                echo "::notice title=Coverage Gates::All coverage gates passed ✅"
            else
                echo "::error title=Coverage Gates::Coverage gates failed ❌"
                case $exit_code in
                    $EXIT_THRESHOLD_FAIL)
                        echo "::error title=Overall Coverage::Overall coverage below threshold"
                        ;;
                    $EXIT_PACKAGE_FAIL)
                        echo "::error title=Package Coverage::Package coverage thresholds not met"
                        ;;
                    $EXIT_REGRESSION_FAIL)
                        echo "::error title=Coverage Regression::Significant coverage regression detected"
                        ;;
                    $EXIT_TREND_FAIL)
                        echo "::error title=Coverage Trend::Negative coverage trend detected"
                        ;;
                esac
            fi
            ;;
    esac
}

# Main execution
main() {
    local gate_type="all"
    local custom_threshold=""
    local strict_mode=false
    local fail_fast=false
    local dry_run=false
    local generate_report=false
    local output_format="text"
    local baseline_commit=""
    local compare_branch=""
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --gate)
                gate_type="$2"
                shift 2
                ;;
            --threshold)
                custom_threshold="$2"
                shift 2
                ;;
            --strict)
                strict_mode=true
                shift
                ;;
            --config)
                CONFIG_FILE="$2"
                shift 2
                ;;
            --baseline)
                baseline_commit="$2"
                shift 2
                ;;
            --branch)
                compare_branch="$2"
                shift 2
                ;;
            --output-format)
                output_format="$2"
                shift 2
                ;;
            --fail-fast)
                fail_fast=true
                shift
                ;;
            --dry-run)
                dry_run=true
                shift
                ;;
            --generate-report)
                generate_report=true
                shift
                ;;
            --help)
                usage
                ;;
            *)
                print_color "$RED" "Unknown option: $1"
                usage
                ;;
        esac
    done
    
    if [ "$output_format" = "text" ]; then
        print_color "$BLUE" "🚦 Coverage Gates - Agent C"
        print_color "$BLUE" "============================"
        echo
    fi
    
    # Initialize
    load_config
    setup_directories
    ensure_coverage
    
    # Override threshold if specified
    if [ -n "$custom_threshold" ]; then
        OVERALL_THRESHOLD="$custom_threshold"
    fi
    
    local gates_passed=0
    local gates_failed=0
    local final_exit_code=$EXIT_SUCCESS
    
    # Run gates based on type
    case $gate_type in
        overall)
            if gate_overall; then
                gates_passed=$((gates_passed + 1))
            else
                gates_failed=$((gates_failed + 1))
                final_exit_code=$EXIT_THRESHOLD_FAIL
            fi
            ;;
        package)
            if gate_package; then
                gates_passed=$((gates_passed + 1))
            else
                gates_failed=$((gates_failed + 1))
                final_exit_code=$EXIT_PACKAGE_FAIL
            fi
            ;;
        regression)
            if gate_regression; then
                gates_passed=$((gates_passed + 1))
            else
                gates_failed=$((gates_failed + 1))
                final_exit_code=$EXIT_REGRESSION_FAIL
            fi
            ;;
        trend)
            if gate_trend; then
                gates_passed=$((gates_passed + 1))
            else
                gates_failed=$((gates_failed + 1))
                final_exit_code=$EXIT_TREND_FAIL
            fi
            ;;
        all)
            # Run all gates
            for gate in overall package regression trend; do
                case $gate in
                    overall)
                        if gate_overall; then
                            gates_passed=$((gates_passed + 1))
                        else
                            gates_failed=$((gates_failed + 1))
                            if [ $final_exit_code -eq $EXIT_SUCCESS ]; then
                                final_exit_code=$EXIT_THRESHOLD_FAIL
                            fi
                            if [ "$fail_fast" = true ]; then
                                break
                            fi
                        fi
                        ;;
                    package)
                        if gate_package; then
                            gates_passed=$((gates_passed + 1))
                        else
                            gates_failed=$((gates_failed + 1))
                            if [ $final_exit_code -eq $EXIT_SUCCESS ]; then
                                final_exit_code=$EXIT_PACKAGE_FAIL
                            fi
                            if [ "$fail_fast" = true ]; then
                                break
                            fi
                        fi
                        ;;
                    regression)
                        if gate_regression; then
                            gates_passed=$((gates_passed + 1))
                        else
                            gates_failed=$((gates_failed + 1))
                            if [ $final_exit_code -eq $EXIT_SUCCESS ]; then
                                final_exit_code=$EXIT_REGRESSION_FAIL
                            fi
                            if [ "$fail_fast" = true ]; then
                                break
                            fi
                        fi
                        ;;
                    trend)
                        if gate_trend; then
                            gates_passed=$((gates_passed + 1))
                        else
                            gates_failed=$((gates_failed + 1))
                            if [ $final_exit_code -eq $EXIT_SUCCESS ]; then
                                final_exit_code=$EXIT_TREND_FAIL
                            fi
                            if [ "$fail_fast" = true ]; then
                                break
                            fi
                        fi
                        ;;
                esac
            done
            ;;
        *)
            print_color "$RED" "❌ Unknown gate type: $gate_type"
            exit $EXIT_CONFIG_ERROR
            ;;
    esac
    
    # Output results
    if [ "$output_format" != "text" ]; then
        output_results "$output_format" "$gates_passed" "$gates_failed" "$final_exit_code"
    else
        echo
        print_color "$BLUE" "════════════════════════════════════════"
        print_color "$BLUE" "           GATES SUMMARY                "
        print_color "$BLUE" "════════════════════════════════════════"
        print_color "$CYAN" "  Gates Passed: $gates_passed"
        print_color "$CYAN" "  Gates Failed: $gates_failed"
        
        if [ $final_exit_code -eq $EXIT_SUCCESS ]; then
            print_color "$GREEN" "  Overall Status: ✅ ALL GATES PASSED"
        else
            print_color "$RED" "  Overall Status: ❌ SOME GATES FAILED"
        fi
        
        print_color "$BLUE" "════════════════════════════════════════"
    fi
    
    # In dry-run mode, always exit success
    if [ "$dry_run" = true ]; then
        exit $EXIT_SUCCESS
    fi
    
    # In strict mode, any failure is critical
    if [ "$strict_mode" = true ] && [ $gates_failed -gt 0 ]; then
        exit $final_exit_code
    fi
    
    exit $final_exit_code
}

# Run main function with all arguments
main "$@"