#!/bin/bash

# Coverage Quality Gates with Configurable Thresholds and Waiver Management
# Comprehensive quality enforcement system with exception handling

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
COVERAGE_DIR="coverage-data"
QUALITY_DIR="$COVERAGE_DIR/quality"
WAIVERS_DIR="$QUALITY_DIR/waivers"
THRESHOLDS_DIR="$QUALITY_DIR/thresholds"
VIOLATIONS_DIR="$QUALITY_DIR/violations"
CONFIG_FILE="coverage-config.yaml"
WAIVERS_FILE="$WAIVERS_DIR/active-waivers.json"
THRESHOLDS_FILE="$THRESHOLDS_DIR/package-thresholds.json"

# Default quality gates
DEFAULT_OVERALL_THRESHOLD=85
DEFAULT_PACKAGE_THRESHOLD=75
DEFAULT_FUNCTION_THRESHOLD=60
DEFAULT_REGRESSION_THRESHOLD=5
DEFAULT_TREND_THRESHOLD=3

# Waiver types
WAIVER_COVERAGE="coverage"
WAIVER_REGRESSION="regression"
WAIVER_PACKAGE="package"
WAIVER_FUNCTION="function"
WAIVER_TREND="trend"

# Quality levels
QUALITY_EXCELLENT=90
QUALITY_GOOD=80
QUALITY_ACCEPTABLE=70
QUALITY_POOR=60

# Timestamp for this run
TIMESTAMP=$(date '+%Y-%m-%d_%H-%M-%S')
ISO_TIMESTAMP=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to show usage
usage() {
    cat << EOF
Usage: $(basename "$0") [OPTIONS]

Coverage Quality Gates with comprehensive threshold management and waiver system.

OPTIONS:
    --check                 Run all quality checks
    --create-waiver         Create a new waiver
    --list-waivers          List active waivers
    --expire-waivers        Expire old waivers
    --set-threshold         Set package threshold
    --list-thresholds       List package thresholds
    --violation-report      Generate violation report
    --quality-score         Calculate quality score
    --config FILE           Configuration file (default: $CONFIG_FILE)
    --strict                Enable strict mode (no waivers allowed)
    --dry-run              Show what would be checked without enforcing
    --output-format FORMAT  Output format (text|json|junit)
    --help                  Show this help message

WAIVER MANAGEMENT:
    --waiver-type TYPE      Waiver type (coverage|regression|package|function|trend)
    --waiver-target TARGET  Target (package name, function, etc.)
    --waiver-reason REASON  Reason for waiver
    --waiver-duration DAYS  Waiver duration in days (default: 30)
    --waiver-approver EMAIL Approver email

THRESHOLD MANAGEMENT:
    --package PACKAGE       Package name for threshold setting
    --threshold PERCENT     Threshold percentage
    --level LEVEL           Quality level (excellent|good|acceptable|poor)

EXAMPLES:
    $(basename "$0") --check                                    # Run all quality checks
    $(basename "$0") --create-waiver --waiver-type coverage --waiver-target internal/auth --waiver-reason "Legacy code refactoring planned"
    $(basename "$0") --set-threshold --package internal/user --threshold 90
    $(basename "$0") --quality-score                           # Calculate overall quality score

EOF
    exit 0
}

# Setup directories
setup_directories() {
    mkdir -p "$COVERAGE_DIR"
    mkdir -p "$QUALITY_DIR"
    mkdir -p "$WAIVERS_DIR"
    mkdir -p "$THRESHOLDS_DIR"
    mkdir -p "$VIOLATIONS_DIR"
}

# Initialize default configuration
init_quality_config() {
    if [ ! -f "$THRESHOLDS_FILE" ]; then
        cat > "$THRESHOLDS_FILE" << EOF
{
    "default_thresholds": {
        "overall": $DEFAULT_OVERALL_THRESHOLD,
        "package": $DEFAULT_PACKAGE_THRESHOLD,
        "function": $DEFAULT_FUNCTION_THRESHOLD,
        "regression": $DEFAULT_REGRESSION_THRESHOLD,
        "trend": $DEFAULT_TREND_THRESHOLD
    },
    "package_thresholds": {
        "internal/auth": 90,
        "internal/user": 88,
        "internal/organization": 85,
        "internal/event": 85,
        "internal/handlers": 80,
        "internal/middleware": 85,
        "internal/services": 82,
        "internal/token": 88,
        "internal/validation": 85
    },
    "function_thresholds": {
        "critical": 95,
        "important": 85,
        "standard": 75,
        "utility": 65
    },
    "quality_levels": {
        "excellent": $QUALITY_EXCELLENT,
        "good": $QUALITY_GOOD,
        "acceptable": $QUALITY_ACCEPTABLE,
        "poor": $QUALITY_POOR
    }
}
EOF
        print_color "$GREEN" "✓ Initialized default thresholds: $THRESHOLDS_FILE"
    fi
    
    if [ ! -f "$WAIVERS_FILE" ]; then
        echo '{"waivers": []}' > "$WAIVERS_FILE"
        print_color "$GREEN" "✓ Initialized waivers file: $WAIVERS_FILE"
    fi
}

# Load thresholds
load_thresholds() {
    if [ -f "$THRESHOLDS_FILE" ]; then
        OVERALL_THRESHOLD=$(jq -r '.default_thresholds.overall' "$THRESHOLDS_FILE")
        PACKAGE_THRESHOLD=$(jq -r '.default_thresholds.package' "$THRESHOLDS_FILE")
        FUNCTION_THRESHOLD=$(jq -r '.default_thresholds.function' "$THRESHOLDS_FILE")
        REGRESSION_THRESHOLD=$(jq -r '.default_thresholds.regression' "$THRESHOLDS_FILE")
        TREND_THRESHOLD=$(jq -r '.default_thresholds.trend' "$THRESHOLDS_FILE")
    else
        OVERALL_THRESHOLD=$DEFAULT_OVERALL_THRESHOLD
        PACKAGE_THRESHOLD=$DEFAULT_PACKAGE_THRESHOLD
        FUNCTION_THRESHOLD=$DEFAULT_FUNCTION_THRESHOLD
        REGRESSION_THRESHOLD=$DEFAULT_REGRESSION_THRESHOLD
        TREND_THRESHOLD=$DEFAULT_TREND_THRESHOLD
    fi
}

# Get package-specific threshold
get_package_threshold() {
    local package=$1
    local threshold
    
    if [ -f "$THRESHOLDS_FILE" ]; then
        threshold=$(jq -r --arg pkg "$package" '.package_thresholds[$pkg] // .default_thresholds.package' "$THRESHOLDS_FILE")
    else
        threshold=$PACKAGE_THRESHOLD
    fi
    
    echo "$threshold"
}

# Check if waiver exists for target
check_waiver() {
    local waiver_type=$1
    local target=$2
    
    if [ ! -f "$WAIVERS_FILE" ]; then
        return 1
    fi
    
    local waiver_count=$(jq -r --arg type "$waiver_type" --arg target "$target" '
        .waivers[] | 
        select(.type == $type and .target == $target and .expires > now) | 
        length
    ' "$WAIVERS_FILE" 2>/dev/null || echo "0")
    
    [ "$waiver_count" -gt 0 ]
}

# Create a new waiver
create_waiver() {
    local waiver_type=$1
    local target=$2
    local reason=$3
    local duration_days=${4:-30}
    local approver=${5:-"system"}
    
    print_color "$BLUE" "📝 Creating waiver..."
    
    local expires_timestamp=$(date -d "+${duration_days} days" -u +"%Y-%m-%dT%H:%M:%SZ" 2>/dev/null || date -v "+${duration_days}d" -u +"%Y-%m-%dT%H:%M:%SZ")
    local waiver_id="waiver-$(date +%s)-$(echo "$target" | tr '/' '_')"
    
    local waiver_data=$(jq -n \
        --arg id "$waiver_id" \
        --arg type "$waiver_type" \
        --arg target "$target" \
        --arg reason "$reason" \
        --arg created "$ISO_TIMESTAMP" \
        --arg expires "$expires_timestamp" \
        --arg approver "$approver" \
        --arg duration "$duration_days" \
        '{
            id: $id,
            type: $type,
            target: $target,
            reason: $reason,
            created: $created,
            expires: $expires,
            approver: $approver,
            duration_days: ($duration | tonumber),
            active: true
        }')
    
    # Add waiver to file
    local temp_file=$(mktemp)
    jq --argjson waiver "$waiver_data" '.waivers += [$waiver]' "$WAIVERS_FILE" > "$temp_file"
    mv "$temp_file" "$WAIVERS_FILE"
    
    print_color "$GREEN" "✓ Waiver created:"
    print_color "$CYAN" "  ID: $waiver_id"
    print_color "$CYAN" "  Type: $waiver_type"
    print_color "$CYAN" "  Target: $target"
    print_color "$CYAN" "  Duration: $duration_days days"
    print_color "$CYAN" "  Expires: $expires_timestamp"
    
    return 0
}

# List active waivers
list_waivers() {
    print_color "$BLUE" "📋 Active Waivers"
    print_color "$BLUE" "================="
    
    if [ ! -f "$WAIVERS_FILE" ]; then
        print_color "$YELLOW" "No waivers file found"
        return
    fi
    
    local current_time=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    local active_waivers=$(jq -r --arg now "$current_time" '
        .waivers[] | 
        select(.expires > $now) |
        [.id, .type, .target, .reason, .expires] |
        @tsv
    ' "$WAIVERS_FILE" 2>/dev/null)
    
    if [ -z "$active_waivers" ]; then
        print_color "$GREEN" "✓ No active waivers"
        return
    fi
    
    printf "%-20s %-12s %-25s %-30s %-20s\\n" "ID" "TYPE" "TARGET" "REASON" "EXPIRES"
    echo "$(printf '%.0s-' {1..110})"
    
    echo "$active_waivers" | while IFS=$'\t' read -r id type target reason expires; do
        printf "%-20s %-12s %-25s %-30s %-20s\\n" \
            "${id:0:18}..." "$type" "${target:0:23}..." "${reason:0:28}..." "${expires:0:18}..."
    done
}

# Expire old waivers
expire_waivers() {
    print_color "$BLUE" "🗑️  Expiring old waivers..."
    
    if [ ! -f "$WAIVERS_FILE" ]; then
        print_color "$YELLOW" "No waivers file found"
        return
    fi
    
    local current_time=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    local temp_file=$(mktemp)
    
    # Remove expired waivers and mark as inactive
    jq --arg now "$current_time" '
        .waivers |= map(
            if .expires <= $now then
                . + {active: false, expired: $now}
            else
                .
            end
        )
    ' "$WAIVERS_FILE" > "$temp_file"
    
    mv "$temp_file" "$WAIVERS_FILE"
    
    local expired_count=$(jq -r --arg now "$current_time" '
        [.waivers[] | select(.expires <= $now)] | length
    ' "$WAIVERS_FILE")
    
    print_color "$GREEN" "✓ Expired $expired_count waivers"
}

# Set package threshold
set_package_threshold() {
    local package=$1
    local threshold=$2
    
    print_color "$BLUE" "🎯 Setting threshold for $package to $threshold%"
    
    local temp_file=$(mktemp)
    jq --arg pkg "$package" --arg threshold "$threshold" '
        .package_thresholds[$pkg] = ($threshold | tonumber)
    ' "$THRESHOLDS_FILE" > "$temp_file"
    
    mv "$temp_file" "$THRESHOLDS_FILE"
    
    print_color "$GREEN" "✓ Threshold set for $package: $threshold%"
}

# List package thresholds
list_thresholds() {
    print_color "$BLUE" "🎯 Package Thresholds"
    print_color "$BLUE" "===================="
    
    if [ ! -f "$THRESHOLDS_FILE" ]; then
        print_color "$YELLOW" "No thresholds file found"
        return
    fi
    
    print_color "$CYAN" "Default Thresholds:"
    jq -r '.default_thresholds | to_entries[] | "  \(.key): \(.value)%"' "$THRESHOLDS_FILE"
    
    echo
    print_color "$CYAN" "Package-Specific Thresholds:"
    jq -r '.package_thresholds | to_entries[] | "  \(.key): \(.value)%"' "$THRESHOLDS_FILE"
}

# Run comprehensive quality checks
run_quality_checks() {
    local strict_mode=${1:-false}
    local dry_run=${2:-false}
    
    print_color "$BLUE" "🔍 Running Quality Checks"
    print_color "$BLUE" "========================="
    
    local violations_file="$VIOLATIONS_DIR/violations-${TIMESTAMP}.json"
    local violations='[]'
    local total_violations=0
    
    # Ensure coverage data exists
    if [ ! -f "coverage.out" ]; then
        print_color "$YELLOW" "⚠️  No coverage data found, generating..."
        go test -timeout 30m -coverprofile=coverage.out -covermode=atomic ./...
    fi
    
    # Check overall coverage
    local current_coverage=$(go tool cover -func=coverage.out | grep "total:" | awk '{print $3}' | sed 's/%//')
    
    if (( $(echo "$current_coverage < $OVERALL_THRESHOLD" | bc -l) )); then
        if [ "$strict_mode" = false ] && check_waiver "$WAIVER_COVERAGE" "overall"; then
            print_color "$YELLOW" "⚠️  Overall coverage below threshold ($current_coverage% < $OVERALL_THRESHOLD%) - WAIVED"
        else
            print_color "$RED" "❌ Overall coverage below threshold: $current_coverage% < $OVERALL_THRESHOLD%"
            local violation=$(jq -n \
                --arg type "overall_coverage" \
                --arg target "overall" \
                --arg current "$current_coverage" \
                --arg threshold "$OVERALL_THRESHOLD" \
                --arg severity "high" \
                '{
                    type: $type,
                    target: $target,
                    current_value: ($current | tonumber),
                    threshold: ($threshold | tonumber),
                    severity: $severity,
                    timestamp: now | todate
                }')
            violations=$(echo "$violations" | jq --argjson v "$violation" '. += [$v]')
            total_violations=$((total_violations + 1))
        fi
    else
        print_color "$GREEN" "✅ Overall coverage meets threshold: $current_coverage% >= $OVERALL_THRESHOLD%"
    fi
    
    # Check package-level coverage
    print_color "$BLUE" "\n📦 Checking package coverage..."
    
    # Generate package coverage data
    local package_violations=0
    while read -r line; do
        if [[ "$line" =~ ^[[:space:]]*$ ]] || [[ "$line" == *"total:"* ]]; then
            continue
        fi
        
        local file=$(echo "$line" | awk '{print $1}')
        local coverage=$(echo "$line" | awk '{print $3}' | sed 's/%//')
        
        # Extract package name
        local package=""
        if [[ "$file" =~ internal/([^/]+) ]]; then
            package="internal/${BASH_REMATCH[1]}"
        elif [[ "$file" =~ cmd/([^/]+) ]]; then
            package="cmd/${BASH_REMATCH[1]}"
        else
            continue
        fi
        
        # Get package-specific threshold
        local pkg_threshold=$(get_package_threshold "$package")
        
        if (( $(echo "$coverage < $pkg_threshold" | bc -l) )); then
            if [ "$strict_mode" = false ] && check_waiver "$WAIVER_PACKAGE" "$package"; then
                print_color "$YELLOW" "⚠️  Package $package below threshold ($coverage% < $pkg_threshold%) - WAIVED"
            else
                print_color "$RED" "❌ Package $package below threshold: $coverage% < $pkg_threshold%"
                local violation=$(jq -n \
                    --arg type "package_coverage" \
                    --arg target "$package" \
                    --arg current "$coverage" \
                    --arg threshold "$pkg_threshold" \
                    --arg severity "medium" \
                    '{
                        type: $type,
                        target: $target,
                        current_value: ($current | tonumber),
                        threshold: ($threshold | tonumber),
                        severity: $severity,
                        timestamp: now | todate
                    }')
                violations=$(echo "$violations" | jq --argjson v "$violation" '. += [$v]')
                package_violations=$((package_violations + 1))
                total_violations=$((total_violations + 1))
            fi
        fi
    done < <(go tool cover -func=coverage.out | grep -v "total:" | head -20)  # Limit for demo
    
    if [ $package_violations -eq 0 ]; then
        print_color "$GREEN" "✅ All checked packages meet thresholds"
    fi
    
    # Check for regressions
    if [ -f "$COVERAGE_DIR/historical/coverage-history.json" ]; then
        print_color "$BLUE" "\n📉 Checking for regressions..."
        
        local baseline_coverage=$(jq -r '.history[-2].coverage // null' "$COVERAGE_DIR/historical/coverage-history.json" 2>/dev/null || echo "null")
        
        if [ "$baseline_coverage" != "null" ] && [ "$baseline_coverage" != "" ]; then
            local regression=$(echo "$baseline_coverage - $current_coverage" | bc -l)
            
            if (( $(echo "$regression > $REGRESSION_THRESHOLD" | bc -l) )); then
                if [ "$strict_mode" = false ] && check_waiver "$WAIVER_REGRESSION" "overall"; then
                    print_color "$YELLOW" "⚠️  Coverage regression detected ($regression%) - WAIVED"
                else
                    print_color "$RED" "❌ Coverage regression: -$regression% (threshold: $REGRESSION_THRESHOLD%)"
                    local violation=$(jq -n \
                        --arg type "regression" \
                        --arg target "overall" \
                        --arg regression "$regression" \
                        --arg threshold "$REGRESSION_THRESHOLD" \
                        --arg severity "high" \
                        '{
                            type: $type,
                            target: $target,
                            regression: ($regression | tonumber),
                            threshold: ($threshold | tonumber),
                            severity: $severity,
                            timestamp: now | todate
                        }')
                    violations=$(echo "$violations" | jq --argjson v "$violation" '. += [$v]')
                    total_violations=$((total_violations + 1))
                fi
            else
                print_color "$GREEN" "✅ No significant regression detected"
            fi
        else
            print_color "$YELLOW" "⚠️  No baseline data for regression check"
        fi
    else
        print_color "$YELLOW" "⚠️  No historical data for regression check"
    fi
    
    # Save violations report
    echo "$violations" > "$violations_file"
    
    # Generate summary
    echo
    print_color "$BLUE" "════════════════════════════════════════"
    print_color "$BLUE" "         QUALITY CHECK SUMMARY          "
    print_color "$BLUE" "════════════════════════════════════════"
    print_color "$CYAN" "  Total Violations: $total_violations"
    
    if [ $total_violations -eq 0 ]; then
        print_color "$GREEN" "  Status: ✅ ALL QUALITY CHECKS PASSED"
        print_color "$GREEN" "  Quality Level: EXCELLENT"
    else
        print_color "$RED" "  Status: ❌ QUALITY VIOLATIONS FOUND"
        if [ $total_violations -le 2 ]; then
            print_color "$YELLOW" "  Quality Level: GOOD (minor issues)"
        elif [ $total_violations -le 5 ]; then
            print_color "$YELLOW" "  Quality Level: ACCEPTABLE (some issues)"
        else
            print_color "$RED" "  Quality Level: POOR (many issues)"
        fi
    fi
    
    print_color "$CYAN" "  Violations Report: $violations_file"
    print_color "$BLUE" "════════════════════════════════════════"
    
    # Return appropriate exit code
    if [ "$dry_run" = true ]; then
        return 0
    elif [ $total_violations -eq 0 ]; then
        return 0
    else
        return 1
    fi
}

# Calculate overall quality score
calculate_quality_score() {
    print_color "$BLUE" "📊 Calculating Quality Score..."
    
    # Ensure coverage data exists
    if [ ! -f "coverage.out" ]; then
        print_color "$YELLOW" "⚠️  No coverage data found, generating..."
        go test -timeout 30m -coverprofile=coverage.out -covermode=atomic ./...
    fi
    
    local current_coverage=$(go tool cover -func=coverage.out | grep "total:" | awk '{print $3}' | sed 's/%//')
    local score=0
    local max_score=100
    
    # Coverage component (40 points)
    local coverage_score=$(echo "scale=2; ($current_coverage / 100) * 40" | bc)
    score=$(echo "scale=2; $score + $coverage_score" | bc)
    
    # Package consistency component (30 points)
    local package_consistency=30  # Simplified - would calculate actual consistency
    score=$(echo "scale=2; $score + $package_consistency" | bc)
    
    # Trend component (20 points)
    local trend_score=20  # Simplified - would analyze actual trends
    score=$(echo "scale=2; $score + $trend_score" | bc)
    
    # Waiver penalty (10 points)
    local active_waivers=0
    if [ -f "$WAIVERS_FILE" ]; then
        active_waivers=$(jq -r --arg now "$(date -u +"%Y-%m-%dT%H:%M:%SZ")" '[.waivers[] | select(.expires > $now)] | length' "$WAIVERS_FILE" 2>/dev/null || echo "0")
    fi
    local waiver_penalty=$(echo "scale=2; $active_waivers * 2" | bc)
    score=$(echo "scale=2; $score - $waiver_penalty" | bc)
    
    # Ensure score is between 0 and 100
    if (( $(echo "$score < 0" | bc -l) )); then
        score=0
    elif (( $(echo "$score > 100" | bc -l) )); then
        score=100
    fi
    
    # Determine quality level
    local quality_level
    if (( $(echo "$score >= 90" | bc -l) )); then
        quality_level="EXCELLENT"
    elif (( $(echo "$score >= 80" | bc -l) )); then
        quality_level="GOOD"
    elif (( $(echo "$score >= 70" | bc -l) )); then
        quality_level="ACCEPTABLE"
    else
        quality_level="POOR"
    fi
    
    print_color "$CYAN" "  Coverage Score: ${coverage_score}/40"
    print_color "$CYAN" "  Consistency Score: ${package_consistency}/30"
    print_color "$CYAN" "  Trend Score: ${trend_score}/20"
    print_color "$CYAN" "  Waiver Penalty: -${waiver_penalty}/10"
    print_color "$CYAN" "  Active Waivers: $active_waivers"
    echo
    print_color "$BLUE" "  Overall Quality Score: ${score}/100"
    print_color "$BLUE" "  Quality Level: $quality_level"
    
    # Save quality score
    local quality_data=$(jq -n \
        --arg timestamp "$ISO_TIMESTAMP" \
        --arg score "$score" \
        --arg level "$quality_level" \
        --arg coverage "$current_coverage" \
        --arg waivers "$active_waivers" \
        '{
            timestamp: $timestamp,
            overall_score: ($score | tonumber),
            quality_level: $level,
            components: {
                coverage: ($coverage | tonumber),
                active_waivers: ($waivers | tonumber)
            }
        }')
    
    echo "$quality_data" > "$QUALITY_DIR/quality-score-${TIMESTAMP}.json"
    
    return 0
}

# Generate violation report
generate_violation_report() {
    print_color "$BLUE" "📋 Generating Violation Report..."
    
    local latest_violations=$(find "$VIOLATIONS_DIR" -name "violations-*.json" -type f | sort | tail -1)
    
    if [ -z "$latest_violations" ] || [ ! -f "$latest_violations" ]; then
        print_color "$YELLOW" "No violation data found"
        return
    fi
    
    local violation_count=$(jq '. | length' "$latest_violations")
    
    if [ "$violation_count" -eq 0 ]; then
        print_color "$GREEN" "✅ No violations found"
        return
    fi
    
    print_color "$RED" "❌ Found $violation_count violations:"
    echo
    
    jq -r '.[] | "  \(.severity | ascii_upcase): \(.type) - \(.target) (\(.current_value)% < \(.threshold)%)"' "$latest_violations"
    
    # Generate HTML report
    local html_report="$VIOLATIONS_DIR/violation-report-${TIMESTAMP}.html"
    cat > "$html_report" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Coverage Violations Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .violation { margin: 10px 0; padding: 10px; border-left: 4px solid; }
        .high { border-color: #dc3545; background: #f8d7da; }
        .medium { border-color: #ffc107; background: #fff3cd; }
        .low { border-color: #17a2b8; background: #d1ecf1; }
    </style>
</head>
<body>
    <h1>Coverage Violations Report</h1>
    <p>Generated: ${ISO_TIMESTAMP}</p>
    <div id="violations"></div>
    
    <script>
        const violations = $(cat "$latest_violations");
        const container = document.getElementById('violations');
        
        violations.forEach(v => {
            const div = document.createElement('div');
            div.className = 'violation ' + v.severity;
            div.innerHTML = '<strong>' + v.type.toUpperCase() + '</strong>: ' + v.target + 
                          ' (' + v.current_value + '% < ' + v.threshold + '%)';
            container.appendChild(div);
        });
    </script>
</body>
</html>
EOF
    
    print_color "$GREEN" "✓ Violation report saved: $html_report"
}

# Main execution
main() {
    local action=""
    local waiver_type=""
    local waiver_target=""
    local waiver_reason=""
    local waiver_duration=30
    local waiver_approver="system"
    local package=""
    local threshold=""
    local strict_mode=false
    local dry_run=false
    local output_format="text"
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --check)
                action="check"
                shift
                ;;
            --create-waiver)
                action="create_waiver"
                shift
                ;;
            --list-waivers)
                action="list_waivers"
                shift
                ;;
            --expire-waivers)
                action="expire_waivers"
                shift
                ;;
            --set-threshold)
                action="set_threshold"
                shift
                ;;
            --list-thresholds)
                action="list_thresholds"
                shift
                ;;
            --violation-report)
                action="violation_report"
                shift
                ;;
            --quality-score)
                action="quality_score"
                shift
                ;;
            --waiver-type)
                waiver_type="$2"
                shift 2
                ;;
            --waiver-target)
                waiver_target="$2"
                shift 2
                ;;
            --waiver-reason)
                waiver_reason="$2"
                shift 2
                ;;
            --waiver-duration)
                waiver_duration="$2"
                shift 2
                ;;
            --waiver-approver)
                waiver_approver="$2"
                shift 2
                ;;
            --package)
                package="$2"
                shift 2
                ;;
            --threshold)
                threshold="$2"
                shift 2
                ;;
            --strict)
                strict_mode=true
                shift
                ;;
            --dry-run)
                dry_run=true
                shift
                ;;
            --output-format)
                output_format="$2"
                shift 2
                ;;
            --config)
                CONFIG_FILE="$2"
                shift 2
                ;;
            --help)
                usage
                ;;
            *)
                print_color "$RED" "Unknown option: $1"
                usage
                ;;
        esac
    done
    
    # Default action
    if [ -z "$action" ]; then
        action="check"
    fi
    
    print_color "$BLUE" "🎯 Coverage Quality Management - Agent C"
    print_color "$BLUE" "========================================"
    echo
    
    # Initialize
    setup_directories
    init_quality_config
    load_thresholds
    
    # Execute action
    case $action in
        check)
            run_quality_checks "$strict_mode" "$dry_run"
            ;;
        create_waiver)
            if [ -z "$waiver_type" ] || [ -z "$waiver_target" ] || [ -z "$waiver_reason" ]; then
                print_color "$RED" "❌ Missing required waiver parameters"
                print_color "$YELLOW" "Required: --waiver-type, --waiver-target, --waiver-reason"
                exit 1
            fi
            create_waiver "$waiver_type" "$waiver_target" "$waiver_reason" "$waiver_duration" "$waiver_approver"
            ;;
        list_waivers)
            list_waivers
            ;;
        expire_waivers)
            expire_waivers
            ;;
        set_threshold)
            if [ -z "$package" ] || [ -z "$threshold" ]; then
                print_color "$RED" "❌ Missing required parameters"
                print_color "$YELLOW" "Required: --package, --threshold"
                exit 1
            fi
            set_package_threshold "$package" "$threshold"
            ;;
        list_thresholds)
            list_thresholds
            ;;
        violation_report)
            generate_violation_report
            ;;
        quality_score)
            calculate_quality_score
            ;;
        *)
            print_color "$RED" "❌ Unknown action: $action"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"