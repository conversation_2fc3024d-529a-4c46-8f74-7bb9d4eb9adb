#!/bin/bash

# Coverage Report Generator for Go Projects
# This script generates comprehensive coverage reports with various output formats

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Default values
OUTPUT_FORMAT="text"
HTML_OUTPUT=false
BADGE_OUTPUT=false
TARGET_COVERAGE=85
COVERAGE_FILE="coverage.out"
OUTPUT_DIR="coverage-report"
PACKAGE_PATH="./..."
SHOW_UNCOVERED=false

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to show usage
usage() {
    cat << EOF
Usage: $(basename "$0") [OPTIONS]

Generate comprehensive coverage reports for Go projects.

OPTIONS:
    -h, --html              Generate HTML coverage report
    -b, --badge             Generate coverage badge (SVG)
    -c, --compare PERCENT   Compare against target coverage (default: 85%)
    -f, --file FILE         Coverage file to use (default: coverage.out)
    -o, --output DIR        Output directory (default: coverage-report)
    -u, --uncovered         Show uncovered code sections
    --help                  Show this help message

EXAMPLES:
    $(basename "$0")                  # Generate text report
    $(basename "$0") --html           # Generate HTML report
    $(basename "$0") --badge          # Generate coverage badge
    $(basename "$0") --compare 90     # Compare against 90% target

EOF
    exit 0
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--html)
            HTML_OUTPUT=true
            shift
            ;;
        -b|--badge)
            BADGE_OUTPUT=true
            shift
            ;;
        -c|--compare)
            TARGET_COVERAGE="$2"
            shift 2
            ;;
        -f|--file)
            COVERAGE_FILE="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        -u|--uncovered)
            SHOW_UNCOVERED=true
            shift
            ;;
        --help)
            usage
            ;;
        *)
            print_color "$RED" "Unknown option: $1"
            usage
            ;;
    esac
done

# Check if coverage file exists
if [ ! -f "$COVERAGE_FILE" ]; then
    print_color "$RED" "❌ Coverage file not found: $COVERAGE_FILE"
    print_color "$YELLOW" "💡 Run tests with coverage first:"
    print_color "$BLUE" "   go test -coverprofile=$COVERAGE_FILE ./..."
    exit 1
fi

# Create output directory
mkdir -p "$OUTPUT_DIR"

print_color "$BLUE" "📊 Coverage Report Generator"
print_color "$BLUE" "══════════════════════════════════"
echo

# Function to generate coverage badge SVG
generate_badge() {
    local coverage=$1
    local color
    
    # Determine badge color based on coverage
    if (( $(echo "$coverage >= 90" | bc -l) )); then
        color="#4c1"  # Bright green
    elif (( $(echo "$coverage >= 80" | bc -l) )); then
        color="#97CA00"  # Green
    elif (( $(echo "$coverage >= 70" | bc -l) )); then
        color="#dfb317"  # Yellow
    elif (( $(echo "$coverage >= 60" | bc -l) )); then
        color="#fe7d37"  # Orange
    else
        color="#e05d44"  # Red
    fi
    
    cat > "$OUTPUT_DIR/coverage-badge.svg" << EOF
<svg xmlns="http://www.w3.org/2000/svg" width="114" height="20">
  <linearGradient id="b" x2="0" y2="100%">
    <stop offset="0" stop-color="#bbb" stop-opacity=".1"/>
    <stop offset="1" stop-opacity=".1"/>
  </linearGradient>
  <mask id="a">
    <rect width="114" height="20" rx="3" fill="#fff"/>
  </mask>
  <g mask="url(#a)">
    <path fill="#555" d="M0 0h63v20H0z"/>
    <path fill="$color" d="M63 0h51v20H63z"/>
    <path fill="url(#b)" d="M0 0h114v20H0z"/>
  </g>
  <g fill="#fff" text-anchor="middle" font-family="DejaVu Sans,Verdana,Geneva,sans-serif" font-size="11">
    <text x="31.5" y="15" fill="#010101" fill-opacity=".3">coverage</text>
    <text x="31.5" y="14">coverage</text>
    <text x="87.5" y="15" fill="#010101" fill-opacity=".3">${coverage}%</text>
    <text x="87.5" y="14">${coverage}%</text>
  </g>
</svg>
EOF
}

# Function to extract package-level coverage
get_package_coverage() {
    go tool cover -func="$COVERAGE_FILE" | grep -v "total:" | awk '{
        pkg = $1
        gsub(/.*\//, "", pkg)
        gsub(/\.go:.*/, "", pkg)
        if (pkg != prev_pkg) {
            if (prev_pkg != "") {
                printf "%s\t%.1f%%\n", prev_pkg, total/count
            }
            prev_pkg = pkg
            total = 0
            count = 0
        }
        # Extract coverage percentage
        cov = $3
        gsub(/%/, "", cov)
        if (cov != "-") {
            total += cov
            count++
        }
    }
    END {
        if (prev_pkg != "") {
            printf "%s\t%.1f%%\n", prev_pkg, total/count
        }
    }' | sort -k2 -nr
}

# Generate text coverage report
print_color "$YELLOW" "📋 Generating coverage summary..."

# Get total coverage
TOTAL_COVERAGE=$(go tool cover -func="$COVERAGE_FILE" | grep "total:" | awk '{print $3}' | sed 's/%//')

# Get detailed function coverage
FUNCTION_COVERAGE=$(go tool cover -func="$COVERAGE_FILE" | grep -v "total:")

# Save text report
{
    echo "Coverage Report"
    echo "==============="
    echo "Generated: $(date)"
    echo "Coverage File: $COVERAGE_FILE"
    echo ""
    echo "Total Coverage: ${TOTAL_COVERAGE}%"
    echo "Target Coverage: ${TARGET_COVERAGE}%"
    echo ""
    
    # Compare against target
    if (( $(echo "$TOTAL_COVERAGE >= $TARGET_COVERAGE" | bc -l) )); then
        echo "Status: ✅ PASSING (meets target)"
    else
        echo "Status: ❌ FAILING (below target)"
    fi
    echo ""
    
    # Package-level summary
    echo "Package Coverage Summary"
    echo "========================"
    get_package_coverage | while IFS=$'\t' read -r pkg cov; do
        printf "%-40s %s\n" "$pkg" "$cov"
    done
    echo ""
    
    # Detailed function coverage
    echo "Function Coverage Details"
    echo "========================="
    echo "$FUNCTION_COVERAGE"
    
} > "$OUTPUT_DIR/coverage-summary.txt"

print_color "$GREEN" "✓ Text report saved to: $OUTPUT_DIR/coverage-summary.txt"

# Generate HTML report if requested
if [ "$HTML_OUTPUT" = true ]; then
    print_color "$YELLOW" "🌐 Generating HTML coverage report..."
    
    go tool cover -html="$COVERAGE_FILE" -o "$OUTPUT_DIR/coverage.html"
    
    # Create index.html with enhanced styling
    cat > "$OUTPUT_DIR/index.html" << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>Coverage Report</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 30px;
        }
        .summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        .metric {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }
        .metric-value {
            font-size: 36px;
            font-weight: bold;
            margin: 10px 0;
        }
        .metric-label {
            color: #6c757d;
            font-size: 14px;
        }
        .passing { color: #28a745; }
        .failing { color: #dc3545; }
        .chart {
            margin: 40px 0;
        }
        iframe {
            width: 100%;
            height: 800px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 Coverage Report</h1>
        <div class="summary">
            <div class="metric">
                <div class="metric-label">Total Coverage</div>
                <div class="metric-value COVERAGE_CLASS">TOTAL_COVERAGE%</div>
            </div>
            <div class="metric">
                <div class="metric-label">Target Coverage</div>
                <div class="metric-value">TARGET_COVERAGE%</div>
            </div>
            <div class="metric">
                <div class="metric-label">Status</div>
                <div class="metric-value COVERAGE_CLASS">COVERAGE_STATUS</div>
            </div>
            <div class="metric">
                <div class="metric-label">Generated</div>
                <div class="metric-value" style="font-size: 16px;">GENERATED_DATE</div>
            </div>
        </div>
        
        <h2>📈 Detailed Coverage Report</h2>
        <iframe src="coverage.html"></iframe>
    </div>
</body>
</html>
EOF

    # Replace placeholders
    if (( $(echo "$TOTAL_COVERAGE >= $TARGET_COVERAGE" | bc -l) )); then
        COVERAGE_CLASS="passing"
        COVERAGE_STATUS="✅ PASS"
    else
        COVERAGE_CLASS="failing"
        COVERAGE_STATUS="❌ FAIL"
    fi
    
    sed -i "s/TOTAL_COVERAGE/$TOTAL_COVERAGE/g" "$OUTPUT_DIR/index.html"
    sed -i "s/TARGET_COVERAGE/$TARGET_COVERAGE/g" "$OUTPUT_DIR/index.html"
    sed -i "s/COVERAGE_CLASS/$COVERAGE_CLASS/g" "$OUTPUT_DIR/index.html"
    sed -i "s/COVERAGE_STATUS/$COVERAGE_STATUS/g" "$OUTPUT_DIR/index.html"
    sed -i "s/GENERATED_DATE/$(date '+%Y-%m-%d %H:%M')/g" "$OUTPUT_DIR/index.html"
    
    print_color "$GREEN" "✓ HTML report saved to: $OUTPUT_DIR/index.html"
fi

# Generate badge if requested
if [ "$BADGE_OUTPUT" = true ]; then
    print_color "$YELLOW" "🏷️  Generating coverage badge..."
    generate_badge "$TOTAL_COVERAGE"
    print_color "$GREEN" "✓ Badge saved to: $OUTPUT_DIR/coverage-badge.svg"
fi

# Show uncovered sections if requested
if [ "$SHOW_UNCOVERED" = true ]; then
    print_color "$YELLOW" "\n🔍 Analyzing uncovered code sections..."
    
    # Find files with low coverage
    echo -e "\n${YELLOW}Files with coverage below ${TARGET_COVERAGE}%:${NC}"
    go tool cover -func="$COVERAGE_FILE" | grep -v "total:" | while read -r line; do
        file=$(echo "$line" | awk '{print $1}')
        coverage=$(echo "$line" | awk '{print $3}' | sed 's/%//')
        
        if [ "$coverage" != "-" ] && (( $(echo "$coverage < $TARGET_COVERAGE" | bc -l) )); then
            printf "${RED}  %-60s %6s%%${NC}\n" "$file" "$coverage"
        fi
    done
    
    # Save uncovered report
    {
        echo "Uncovered Code Analysis"
        echo "======================="
        echo "Files with coverage below ${TARGET_COVERAGE}%:"
        echo ""
        
        go tool cover -func="$COVERAGE_FILE" | grep -v "total:" | while read -r line; do
            file=$(echo "$line" | awk '{print $1}')
            coverage=$(echo "$line" | awk '{print $3}' | sed 's/%//')
            
            if [ "$coverage" != "-" ] && (( $(echo "$coverage < $TARGET_COVERAGE" | bc -l) )); then
                printf "%-60s %6s%%\n" "$file" "$coverage"
            fi
        done
    } > "$OUTPUT_DIR/uncovered-analysis.txt"
fi

# Print summary statistics
echo
print_color "$BLUE" "════════════════════════════════════════"
print_color "$BLUE" "         COVERAGE SUMMARY              "
print_color "$BLUE" "════════════════════════════════════════"
print_color "$CYAN" "  Total Coverage: ${TOTAL_COVERAGE}%"
print_color "$CYAN" "  Target Coverage: ${TARGET_COVERAGE}%"

if (( $(echo "$TOTAL_COVERAGE >= $TARGET_COVERAGE" | bc -l) )); then
    print_color "$GREEN" "  Status: ✅ PASSING"
    COVERAGE_DIFF=$(echo "$TOTAL_COVERAGE - $TARGET_COVERAGE" | bc)
    print_color "$GREEN" "  Margin: +${COVERAGE_DIFF}%"
else
    print_color "$RED" "  Status: ❌ FAILING"
    COVERAGE_DIFF=$(echo "$TARGET_COVERAGE - $TOTAL_COVERAGE" | bc)
    print_color "$RED" "  Gap: -${COVERAGE_DIFF}%"
fi

print_color "$BLUE" "════════════════════════════════════════"

# Show top covered packages
echo
print_color "$GREEN" "🏆 Top 5 Best Covered Packages:"
get_package_coverage | head -5 | while IFS=$'\t' read -r pkg cov; do
    printf "   ${GREEN}%-40s %s${NC}\n" "$pkg" "$cov"
done

# Show least covered packages
echo
print_color "$RED" "⚠️  Top 5 Least Covered Packages:"
get_package_coverage | tail -5 | sort -k2 -n | while IFS=$'\t' read -r pkg cov; do
    printf "   ${RED}%-40s %s${NC}\n" "$pkg" "$cov"
done

echo
print_color "$BLUE" "📁 Reports saved to: $OUTPUT_DIR/"

# Exit with appropriate code
if (( $(echo "$TOTAL_COVERAGE >= $TARGET_COVERAGE" | bc -l) )); then
    exit 0
else
    exit 1
fi