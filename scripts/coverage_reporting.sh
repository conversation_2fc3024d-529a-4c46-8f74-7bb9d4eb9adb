#!/bin/bash

# Real-time Coverage Reporting with Enhanced HTML Generation and Trend Tracking
# Advanced reporting system with interactive dashboards and historical analysis

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
COVERAGE_DIR="coverage-data"
REPORTS_DIR="$COVERAGE_DIR/reports"
STATIC_DIR="$REPORTS_DIR/static"
API_DIR="$REPORTS_DIR/api"
HISTORICAL_DIR="$COVERAGE_DIR/historical"
BADGES_DIR="$COVERAGE_DIR/badges"
COVERAGE_FILE="coverage.out"
CONFIG_FILE="coverage-config.yaml"

# Report types
REPORT_DASHBOARD="dashboard"
REPORT_DETAILED="detailed"
REPORT_PACKAGE="package"
REPORT_TRENDS="trends"
REPORT_API="api"

# Timestamp for this run
TIMESTAMP=$(date '+%Y-%m-%d_%H-%M-%S')
ISO_TIMESTAMP=$(date -u +"%Y-%m-%dT%H:%M:%SZ")

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to show usage
usage() {
    cat << EOF
Usage: $(basename "$0") [OPTIONS]

Real-time coverage reporting with enhanced HTML generation and trend tracking.

OPTIONS:
    --type TYPE             Report type (dashboard|detailed|package|trends|api|all)
    --output DIR            Output directory (default: $REPORTS_DIR)
    --config FILE           Configuration file (default: $CONFIG_FILE)
    --theme THEME           Report theme (light|dark|auto)
    --interactive           Enable interactive features
    --include-source        Include source code in reports
    --realtime              Enable real-time updates
    --serve                 Start HTTP server for reports
    --port PORT             Server port (default: 8080)
    --watch                 Watch for coverage changes
    --export FORMAT         Export format (pdf|json|csv)
    --help                  Show this help message

REPORT TYPES:
    dashboard   - Interactive overview dashboard (default)
    detailed    - Detailed coverage report with source
    package     - Package-level analysis
    trends      - Historical trend analysis
    api         - JSON API for programmatic access
    all         - Generate all report types

THEMES:
    light       - Light theme (default)
    dark        - Dark theme
    auto        - Auto-detect based on system preference

EXAMPLES:
    $(basename "$0")                          # Generate dashboard
    $(basename "$0") --type all --interactive # All reports with interactivity
    $(basename "$0") --serve --watch          # Live server with auto-refresh
    $(basename "$0") --export pdf             # Export to PDF

EOF
    exit 0
}

# Setup directories
setup_directories() {
    mkdir -p "$COVERAGE_DIR"
    mkdir -p "$REPORTS_DIR"
    mkdir -p "$STATIC_DIR"
    mkdir -p "$API_DIR"
    mkdir -p "$HISTORICAL_DIR"
    mkdir -p "$BADGES_DIR"
    mkdir -p "$STATIC_DIR/css"
    mkdir -p "$STATIC_DIR/js"
    mkdir -p "$STATIC_DIR/assets"
}

# Generate CSS assets
generate_css() {
    local theme=${1:-light}
    
    cat > "$STATIC_DIR/css/coverage-dashboard.css" << EOF
/* Coverage Dashboard Styles */
:root {
    --primary-color: #4f46e5;
    --secondary-color: #7c3aed;
    --success-color: #059669;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --info-color: #0ea5e9;
    
    /* Light theme (default) */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --shadow-color: rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --text-primary: #f1f5f9;
    --text-secondary: #94a3b8;
    --border-color: #475569;
    --shadow-color: rgba(0, 0, 0, 0.3);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 30px;
    border-radius: 16px;
    margin-bottom: 30px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.header-content {
    position: relative;
    z-index: 1;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 700;
}

.header p {
    opacity: 0.9;
    font-size: 1.1rem;
}

.controls {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 30px;
    display: flex;
    gap: 15px;
    align-items: center;
    flex-wrap: wrap;
    box-shadow: 0 2px 8px var(--shadow-color);
}

.btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-block;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(79, 70, 229, 0.4);
}

.btn-secondary {
    background: var(--text-secondary);
}

.btn-success {
    background: var(--success-color);
}

.btn-warning {
    background: var(--warning-color);
}

.btn-danger {
    background: var(--danger-color);
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
}

.metric-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 24px;
    text-align: center;
    transition: transform 0.2s, box-shadow 0.2s;
    box-shadow: 0 2px 8px var(--shadow-color);
    position: relative;
    overflow: hidden;
}

.metric-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px var(--shadow-color);
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.metric-value {
    font-size: 3rem;
    font-weight: 800;
    margin: 12px 0;
    line-height: 1;
    color: var(--primary-color);
}

.metric-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.metric-trend {
    font-size: 0.85rem;
    margin-top: 8px;
    padding: 4px 8px;
    border-radius: 20px;
    display: inline-block;
}

.trend-up {
    background: rgba(5, 150, 105, 0.1);
    color: var(--success-color);
}

.trend-down {
    background: rgba(220, 38, 38, 0.1);
    color: var(--danger-color);
}

.trend-stable {
    background: rgba(79, 70, 229, 0.1);
    color: var(--primary-color);
}

.chart-section {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 30px;
    margin: 30px 0;
    box-shadow: 0 2px 8px var(--shadow-color);
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 10px;
}

.chart-container {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 20px;
    min-height: 300px;
    border: 1px solid var(--border-color);
}

.package-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
    margin-top: 20px;
}

.package-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 16px;
    font-size: 0.9rem;
    transition: transform 0.2s;
}

.package-card:hover {
    transform: translateY(-1px);
}

.package-name {
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-primary);
}

.package-coverage {
    font-size: 1.2rem;
    font-weight: 700;
}

.coverage-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
    margin: 8px 0;
    overflow: hidden;
}

.coverage-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.coverage-excellent {
    background: linear-gradient(90deg, #059669, #34d399);
}

.coverage-good {
    background: linear-gradient(90deg, #d97706, #fbbf24);
}

.coverage-poor {
    background: linear-gradient(90deg, #dc2626, #f87171);
}

.status-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-passing {
    background: rgba(5, 150, 105, 0.1);
    color: var(--success-color);
}

.status-failing {
    background: rgba(220, 38, 38, 0.1);
    color: var(--danger-color);
}

.status-warning {
    background: rgba(217, 119, 6, 0.1);
    color: var(--warning-color);
}

.refresh-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--success-color);
    color: white;
    padding: 10px 15px;
    border-radius: 8px;
    font-size: 0.9rem;
    transform: translateY(-100px);
    transition: transform 0.3s ease;
    z-index: 1000;
}

.refresh-indicator.show {
    transform: translateY(0);
}

.theme-toggle {
    position: fixed;
    top: 20px;
    left: 20px;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.2s ease;
    z-index: 1000;
}

.theme-toggle:hover {
    transform: scale(1.1);
}

@media (max-width: 768px) {
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .btn {
        text-align: center;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .metric-value {
        font-size: 2.5rem;
    }
}

/* Animation classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Dark mode media query */
@media (prefers-color-scheme: dark) {
    [data-theme="auto"] {
        --bg-primary: #0f172a;
        --bg-secondary: #1e293b;
        --bg-tertiary: #334155;
        --text-primary: #f1f5f9;
        --text-secondary: #94a3b8;
        --border-color: #475569;
        --shadow-color: rgba(0, 0, 0, 0.3);
    }
}
EOF
}

# Generate JavaScript assets
generate_javascript() {
    cat > "$STATIC_DIR/js/coverage-dashboard.js" << 'EOF'
// Coverage Dashboard JavaScript
class CoverageDashboard {
    constructor() {
        this.theme = localStorage.getItem('coverage-theme') || 'auto';
        this.autoRefresh = false;
        this.refreshInterval = null;
        this.websocket = null;
        
        this.init();
    }
    
    init() {
        this.setTheme(this.theme);
        this.setupEventListeners();
        this.loadData();
        this.setupAutoRefresh();
        this.setupWebSocket();
    }
    
    setupEventListeners() {
        // Theme toggle
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggleTheme());
        }
        
        // Refresh button
        const refreshBtn = document.getElementById('refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshData());
        }
        
        // Auto-refresh toggle
        const autoRefreshToggle = document.getElementById('auto-refresh');
        if (autoRefreshToggle) {
            autoRefreshToggle.addEventListener('change', (e) => {
                this.autoRefresh = e.target.checked;
                if (this.autoRefresh) {
                    this.startAutoRefresh();
                } else {
                    this.stopAutoRefresh();
                }
            });
        }
        
        // Package filter
        const packageFilter = document.getElementById('package-filter');
        if (packageFilter) {
            packageFilter.addEventListener('input', (e) => this.filterPackages(e.target.value));
        }
        
        // Export buttons
        document.querySelectorAll('[data-export]').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const format = e.target.dataset.export;
                this.exportData(format);
            });
        });
    }
    
    setTheme(theme) {
        this.theme = theme;
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('coverage-theme', theme);
        
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.textContent = theme === 'dark' ? '☀️' : '🌙';
        }
    }
    
    toggleTheme() {
        const newTheme = this.theme === 'dark' ? 'light' : 'dark';
        this.setTheme(newTheme);
    }
    
    async loadData() {
        try {
            const [coverageData, packageData, trendsData] = await Promise.all([
                this.fetchJSON('../coverage-summary.json'),
                this.fetchJSON('../package-coverage.json'),
                this.fetchJSON('../trends/latest-trends.json')
            ]);
            
            this.updateDashboard(coverageData, packageData, trendsData);
        } catch (error) {
            console.error('Error loading data:', error);
            this.showError('Failed to load coverage data');
        }
    }
    
    async fetchJSON(url) {
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    }
    
    updateDashboard(coverageData, packageData, trendsData) {
        this.updateMetrics(coverageData);
        this.updatePackages(packageData);
        this.updateTrends(trendsData);
        this.updateCharts(coverageData, trendsData);
    }
    
    updateMetrics(data) {
        const elements = {
            'current-coverage': data.overall_coverage + '%',
            'target-coverage': data.target_coverage + '%',
            'coverage-status': data.status === 'passing' ? '✅ PASS' : '❌ FAIL',
            'last-updated': new Date(data.timestamp).toLocaleString()
        };
        
        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
                element.classList.add('fade-in');
            }
        });
        
        // Update status classes
        const statusElement = document.getElementById('coverage-status');
        if (statusElement) {
            statusElement.className = `metric-value ${data.status === 'passing' ? 'status-passing' : 'status-failing'}`;
        }
    }
    
    updatePackages(data) {
        const container = document.getElementById('package-grid');
        if (!container || !data.packages) return;
        
        container.innerHTML = '';
        
        data.packages.forEach(pkg => {
            const card = this.createPackageCard(pkg);
            container.appendChild(card);
        });
    }
    
    createPackageCard(pkg) {
        const card = document.createElement('div');
        card.className = 'package-card fade-in';
        card.dataset.package = pkg.package;
        
        const coverage = pkg.coverage.toFixed(1);
        const statusClass = pkg.coverage >= 75 ? 'coverage-excellent' : 
                           pkg.coverage >= 60 ? 'coverage-good' : 'coverage-poor';
        
        card.innerHTML = `
            <div class="package-name">${pkg.package}</div>
            <div class="package-coverage ${statusClass}">${coverage}%</div>
            <div class="coverage-bar">
                <div class="coverage-fill ${statusClass}" style="width: ${coverage}%"></div>
            </div>
            <div class="package-details">
                ${pkg.covered}/${pkg.functions} functions
            </div>
        `;
        
        return card;
    }
    
    updateTrends(data) {
        if (!data) return;
        
        const trendElement = document.getElementById('coverage-trend');
        if (trendElement) {
            const trendClass = `trend-${data.trend}`;
            trendElement.className = `metric-trend ${trendClass}`;
            trendElement.textContent = data.trend.charAt(0).toUpperCase() + data.trend.slice(1);
        }
    }
    
    updateCharts(coverageData, trendsData) {
        // This would integrate with Chart.js or similar
        // For now, we'll create a simple trend visualization
        if (trendsData && trendsData.history) {
            this.renderTrendChart(trendsData.history);
        }
    }
    
    renderTrendChart(history) {
        const canvas = document.getElementById('trend-chart');
        if (!canvas) return;
        
        const ctx = canvas.getContext('2d');
        const width = canvas.width;
        const height = canvas.height;
        
        // Clear canvas
        ctx.clearRect(0, 0, width, height);
        
        // Simple line chart
        const data = history.slice(-20); // Last 20 data points
        if (data.length < 2) return;
        
        const maxCoverage = Math.max(...data.map(d => d.coverage));
        const minCoverage = Math.min(...data.map(d => d.coverage));
        const range = maxCoverage - minCoverage || 1;
        
        ctx.strokeStyle = '#4f46e5';
        ctx.lineWidth = 2;
        ctx.beginPath();
        
        data.forEach((point, index) => {
            const x = (index / (data.length - 1)) * width;
            const y = height - ((point.coverage - minCoverage) / range) * height;
            
            if (index === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        });
        
        ctx.stroke();
    }
    
    filterPackages(query) {
        const packages = document.querySelectorAll('.package-card');
        packages.forEach(card => {
            const packageName = card.dataset.package.toLowerCase();
            const visible = packageName.includes(query.toLowerCase());
            card.style.display = visible ? 'block' : 'none';
        });
    }
    
    async refreshData() {
        this.showRefreshIndicator();
        await this.loadData();
        setTimeout(() => this.hideRefreshIndicator(), 2000);
    }
    
    showRefreshIndicator() {
        const indicator = document.getElementById('refresh-indicator');
        if (indicator) {
            indicator.classList.add('show');
        }
    }
    
    hideRefreshIndicator() {
        const indicator = document.getElementById('refresh-indicator');
        if (indicator) {
            indicator.classList.remove('show');
        }
    }
    
    startAutoRefresh() {
        this.refreshInterval = setInterval(() => {
            this.refreshData();
        }, 30000); // Refresh every 30 seconds
    }
    
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }
    
    setupWebSocket() {
        // WebSocket for real-time updates (if server supports it)
        try {
            this.websocket = new WebSocket('ws://localhost:8080/ws/coverage');
            
            this.websocket.onmessage = (event) => {
                const data = JSON.parse(event.data);
                if (data.type === 'coverage_update') {
                    this.refreshData();
                }
            };
            
            this.websocket.onerror = () => {
                // Fallback to auto-refresh if WebSocket fails
                console.log('WebSocket failed, falling back to polling');
            };
        } catch (error) {
            // WebSocket not available, use polling
        }
    }
    
    exportData(format) {
        const url = `../api/export?format=${format}`;
        window.open(url, '_blank');
    }
    
    showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-error';
        errorDiv.textContent = message;
        document.body.appendChild(errorDiv);
        
        setTimeout(() => {
            errorDiv.remove();
        }, 5000);
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new CoverageDashboard();
});
EOF
}

# Generate dashboard HTML
generate_dashboard() {
    local theme=${1:-light}
    local interactive=${2:-false}
    
    print_color "$BLUE" "🌐 Generating interactive dashboard..."
    
    # Ensure coverage data exists
    if [ ! -f "$COVERAGE_FILE" ]; then
        print_color "$YELLOW" "⚠️  No coverage data found, generating..."
        go test -timeout 30m -coverprofile="$COVERAGE_FILE" -covermode=atomic ./...
    fi
    
    local current_coverage=$(go tool cover -func="$COVERAGE_FILE" | grep "total:" | awk '{print $3}' | sed 's/%//')
    local threshold=${OVERALL_THRESHOLD:-85}
    
    cat > "$REPORTS_DIR/dashboard.html" << EOF
<!DOCTYPE html>
<html lang="en" data-theme="$theme">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coverage Dashboard - The Moment Backend</title>
    <link rel="stylesheet" href="static/css/coverage-dashboard.css">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📊</text></svg>">
    <meta name="description" content="Real-time test coverage dashboard for The Moment Backend">
</head>
<body>
    <!-- Theme Toggle -->
    <div class="theme-toggle" id="theme-toggle">🌙</div>
    
    <!-- Refresh Indicator -->
    <div class="refresh-indicator" id="refresh-indicator">
        📊 Coverage data updated
    </div>
    
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <h1>📊 Coverage Dashboard</h1>
                <p>Real-time test coverage monitoring for The Moment Backend</p>
            </div>
        </div>
        
        <!-- Controls -->
        <div class="controls">
            <button class="btn" id="refresh-btn">🔄 Refresh</button>
            <label>
                <input type="checkbox" id="auto-refresh"> Auto-refresh (30s)
            </label>
            <input type="text" id="package-filter" placeholder="Filter packages..." style="padding: 8px; border-radius: 4px; border: 1px solid var(--border-color);">
            <button class="btn btn-secondary" data-export="json">📄 Export JSON</button>
            <button class="btn btn-secondary" data-export="csv">📊 Export CSV</button>
            <a href="coverage-detail.html" class="btn">🔍 Detailed View</a>
        </div>
        
        <!-- Metrics Grid -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-label">Overall Coverage</div>
                <div class="metric-value" id="current-coverage">${current_coverage}%</div>
                <div class="metric-trend trend-stable" id="coverage-trend">Stable</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-label">Target Threshold</div>
                <div class="metric-value" id="target-coverage">${threshold}%</div>
                <div class="metric-trend trend-stable">Required</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-label">Status</div>
                <div class="metric-value status-passing" id="coverage-status">✅ PASS</div>
                <div class="metric-trend trend-up">Meets Target</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-label">Last Updated</div>
                <div class="metric-value" style="font-size: 1.2rem;" id="last-updated">${ISO_TIMESTAMP}</div>
                <div class="metric-trend trend-stable">Latest</div>
            </div>
        </div>
        
        <!-- Coverage Trends Chart -->
        <div class="chart-section">
            <h2 class="section-title">📈 Coverage Trends</h2>
            <div class="chart-container">
                <canvas id="trend-chart" width="800" height="300"></canvas>
            </div>
        </div>
        
        <!-- Package Coverage -->
        <div class="chart-section">
            <h2 class="section-title">📦 Package Coverage</h2>
            <div class="package-grid" id="package-grid">
                <!-- Populated by JavaScript -->
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="chart-section">
            <h2 class="section-title">⚡ Quick Actions</h2>
            <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                <button class="btn" onclick="window.open('../gates/latest-gate-results.json', '_blank')">📋 Gate Results</button>
                <button class="btn" onclick="window.open('coverage-detail.html', '_blank')">🔬 Source Analysis</button>
                <button class="btn" onclick="window.open('../badges/coverage-badge.svg', '_blank')">🏷️ Coverage Badge</button>
                <button class="btn btn-warning" onclick="location.reload()">🔄 Force Refresh</button>
            </div>
        </div>
        
        <!-- Footer -->
        <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid var(--border-color); color: var(--text-secondary);">
            <p>Generated by Agent C Coverage System • Updated: <span id="footer-timestamp">${ISO_TIMESTAMP}</span></p>
            <p>The Moment Backend • Real-time Coverage Monitoring</p>
        </div>
    </div>
    
    <script src="static/js/coverage-dashboard.js"></script>
</body>
</html>
EOF
    
    print_color "$GREEN" "✓ Dashboard generated: $REPORTS_DIR/dashboard.html"
}

# Generate detailed coverage report
generate_detailed_report() {
    print_color "$BLUE" "📋 Generating detailed coverage report..."
    
    # Generate standard Go coverage HTML
    go tool cover -html="$COVERAGE_FILE" -o "$REPORTS_DIR/coverage-detail.html"
    
    # Create enhanced detailed report
    cat > "$REPORTS_DIR/detailed.html" << 'EOF'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detailed Coverage Report - The Moment Backend</title>
    <link rel="stylesheet" href="static/css/coverage-dashboard.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <h1>🔬 Detailed Coverage Analysis</h1>
                <p>Function-level coverage analysis with source code</p>
            </div>
        </div>
        
        <div class="controls">
            <a href="dashboard.html" class="btn">← Back to Dashboard</a>
            <a href="coverage-detail.html" class="btn">View Go Coverage Report</a>
        </div>
        
        <div class="chart-section">
            <h2 class="section-title">📄 Go Coverage Report</h2>
            <iframe src="coverage-detail.html" style="width: 100%; height: 800px; border: 1px solid var(--border-color); border-radius: 8px;"></iframe>
        </div>
    </div>
</body>
</html>
EOF
    
    print_color "$GREEN" "✓ Detailed report generated: $REPORTS_DIR/detailed.html"
}

# Generate package analysis report
generate_package_report() {
    print_color "$BLUE" "📦 Generating package analysis report..."
    
    # Run package analysis and save data
    local package_data="$API_DIR/package-analysis.json"
    ./scripts/coverage_collect.sh --packages --json > /dev/null
    cp "$COVERAGE_DIR/package-coverage.json" "$package_data"
    
    cat > "$REPORTS_DIR/packages.html" << 'EOF'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Package Coverage Analysis - The Moment Backend</title>
    <link rel="stylesheet" href="static/css/coverage-dashboard.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <h1>📦 Package Coverage Analysis</h1>
                <p>Detailed package-level coverage breakdown</p>
            </div>
        </div>
        
        <div class="controls">
            <a href="dashboard.html" class="btn">← Back to Dashboard</a>
            <input type="text" id="package-search" placeholder="Search packages..." style="padding: 8px; border-radius: 4px; border: 1px solid var(--border-color);">
            <select id="sort-by" style="padding: 8px; border-radius: 4px; border: 1px solid var(--border-color);">
                <option value="coverage">Sort by Coverage</option>
                <option value="name">Sort by Name</option>
                <option value="functions">Sort by Functions</option>
            </select>
        </div>
        
        <div class="chart-section">
            <h2 class="section-title">📊 Package Performance</h2>
            <div id="package-analysis-grid" class="package-grid">
                <!-- Populated by JavaScript -->
            </div>
        </div>
    </div>
    
    <script>
        // Load and display package analysis
        fetch('api/package-analysis.json')
            .then(response => response.json())
            .then(data => {
                displayPackageAnalysis(data.packages);
            });
        
        function displayPackageAnalysis(packages) {
            const grid = document.getElementById('package-analysis-grid');
            grid.innerHTML = '';
            
            packages.forEach(pkg => {
                const card = document.createElement('div');
                card.className = 'package-card';
                
                const coverage = pkg.coverage.toFixed(1);
                const statusClass = pkg.coverage >= 75 ? 'status-passing' : 'status-failing';
                
                card.innerHTML = `
                    <div class="package-name">${pkg.package}</div>
                    <div class="package-coverage ${statusClass}">${coverage}%</div>
                    <div class="coverage-bar">
                        <div class="coverage-fill" style="width: ${coverage}%; background: ${pkg.coverage >= 75 ? 'var(--success-color)' : 'var(--danger-color)'}"></div>
                    </div>
                    <div style="margin-top: 10px; font-size: 0.9rem; color: var(--text-secondary);">
                        <div>Functions: ${pkg.functions}</div>
                        <div>Covered: ${pkg.covered}</div>
                        <div>Uncovered: ${pkg.uncovered}</div>
                        <div>Rate: ${((pkg.covered / pkg.functions) * 100).toFixed(1)}%</div>
                    </div>
                `;
                
                grid.appendChild(card);
            });
        }
    </script>
</body>
</html>
EOF
    
    print_color "$GREEN" "✓ Package report generated: $REPORTS_DIR/packages.html"
}

# Generate API endpoints for programmatic access
generate_api_endpoints() {
    print_color "$BLUE" "🔌 Generating API endpoints..."
    
    # Coverage summary endpoint
    local current_coverage=$(go tool cover -func="$COVERAGE_FILE" | grep "total:" | awk '{print $3}' | sed 's/%//')
    
    cat > "$API_DIR/summary.json" << EOF
{
    "timestamp": "$ISO_TIMESTAMP",
    "overall_coverage": $current_coverage,
    "target_coverage": ${OVERALL_THRESHOLD:-85},
    "status": "$([ $(echo "$current_coverage >= ${OVERALL_THRESHOLD:-85}" | bc -l) -eq 1 ] && echo 'passing' || echo 'failing')",
    "margin": $(echo "$current_coverage - ${OVERALL_THRESHOLD:-85}" | bc),
    "generated_by": "Agent C Coverage System"
}
EOF
    
    # Export endpoints
    cat > "$API_DIR/export" << 'EOF'
#!/bin/bash
# API Export endpoint
format=${1:-json}
case $format in
    json)
        cat summary.json
        ;;
    csv)
        echo "package,coverage,functions,covered,uncovered"
        jq -r '.packages[] | [.package, .coverage, .functions, .covered, .uncovered] | @csv' package-analysis.json
        ;;
    *)
        echo '{"error": "Unsupported format"}'
        ;;
esac
EOF
    chmod +x "$API_DIR/export"
    
    print_color "$GREEN" "✓ API endpoints generated in: $API_DIR/"
}

# Start HTTP server for reports
start_server() {
    local port=${1:-8080}
    
    print_color "$BLUE" "🚀 Starting coverage report server..."
    print_color "$CYAN" "   Server URL: http://localhost:$port"
    print_color "$CYAN" "   Dashboard: http://localhost:$port/dashboard.html"
    
    cd "$REPORTS_DIR"
    
    # Try Python 3 first, then Python 2
    if command -v python3 >/dev/null 2>&1; then
        python3 -m http.server "$port"
    elif command -v python >/dev/null 2>&1; then
        python -m SimpleHTTPServer "$port"
    else
        print_color "$RED" "❌ Python not found, cannot start server"
        exit 1
    fi
}

# Watch for coverage changes
watch_coverage() {
    print_color "$BLUE" "👀 Watching for coverage changes..."
    
    if ! command -v fswatch >/dev/null 2>&1; then
        print_color "$YELLOW" "⚠️  fswatch not installed, using simple polling"
        
        while true; do
            sleep 30
            if [ -f "$COVERAGE_FILE" ]; then
                local mtime=$(stat -c %Y "$COVERAGE_FILE" 2>/dev/null || stat -f %m "$COVERAGE_FILE" 2>/dev/null)
                local now=$(date +%s)
                local age=$((now - mtime))
                
                if [ $age -lt 60 ]; then  # Updated in last minute
                    print_color "$GREEN" "🔄 Coverage updated, regenerating reports..."
                    generate_all_reports
                fi
            fi
        done
    else
        fswatch -o "$COVERAGE_FILE" | while read -r event; do
            print_color "$GREEN" "🔄 Coverage file changed, regenerating reports..."
            sleep 2  # Brief delay to ensure file is fully written
            generate_all_reports
        done
    fi
}

# Generate all report types
generate_all_reports() {
    print_color "$BLUE" "📊 Generating all coverage reports..."
    
    setup_directories
    generate_css
    generate_javascript
    generate_dashboard
    generate_detailed_report
    generate_package_report
    generate_api_endpoints
    
    # Generate index page
    cat > "$REPORTS_DIR/index.html" << EOF
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coverage Reports - The Moment Backend</title>
    <link rel="stylesheet" href="static/css/coverage-dashboard.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <h1>📊 Coverage Reports</h1>
                <p>Choose your coverage analysis view</p>
            </div>
        </div>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
            <div class="metric-card">
                <h3>🏠 Dashboard</h3>
                <p>Interactive overview with real-time updates</p>
                <a href="dashboard.html" class="btn" style="margin-top: 15px;">View Dashboard</a>
            </div>
            
            <div class="metric-card">
                <h3>🔬 Detailed Analysis</h3>
                <p>Function-level coverage with source code</p>
                <a href="detailed.html" class="btn" style="margin-top: 15px;">View Details</a>
            </div>
            
            <div class="metric-card">
                <h3>📦 Package Analysis</h3>
                <p>Package-level coverage breakdown</p>
                <a href="packages.html" class="btn" style="margin-top: 15px;">View Packages</a>
            </div>
        </div>
    </div>
</body>
</html>
EOF
    
    print_color "$GREEN" "✓ All reports generated successfully"
}

# Main execution
main() {
    local report_type="dashboard"
    local theme="light"
    local interactive=false
    local serve=false
    local watch=false
    local port=8080
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --type)
                report_type="$2"
                shift 2
                ;;
            --output)
                REPORTS_DIR="$2"
                shift 2
                ;;
            --theme)
                theme="$2"
                shift 2
                ;;
            --interactive)
                interactive=true
                shift
                ;;
            --serve)
                serve=true
                shift
                ;;
            --port)
                port="$2"
                shift 2
                ;;
            --watch)
                watch=true
                shift
                ;;
            --help)
                usage
                ;;
            *)
                print_color "$RED" "Unknown option: $1"
                usage
                ;;
        esac
    done
    
    print_color "$BLUE" "📊 Coverage Reporting Agent C"
    print_color "$BLUE" "============================="
    echo
    
    # Ensure coverage data exists
    if [ ! -f "$COVERAGE_FILE" ]; then
        print_color "$YELLOW" "⚠️  No coverage data found, running collection..."
        ./scripts/coverage_collect.sh --packages --json || {
            print_color "$RED" "❌ Failed to collect coverage data"
            exit 1
        }
    fi
    
    # Generate reports based on type
    case $report_type in
        dashboard)
            setup_directories
            generate_css "$theme"
            generate_javascript
            generate_dashboard "$theme" "$interactive"
            ;;
        detailed)
            generate_detailed_report
            ;;
        package)
            generate_package_report
            ;;
        api)
            generate_api_endpoints
            ;;
        all)
            generate_all_reports
            ;;
        *)
            print_color "$RED" "❌ Unknown report type: $report_type"
            exit 1
            ;;
    esac
    
    # Start server if requested
    if [ "$serve" = true ]; then
        if [ "$watch" = true ]; then
            # Start watching in background
            watch_coverage &
            WATCH_PID=$!
            
            # Cleanup on exit
            trap "kill $WATCH_PID 2>/dev/null" EXIT
        fi
        
        start_server "$port"
    elif [ "$watch" = true ]; then
        watch_coverage
    fi
    
    print_color "$GREEN" "✅ Coverage reporting complete"
    print_color "$CYAN" "📁 Reports available in: $REPORTS_DIR"
}

# Run main function with all arguments
main "$@"