#!/bin/bash
set -e

# create-tech-debt-waiver.sh - Formal tech debt waiver process
# This implements the 4-hour time-box approach with formal waiver documentation

WAIVER_TYPE=$1
REASON=$2
WAIVERS_DIR="tech-debt-waivers"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
WAIVER_FILE="${WAIVERS_DIR}/${WAIVER_TYPE}_${TIMESTAMP}.md"

if [ -z "$WAIVER_TYPE" ] || [ -z "$REASON" ]; then
    echo "Usage: $0 <waiver_type> <reason>"
    echo ""
    echo "Waiver Types:"
    echo "  coverage    - Test coverage below threshold"
    echo "  complexity  - Code complexity above limit"
    echo "  lint        - Linting violations"
    echo "  dependency  - Unresolved dependency issues"
    echo ""
    echo "Example:"
    echo "  $0 coverage 'Legacy code requires refactoring - scheduled for Sprint 15'"
    exit 1
fi

# Create waivers directory if it doesn't exist
mkdir -p "${WAIVERS_DIR}"

# Get git information
GIT_BRANCH=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
GIT_AUTHOR=$(git config user.name 2>/dev/null || echo "unknown")

# Create waiver document
cat > "${WAIVER_FILE}" << EOF
# Tech Debt Waiver: ${WAIVER_TYPE}

**Date:** $(date '+%Y-%m-%d %H:%M:%S')
**Author:** ${GIT_AUTHOR}
**Branch:** ${GIT_BRANCH}
**Commit:** ${GIT_COMMIT}

## Waiver Details

**Type:** ${WAIVER_TYPE}
**Reason:** ${REASON}

## Context

### Current Situation
- Code quality gate failed for: ${WAIVER_TYPE}
- Immediate fix would require significant refactoring
- Delivery timeline requires expedited merge

### Technical Debt Assessment
- **Severity:** (Low/Medium/High/Critical)
- **Affected Components:** 
- **Estimated Fix Time:** 
- **Business Impact:** 

### Remediation Plan

#### Immediate Actions
- [ ] Document known limitations
- [ ] Add monitoring/alerting if applicable
- [ ] Update team backlog with follow-up tasks

#### Future Work (Target Sprint/Date)
- [ ] Schedule refactoring work
- [ ] Allocate dedicated time for technical debt
- [ ] Update architecture documentation

### Risk Assessment

#### Risks of Deferring
- 
- 

#### Mitigation Strategies
- 
- 

## Approval

**Engineering Lead:** _Pending_
**Product Owner:** _Pending_ (if business impact)
**Expiry Date:** $(date -d '+30 days' '+%Y-%m-%d')

## Notes

EOF

echo "📝 Tech debt waiver created: ${WAIVER_FILE}"
echo ""
echo "⚠️  IMPORTANT: This waiver expires in 30 days"
echo "📋 Please complete the following:"
echo "   1. Fill in the technical debt assessment details"
echo "   2. Define concrete remediation plan with dates"
echo "   3. Get required approvals from engineering lead"
echo "   4. Add follow-up tasks to sprint backlog"
echo ""
echo "🔄 To check waiver status:"
echo "   ./scripts/check-waivers.sh"
echo ""
echo "✅ Temporary bypass granted for CI/CD pipeline"

# Create a bypass token that CI can check
echo "${WAIVER_TYPE}:${TIMESTAMP}:${REASON}" > ".temp_waiver_${WAIVER_TYPE}"

echo ""
echo "⏰ Remember: This is a time-boxed solution. Technical debt must be addressed!"