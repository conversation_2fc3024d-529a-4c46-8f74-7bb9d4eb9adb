#!/bin/bash

# Parallel Test Runner for Go Projects
# This script runs Go tests in parallel for faster execution

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
PARALLEL_WORKERS=0
COVERAGE_ENABLED=false
PACKAGE_PATH="./..."
VERBOSE=false
TIMEOUT="10m"
COVERAGE_FILE="coverage.out"
TEMP_DIR=".test-results"

# Function to print colored output
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to show usage
usage() {
    cat << EOF
Usage: $(basename "$0") [OPTIONS] [PACKAGE_PATH]

Run Go tests in parallel with optional coverage collection.

OPTIONS:
    -p, --parallel WORKERS   Number of parallel workers (default: CPU count)
    -c, --coverage          Enable coverage collection
    -v, --verbose           Verbose output
    -t, --timeout DURATION  Test timeout (default: 10m)
    -h, --help              Show this help message

EXAMPLES:
    $(basename "$0")                    # Run all tests in parallel
    $(basename "$0") -p 4               # Use 4 parallel workers
    $(basename "$0") --coverage         # With coverage
    $(basename "$0") services/          # Specific package

EOF
    exit 0
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--parallel)
            PARALLEL_WORKERS="$2"
            shift 2
            ;;
        -c|--coverage)
            COVERAGE_ENABLED=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -t|--timeout)
            TIMEOUT="$2"
            shift 2
            ;;
        -h|--help)
            usage
            ;;
        -*)
            print_color "$RED" "Unknown option: $1"
            usage
            ;;
        *)
            PACKAGE_PATH="$1"
            shift
            ;;
    esac
done

# Detect number of CPUs if not specified
if [ "$PARALLEL_WORKERS" -eq 0 ]; then
    if command -v nproc &> /dev/null; then
        PARALLEL_WORKERS=$(nproc)
    elif command -v sysctl &> /dev/null; then
        PARALLEL_WORKERS=$(sysctl -n hw.ncpu)
    else
        PARALLEL_WORKERS=4
    fi
fi

print_color "$BLUE" "🚀 Starting parallel test execution"
print_color "$BLUE" "   Workers: $PARALLEL_WORKERS"
print_color "$BLUE" "   Package: $PACKAGE_PATH"
print_color "$BLUE" "   Coverage: $COVERAGE_ENABLED"
print_color "$BLUE" "   Timeout: $TIMEOUT"
echo

# Create temporary directory for test results
mkdir -p "$TEMP_DIR"
rm -f "$TEMP_DIR"/*.out "$TEMP_DIR"/*.log

# Get list of packages to test
print_color "$YELLOW" "📦 Discovering packages..."
PACKAGES=$(go list "$PACKAGE_PATH" 2>/dev/null | grep -v '/mocks' | grep -v '/docs' || true)

if [ -z "$PACKAGES" ]; then
    print_color "$RED" "❌ No packages found to test"
    exit 1
fi

PACKAGE_COUNT=$(echo "$PACKAGES" | wc -l)
print_color "$GREEN" "✓ Found $PACKAGE_COUNT packages to test"
echo

# Function to run tests for a single package
run_package_test() {
    local pkg=$1
    local index=$2
    local pkg_name=$(basename "$pkg")
    local log_file="$TEMP_DIR/${index}_${pkg_name}.log"
    local status_file="$TEMP_DIR/${index}_${pkg_name}.status"
    
    # Prepare test command
    local test_cmd="go test -timeout=$TIMEOUT"
    
    if [ "$COVERAGE_ENABLED" = true ]; then
        local coverage_file="$TEMP_DIR/${index}_${pkg_name}.coverage"
        test_cmd="$test_cmd -coverprofile=$coverage_file -covermode=atomic"
    fi
    
    if [ "$VERBOSE" = true ]; then
        test_cmd="$test_cmd -v"
    fi
    
    test_cmd="$test_cmd $pkg"
    
    # Run test and capture output
    if $test_cmd > "$log_file" 2>&1; then
        echo "PASS" > "$status_file"
        if [ "$VERBOSE" = true ]; then
            echo -e "${GREEN}✓${NC} $pkg"
        fi
    else
        echo "FAIL" > "$status_file"
        if [ "$VERBOSE" = true ]; then
            echo -e "${RED}✗${NC} $pkg"
        fi
    fi
}

# Export function for parallel execution
export -f run_package_test
export RED GREEN YELLOW BLUE NC
export COVERAGE_ENABLED VERBOSE TIMEOUT TEMP_DIR

# Progress tracking
TOTAL_PACKAGES=$PACKAGE_COUNT
COMPLETED=0

# Function to update progress
update_progress() {
    COMPLETED=$((COMPLETED + 1))
    local percentage=$((COMPLETED * 100 / TOTAL_PACKAGES))
    printf "\r📊 Progress: [%-50s] %d%% (%d/%d)" \
        "$(printf '#%.0s' $(seq 1 $((percentage / 2))))" \
        "$percentage" "$COMPLETED" "$TOTAL_PACKAGES"
}

# Run tests in parallel with progress tracking
print_color "$YELLOW" "🔄 Running tests..."
echo

# Create a named pipe for progress updates
PROGRESS_PIPE="$TEMP_DIR/progress.pipe"
mkfifo "$PROGRESS_PIPE"

# Start progress monitor in background
(
    while read -r line < "$PROGRESS_PIPE"; do
        update_progress
    done
) &
PROGRESS_PID=$!

# Run tests in parallel
echo "$PACKAGES" | nl -nln | xargs -P "$PARALLEL_WORKERS" -I {} bash -c '
    pkg=$(echo "{}" | awk "{print \$2}")
    index=$(echo "{}" | awk "{print \$1}")
    run_package_test "$pkg" "$index"
    echo "done" > "'$PROGRESS_PIPE'"
'

# Clean up progress monitor
echo "exit" > "$PROGRESS_PIPE"
wait $PROGRESS_PID 2>/dev/null || true
rm -f "$PROGRESS_PIPE"

echo # New line after progress bar
echo

# Collect results
print_color "$YELLOW" "📊 Collecting results..."

FAILED_PACKAGES=""
PASSED_COUNT=0
FAILED_COUNT=0

# Check test results
for status_file in "$TEMP_DIR"/*.status; do
    if [ -f "$status_file" ]; then
        status=$(cat "$status_file")
        pkg_info=$(basename "$status_file" .status)
        
        if [ "$status" = "PASS" ]; then
            PASSED_COUNT=$((PASSED_COUNT + 1))
        else
            FAILED_COUNT=$((FAILED_COUNT + 1))
            log_file="${status_file%.status}.log"
            FAILED_PACKAGES="$FAILED_PACKAGES\n$pkg_info"
            
            # Show failure details
            if [ -f "$log_file" ]; then
                print_color "$RED" "\n❌ Failed: $pkg_info"
                tail -n 20 "$log_file" | sed 's/^/   /'
            fi
        fi
    fi
done

# Merge coverage files if coverage is enabled
if [ "$COVERAGE_ENABLED" = true ]; then
    print_color "$YELLOW" "\n📈 Merging coverage files..."
    
    # Create header for merged coverage file
    echo "mode: atomic" > "$COVERAGE_FILE"
    
    # Merge all coverage files
    for cov_file in "$TEMP_DIR"/*.coverage; do
        if [ -f "$cov_file" ]; then
            # Skip the mode line and append the rest
            tail -n +2 "$cov_file" >> "$COVERAGE_FILE" 2>/dev/null || true
        fi
    done
    
    # Calculate total coverage
    if [ -s "$COVERAGE_FILE" ]; then
        COVERAGE_PERCENT=$(go tool cover -func="$COVERAGE_FILE" | grep "total" | awk '{print $3}')
        print_color "$GREEN" "✓ Coverage: $COVERAGE_PERCENT"
    fi
fi

# Print summary
echo
print_color "$BLUE" "════════════════════════════════════════"
print_color "$BLUE" "           TEST SUMMARY                 "
print_color "$BLUE" "════════════════════════════════════════"
print_color "$GREEN" "  ✓ Passed: $PASSED_COUNT"
print_color "$RED" "  ✗ Failed: $FAILED_COUNT"
print_color "$BLUE" "  Total: $PACKAGE_COUNT packages"
print_color "$BLUE" "  Duration: ${SECONDS}s"
if [ "$COVERAGE_ENABLED" = true ] && [ -n "${COVERAGE_PERCENT:-}" ]; then
    print_color "$BLUE" "  Coverage: $COVERAGE_PERCENT"
fi
print_color "$BLUE" "════════════════════════════════════════"

# Clean up temporary files
if [ "$VERBOSE" = false ]; then
    rm -rf "$TEMP_DIR"
fi

# Exit with appropriate code
if [ "$FAILED_COUNT" -gt 0 ]; then
    exit 1
else
    print_color "$GREEN" "\n✅ All tests passed!"
    exit 0
fi