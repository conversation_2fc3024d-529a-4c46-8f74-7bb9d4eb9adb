#!/bin/bash

# Security Scan with SARIF Output
# This script runs various security scans and generates SARIF reports
# for GitHub Security tab integration

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
REPORT_DIR="security-reports"
SARIF_DIR="${REPORT_DIR}/sarif"

# Create directories
mkdir -p "${SARIF_DIR}"

echo -e "${BLUE}🔍 Running Security Scans with SARIF Output${NC}"
echo "================================================"

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to install tool if not exists
ensure_tool() {
    local tool=$1
    local install_cmd=$2
    
    if ! command_exists "$tool"; then
        echo -e "${YELLOW}Installing $tool...${NC}"
        eval "$install_cmd"
    fi
}

# Ensure required tools are installed
ensure_tool "gosec" "go install github.com/securego/gosec/v2/cmd/gosec@latest"
ensure_tool "nancy" "go install github.com/sonatype-nexus-community/nancy@latest"
ensure_tool "govulncheck" "go install golang.org/x/vuln/cmd/govulncheck@latest"
ensure_tool "staticcheck" "go install honnef.co/go/tools/cmd/staticcheck@latest"

# 1. Run Gosec with SARIF output
echo -e "\n${BLUE}Running Gosec (Static Security Analysis)...${NC}"
if gosec -fmt=sarif -out="${SARIF_DIR}/gosec.sarif" -stdout ./... 2>/dev/null; then
    echo -e "${GREEN}✓ Gosec scan completed${NC}"
else
    echo -e "${YELLOW}⚠ Gosec found issues (see ${SARIF_DIR}/gosec.sarif)${NC}"
fi

# 2. Run Nancy and convert to SARIF
echo -e "\n${BLUE}Running Nancy (Dependency Vulnerability Scan)...${NC}"
go list -json -deps ./... > "${REPORT_DIR}/deps.json"
if nancy sleuth -p "${REPORT_DIR}/deps.json" -o json > "${REPORT_DIR}/nancy.json" 2>/dev/null; then
    echo -e "${GREEN}✓ Nancy scan completed${NC}"
    
    # Convert Nancy JSON to SARIF format
    cat > "${SARIF_DIR}/nancy.sarif" << 'EOF'
{
  "$schema": "https://raw.githubusercontent.com/oasis-tcs/sarif-spec/master/Schemata/sarif-schema-2.1.0.json",
  "version": "2.1.0",
  "runs": [
    {
      "tool": {
        "driver": {
          "name": "Nancy",
          "informationUri": "https://github.com/sonatype-nexus-community/nancy",
          "rules": []
        }
      },
      "results": []
    }
  ]
}
EOF
    
    # Parse Nancy results and add to SARIF (requires jq)
    if command_exists "jq"; then
        # This is a simplified conversion - in production, use a proper converter
        jq -r '.vulnerable[]? | 
            {
                ruleId: .Vulnerabilities[0].Id,
                level: (if .Vulnerabilities[0].CvssScore >= 7 then "error" elif .Vulnerabilities[0].CvssScore >= 4 then "warning" else "note" end),
                message: {
                    text: (.Vulnerabilities[0].Title + " in " + .Coordinates)
                },
                locations: [{
                    physicalLocation: {
                        artifactLocation: {
                            uri: "go.mod"
                        }
                    }
                }]
            }' "${REPORT_DIR}/nancy.json" 2>/dev/null | jq -s '.' > "${REPORT_DIR}/nancy-results.json" || true
    fi
else
    echo -e "${YELLOW}⚠ Nancy found vulnerabilities${NC}"
fi

# 3. Run govulncheck
echo -e "\n${BLUE}Running Govulncheck (Go Vulnerability Check)...${NC}"
if govulncheck -json ./... > "${REPORT_DIR}/govulncheck.json" 2>/dev/null; then
    echo -e "${GREEN}✓ Govulncheck completed${NC}"
else
    echo -e "${YELLOW}⚠ Govulncheck found vulnerabilities${NC}"
fi

# 4. Run staticcheck with SARIF output
echo -e "\n${BLUE}Running Staticcheck (Advanced Static Analysis)...${NC}"
if staticcheck -f sarif ./... > "${SARIF_DIR}/staticcheck.sarif" 2>/dev/null; then
    echo -e "${GREEN}✓ Staticcheck completed${NC}"
else
    echo -e "${YELLOW}⚠ Staticcheck found issues${NC}"
fi

# 5. Combine all results into a summary
echo -e "\n${BLUE}Generating Summary Report...${NC}"
cat > "${REPORT_DIR}/security-summary.txt" << EOF
Security Scan Summary
====================
Date: $(date)

Scans Performed:
1. Gosec (Static Security Analysis)
2. Nancy (Dependency Vulnerabilities)
3. Govulncheck (Go Vulnerabilities)
4. Staticcheck (Code Quality)

SARIF Reports Generated:
$(ls -la "${SARIF_DIR}"/*.sarif 2>/dev/null || echo "No SARIF reports found")

To upload to GitHub Security tab:
1. Use github/codeql-action/upload-sarif@v3 action
2. Upload each SARIF file separately
3. View results in Security -> Code scanning alerts

Local Review:
- SARIF reports: ${SARIF_DIR}/
- JSON reports: ${REPORT_DIR}/
- Summary: ${REPORT_DIR}/security-summary.txt
EOF

# 6. Create a combined metrics file
if command_exists "jq"; then
    echo -e "\n${BLUE}Generating Security Metrics...${NC}"
    
    # Count issues from each tool
    GOSEC_COUNT=$(grep -c '"level"' "${SARIF_DIR}/gosec.sarif" 2>/dev/null || echo "0")
    NANCY_COUNT=$(jq '.vulnerable | length' "${REPORT_DIR}/nancy.json" 2>/dev/null || echo "0")
    
    cat > "${REPORT_DIR}/security-metrics.json" << EOF
{
  "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "metrics": {
    "gosec_issues": ${GOSEC_COUNT},
    "dependency_vulnerabilities": ${NANCY_COUNT},
    "total_issues": $((GOSEC_COUNT + NANCY_COUNT))
  },
  "reports": {
    "sarif": [
      "gosec.sarif",
      "nancy.sarif",
      "staticcheck.sarif"
    ],
    "json": [
      "nancy.json",
      "govulncheck.json"
    ]
  }
}
EOF
fi

echo -e "\n${GREEN}✅ Security scan completed!${NC}"
echo -e "Reports available in: ${REPORT_DIR}/"
echo -e "SARIF reports in: ${SARIF_DIR}/"

# Check if any critical issues were found
if [ -f "${REPORT_DIR}/nancy.json" ] && command_exists "jq"; then
    CRITICAL_COUNT=$(jq '[.vulnerable[]?.Vulnerabilities[]? | select(.CvssScore >= 9)] | length' "${REPORT_DIR}/nancy.json" 2>/dev/null || echo "0")
    if [ "$CRITICAL_COUNT" -gt 0 ]; then
        echo -e "\n${RED}❌ CRITICAL: Found $CRITICAL_COUNT critical vulnerabilities!${NC}"
        exit 1
    fi
fi

echo -e "\n${BLUE}Next steps:${NC}"
echo "1. Review the reports in ${REPORT_DIR}/"
echo "2. Fix any critical vulnerabilities"
echo "3. Upload SARIF reports to GitHub Security tab"
echo "4. Run 'make vuln-scan-update' to update vulnerability databases"