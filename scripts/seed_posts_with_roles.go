package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"time"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/config"

	"github.com/jackc/pgx/v5/pgxpool"
	_ "github.com/lib/pq"
)

// SeedData contains the seed configuration
type SeedData struct {
	OrganizationName string
	Users            []UserSeed
	PostsPerUser     int
}

// UserSeed represents a user to create
type UserSeed struct {
	DisplayName  string
	Phone        string
	PlatformRole string // super_admin, staff, user
	OrgRole      string // owner, admin, manager, member
}

func main() {
	if err := run(); err != nil {
		log.Fatal(err)
	}
}

func run() error {
	// Load configuration
	cfg := config.Load()

	// Connect to database
	ctx := context.Background()
	pool, err := pgxpool.New(ctx, cfg.Database.URL)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer pool.Close()

	// Create queries
	queries := db.New(pool)

	// Define seed data
	seedData := SeedData{
		OrganizationName: "Demo Organization",
		Users: []UserSeed{
			{
				DisplayName:  "Super Admin User",
				Phone:        "+19990000001",
				PlatformRole: "super_admin",
				OrgRole:      "", // Super admins don't need org roles
			},
			{
				DisplayName:  "Staff User",
				Phone:        "+19990000002",
				PlatformRole: "staff",
				OrgRole:      "", // Staff don't need org roles
			},
			{
				DisplayName:  "Organization Owner",
				Phone:        "+19990000003",
				PlatformRole: "user",
				OrgRole:      "owner",
			},
			{
				DisplayName:  "Organization Admin",
				Phone:        "+19990000004",
				PlatformRole: "user",
				OrgRole:      "admin",
			},
			{
				DisplayName:  "Organization Manager",
				Phone:        "+19990000005",
				PlatformRole: "user",
				OrgRole:      "manager",
			},
			{
				DisplayName:  "Organization Member",
				Phone:        "+19990000006",
				PlatformRole: "user",
				OrgRole:      "member",
			},
		},
		PostsPerUser: 3, // Create 3 posts per user
	}

	// Run seeding
	if err := runSeed(ctx, queries, seedData); err != nil {
		return fmt.Errorf("failed to seed data: %w", err)
	}

	log.Println("Successfully seeded posts with various user roles!")
	return nil
}

func runSeed(ctx context.Context, queries *db.Queries, data SeedData) error {
	// Create users
	users := make(map[string]db.User)
	var ownerUser db.User

	log.Println("Creating users...")
	for _, userSeed := range data.Users {
		// Check if user already exists
		existingUser, err := queries.GetUserByPhone(ctx, &userSeed.Phone)
		var user db.User

		if errors.Is(err, sql.ErrNoRows) {
			// Create user
			now := time.Now()
			params := db.CreateUserWithPhoneParams{
				DisplayName:           userSeed.DisplayName,
				Phone:                 &userSeed.Phone,
				PhoneVerifiedAt:       &now,
				InterfaceLanguage:     "en",
				CommunicationLanguage: "en",
			}

			user, err = queries.CreateUserWithPhone(ctx, params)
			if err != nil {
				return fmt.Errorf("failed to create user %s: %w", userSeed.DisplayName, err)
			}
			log.Printf("Created user: %s", userSeed.DisplayName)

			// Update platform role if needed
			if userSeed.PlatformRole != "" && userSeed.PlatformRole != "user" {
				err = queries.UpdateUserPlatformRole(ctx, db.UpdateUserPlatformRoleParams{
					ID:           user.ID,
					PlatformRole: db.PlatformRoleEnum(userSeed.PlatformRole),
				})
				if err != nil {
					return fmt.Errorf("failed to update platform role for %s: %w", userSeed.DisplayName, err)
				}
			}
		} else if err != nil {
			return fmt.Errorf("failed to check existing user: %w", err)
		} else {
			user = existingUser
			log.Printf("User %s already exists", userSeed.DisplayName)
		}

		users[userSeed.DisplayName] = user

		// Keep track of the owner for organization creation
		if userSeed.OrgRole == "owner" {
			ownerUser = user
		}
	}

	// Create organization
	log.Printf("Creating organization: %s", data.OrganizationName)

	// Check if organization already exists
	existingOrgs, err := queries.GetOrganizationByName(ctx, data.OrganizationName)
	if err != nil && !errors.Is(err, sql.ErrNoRows) {
		return fmt.Errorf("failed to check existing organization: %w", err)
	}

	var org db.Organization
	if err == nil {
		org = existingOrgs
		log.Printf("Organization %s already exists", data.OrganizationName)
	} else {
		// Create organization with the owner
		orgParams := db.CreateOrganizationParams{
			Name:        data.OrganizationName,
			Description: &[]string{"A demo organization with posts from users of various roles"}[0],
			OwnerUserID: ownerUser.ID,
			Status:      "active",
			ImageUrl:    &[]string{"https://example.com/demo-logo.png"}[0],
			ThemeColor:  &[]string{"#0066cc"}[0],
		}

		org, err = queries.CreateOrganization(ctx, orgParams)
		if err != nil {
			return fmt.Errorf("failed to create organization: %w", err)
		}
		log.Printf("Created organization: %s", org.Name)
	}

	// Add users to organization with their roles
	log.Println("Adding users to organization...")
	for _, userSeed := range data.Users {
		// Skip users without org roles (super_admin, staff)
		if userSeed.OrgRole == "" {
			continue
		}

		user := users[userSeed.DisplayName]

		// Check if membership already exists
		membership, err := queries.GetUserOrganizationMembership(ctx, db.GetUserOrganizationMembershipParams{
			UserID:         user.ID,
			OrganizationID: org.ID,
		})
		if err == nil {
			log.Printf("User %s already member of organization with role %s", userSeed.DisplayName, membership.Role)
		} else if errors.Is(err, sql.ErrNoRows) {
			// Add user to organization
			_, err = queries.AddUserToOrganization(ctx, db.AddUserToOrganizationParams{
				UserID:         user.ID,
				OrganizationID: org.ID,
				Role:           userSeed.OrgRole,
			})
			if err != nil {
				return fmt.Errorf("failed to add user %s to organization: %w", userSeed.DisplayName, err)
			}
			log.Printf("Added %s to organization as %s", userSeed.DisplayName, userSeed.OrgRole)
		} else {
			return fmt.Errorf("failed to check membership: %w", err)
		}
	}

	// Create posts for each user
	log.Println("Creating posts...")
	publishedAt := time.Now()
	postTemplates := []struct {
		TitleSuffix   string
		ContentPrefix string
	}{
		{
			TitleSuffix:   "Announcement",
			ContentPrefix: "Important announcement from",
		},
		{
			TitleSuffix:   "Update",
			ContentPrefix: "Latest update provided by",
		},
		{
			TitleSuffix:   "News",
			ContentPrefix: "Breaking news shared by",
		},
	}

	for userName := range users {
		for i, template := range postTemplates {
			if i >= data.PostsPerUser {
				break
			}

			// Generate unique slug
			slug := fmt.Sprintf("%s-%s-%d", slugify(userName), slugify(template.TitleSuffix), time.Now().Unix())

			// Create content as JSON
			content := map[string]interface{}{
				"type": "doc",
				"content": []map[string]interface{}{
					{
						"type": "paragraph",
						"content": []map[string]interface{}{
							{
								"type": "text",
								"text": fmt.Sprintf("%s %s. This demonstrates that posts from all user roles are returned by the API.", template.ContentPrefix, userName),
							},
						},
					},
				},
			}

			contentJSON, err := json.Marshal(content)
			if err != nil {
				return fmt.Errorf("failed to marshal content: %w", err)
			}

			// Create post
			postParams := db.CreatePostParams{
				OrganizationID: org.ID,
				Title:          fmt.Sprintf("%s %s", userName, template.TitleSuffix),
				Slug:           slug,
				Content:        contentJSON,
				Status:         "published",
				AuthorID:       users[userName].ID,
				PublishedAt:    &publishedAt,
			}

			post, err := queries.CreatePost(ctx, postParams)
			if err != nil {
				// Skip if post already exists (unique constraint violation)
				if err.Error() == "pq: duplicate key value violates unique constraint" {
					log.Printf("Post already exists: %s", postParams.Title)
					continue
				}
				return fmt.Errorf("failed to create post: %w", err)
			}

			log.Printf("Created post: %s (by %s)", post.Title, userName)
		}
	}

	return nil
}

// Helper function to create URL-friendly slugs
func slugify(s string) string {
	// Simple slugification - in production, use a proper slugify library
	slug := ""
	for _, c := range s {
		if (c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') || (c >= '0' && c <= '9') {
			slug += string(c)
		} else if c == ' ' || c == '-' {
			slug += "-"
		}
	}
	return slug
}
