#!/bin/bash

# Script to set up Git hooks for the project

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

echo "🔧 Setting up Git hooks..."

# Configure Git to use our hooks directory
cd "$PROJECT_ROOT"
git config core.hooksPath .githooks

echo "✅ Git hooks configured successfully!"
echo ""
echo "The following hooks are now active:"
echo "  - pre-commit: Validates schema-code synchronization"
echo ""
echo "To disable hooks temporarily, run:"
echo "  git config --unset core.hooksPath"
echo ""
echo "To re-enable hooks, run:"
echo "  git config core.hooksPath .githooks"