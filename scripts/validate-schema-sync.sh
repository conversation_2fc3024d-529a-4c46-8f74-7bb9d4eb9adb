#!/bin/bash

# Script to validate that database schema and Go code are in sync
# This script checks that platform roles defined in code match those in the database

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"

echo "🔍 Validating schema-code synchronization..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Track if any errors are found
ERRORS_FOUND=0

# Function to extract platform roles from Go code
get_go_platform_roles() {
    grep -E 'PlatformRole(User|Staff|SuperAdmin)\s*=\s*"[^"]*"' "$PROJECT_ROOT/internal/authorization/roles.go" | \
    sed -E 's/.*=\s*"([^"]*)".*/\1/' | sort | uniq
}

# Function to extract platform roles from migrations
get_db_platform_roles() {
    # Look for the most recent migration that defines platform roles
    local migration_file=$(ls -1 "$PROJECT_ROOT/db/migrations/"*platform_role*.up.sql 2>/dev/null | tail -1)
    
    if [ -z "$migration_file" ]; then
        echo "Warning: No platform role migration found" >&2
        return 1
    fi
    
    # Extract roles from enum definition or check constraint
    if grep -q "CREATE TYPE platform_role_enum" "$migration_file"; then
        # Extract from enum type
        grep -A5 "CREATE TYPE platform_role_enum AS ENUM" "$migration_file" | \
        grep -oE "'[^']+'" | tr -d "'" | sort | uniq
    else
        # Extract from check constraint
        grep -E "CHECK.*platform_role.*IN" "$migration_file" | \
        grep -oE "'[^']+'" | tr -d "'" | sort | uniq
    fi
}

# Function to check for hardcoded role strings in the codebase
check_hardcoded_roles() {
    echo "Checking for hardcoded role strings..."
    
    # Define valid role constants
    local valid_constants=(
        "PlatformRoleUser"
        "PlatformRoleStaff"
        "PlatformRoleSuperAdmin"
        "PlatformRoleMember"
        "PlatformRoleAdmin"
    )
    
    # Search for hardcoded strings that look like roles
    local hardcoded=$(grep -r --include="*.go" -E '"(user|staff|super_admin|member|admin|superadmin)"' "$PROJECT_ROOT" | \
        grep -v "roles.go" | \
        grep -E "(platform_role|PlatformRole)" | \
        grep -v "_test.go" | \
        grep -v "migrations" || true)
    
    if [ -n "$hardcoded" ]; then
        echo -e "${YELLOW}Warning: Found potential hardcoded role strings:${NC}"
        echo "$hardcoded" | head -10
        echo -e "${YELLOW}Consider using constants from authorization.roles package instead${NC}"
    fi
}

# Main validation
echo "1. Extracting platform roles from Go code..."
GO_ROLES=$(get_go_platform_roles)
echo "   Found Go roles: $GO_ROLES"

echo ""
echo "2. Extracting platform roles from database migrations..."
DB_ROLES=$(get_db_platform_roles)
echo "   Found DB roles: $DB_ROLES"

echo ""
echo "3. Comparing roles..."

# Convert to arrays for comparison
IFS=$'\n' read -d '' -r -a go_array <<< "$GO_ROLES" || true
IFS=$'\n' read -d '' -r -a db_array <<< "$DB_ROLES" || true

# Check if all Go roles exist in DB
for role in "${go_array[@]}"; do
    if ! echo "$DB_ROLES" | grep -q "^$role$"; then
        echo -e "${RED}❌ Error: Go role '$role' not found in database schema${NC}"
        ERRORS_FOUND=$((ERRORS_FOUND + 1))
    fi
done

# Check if all DB roles exist in Go
for role in "${db_array[@]}"; do
    if ! echo "$GO_ROLES" | grep -q "^$role$"; then
        echo -e "${RED}❌ Error: Database role '$role' not found in Go code${NC}"
        ERRORS_FOUND=$((ERRORS_FOUND + 1))
    fi
done

if [ $ERRORS_FOUND -eq 0 ]; then
    echo -e "${GREEN}✅ All platform roles are synchronized between code and database${NC}"
else
    echo -e "${RED}❌ Found $ERRORS_FOUND synchronization errors${NC}"
fi

echo ""
echo "4. Checking for hardcoded role strings..."
check_hardcoded_roles

echo ""
echo "5. Validating SQLC generation..."

# Check if models.go exists and contains platform role
if [ -f "$PROJECT_ROOT/db/models.go" ]; then
    if grep -q "PlatformRole.*string" "$PROJECT_ROOT/db/models.go"; then
        echo -e "${GREEN}✅ SQLC models contain PlatformRole field${NC}"
    else
        echo -e "${RED}❌ Error: SQLC models missing PlatformRole field${NC}"
        echo "   Run 'make sqlc-generate' to regenerate models"
        ERRORS_FOUND=$((ERRORS_FOUND + 1))
    fi
else
    echo -e "${YELLOW}⚠️  Warning: models.go not found. Run 'make sqlc-generate'${NC}"
fi

echo ""
if [ $ERRORS_FOUND -gt 0 ]; then
    echo -e "${RED}❌ Validation failed with $ERRORS_FOUND errors${NC}"
    exit 1
else
    echo -e "${GREEN}✅ All validations passed!${NC}"
    exit 0
fi