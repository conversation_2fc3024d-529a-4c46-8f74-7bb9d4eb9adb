#!/bin/bash

# validate_swagger.sh - Validate Swagger documentation and API improvements
# Created as part of Plan 94 Phase 6 - Clean Up and Validate Documentation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 Swagger Documentation Validation (Plan 94)${NC}"
echo "=============================================="

# Define expected files
SWAGGER_YAML="docs/swagger.yaml"
SWAGGER_JSON="docs/swagger.json"
DOCS_GO="docs/docs.go"

# Check if Swagger files exist
echo "Checking Swagger documentation files..."
for file in "$SWAGGER_YAML" "$SWAGGER_JSON" "$DOCS_GO"; do
    if [ -f "$file" ]; then
        echo -e "  ${GREEN}✓ Found: $file${NC}"
    else
        echo -e "  ${RED}✗ Missing: $file${NC}"
        echo -e "${RED}Error: Run 'swag init -g cmd/api/main.go --parseDependency --parseInternal' to generate Swagger docs${NC}"
        exit 1
    fi
done

# Validate YAML syntax
echo -n "Validating YAML syntax... "
if command -v yamllint >/dev/null 2>&1; then
    if yamllint -d relaxed "$SWAGGER_YAML" >/dev/null 2>&1; then
        echo -e "${GREEN}✓${NC}"
    else
        echo -e "${RED}✗${NC}"
        echo -e "${RED}YAML validation errors:${NC}"
        yamllint -d relaxed "$SWAGGER_YAML"
        exit 1
    fi
else
    echo -e "${YELLOW}⚠ yamllint not installed, skipping YAML validation${NC}"
fi

# Validate JSON syntax
echo -n "Validating JSON syntax... "
if command -v jq >/dev/null 2>&1; then
    if jq empty "$SWAGGER_JSON" >/dev/null 2>&1; then
        echo -e "${GREEN}✓${NC}"
    else
        echo -e "${RED}✗${NC}"
        echo -e "${RED}JSON validation failed${NC}"
        exit 1
    fi
else
    echo -e "${YELLOW}⚠ jq not installed, skipping JSON validation${NC}"
fi

# Check for previously missing endpoints (Plan 94 improvements)
echo ""
echo "🎯 Validating Plan 94 improvements..."
echo "Checking for previously missing endpoints:"

declare -a REQUIRED_ENDPOINTS=(
    "/organizations/{orgID}/events/{eventID}/status"  # Event status update
    "/users/me/volunteer/applications"                # List user volunteer applications
    "/events/{eventId}/volunteer-applications"        # Apply to volunteer
    "/events/{eventId}/registrations"                 # List event registrations
    "/event-tags"                                     # Event tags management
)

MISSING_COUNT=0
for endpoint in "${REQUIRED_ENDPOINTS[@]}"; do
    if grep -q "$endpoint" "$SWAGGER_YAML"; then
        echo -e "  ${GREEN}✓ Found: $endpoint${NC}"
    else
        echo -e "  ${RED}✗ Missing: $endpoint${NC}"
        ((MISSING_COUNT++))
    fi
done

if [ $MISSING_COUNT -eq 0 ]; then
    echo -e "${GREEN}✅ All Plan 94 endpoints are documented!${NC}"
else
    echo -e "${RED}❌ $MISSING_COUNT endpoint(s) still missing from documentation${NC}"
    exit 1
fi

# Check for authentication security schemes
echo ""
echo "Checking authentication documentation..."
if grep -q "securityDefinitions:" "$SWAGGER_YAML" || grep -q "components:" "$SWAGGER_YAML"; then
    echo -e "  ${GREEN}✓ Security definitions found${NC}"
    
    # Check for JWT Bearer authentication
    if grep -q -i "bearer\|jwt" "$SWAGGER_YAML"; then
        echo -e "  ${GREEN}✓ JWT/Bearer authentication documented${NC}"
    else
        echo -e "  ${YELLOW}⚠ JWT/Bearer authentication may not be fully documented${NC}"
    fi
else
    echo -e "  ${RED}✗ Security definitions missing${NC}"
fi

# Validate server is running for live testing
echo ""
echo "Checking if development server is accessible..."
if command -v curl >/dev/null 2>&1; then
    if curl -s -f http://localhost:8080/docs/ >/dev/null 2>&1; then
        echo -e "  ${GREEN}✓ Swagger UI accessible at http://localhost:8080/docs/${NC}"
    else
        echo -e "  ${YELLOW}⚠ Development server not running. Start with 'make dev' to test Swagger UI${NC}"
    fi
else
    echo -e "  ${YELLOW}⚠ curl not available, skipping server check${NC}"
fi

# Generate statistics
echo ""
echo "📊 Swagger Documentation Statistics:"
echo "-----------------------------------"

# Count total endpoints
ENDPOINT_COUNT=$(grep -c -E "^\s+[a-z]+:" "$SWAGGER_YAML" || echo "0")
echo "Total HTTP methods: $ENDPOINT_COUNT"

# Count unique paths
PATH_COUNT=$(grep -c -E "^\s+/[^:]*:" "$SWAGGER_YAML" || echo "0")
echo "Total API paths: $PATH_COUNT"

# Count definitions/schemas
DEFINITION_COUNT=$(grep -c -E "^\s+[A-Z][^:]*:" "$SWAGGER_YAML" | head -n 1 || echo "0")
echo "Total schema definitions: $DEFINITION_COUNT"

# Check for specific HTTP methods
for method in GET POST PUT PATCH DELETE; do
    method_lower=$(echo "$method" | tr '[:upper:]' '[:lower:]')
    method_count=$(grep -i -c "^\s*${method_lower}:" "$SWAGGER_YAML" || echo "0")
    echo "$method endpoints: $method_count"
done

# File sizes
YAML_SIZE=$(ls -lh "$SWAGGER_YAML" | awk '{print $5}')
JSON_SIZE=$(ls -lh "$SWAGGER_JSON" | awk '{print $5}')
echo "Documentation size: YAML ($YAML_SIZE), JSON ($JSON_SIZE)"

echo ""
echo -e "${GREEN}✅ Swagger documentation validation completed successfully!${NC}"
echo ""
echo "💡 Next steps:"
echo "  • Start server: make dev"
echo "  • View docs: http://localhost:8080/docs/"
echo "  • Run tests: make test"
echo "  • API base URL: http://localhost:8080/api/v1"

# Optional: Show recent documentation updates
if command -v git >/dev/null 2>&1 && git rev-parse --git-dir > /dev/null 2>&1; then
    echo ""
    echo "📋 Recent documentation changes:"
    git log --oneline -5 -- docs/ | head -3 || echo "  No recent git history found"
fi