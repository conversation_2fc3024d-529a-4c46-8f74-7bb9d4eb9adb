#!/bin/bash

# Comprehensive Vulnerability Management Script
# This script provides a unified interface for vulnerability scanning and management

set -euo pipefail

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"
REPORT_DIR="${PROJECT_ROOT}/security-reports"
CACHE_DIR="${PROJECT_ROOT}/.security-cache"
CONFIG_FILE="${PROJECT_ROOT}/.security-config.yaml"

# Default values
SEVERITY_THRESHOLD="HIGH"
OUTPUT_FORMAT="text"
SCAN_TIMEOUT="300"

# Create necessary directories
mkdir -p "${REPORT_DIR}" "${CACHE_DIR}"

# Help function
show_help() {
    cat << EOF
Vulnerability Management Tool
============================

Usage: $0 [command] [options]

Commands:
  scan              Run all vulnerability scans
  report            Generate vulnerability reports
  check             Check against security policy
  update            Update vulnerability databases
  fix               Attempt to auto-fix vulnerabilities
  baseline          Create vulnerability baseline
  diff              Compare with baseline
  monitor           Continuous monitoring mode

Options:
  -s, --severity    Minimum severity (CRITICAL|HIGH|MEDIUM|LOW)
  -f, --format      Output format (text|json|sarif|html)
  -o, --output      Output file
  -t, --timeout     Scan timeout in seconds
  -v, --verbose     Verbose output
  -h, --help        Show this help

Examples:
  $0 scan --severity HIGH
  $0 report --format html --output report.html
  $0 check --severity CRITICAL
  $0 monitor --severity HIGH

EOF
}

# Parse command line arguments
parse_args() {
    COMMAND="${1:-help}"
    shift || true

    while [[ $# -gt 0 ]]; do
        case $1 in
            -s|--severity)
                SEVERITY_THRESHOLD="$2"
                shift 2
                ;;
            -f|--format)
                OUTPUT_FORMAT="$2"
                shift 2
                ;;
            -o|--output)
                OUTPUT_FILE="$2"
                shift 2
                ;;
            -t|--timeout)
                SCAN_TIMEOUT="$2"
                shift 2
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                echo -e "${RED}Unknown option: $1${NC}"
                show_help
                exit 1
                ;;
        esac
    done
}

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if command exists
check_command() {
    if ! command -v "$1" >/dev/null 2>&1; then
        log_error "$1 is not installed"
        return 1
    fi
    return 0
}

# Install missing tools
install_tools() {
    log_info "Checking required tools..."
    
    local tools=(
        "nancy:go install github.com/sonatype-nexus-community/nancy@latest"
        "gosec:go install github.com/securego/gosec/v2/cmd/gosec@latest"
        "govulncheck:go install golang.org/x/vuln/cmd/govulncheck@latest"
        "trivy:curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/install.sh | sh -s -- -b /usr/local/bin"
    )
    
    for tool_spec in "${tools[@]}"; do
        IFS=':' read -r tool install_cmd <<< "$tool_spec"
        if ! check_command "$tool"; then
            log_info "Installing $tool..."
            eval "$install_cmd"
        fi
    done
}

# Run Nancy scan
run_nancy_scan() {
    log_info "Running Nancy dependency scan..."
    
    local nancy_report="${REPORT_DIR}/nancy-report.json"
    
    if go list -json -deps ./... | nancy sleuth -o json > "$nancy_report" 2>/dev/null; then
        local vuln_count=$(jq '.vulnerable | length' "$nancy_report" 2>/dev/null || echo "0")
        if [ "$vuln_count" -eq 0 ]; then
            log_success "Nancy: No vulnerabilities found"
        else
            log_warning "Nancy: Found $vuln_count vulnerabilities"
        fi
        return $vuln_count
    else
        log_error "Nancy scan failed"
        return 1
    fi
}

# Run Gosec scan
run_gosec_scan() {
    log_info "Running Gosec static analysis..."
    
    local gosec_report="${REPORT_DIR}/gosec-report.json"
    
    if gosec -fmt=json -out="$gosec_report" ./... 2>/dev/null; then
        local issue_count=$(jq '.Issues | length' "$gosec_report" 2>/dev/null || echo "0")
        if [ "$issue_count" -eq 0 ]; then
            log_success "Gosec: No security issues found"
        else
            log_warning "Gosec: Found $issue_count security issues"
        fi
        return $issue_count
    else
        log_error "Gosec scan failed"
        return 1
    fi
}

# Run Govulncheck scan
run_govulncheck_scan() {
    log_info "Running Go vulnerability check..."
    
    local govuln_report="${REPORT_DIR}/govulncheck-report.json"
    
    if govulncheck -json ./... > "$govuln_report" 2>&1; then
        log_success "Govulncheck completed"
        return 0
    else
        log_warning "Govulncheck found vulnerabilities"
        return 1
    fi
}

# Run all scans
run_all_scans() {
    log_info "Starting comprehensive vulnerability scan..."
    
    local total_issues=0
    
    # Run each scan and collect results
    run_nancy_scan || ((total_issues+=$?))
    run_gosec_scan || ((total_issues+=$?))
    run_govulncheck_scan || ((total_issues+=$?))
    
    # Generate summary
    generate_summary "$total_issues"
    
    return $total_issues
}

# Generate summary report
generate_summary() {
    local total_issues=$1
    local summary_file="${REPORT_DIR}/vulnerability-summary.json"
    
    cat > "$summary_file" << EOF
{
  "timestamp": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "total_issues": $total_issues,
  "scans": {
    "nancy": {
      "report": "nancy-report.json",
      "vulnerabilities": $(jq '.vulnerable | length' "${REPORT_DIR}/nancy-report.json" 2>/dev/null || echo "0")
    },
    "gosec": {
      "report": "gosec-report.json",
      "issues": $(jq '.Issues | length' "${REPORT_DIR}/gosec-report.json" 2>/dev/null || echo "0")
    },
    "govulncheck": {
      "report": "govulncheck-report.json"
    }
  },
  "severity_threshold": "$SEVERITY_THRESHOLD"
}
EOF
    
    log_info "Summary report generated: $summary_file"
}

# Generate HTML report
generate_html_report() {
    local html_file="${OUTPUT_FILE:-${REPORT_DIR}/vulnerability-report.html}"
    
    log_info "Generating HTML report..."
    
    cat > "$html_file" << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>Vulnerability Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .critical { color: #d32f2f; font-weight: bold; }
        .high { color: #f57c00; font-weight: bold; }
        .medium { color: #fbc02d; }
        .low { color: #388e3c; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Vulnerability Scan Report</h1>
        <p>Generated: <script>document.write(new Date().toLocaleString());</script></p>
    </div>
EOF
    
    # Add scan results
    if [ -f "${REPORT_DIR}/vulnerability-summary.json" ]; then
        local nancy_count=$(jq '.scans.nancy.vulnerabilities' "${REPORT_DIR}/vulnerability-summary.json")
        local gosec_count=$(jq '.scans.gosec.issues' "${REPORT_DIR}/vulnerability-summary.json")
        
        cat >> "$html_file" << EOF
    <div class="section">
        <h2>Summary</h2>
        <table>
            <tr><th>Scanner</th><th>Issues Found</th><th>Status</th></tr>
            <tr><td>Nancy (Dependencies)</td><td>$nancy_count</td><td class="$([ $nancy_count -eq 0 ] && echo 'low' || echo 'high')">$([ $nancy_count -eq 0 ] && echo 'PASS' || echo 'FAIL')</td></tr>
            <tr><td>Gosec (Static Analysis)</td><td>$gosec_count</td><td class="$([ $gosec_count -eq 0 ] && echo 'low' || echo 'high')">$([ $gosec_count -eq 0 ] && echo 'PASS' || echo 'FAIL')</td></tr>
        </table>
    </div>
EOF
    fi
    
    echo "</body></html>" >> "$html_file"
    
    log_success "HTML report generated: $html_file"
}

# Check against security policy
check_security_policy() {
    log_info "Checking against security policy..."
    
    local violations=0
    
    # Check for critical vulnerabilities
    if [ -f "${REPORT_DIR}/nancy-report.json" ]; then
        local critical_vulns=$(jq '[.vulnerable[]?.Vulnerabilities[]? | select(.CvssScore >= 9)] | length' "${REPORT_DIR}/nancy-report.json" 2>/dev/null || echo "0")
        
        if [ "$critical_vulns" -gt 0 ]; then
            log_error "Policy violation: $critical_vulns critical vulnerabilities found"
            ((violations++))
        fi
    fi
    
    # Check for high severity issues
    if [ "$SEVERITY_THRESHOLD" = "HIGH" ] || [ "$SEVERITY_THRESHOLD" = "CRITICAL" ]; then
        local high_vulns=$(jq '[.vulnerable[]?.Vulnerabilities[]? | select(.CvssScore >= 7)] | length' "${REPORT_DIR}/nancy-report.json" 2>/dev/null || echo "0")
        
        if [ "$high_vulns" -gt 0 ]; then
            log_warning "Policy warning: $high_vulns high severity vulnerabilities found"
            ((violations++))
        fi
    fi
    
    if [ "$violations" -eq 0 ]; then
        log_success "Security policy check passed"
    else
        log_error "Security policy check failed with $violations violations"
    fi
    
    return $violations
}

# Update vulnerability databases
update_databases() {
    log_info "Updating vulnerability databases..."
    
    # Update Nancy database
    if command -v nancy >/dev/null 2>&1; then
        log_info "Updating Nancy database..."
        nancy update || log_warning "Failed to update Nancy database"
    fi
    
    # Update Trivy database
    if command -v trivy >/dev/null 2>&1; then
        log_info "Updating Trivy database..."
        trivy image --download-db-only || log_warning "Failed to update Trivy database"
    fi
    
    log_success "Database update completed"
}

# Monitor mode
monitor_vulnerabilities() {
    log_info "Starting vulnerability monitoring (Press Ctrl+C to stop)..."
    
    while true; do
        clear
        echo -e "${CYAN}=== Vulnerability Monitor ===${NC}"
        echo -e "Time: $(date)"
        echo -e "Severity Threshold: $SEVERITY_THRESHOLD\n"
        
        # Run scans
        run_all_scans
        
        # Check policy
        check_security_policy
        
        echo -e "\n${BLUE}Next scan in 60 seconds...${NC}"
        sleep 60
    done
}

# Create baseline
create_baseline() {
    log_info "Creating vulnerability baseline..."
    
    # Run full scan
    run_all_scans
    
    # Save baseline
    local baseline_dir="${CACHE_DIR}/baseline"
    mkdir -p "$baseline_dir"
    
    cp "${REPORT_DIR}"/*.json "$baseline_dir/" 2>/dev/null || true
    
    # Create baseline metadata
    cat > "$baseline_dir/metadata.json" << EOF
{
  "created": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")",
  "severity_threshold": "$SEVERITY_THRESHOLD",
  "total_issues": $(jq '.total_issues' "${REPORT_DIR}/vulnerability-summary.json" 2>/dev/null || echo "0")
}
EOF
    
    log_success "Baseline created in $baseline_dir"
}

# Compare with baseline
compare_baseline() {
    log_info "Comparing with baseline..."
    
    local baseline_dir="${CACHE_DIR}/baseline"
    
    if [ ! -d "$baseline_dir" ]; then
        log_error "No baseline found. Run '$0 baseline' first."
        return 1
    fi
    
    # Run current scan
    run_all_scans
    
    # Compare results
    local baseline_issues=$(jq '.total_issues' "$baseline_dir/metadata.json" 2>/dev/null || echo "0")
    local current_issues=$(jq '.total_issues' "${REPORT_DIR}/vulnerability-summary.json" 2>/dev/null || echo "0")
    
    local diff=$((current_issues - baseline_issues))
    
    if [ "$diff" -gt 0 ]; then
        log_warning "Vulnerability count increased by $diff since baseline"
    elif [ "$diff" -lt 0 ]; then
        log_success "Vulnerability count decreased by ${diff#-} since baseline"
    else
        log_info "No change in vulnerability count since baseline"
    fi
    
    return 0
}

# Main function
main() {
    parse_args "$@"
    
    # Change to project root
    cd "$PROJECT_ROOT"
    
    case "$COMMAND" in
        scan)
            install_tools
            run_all_scans
            ;;
        report)
            case "$OUTPUT_FORMAT" in
                html)
                    generate_html_report
                    ;;
                json)
                    cat "${REPORT_DIR}/vulnerability-summary.json"
                    ;;
                *)
                    cat "${REPORT_DIR}/vulnerability-summary.json" | jq '.'
                    ;;
            esac
            ;;
        check)
            check_security_policy
            ;;
        update)
            update_databases
            ;;
        baseline)
            create_baseline
            ;;
        diff)
            compare_baseline
            ;;
        monitor)
            monitor_vulnerabilities
            ;;
        *)
            show_help
            ;;
    esac
}

# Run main function
main "$@"