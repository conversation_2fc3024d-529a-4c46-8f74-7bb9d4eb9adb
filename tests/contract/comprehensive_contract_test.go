package contract

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"Membership-SAAS-System-Backend/internal/config"
	"Membership-SAAS-System-Backend/internal/server"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/getkin/kin-openapi/openapi3filter"
	"github.com/getkin/kin-openapi/routers/gorillamux"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestAllEndpointsDocumented ensures all API endpoints are documented in OpenAPI spec
func TestAllEndpointsDocumented(t *testing.T) {
	// Skip if no database configuration available
	if os.Getenv("DATABASE_URL") == "" && os.Getenv("DB_HOST") == "" {
		t.Skip("Skipping test - database configuration required")
	}

	// Load configuration
	cfg := config.Load()

	// Create server
	srv, err := server.New(cfg)
	require.NoError(t, err, "Failed to create server")

	// Load OpenAPI specification
	loader := openapi3.NewLoader()
	spec, err := loader.LoadFromFile(GetOpenAPISpecPath())
	require.NoError(t, err, "Failed to load OpenAPI specification")

	// Get all routes from Echo server
	echoRoutes := srv.Echo().Routes()

	// Track undocumented routes
	var undocumentedRoutes []string

	for _, route := range echoRoutes {
		// Skip non-API routes
		if route.Path == "/health" || route.Path == "/metrics" {
			continue
		}

		// Normalize Echo path to OpenAPI format
		normalizedPath := NormalizeEchoPath(route.Path)

		// Check if path exists in OpenAPI spec
		pathItem := spec.Paths.Find(normalizedPath)
		if pathItem == nil {
			undocumentedRoutes = append(undocumentedRoutes, route.Method+" "+route.Path)
			continue
		}

		// Check if method is documented
		var methodExists bool
		switch route.Method {
		case http.MethodGet:
			methodExists = pathItem.Get != nil
		case http.MethodPost:
			methodExists = pathItem.Post != nil
		case http.MethodPut:
			methodExists = pathItem.Put != nil
		case http.MethodPatch:
			methodExists = pathItem.Patch != nil
		case http.MethodDelete:
			methodExists = pathItem.Delete != nil
		}

		if !methodExists {
			undocumentedRoutes = append(undocumentedRoutes, route.Method+" "+route.Path)
		}
	}

	// Report undocumented routes
	if len(undocumentedRoutes) > 0 {
		t.Errorf("Found %d undocumented routes:", len(undocumentedRoutes))
		for _, route := range undocumentedRoutes {
			t.Errorf("  - %s", route)
		}
	}
}

// TestContractValidation tests request/response validation against OpenAPI spec
func TestContractValidation(t *testing.T) {
	// Skip if no database configuration available
	if os.Getenv("DATABASE_URL") == "" && os.Getenv("DB_HOST") == "" {
		t.Skip("Skipping test - database configuration required")
	}

	// Load configuration
	cfg := config.Load()

	// Create server
	srv, err := server.New(cfg)
	require.NoError(t, err, "Failed to create server")

	// Load OpenAPI specification
	loader := openapi3.NewLoader()
	spec, err := loader.LoadFromFile(GetOpenAPISpecPath())
	require.NoError(t, err, "Failed to load OpenAPI specification")

	// Create router from spec
	router, err := gorillamux.NewRouter(spec)
	require.NoError(t, err, "Failed to create router from OpenAPI spec")

	// Test cases for contract validation
	testCases := []struct {
		name       string
		method     string
		path       string
		body       interface{}
		headers    map[string]string
		wantStatus int
	}{
		{
			name:   "Phone check - valid request",
			method: http.MethodPost,
			path:   "/api/v1/authn/phone/check",
			body: map[string]string{
				"phone_number": "+1234567890",
			},
			wantStatus: http.StatusOK,
		},
		{
			name:       "Phone check - missing phone number",
			method:     http.MethodPost,
			path:       "/api/v1/authn/phone/check",
			body:       map[string]string{},
			wantStatus: http.StatusBadRequest,
		},
		{
			name:       "List organizations - no auth",
			method:     http.MethodGet,
			path:       "/api/v1/organizations",
			wantStatus: http.StatusOK,
		},
		{
			name:       "Get user profile - no auth",
			method:     http.MethodGet,
			path:       "/api/v1/users/me",
			wantStatus: http.StatusUnauthorized,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create request
			var bodyReader *bytes.Reader
			if tc.body != nil {
				bodyBytes, err := json.Marshal(tc.body)
				require.NoError(t, err)
				bodyReader = bytes.NewReader(bodyBytes)
			}

			req := httptest.NewRequest(tc.method, tc.path, bodyReader)
			if tc.body != nil {
				req.Header.Set("Content-Type", "application/json")
			}
			for k, v := range tc.headers {
				req.Header.Set(k, v)
			}

			// Validate request against OpenAPI spec
			route, pathParams, err := router.FindRoute(req)
			if err == nil {
				requestValidationInput := &openapi3filter.RequestValidationInput{
					Request:    req,
					PathParams: pathParams,
					Route:      route,
				}

				err = openapi3filter.ValidateRequest(context.Background(), requestValidationInput)
				if tc.wantStatus >= 400 && tc.body != nil {
					// For error cases with body, we might expect validation to fail
					t.Logf("Request validation error (expected for error case): %v", err)
				} else if err != nil {
					t.Errorf("Request validation failed: %v", err)
				}
			}

			// Execute request
			rec := httptest.NewRecorder()
			srv.Echo().ServeHTTP(rec, req)

			// Check status code
			assert.Equal(t, tc.wantStatus, rec.Code, "Unexpected status code")

			// Validate response against OpenAPI spec if request was valid
			if route != nil && rec.Code < 500 {
				responseValidationInput := &openapi3filter.ResponseValidationInput{
					RequestValidationInput: &openapi3filter.RequestValidationInput{
						Request:    req,
						PathParams: pathParams,
						Route:      route,
					},
					Status: rec.Code,
					Header: rec.Header(),
					Body:   io.NopCloser(bytes.NewReader(rec.Body.Bytes())),
				}

				err = openapi3filter.ValidateResponse(context.Background(), responseValidationInput)
				if err != nil {
					t.Errorf("Response validation failed: %v", err)
				}
			}
		})
	}
}

// TestOpenAPISecuritySchemes verifies security requirements are properly defined
func TestOpenAPISecuritySchemes(t *testing.T) {
	// Load OpenAPI specification
	loader := openapi3.NewLoader()
	spec, err := loader.LoadFromFile(GetOpenAPISpecPath())
	require.NoError(t, err, "Failed to load OpenAPI specification")

	// For Swagger 2.0, security definitions are in a different location
	// Let's check if the spec has security requirements defined
	hasSecurityDefined := false

	// Check if any paths have security requirements
	for _, pathItem := range spec.Paths.Map() {
		operations := []*openapi3.Operation{
			pathItem.Get,
			pathItem.Post,
			pathItem.Put,
			pathItem.Patch,
			pathItem.Delete,
		}

		for _, op := range operations {
			if op != nil && op.Security != nil && len(*op.Security) > 0 {
				hasSecurityDefined = true
				break
			}
		}
		if hasSecurityDefined {
			break
		}
	}

	assert.True(t, hasSecurityDefined, "API specification should have security requirements defined")

	// Check that protected endpoints have security requirements
	protectedPaths := []string{
		"/api/v1/users/me",
		"/api/v1/organizations/{orgId}",
		"/api/v1/me/event-registrations",
		"/api/v1/events/{eventId}/volunteer-applications",
	}

	for _, path := range protectedPaths {
		pathItem := spec.Paths.Find(path)
		require.NotNil(t, pathItem, "Path %s should be defined", path)

		// Check various methods
		operations := map[string]*openapi3.Operation{
			"GET":    pathItem.Get,
			"POST":   pathItem.Post,
			"PUT":    pathItem.Put,
			"PATCH":  pathItem.Patch,
			"DELETE": pathItem.Delete,
		}

		for method, operation := range operations {
			if operation != nil {
				require.NotEmpty(t, operation.Security, "%s %s should have security requirements", method, path)

				// Check for bearer auth
				found := false
				for _, secReq := range *operation.Security {
					if _, ok := secReq["bearerAuth"]; ok {
						found = true
						break
					}
				}
				assert.True(t, found, "%s %s should require bearer auth", method, path)
			}
		}
	}
}

// TestResponseSchemas verifies all responses reference valid schemas
func TestResponseSchemas(t *testing.T) {
	// Load OpenAPI specification
	loader := openapi3.NewLoader()
	spec, err := loader.LoadFromFile(GetOpenAPISpecPath())
	require.NoError(t, err, "Failed to load OpenAPI specification")

	if spec.Paths == nil || spec.Paths.Len() == 0 {
		t.Skip("OpenAPI spec is in Swagger 2.0 format, skipping OpenAPI 3.0 specific tests")
	}

	// Iterate through all paths and operations
	for path, pathItem := range spec.Paths.Map() {
		operations := map[string]*openapi3.Operation{
			"GET":    pathItem.Get,
			"POST":   pathItem.Post,
			"PUT":    pathItem.Put,
			"PATCH":  pathItem.Patch,
			"DELETE": pathItem.Delete,
		}

		for method, operation := range operations {
			if operation == nil {
				continue
			}

			// Check all responses
			for statusCode, responseRef := range operation.Responses.Map() {
				if responseRef.Value == nil {
					continue
				}

				response := responseRef.Value

				// Check content types
				if response.Content != nil {
					for contentType, mediaType := range response.Content {
						if contentType == "application/json" && mediaType.Schema != nil {
							// If it's a reference, verify it exists
							if mediaType.Schema.Ref != "" {
								schemaName := getSchemaNameFromRef(mediaType.Schema.Ref)
								_, found := spec.Components.Schemas[schemaName]
								assert.True(t, found, "%s %s response %s references non-existent schema: %s",
									method, path, statusCode, schemaName)
							}
						}
					}
				}
			}
		}
	}
}

// Helper function to extract schema name from reference
func getSchemaNameFromRef(ref string) string {
	parts := bytes.Split([]byte(ref), []byte("/"))
	if len(parts) > 0 {
		return string(parts[len(parts)-1])
	}
	return ""
}

// TestEndpointExistence verifies that documented endpoints actually exist
func TestEndpointExistence(t *testing.T) {
	// Skip if no database configuration available
	if os.Getenv("DATABASE_URL") == "" && os.Getenv("DB_HOST") == "" {
		t.Skip("Skipping test - database configuration required")
	}

	// Load configuration
	cfg := config.Load()

	// Create server
	srv, err := server.New(cfg)
	require.NoError(t, err, "Failed to create server")

	// Test a sample of critical endpoints
	criticalEndpoints := []struct {
		method string
		path   string
	}{
		// Authentication
		{http.MethodPost, "/api/v1/authn/phone/check"},
		{http.MethodPost, "/api/v1/authn/phone/otp/initiate"},
		{http.MethodPost, "/api/v1/authn/phone/otp/verify"},
		{http.MethodPost, "/api/v1/authn/token/refresh"},

		// Users
		{http.MethodGet, "/api/v1/users/me"},
		{http.MethodPatch, "/api/v1/users/me"},

		// Organizations
		{http.MethodGet, "/api/v1/organizations"},
		{http.MethodPost, "/api/v1/organizations"},

		// Events
		{http.MethodGet, "/api/v1/events"},
		{http.MethodGet, "/api/v1/event-tags"},

		// Event Registrations
		{http.MethodPost, "/api/v1/me/event-registrations"},
		{http.MethodGet, "/api/v1/me/event-registrations"},
	}

	for _, endpoint := range criticalEndpoints {
		t.Run(endpoint.method+" "+endpoint.path, func(t *testing.T) {
			// Create a minimal request
			req := httptest.NewRequest(endpoint.method, endpoint.path, nil)
			if endpoint.method == http.MethodPost || endpoint.method == http.MethodPatch {
				req.Header.Set("Content-Type", "application/json")
			}

			rec := httptest.NewRecorder()
			srv.Echo().ServeHTTP(rec, req)

			// We don't expect 404 for documented endpoints
			assert.NotEqual(t, http.StatusNotFound, rec.Code,
				"Endpoint %s %s should exist but returned 404", endpoint.method, endpoint.path)
		})
	}
}

// Helper to normalize Echo middleware wrapped handlers
func getHandlerName(h echo.HandlerFunc) string {
	// This is a simplified version - in practice you might need more sophisticated logic
	return "handler"
}
