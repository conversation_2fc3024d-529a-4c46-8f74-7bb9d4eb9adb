package contract

import (
	"bytes"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"Membership-SAAS-System-Backend/internal/config"
	"Membership-SAAS-System-Backend/internal/server"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/getkin/kin-openapi/openapi3filter"
	"github.com/getkin/kin-openapi/routers/gorillamux"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestExpandedContractValidation tests a comprehensive set of endpoints against OpenAPI spec
func TestExpandedContractValidation(t *testing.T) {
	// Skip if no test database available
	if os.Getenv("DB_HOST") == "" {
		t.Skip("Skipping contract test - no database configuration")
	}

	// Load configuration
	cfg := config.Load()

	// Create server
	srv, err := server.New(cfg)
	require.NoError(t, err, "Failed to create server")

	// Load OpenAPI specification
	loader := openapi3.NewLoader()
	spec, err := loader.LoadFromFile(GetOpenAPISpecPath())
	require.NoError(t, err, "Failed to load OpenAPI specification")

	// Create router from spec
	router, err := gorillamux.NewRouter(spec)
	require.NoError(t, err, "Failed to create router from OpenAPI spec")

	// Comprehensive test cases for contract validation
	testCases := []struct {
		name                string
		method              string
		path                string
		body                interface{}
		headers             map[string]string
		wantStatus          int
		expectRequestError  bool
		expectResponseError bool
	}{
		// Authentication endpoints
		{
			name:   "Phone check - valid request",
			method: http.MethodPost,
			path:   "/api/v1/authn/phone/check",
			body: map[string]string{
				"phone_number": "+1234567890",
			},
			wantStatus: http.StatusOK,
		},
		{
			name:               "Phone check - missing phone number",
			method:             http.MethodPost,
			path:               "/api/v1/authn/phone/check",
			body:               map[string]string{},
			wantStatus:         http.StatusBadRequest,
			expectRequestError: true,
		},
		{
			name:   "Phone check - invalid phone format",
			method: http.MethodPost,
			path:   "/api/v1/authn/phone/check",
			body: map[string]string{
				"phone_number": "1234567890", // Missing + prefix
			},
			wantStatus:         http.StatusBadRequest,
			expectRequestError: true,
		},
		{
			name:   "OTP initiate - valid request",
			method: http.MethodPost,
			path:   "/api/v1/authn/phone/otp/initiate",
			body: map[string]string{
				"phone_number": "+1234567890",
			},
			wantStatus: http.StatusNotFound, // Phone doesn't exist yet
		},
		{
			name:   "OTP verify - invalid OTP format",
			method: http.MethodPost,
			path:   "/api/v1/authn/phone/otp/verify",
			body: map[string]string{
				"session_id": "123e4567-e89b-12d3-a456-426614174000",
				"otp_code":   "12345", // Should be 6 digits
			},
			wantStatus:         http.StatusBadRequest,
			expectRequestError: true,
		},

		// Organization endpoints
		{
			name:       "List organizations - public access",
			method:     http.MethodGet,
			path:       "/api/v1/organizations",
			wantStatus: http.StatusOK,
		},
		{
			name:       "List organizations - with pagination",
			method:     http.MethodGet,
			path:       "/api/v1/organizations?page=1&limit=10",
			wantStatus: http.StatusOK,
		},
		{
			name:   "Create organization - valid request (auth required)",
			method: http.MethodPost,
			path:   "/api/v1/organizations",
			body: map[string]string{
				"name": "Test Organization",
				"slug": "test-org",
			},
			headers: map[string]string{
				"Authorization": "Bearer FAKE_JWT_FOR_VALIDATION",
			},
			wantStatus: http.StatusUnauthorized, // Will fail auth
		},
		{
			name:   "Create organization - invalid slug format",
			method: http.MethodPost,
			path:   "/api/v1/organizations",
			body: map[string]string{
				"name": "Test Organization",
				"slug": "Test Org!", // Invalid characters
			},
			headers: map[string]string{
				"Authorization": "Bearer FAKE_JWT",
			},
			wantStatus:         http.StatusBadRequest,
			expectRequestError: true,
		},
		{
			name:   "Create organization - name too long",
			method: http.MethodPost,
			path:   "/api/v1/organizations",
			body: map[string]string{
				"name": string(make([]byte, 101)), // Exceeds maxLength
				"slug": "test-org",
			},
			headers: map[string]string{
				"Authorization": "Bearer FAKE_JWT",
			},
			wantStatus:         http.StatusBadRequest,
			expectRequestError: true,
		},

		// Event endpoints
		{
			name:       "List public events",
			method:     http.MethodGet,
			path:       "/api/v1/events",
			wantStatus: http.StatusOK,
		},
		{
			name:       "Get event details - not found",
			method:     http.MethodGet,
			path:       "/api/v1/events/123e4567-e89b-12d3-a456-426614174000",
			wantStatus: http.StatusNotFound,
		},
		{
			name:   "Create event - missing required fields",
			method: http.MethodPost,
			path:   "/api/v1/organizations/123e4567-e89b-12d3-a456-426614174000/events",
			body: map[string]string{
				"title": "Test Event",
				// Missing start_date and end_date
			},
			headers: map[string]string{
				"Authorization": "Bearer FAKE_JWT",
			},
			wantStatus:         http.StatusBadRequest,
			expectRequestError: true,
		},

		// Event registration endpoints
		{
			name:   "Register for event - auth required",
			method: http.MethodPost,
			path:   "/api/v1/me/event-registrations",
			body: map[string]string{
				"event_id": "123e4567-e89b-12d3-a456-426614174000",
			},
			wantStatus: http.StatusUnauthorized,
		},
		{
			name:   "Check in participant - valid QR code",
			method: http.MethodPost,
			path:   "/api/v1/event-registrations/check-in",
			body: map[string]string{
				"qr_code": "VALID_QR_CODE_STRING",
			},
			headers: map[string]string{
				"Authorization": "Bearer FAKE_JWT",
			},
			wantStatus: http.StatusUnauthorized,
		},

		// User endpoints
		{
			name:       "Get user profile - no auth",
			method:     http.MethodGet,
			path:       "/api/v1/users/me",
			wantStatus: http.StatusUnauthorized,
		},
		{
			name:   "Update user profile - with auth",
			method: http.MethodPatch,
			path:   "/api/v1/users/me",
			body: map[string]interface{}{
				"first_name": "John",
				"last_name":  "Doe",
			},
			headers: map[string]string{
				"Authorization": "Bearer FAKE_JWT",
			},
			wantStatus: http.StatusUnauthorized,
		},

		// Event tags
		{
			name:       "List event tags - public",
			method:     http.MethodGet,
			path:       "/api/v1/event-tags",
			wantStatus: http.StatusOK,
		},
		{
			name:       "List event tags - with language filter",
			method:     http.MethodGet,
			path:       "/api/v1/event-tags?language=en",
			wantStatus: http.StatusOK,
		},

		// Volunteer endpoints
		{
			name:   "Apply for event volunteering",
			method: http.MethodPost,
			path:   "/api/v1/events/123e4567-e89b-12d3-a456-426614174000/volunteer-applications",
			body: map[string]string{
				"motivation": "I want to help",
			},
			headers: map[string]string{
				"Authorization": "Bearer FAKE_JWT",
			},
			wantStatus: http.StatusUnauthorized,
		},

		// Error response validation
		{
			name:       "Invalid endpoint - 404",
			method:     http.MethodGet,
			path:       "/api/v1/invalid-endpoint",
			wantStatus: http.StatusNotFound,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create request
			var bodyReader io.Reader
			if tc.body != nil {
				bodyBytes, err := json.Marshal(tc.body)
				require.NoError(t, err)
				bodyReader = bytes.NewReader(bodyBytes)
			}

			req := httptest.NewRequest(tc.method, tc.path, bodyReader)
			if tc.body != nil {
				req.Header.Set("Content-Type", "application/json")
			}
			for k, v := range tc.headers {
				req.Header.Set(k, v)
			}

			// Find route in OpenAPI spec
			route, pathParams, err := router.FindRoute(req)
			if tc.path == "/api/v1/invalid-endpoint" {
				// This endpoint shouldn't exist
				assert.Error(t, err, "Invalid endpoint should not be found in OpenAPI spec")
				return
			}

			require.NoError(t, err, "Route should be documented in OpenAPI spec: %s %s", tc.method, tc.path)

			// Validate request against OpenAPI spec
			requestValidationInput := &openapi3filter.RequestValidationInput{
				Request:    req,
				PathParams: pathParams,
				Route:      route,
			}

			ctx := context.Background()
			err = openapi3filter.ValidateRequest(ctx, requestValidationInput)

			if tc.expectRequestError {
				assert.Error(t, err, "Expected request validation error but got none")
			} else {
				assert.NoError(t, err, "Request validation failed: %v", err)
			}

			// Execute request
			rec := httptest.NewRecorder()
			srv.Echo().ServeHTTP(rec, req)

			// Check status code
			assert.Equal(t, tc.wantStatus, rec.Code, "Unexpected status code for %s %s", tc.method, tc.path)

			// Validate response against OpenAPI spec
			if route != nil && rec.Code < 500 {
				responseValidationInput := &openapi3filter.ResponseValidationInput{
					RequestValidationInput: requestValidationInput,
					Status:                 rec.Code,
					Header:                 rec.Header(),
					Body:                   io.NopCloser(bytes.NewReader(rec.Body.Bytes())),
				}

				err = openapi3filter.ValidateResponse(ctx, responseValidationInput)
				if tc.expectResponseError {
					assert.Error(t, err, "Expected response validation error but got none")
				} else {
					assert.NoError(t, err, "Response validation failed: %v", err)
				}
			}
		})
	}
}

// TestCriticalSchemaValidation tests that critical schemas properly validate data
func TestCriticalSchemaValidation(t *testing.T) {
	// Load OpenAPI specification
	loader := openapi3.NewLoader()
	spec, err := loader.LoadFromFile(GetOpenAPISpecPath())
	require.NoError(t, err, "Failed to load OpenAPI specification")

	// Test phone number validation
	t.Run("Phone number validation", func(t *testing.T) {
		phoneSchema := spec.Components.Schemas["PhoneCheckRequest"]
		require.NotNil(t, phoneSchema, "PhoneCheckRequest schema should exist")

		validCases := []map[string]interface{}{
			{"phone_number": "+1234567890"},
			{"phone_number": "+442071234567"},
			{"phone_number": "+86138000000"},
		}

		for _, validCase := range validCases {
			err := phoneSchema.Value.VisitJSON(validCase)
			assert.NoError(t, err, "Valid phone number should pass: %v", validCase)
		}

		invalidCases := []map[string]interface{}{
			{"phone_number": "1234567890"},        // Missing +
			{"phone_number": "+0123456789"},       // Invalid country code
			{"phone_number": "+1234567890123456"}, // Too long
			{},                                    // Missing required field
		}

		for _, invalidCase := range invalidCases {
			err := phoneSchema.Value.VisitJSON(invalidCase)
			assert.Error(t, err, "Invalid phone number should fail: %v", invalidCase)
		}
	})

	// Test OTP validation
	t.Run("OTP code validation", func(t *testing.T) {
		otpSchema := spec.Components.Schemas["OTPVerifyRequest"]
		require.NotNil(t, otpSchema, "OTPVerifyRequest schema should exist")

		validCase := map[string]interface{}{
			"session_id": "123e4567-e89b-12d3-a456-426614174000",
			"otp_code":   "123456",
		}
		err := otpSchema.Value.VisitJSON(validCase)
		assert.NoError(t, err, "Valid OTP request should pass")

		invalidCases := []map[string]interface{}{
			{
				"session_id": "123e4567-e89b-12d3-a456-426614174000",
				"otp_code":   "12345", // Too short
			},
			{
				"session_id": "123e4567-e89b-12d3-a456-426614174000",
				"otp_code":   "12345a", // Contains letter
			},
			{
				"session_id": "123e4567-e89b-12d3-a456-426614174000",
				"otp_code":   "1234567", // Too long
			},
		}

		for _, invalidCase := range invalidCases {
			err := otpSchema.Value.VisitJSON(invalidCase)
			assert.Error(t, err, "Invalid OTP request should fail: %v", invalidCase)
		}
	})

	// Test organization slug validation
	t.Run("Organization slug validation", func(t *testing.T) {
		orgSchema := spec.Components.Schemas["CreateOrganizationRequest"]
		require.NotNil(t, orgSchema, "CreateOrganizationRequest schema should exist")

		validCase := map[string]interface{}{
			"name": "Test Organization",
			"slug": "test-organization",
		}
		err := orgSchema.Value.VisitJSON(validCase)
		assert.NoError(t, err, "Valid organization should pass")

		invalidCases := []map[string]interface{}{
			{
				"name": "Test",
				"slug": "Test-Org", // Contains uppercase
			},
			{
				"name": "Test",
				"slug": "test_org", // Contains underscore
			},
			{
				"name": "Test",
				"slug": "test-", // Ends with dash
			},
			{
				"name": string(make([]byte, 101)), // Name too long
				"slug": "test",
			},
		}

		for _, invalidCase := range invalidCases {
			err := orgSchema.Value.VisitJSON(invalidCase)
			assert.Error(t, err, "Invalid organization should fail: %v", invalidCase)
		}
	})
}
