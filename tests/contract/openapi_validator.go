package contract

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"path/filepath"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/getkin/kin-openapi/openapi3filter"
	"github.com/getkin/kin-openapi/routers"
	"github.com/getkin/kin-openapi/routers/gorillamux"
)

// OpenAPIValidator provides OpenAPI specification validation
type OpenAPIValidator struct {
	spec   *openapi3.T
	router routers.Router
}

// NewOpenAPIValidator creates a new OpenAPI validator
func NewOpenAPIValidator(specPath string) (*OpenAPIValidator, error) {
	// Load OpenAPI specification
	loader := openapi3.NewLoader()
	spec, err := loader.LoadFromFile(specPath)
	if err != nil {
		return nil, fmt.Errorf("failed to load OpenAPI spec: %w", err)
	}

	// Create router from spec
	router, err := gorillamux.NewRouter(spec)
	if err != nil {
		return nil, fmt.Errorf("failed to create router: %w", err)
	}

	return &OpenAPIValidator{
		spec:   spec,
		router: router,
	}, nil
}

// ValidateRequest validates an HTTP request against the OpenAPI spec
func (v *OpenAPIValidator) ValidateRequest(req *http.Request) error {
	// Find route
	route, pathParams, err := v.router.FindRoute(req)
	if err != nil {
		return fmt.Errorf("route not found: %w", err)
	}

	// Create validation input
	requestValidationInput := &openapi3filter.RequestValidationInput{
		Request:    req,
		PathParams: pathParams,
		Route:      route,
	}

	// Validate request
	ctx := req.Context()
	if err := openapi3filter.ValidateRequest(ctx, requestValidationInput); err != nil {
		return fmt.Errorf("request validation failed: %w", err)
	}

	return nil
}

// ValidateResponse validates an HTTP response against the OpenAPI spec
func (v *OpenAPIValidator) ValidateResponse(req *http.Request, statusCode int, headers http.Header, body []byte) error {
	// Find route
	route, pathParams, err := v.router.FindRoute(req)
	if err != nil {
		return fmt.Errorf("route not found: %w", err)
	}

	// Create response validation input
	responseValidationInput := &openapi3filter.ResponseValidationInput{
		RequestValidationInput: &openapi3filter.RequestValidationInput{
			Request:    req,
			PathParams: pathParams,
			Route:      route,
		},
		Status: statusCode,
		Header: headers,
		Body:   io.NopCloser(bytes.NewReader(body)),
	}

	// Validate response
	ctx := req.Context()
	if err := openapi3filter.ValidateResponse(ctx, responseValidationInput); err != nil {
		return fmt.Errorf("response validation failed: %w", err)
	}

	return nil
}

// GetPaths returns all paths defined in the OpenAPI spec
func (v *OpenAPIValidator) GetPaths() map[string][]string {
	paths := make(map[string][]string)

	for path, pathItem := range v.spec.Paths.Map() {
		var methods []string
		if pathItem.Get != nil {
			methods = append(methods, "GET")
		}
		if pathItem.Post != nil {
			methods = append(methods, "POST")
		}
		if pathItem.Put != nil {
			methods = append(methods, "PUT")
		}
		if pathItem.Patch != nil {
			methods = append(methods, "PATCH")
		}
		if pathItem.Delete != nil {
			methods = append(methods, "DELETE")
		}
		paths[path] = methods
	}

	return paths
}

// NormalizeEchoPath converts Echo path parameters to OpenAPI format
func NormalizeEchoPath(path string) string {
	// Replace Echo path parameters with OpenAPI format
	replacements := map[string]string{
		":orgId":            "{orgId}",
		":eventId":          "{eventId}",
		":postId":           "{postId}",
		":registrationId":   "{registrationId}",
		":appId":            "{appId}",
		":userId":           "{userId}",
		":tagId":            "{tagId}",
		":reqID":            "{reqID}",
		":docID":            "{docID}",
		":resourceId":       "{resourceId}",
		":fileId":           "{fileId}",
		":mediaItemId":      "{mediaItemId}",
		":postIdOrSlug":     "{postIdOrSlug}",
		":resourceIdOrSlug": "{resourceIdOrSlug}",
		":fileIdOrName":     "{fileIdOrName}",
		":fileOrFolderId":   "{fileOrFolderId}",
		":itemId":           "{itemId}",
		":email":            "{email}",
		":phone":            "{phone}",
	}

	normalizedPath := path
	for echo, openapi := range replacements {
		normalizedPath = strings.ReplaceAll(normalizedPath, echo, openapi)
	}

	return normalizedPath
}

// ContractTestRequest creates an HTTP request for contract testing
func ContractTestRequest(method, path string, body interface{}, headers map[string]string) (*http.Request, error) {
	var bodyReader io.Reader
	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal body: %w", err)
		}
		bodyReader = bytes.NewReader(jsonBody)
	}

	req := httptest.NewRequest(method, path, bodyReader)

	// Set default content type for requests with body
	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	// Add custom headers
	for k, v := range headers {
		req.Header.Set(k, v)
	}

	return req, nil
}

// ExtractResponseSchema gets the expected schema name for a response
func (v *OpenAPIValidator) ExtractResponseSchema(path, method string, statusCode int) (string, error) {
	// Find path item
	pathItem := v.spec.Paths.Find(path)
	if pathItem == nil {
		return "", fmt.Errorf("path not found: %s", path)
	}

	// Get operation
	var operation *openapi3.Operation
	switch method {
	case "GET":
		operation = pathItem.Get
	case "POST":
		operation = pathItem.Post
	case "PUT":
		operation = pathItem.Put
	case "PATCH":
		operation = pathItem.Patch
	case "DELETE":
		operation = pathItem.Delete
	default:
		return "", fmt.Errorf("unsupported method: %s", method)
	}

	if operation == nil {
		return "", fmt.Errorf("operation not found for %s %s", method, path)
	}

	// Get response
	statusStr := fmt.Sprintf("%d", statusCode)
	response := operation.Responses.Value(statusStr)
	if response == nil {
		response = operation.Responses.Default()
	}

	if response == nil || response.Value == nil {
		return "", fmt.Errorf("response not found for status %d", statusCode)
	}

	// Extract schema reference
	if response.Value.Content != nil {
		if jsonContent := response.Value.Content["application/json"]; jsonContent != nil {
			if jsonContent.Schema != nil && jsonContent.Schema.Ref != "" {
				// Extract schema name from reference
				parts := strings.Split(jsonContent.Schema.Ref, "/")
				if len(parts) > 0 {
					return parts[len(parts)-1], nil
				}
			}
		}
	}

	return "", nil
}

// GetOpenAPISpecPath returns the path to the OpenAPI specification file
func GetOpenAPISpecPath() string {
	return filepath.Join("..", "..", "docs", "openapi.yaml")
}
