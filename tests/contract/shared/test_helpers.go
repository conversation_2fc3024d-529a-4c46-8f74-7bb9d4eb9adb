package shared

import (
	"encoding/json"
	"io"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// ContractTestConfig holds configuration for contract tests
type ContractTestConfig struct {
	PactDir         string
	ProviderBaseURL string
	ConsumerName    string
	ProviderName    string
	ContractVersion string
}

// DefaultContractConfig returns default configuration
func DefaultContractConfig() *ContractTestConfig {
	return &ContractTestConfig{
		PactDir:         "../../../contracts/pacts",
		ProviderBaseURL: "http://localhost:8080",
		ConsumerName:    "the-moment-web-app",
		ProviderName:    "the-moment-backend",
		ContractVersion: "1.0.0",
	}
}

// UserResponse represents the expected user API response
type UserResponse struct {
	ID                string    `json:"id"`
	DisplayName       string    `json:"display_name"`
	InterfaceLanguage string    `json:"interface_language"`
	UserRole          string    `json:"user_role"`
	Phone             string    `json:"phone"`
	Email             string    `json:"email"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
}

// ValidateUserResponse validates that a user response matches the contract
func ValidateUserResponse(t *testing.T, resp *http.Response) {
	assert.Equal(t, http.StatusOK, resp.StatusCode)

	body, err := io.ReadAll(resp.Body)
	assert.NoError(t, err)

	var user UserResponse
	err = json.Unmarshal(body, &user)
	assert.NoError(t, err)

	// Validate required fields
	assert.NotEmpty(t, user.ID)
	assert.NotEmpty(t, user.DisplayName)
	assert.NotEmpty(t, user.InterfaceLanguage)
	assert.NotEmpty(t, user.UserRole)
}

// SetupTestState prepares the test environment for a specific provider state
func SetupTestState(state string) error {
	switch state {
	case "user is authenticated":
		// Set up authentication state
		return nil
	case "user exists":
		// Ensure user exists in test database
		return nil
	default:
		return nil
	}
}

// CleanupTestState cleans up after a test
func CleanupTestState(state string) error {
	// Clean up any test data created
	return nil
}
