package contract

import (
	"context"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"Membership-SAAS-System-Backend/internal/config"
	"Membership-SAAS-System-Backend/internal/server"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestOpenAPISpecExists verifies the OpenAPI specification file exists
func TestOpenAPISpecExists(t *testing.T) {
	specPath := GetOpenAPISpecPath()
	_, err := os.Stat(specPath)
	assert.NoError(t, err, "OpenAPI specification file should exist at %s", specPath)
}

// TestOpenAPISpecValid verifies the OpenAPI specification is valid
func TestOpenAPISpecValid(t *testing.T) {
	// Load OpenAPI specification
	loader := openapi3.NewLoader()
	spec, err := loader.LoadFromFile(GetOpenAPISpecPath())
	require.NoError(t, err, "Failed to load OpenAPI specification")

	// Validate the spec
	ctx := context.Background()
	err = spec.Validate(ctx)
	assert.NoError(t, err, "OpenAPI specification should be valid")
}

// TestHealthEndpointContract tests the health endpoint matches OpenAPI spec
func TestHealthEndpointContract(t *testing.T) {
	// Skip if no test database available
	if os.Getenv("DB_HOST") == "" {
		t.Skip("Skipping contract test - no database configuration")
	}

	// Load configuration
	cfg := config.Load()

	// Create server
	srv, err := server.New(cfg)
	require.NoError(t, err, "Failed to create server")

	// Test health endpoint
	req := httptest.NewRequest(http.MethodGet, "/health", nil)
	rec := httptest.NewRecorder()

	srv.Echo().ServeHTTP(rec, req)

	// Verify response
	assert.Equal(t, http.StatusOK, rec.Code)
	assert.Contains(t, rec.Header().Get("Content-Type"), "application/json")
}

// TestAuthEndpointsExist verifies key authentication endpoints exist
func TestAuthEndpointsExist(t *testing.T) {
	// Skip if no test database available
	if os.Getenv("DB_HOST") == "" {
		t.Skip("Skipping contract test - no database configuration")
	}

	// Load configuration
	cfg := config.Load()

	// Create server
	srv, err := server.New(cfg)
	require.NoError(t, err, "Failed to create server")

	// Test key authentication endpoints exist
	endpoints := []struct {
		method string
		path   string
	}{
		{http.MethodPost, "/api/v1/authn/phone/check"},
		{http.MethodPost, "/api/v1/authn/phone/otp/initiate"},
		{http.MethodPost, "/api/v1/authn/phone/otp/verify"},
		{http.MethodPost, "/api/v1/authn/token/refresh"},
	}

	for _, endpoint := range endpoints {
		t.Run(endpoint.method+" "+endpoint.path, func(t *testing.T) {
			req := httptest.NewRequest(endpoint.method, endpoint.path, nil)
			req.Header.Set("Content-Type", "application/json")
			rec := httptest.NewRecorder()

			srv.Echo().ServeHTTP(rec, req)

			// We expect 400 Bad Request for missing body, not 404
			assert.NotEqual(t, http.StatusNotFound, rec.Code, "Endpoint should exist: %s %s", endpoint.method, endpoint.path)
		})
	}
}

// TestOpenAPIComponentsStructure verifies the components structure
func TestOpenAPIComponentsStructure(t *testing.T) {
	// Load OpenAPI specification
	loader := openapi3.NewLoader()
	spec, err := loader.LoadFromFile(GetOpenAPISpecPath())
	require.NoError(t, err, "Failed to load OpenAPI specification")

	// Check required sections exist
	assert.NotNil(t, spec.Info, "Info section should exist")
	assert.NotNil(t, spec.Paths, "Paths section should exist")
	assert.NotNil(t, spec.Components, "Components section should exist")

	// Check components structure
	if spec.Components != nil {
		assert.NotEmpty(t, spec.Components.Schemas, "Schemas should be defined")
		assert.NotEmpty(t, spec.Components.SecuritySchemes, "Security schemes should be defined")
	}

	// Verify key schemas exist
	requiredSchemas := []string{
		"Error",
		"SuccessMessage",
		"PhoneCheckRequest",
		"PhoneCheckResponse",
		"AuthTokenResponse",
		"UserProfile",
		"Organization",
		"Event",
		"EventRegistration",
	}

	for _, schemaName := range requiredSchemas {
		assert.NotNil(t, spec.Components.Schemas[schemaName], "Schema %s should be defined", schemaName)
	}
}
