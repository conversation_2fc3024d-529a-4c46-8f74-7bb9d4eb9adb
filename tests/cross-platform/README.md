# Cross-Platform Tests

This directory contains comprehensive cross-platform tests that validate consistency between web and mobile platforms accessing the backend API.

## Overview

The cross-platform test suite ensures that operations performed from different platforms (web and mobile) produce consistent results and maintain data integrity across the system.

## Test Categories

### 1. Organization Management Tests (5 scenarios)
- **Test 1**: Organization Creation Consistency
- **Test 2**: Organization Update Synchronization
- **Test 3**: Organization Member Management
- **Test 4**: Organization Settings and Preferences
- **Test 5**: Organization Deletion and Cascade Effects

### 2. Event Lifecycle Tests (5 scenarios)
- **Test 6**: Event Creation and Publication Flow
- **Test 7**: Event Registration Management
- **Test 8**: Event Updates and Notifications
- **Test 9**: Event Cancellation Flow
- **Test 10**: Recurring Event Management

### 3. Member Permissions Tests (5 scenarios)
- **Test 11**: Role-Based Access Control
- **Test 12**: Permission Inheritance and Propagation
- **Test 13**: Dynamic Permission Updates
- **Test 14**: Cross-Organization Permission Isolation
- **Test 15**: Permission Delegation and Temporary Access

### 4. Content Management Tests (5 scenarios)
- **Test 16**: Post Creation and Publishing
- **Test 17**: Content Versioning and History
- **Test 18**: Media Attachments and File Handling
- **Test 19**: Content Search and Filtering
- **Test 20**: Content Scheduling and Automation

### 5. Advanced Scenarios Tests (6 scenarios)
- **Test 21**: File Upload and Media Handling
- **Test 22**: Notification Flow and Delivery
- **Test 23**: Search and Filtering Consistency
- **Test 24**: Pagination Consistency
- **Test 25**: Concurrent Operations and Conflict Resolution
- **Test 26**: Error Handling Consistency

### 6. Authentication State Tests (5 scenarios)
- **Test 27**: PKCE OAuth Flow Consistency
- **Test 28**: Session Management Across Platforms
- **Test 29**: Organization Switching Consistency
- **Test 30**: Token Refresh Consistency
- **Test 31**: Concurrent Auth State Changes

### 7. API Contract Tests (5 scenarios)
- **Test 32**: API Response Format Consistency
- **Test 33**: Error Response Consistency
- **Test 34**: Pagination Contract Consistency
- **Test 35**: Headers and Metadata Consistency
- **Test 36**: Rate Limiting Consistency

### 8. Sync State Tests (6 scenarios)
- **Test 37**: Offline/Online Transitions
- **Test 38**: Data Synchronization Conflicts
- **Test 39**: Push Notification Consistency
- **Test 40**: Realtime Data Sync
- **Test 41**: Bulk Data Synchronization
- **Test 42**: File Synchronization

## Total Test Coverage

- **42 comprehensive test scenarios** (expanded from 26)
- Each scenario tests operations from both web and mobile platforms
- Validates data consistency, permissions, synchronization, and platform-specific behaviors
- Covers authentication flows, API contracts, offline/online transitions, and real-time sync

## Running the Tests

### Run all cross-platform tests:
```bash
go test ./tests/cross-platform -v
```

### Run specific test suites:
```bash
# Organization Management
go test ./tests/cross-platform -v -run TestOrganizationManagementSuite

# Event Lifecycle
go test ./tests/cross-platform -v -run TestEventLifecycleSuite

# Member Permissions
go test ./tests/cross-platform -v -run TestMemberPermissionsSuite

# Content Management
go test ./tests/cross-platform -v -run TestContentManagementSuite

# Advanced Scenarios
go test ./tests/cross-platform -v -run TestAdvancedScenariosSuite

# Authentication State
go test ./tests/cross-platform -v -run TestAuthenticationStateSuite

# API Contract
go test ./tests/cross-platform -v -run TestAPIContractSuite

# Sync State
go test ./tests/cross-platform -v -run TestSyncStateSuite
```

### Run with specific test:
```bash
# Run a specific test
go test ./tests/cross-platform -v -run TestOrganizationManagementSuite/TestOrganizationCreationConsistency
```

## Test Infrastructure

### Platform Simulation

Tests simulate operations from different platforms:
- **Web Operations**: Standard API calls with web-specific headers
- **Mobile Operations**: API calls with mobile-specific headers (X-Platform: mobile, X-App-Version)

### Test Data Management

- Unique test data prefixes prevent conflicts
- Automatic cleanup after test completion
- Retry logic for handling eventual consistency

### Helper Functions

- `CreateTestUser`: Creates test users with unique phone/email
- `CreateTestOrganization`: Sets up test organizations
- `CreateTestEvent`: Creates test events
- `SimulateWebOperation`: Simulates web platform requests
- `SimulateMobileOperation`: Simulates mobile platform requests
- `ValidateDataConsistency`: Ensures data matches across platforms
- `WaitForEventualConsistency`: Handles async operations

## Key Testing Patterns

### 1. Cross-Platform Validation
```go
// Create from web
webRec := s.SimulateWebOperation(t, "POST", "/api/v1/resource", data, token)

// Read from mobile
mobileRec := s.SimulateMobileOperation(t, "GET", "/api/v1/resource/id", nil, token)

// Validate consistency
s.ValidateDataConsistency(t, webData, mobileData)
```

### 2. Permission Testing
```go
// Test different roles
for role, user := range roles {
    rec := s.SimulateOperation(t, method, endpoint, data, user.Token)
    // Assert based on expected permissions for role
}
```

### 3. Concurrent Operations
```go
var wg sync.WaitGroup
// Launch concurrent operations from different platforms
// Validate conflict resolution and final state
```

### 4. Error Consistency
```go
// Test same error scenarios from both platforms
// Ensure error codes and messages are consistent
```

## Integration with CI/CD

These tests can be integrated into the CI/CD pipeline:

```yaml
cross-platform-tests:
  runs-on: ubuntu-latest
  steps:
    - name: Run Cross-Platform Tests
      run: go test ./tests/cross-platform -v -cover
```

## Future Enhancements

1. **WebSocket Testing**: Real-time event synchronization
2. **Offline Mode**: Test offline queue and sync
3. **Performance Metrics**: Cross-platform response time comparison
4. **Load Testing**: Concurrent user simulation from multiple platforms
5. **Visual Testing**: UI consistency validation

## Troubleshooting

### Common Issues

1. **Test Failures**: Check test data cleanup and isolation
2. **Timing Issues**: Increase timeout values for eventual consistency
3. **Permission Errors**: Verify test user roles and organization membership
4. **Database Conflicts**: Ensure unique test data prefixes

### Debug Mode

Run with verbose output:
```bash
go test ./tests/cross-platform -v -run TestName -count=1
```

## Contributing

When adding new cross-platform tests:

1. Follow the existing test structure
2. Use descriptive test names
3. Include both success and error scenarios
4. Test from both web and mobile platforms
5. Clean up test data properly
6. Document any new patterns or helpers