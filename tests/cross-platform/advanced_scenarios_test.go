package crossplatform

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// AdvancedScenariosTestSuite tests advanced cross-platform scenarios
type AdvancedScenariosTestSuite struct {
	suite.Suite
	*CrossPlatformTestSuite
}

func (s *AdvancedScenariosTestSuite) SetupSuite() {
	s.CrossPlatformTestSuite = SetupCrossPlatformTest(s.T())
}

func (s *AdvancedScenariosTestSuite) TearDownSuite() {
	s.CleanupTestData(s.T())
	s.TeardownCrossPlatformTest(s.T())
}

// Test 21: File Upload and Media Handling
func (s *AdvancedScenariosTestSuite) TestFileUploadMediaHandling() {
	t := s.T()

	// Create users
	uploader := s.CreateTestUser(t, "file-uploader", "+15550005001", "<EMAIL>")
	viewer := s.CreateTestUser(t, "file-viewer", "+15550005002", "<EMAIL>")

	// Create organization
	orgID := s.CreateTestOrganization(t, "file-org", uploader.Token)

	// Add viewer
	addViewerReq := map[string]interface{}{
		"user_id": viewer.ID.String(),
		"role":    "member",
	}
	rec := s.SimulateWebOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/members", orgID), addViewerReq, uploader.Token)
	require.Equal(t, http.StatusCreated, rec.Code)

	// Simulate profile picture upload from mobile
	profilePicReq := map[string]interface{}{
		"file_type": "image/jpeg",
		"file_size": 2048000, // 2MB
		"purpose":   "profile_picture",
	}

	mobileUploadRec := s.SimulateMobileOperation(t, "POST", "/api/v1/uploads/request", profilePicReq, uploader.Token)
	if mobileUploadRec.Code == http.StatusOK {
		var uploadResp map[string]interface{}
		json.Unmarshal(mobileUploadRec.Body.Bytes(), &uploadResp)

		uploadID := uploadResp["upload_id"].(string)
		uploadURL := uploadResp["upload_url"].(string)

		// Simulate upload completion
		completeReq := map[string]interface{}{
			"upload_id": uploadID,
			"status":    "completed",
			"file_url":  uploadURL,
		}

		completeRec := s.SimulateMobileOperation(t, "POST", "/api/v1/uploads/complete", completeReq, uploader.Token)
		require.Equal(t, http.StatusOK, completeRec.Code)

		// Update profile with new picture
		profileUpdateReq := map[string]interface{}{
			"profile_picture_url": uploadURL,
		}

		profileRec := s.SimulateWebOperation(t, "PUT", "/api/v1/users/profile", profileUpdateReq, uploader.Token)
		assert.Equal(t, http.StatusOK, profileRec.Code)
	}

	// Upload organization documents from web
	docsToUpload := []map[string]interface{}{
		{
			"filename":  "organization-handbook.pdf",
			"file_type": "application/pdf",
			"file_size": 5242880, // 5MB
			"category":  "documentation",
		},
		{
			"filename":  "event-banner.png",
			"file_type": "image/png",
			"file_size": 1048576, // 1MB
			"category":  "marketing",
		},
		{
			"filename":  "member-list.xlsx",
			"file_type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
			"file_size": 524288, // 512KB
			"category":  "administrative",
		},
	}

	uploadedFiles := make([]map[string]interface{}, 0)

	for _, doc := range docsToUpload {
		uploadReq := map[string]interface{}{
			"organization_id": orgID.String(),
			"file_type":       doc["file_type"],
			"file_size":       doc["file_size"],
			"purpose":         "organization_resource",
			"metadata":        doc,
		}

		webUploadRec := s.SimulateWebOperation(t, "POST", "/api/v1/uploads/request", uploadReq, uploader.Token)
		if webUploadRec.Code == http.StatusOK {
			var uploadResp map[string]interface{}
			json.Unmarshal(webUploadRec.Body.Bytes(), &uploadResp)

			// Complete upload
			completeReq := map[string]interface{}{
				"upload_id": uploadResp["upload_id"],
				"status":    "completed",
				"file_url":  fmt.Sprintf("/uploads/org/%s/%s", orgID, doc["filename"]),
			}

			s.SimulateWebOperation(t, "POST", "/api/v1/uploads/complete", completeReq, uploader.Token)

			uploadedFiles = append(uploadedFiles, map[string]interface{}{
				"id":       uploadResp["upload_id"],
				"filename": doc["filename"],
				"url":      completeReq["file_url"],
				"category": doc["category"],
			})
		}
	}

	// Viewer accesses files from mobile
	mobileFilesRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s/files", orgID), nil, viewer.Token)
	require.Equal(t, http.StatusOK, mobileFilesRec.Code)

	var filesResp map[string]interface{}
	json.Unmarshal(mobileFilesRec.Body.Bytes(), &filesResp)

	files := filesResp["files"].([]interface{})
	assert.GreaterOrEqual(t, len(files), len(uploadedFiles))

	// Test file access permissions
	if len(uploadedFiles) > 0 {
		restrictedFile := uploadedFiles[2] // member-list.xlsx

		// Set restricted access
		restrictReq := map[string]interface{}{
			"access_level": "admin_only",
			"reason":       "Contains sensitive member information",
		}

		restrictRec := s.SimulateWebOperation(t, "PUT", fmt.Sprintf("/api/v1/files/%s/permissions", restrictedFile["id"]), restrictReq, uploader.Token)
		if restrictRec.Code == http.StatusOK {
			// Viewer should not be able to access
			viewerAccessRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/files/%s/download", restrictedFile["id"]), nil, viewer.Token)
			assert.Equal(t, http.StatusForbidden, viewerAccessRec.Code)
		}
	}
}

// Test 22: Notification Flow and Delivery
func (s *AdvancedScenariosTestSuite) TestNotificationFlowDelivery() {
	t := s.T()

	// Create users with notification preferences
	organizer := s.CreateTestUser(t, "notif-organizer", "+15550005003", "<EMAIL>")
	attendee1 := s.CreateTestUser(t, "notif-attendee1", "+15550005004", "<EMAIL>")
	attendee2 := s.CreateTestUser(t, "notif-attendee2", "+15550005005", "<EMAIL>")

	// Set notification preferences
	notifPrefs := []struct {
		user  UserData
		prefs map[string]interface{}
	}{
		{
			attendee1,
			map[string]interface{}{
				"email_notifications": true,
				"push_notifications":  true,
				"sms_notifications":   false,
				"notification_types": map[string]bool{
					"event_updates":     true,
					"event_reminders":   true,
					"new_content":       true,
					"org_announcements": true,
				},
			},
		},
		{
			attendee2,
			map[string]interface{}{
				"email_notifications": false,
				"push_notifications":  true,
				"sms_notifications":   true,
				"notification_types": map[string]bool{
					"event_updates":     true,
					"event_reminders":   false,
					"new_content":       false,
					"org_announcements": true,
				},
			},
		},
	}

	for _, pref := range notifPrefs {
		prefRec := s.SimulateWebOperation(t, "PUT", "/api/v1/users/notification-preferences", pref.prefs, pref.user.Token)
		require.Equal(t, http.StatusOK, prefRec.Code)
	}

	// Create organization and event
	orgID := s.CreateTestOrganization(t, "notif-org", organizer.Token)

	// Add attendees to organization
	for _, attendee := range []UserData{attendee1, attendee2} {
		addReq := map[string]interface{}{
			"user_id": attendee.ID.String(),
			"role":    "member",
		}
		rec := s.SimulateMobileOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/members", orgID), addReq, organizer.Token)
		require.Equal(t, http.StatusCreated, rec.Code)
	}

	// Create event with notifications
	eventReq := map[string]interface{}{
		"title":            "Notification Test Event",
		"description":      "Testing cross-platform notifications",
		"event_type":       "workshop",
		"start_date":       time.Now().Add(24 * time.Hour).Format(time.RFC3339),
		"end_date":         time.Now().Add(26 * time.Hour).Format(time.RFC3339),
		"max_participants": 10,
		"location":         "Conference Room",
		"is_published":     true,
		"organization_id":  orgID.String(),
		"notification_settings": map[string]interface{}{
			"send_on_publish":    true,
			"send_on_update":     true,
			"reminder_intervals": []string{"24h", "1h"},
		},
	}

	eventRec := s.SimulateWebOperation(t, "POST", "/api/v1/events", eventReq, organizer.Token)
	require.Equal(t, http.StatusCreated, eventRec.Code)

	var eventResp map[string]interface{}
	json.Unmarshal(eventRec.Body.Bytes(), &eventResp)
	eventID := eventResp["id"].(string)

	// Check notification queue
	time.Sleep(100 * time.Millisecond) // Allow time for notification processing

	notifQueueRec := s.SimulateMobileOperation(t, "GET", "/api/v1/admin/notifications/queue", nil, organizer.Token)
	if notifQueueRec.Code == http.StatusOK {
		var queueResp map[string]interface{}
		json.Unmarshal(notifQueueRec.Body.Bytes(), &queueResp)

		notifications := queueResp["notifications"].([]interface{})
		assert.Greater(t, len(notifications), 0)
	}

	// Attendees register for event
	for _, attendee := range []UserData{attendee1, attendee2} {
		regReq := map[string]interface{}{
			"user_id": attendee.ID.String(),
		}
		regRec := s.SimulateMobileOperation(t, "POST", fmt.Sprintf("/api/v1/events/%s/register", eventID), regReq, attendee.Token)
		require.Equal(t, http.StatusCreated, regRec.Code)
	}

	// Update event (triggers update notifications)
	updateReq := map[string]interface{}{
		"location": "Virtual - Zoom Meeting",
		"location_details": map[string]string{
			"meeting_link": "https://zoom.us/j/123456789",
			"passcode":     "abc123",
		},
		"update_note": "Event moved to virtual format",
	}

	updateRec := s.SimulateWebOperation(t, "PUT", fmt.Sprintf("/api/v1/events/%s", eventID), updateReq, organizer.Token)
	require.Equal(t, http.StatusOK, updateRec.Code)

	// Check notifications for attendees
	for _, attendee := range []UserData{attendee1, attendee2} {
		userNotifsRec := s.SimulateMobileOperation(t, "GET", "/api/v1/notifications", nil, attendee.Token)
		require.Equal(t, http.StatusOK, userNotifsRec.Code)

		var userNotifs map[string]interface{}
		json.Unmarshal(userNotifsRec.Body.Bytes(), &userNotifs)

		notifications := userNotifs["notifications"].([]interface{})

		// Find event update notification
		found := false
		for _, notif := range notifications {
			n := notif.(map[string]interface{})
			if n["type"] == "event_update" && n["event_id"] == eventID {
				found = true

				// Mark as read from different platform
				readReq := map[string]interface{}{
					"notification_ids": []string{n["id"].(string)},
				}
				readRec := s.SimulateWebOperation(t, "POST", "/api/v1/notifications/mark-read", readReq, attendee.Token)
				assert.Equal(t, http.StatusOK, readRec.Code)
				break
			}
		}
		assert.True(t, found, "Should have event update notification")
	}

	// Test notification preferences enforcement
	// Create announcement (should notify based on preferences)
	announcementReq := map[string]interface{}{
		"title":           "Important Organization Update",
		"content":         "New policies effective immediately",
		"content_type":    "announcement",
		"organization_id": orgID.String(),
		"is_published":    true,
		"notify_members":  true,
	}

	announceRec := s.SimulateMobileOperation(t, "POST", "/api/v1/posts", announcementReq, organizer.Token)
	require.Equal(t, http.StatusCreated, announceRec.Code)

	// Verify notification delivery respects preferences
	// attendee1 should get it (org_announcements: true)
	// attendee2 should get it (org_announcements: true)
	time.Sleep(100 * time.Millisecond)

	for _, attendee := range []UserData{attendee1, attendee2} {
		finalNotifsRec := s.SimulateWebOperation(t, "GET", "/api/v1/notifications/unread", nil, attendee.Token)
		require.Equal(t, http.StatusOK, finalNotifsRec.Code)

		var finalNotifs map[string]interface{}
		json.Unmarshal(finalNotifsRec.Body.Bytes(), &finalNotifs)

		unreadNotifs := finalNotifs["notifications"].([]interface{})

		announcementFound := false
		for _, notif := range unreadNotifs {
			n := notif.(map[string]interface{})
			if n["type"] == "org_announcement" {
				announcementFound = true
				break
			}
		}
		assert.True(t, announcementFound, "Should have announcement notification")
	}
}

// Test 23: Search and Filtering Consistency
func (s *AdvancedScenariosTestSuite) TestSearchFilteringConsistency() {
	t := s.T()

	// Create test data
	creator := s.CreateTestUser(t, "search-creator", "+15550005006", "<EMAIL>")
	orgID := s.CreateTestOrganization(t, "search-test-org", creator.Token)

	// Create diverse content for searching
	testData := []struct {
		Type     string
		Platform string
		Data     map[string]interface{}
	}{
		{
			"event",
			"web",
			map[string]interface{}{
				"title":           "Summer Technology Conference 2024",
				"description":     "Annual conference featuring AI and blockchain talks",
				"event_type":      "conference",
				"tags":            []string{"technology", "ai", "blockchain", "summer"},
				"start_date":      time.Now().Add(30 * 24 * time.Hour).Format(time.RFC3339),
				"end_date":        time.Now().Add(32 * 24 * time.Hour).Format(time.RFC3339),
				"location":        "Tech Convention Center",
				"organization_id": orgID.String(),
				"is_published":    true,
			},
		},
		{
			"event",
			"mobile",
			map[string]interface{}{
				"title":           "Blockchain Workshop for Beginners",
				"description":     "Learn blockchain basics in this hands-on workshop",
				"event_type":      "workshop",
				"tags":            []string{"blockchain", "beginner", "workshop"},
				"start_date":      time.Now().Add(7 * 24 * time.Hour).Format(time.RFC3339),
				"end_date":        time.Now().Add(7 * 24 * time.Hour).Add(3 * time.Hour).Format(time.RFC3339),
				"location":        "Online",
				"organization_id": orgID.String(),
				"is_published":    true,
			},
		},
		{
			"post",
			"web",
			map[string]interface{}{
				"title":           "Introduction to Blockchain Technology",
				"content":         "Blockchain is a revolutionary technology that enables...",
				"content_type":    "article",
				"tags":            []string{"blockchain", "technology", "introduction"},
				"organization_id": orgID.String(),
				"is_published":    true,
			},
		},
		{
			"post",
			"mobile",
			map[string]interface{}{
				"title":           "AI Conference Highlights",
				"content":         "Key takeaways from the recent AI conference include...",
				"content_type":    "news",
				"tags":            []string{"ai", "conference", "highlights"},
				"organization_id": orgID.String(),
				"is_published":    true,
			},
		},
	}

	// Create test data
	createdItems := make(map[string][]string)
	for _, item := range testData {
		var rec *httptest.ResponseRecorder
		endpoint := fmt.Sprintf("/api/v1/%ss", item.Type)

		if item.Platform == "web" {
			rec = s.SimulateWebOperation(t, "POST", endpoint, item.Data, creator.Token)
		} else {
			rec = s.SimulateMobileOperation(t, "POST", endpoint, item.Data, creator.Token)
		}
		require.Equal(t, http.StatusCreated, rec.Code)

		var resp map[string]interface{}
		json.Unmarshal(rec.Body.Bytes(), &resp)

		if createdItems[item.Type] == nil {
			createdItems[item.Type] = make([]string, 0)
		}
		createdItems[item.Type] = append(createdItems[item.Type], resp["id"].(string))
	}

	// Test global search from both platforms
	searchQueries := []struct {
		Query    string
		Platform string
		Expected int
	}{
		{"blockchain", "web", 3},    // Should find 2 events + 1 post
		{"blockchain", "mobile", 3}, // Same results from mobile
		{"conference", "web", 2},    // 1 event + 1 post
		{"conference", "mobile", 2}, // Same results
		{"workshop", "web", 1},      // 1 event
		{"workshop", "mobile", 1},   // Same results
	}

	for _, sq := range searchQueries {
		var searchRec *httptest.ResponseRecorder
		searchURL := fmt.Sprintf("/api/v1/search?q=%s", strings.ReplaceAll(sq.Query, " ", "+"))

		if sq.Platform == "web" {
			searchRec = s.SimulateWebOperation(t, "GET", searchURL, nil, creator.Token)
		} else {
			searchRec = s.SimulateMobileOperation(t, "GET", searchURL, nil, creator.Token)
		}
		require.Equal(t, http.StatusOK, searchRec.Code)

		var searchResp map[string]interface{}
		json.Unmarshal(searchRec.Body.Bytes(), &searchResp)

		results := searchResp["results"].([]interface{})
		assert.GreaterOrEqual(t, len(results), sq.Expected,
			"Query '%s' from %s should return at least %d results", sq.Query, sq.Platform, sq.Expected)
	}

	// Test filtered searches
	// Events by type
	webEventFilterRec := s.SimulateWebOperation(t, "GET", "/api/v1/events?event_type=workshop", nil, creator.Token)
	mobileEventFilterRec := s.SimulateMobileOperation(t, "GET", "/api/v1/events?event_type=workshop", nil, creator.Token)

	require.Equal(t, http.StatusOK, webEventFilterRec.Code)
	require.Equal(t, http.StatusOK, mobileEventFilterRec.Code)

	var webEventFilter, mobileEventFilter map[string]interface{}
	json.Unmarshal(webEventFilterRec.Body.Bytes(), &webEventFilter)
	json.Unmarshal(mobileEventFilterRec.Body.Bytes(), &mobileEventFilter)

	// Results should be identical
	s.ValidateDataConsistency(t, webEventFilter, mobileEventFilter)

	// Posts by tag
	webPostTagRec := s.SimulateWebOperation(t, "GET", "/api/v1/posts?tags=blockchain", nil, creator.Token)
	mobilePostTagRec := s.SimulateMobileOperation(t, "GET", "/api/v1/posts?tags=blockchain", nil, creator.Token)

	require.Equal(t, http.StatusOK, webPostTagRec.Code)
	require.Equal(t, http.StatusOK, mobilePostTagRec.Code)

	var webPostTag, mobilePostTag map[string]interface{}
	json.Unmarshal(webPostTagRec.Body.Bytes(), &webPostTag)
	json.Unmarshal(mobilePostTagRec.Body.Bytes(), &mobilePostTag)

	webPosts := webPostTag["posts"].([]interface{})
	mobilePosts := mobilePostTag["posts"].([]interface{})

	assert.Equal(t, len(webPosts), len(mobilePosts))
	assert.Equal(t, 1, len(webPosts)) // Only one post has blockchain tag
}

// Test 24: Pagination Consistency
func (s *AdvancedScenariosTestSuite) TestPaginationConsistency() {
	t := s.T()

	// Create user and organization
	creator := s.CreateTestUser(t, "pagination-creator", "+15550005007", "<EMAIL>")
	orgID := s.CreateTestOrganization(t, "pagination-org", creator.Token)

	// Create multiple events for pagination testing
	numEvents := 25
	for i := 0; i < numEvents; i++ {
		eventReq := map[string]interface{}{
			"title":           fmt.Sprintf("Pagination Test Event %d", i+1),
			"description":     fmt.Sprintf("Event number %d for pagination testing", i+1),
			"event_type":      "meeting",
			"start_date":      time.Now().Add(time.Duration(i+1) * 24 * time.Hour).Format(time.RFC3339),
			"end_date":        time.Now().Add(time.Duration(i+1)*24*time.Hour + 2*time.Hour).Format(time.RFC3339),
			"location":        fmt.Sprintf("Room %d", i+1),
			"organization_id": orgID.String(),
			"is_published":    true,
		}

		// Alternate between platforms for creation
		if i%2 == 0 {
			s.SimulateWebOperation(t, "POST", "/api/v1/events", eventReq, creator.Token)
		} else {
			s.SimulateMobileOperation(t, "POST", "/api/v1/events", eventReq, creator.Token)
		}
	}

	// Test pagination from both platforms
	pageSize := 10

	// Page 1 from web
	webPage1Rec := s.SimulateWebOperation(t, "GET", fmt.Sprintf("/api/v1/events?page=1&limit=%d", pageSize), nil, creator.Token)
	require.Equal(t, http.StatusOK, webPage1Rec.Code)

	// Page 1 from mobile
	mobilePage1Rec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/events?page=1&limit=%d", pageSize), nil, creator.Token)
	require.Equal(t, http.StatusOK, mobilePage1Rec.Code)

	var webPage1, mobilePage1 map[string]interface{}
	json.Unmarshal(webPage1Rec.Body.Bytes(), &webPage1)
	json.Unmarshal(mobilePage1Rec.Body.Bytes(), &mobilePage1)

	// Validate same results
	webEvents1 := webPage1["events"].([]interface{})
	mobileEvents1 := mobilePage1["events"].([]interface{})

	assert.Equal(t, pageSize, len(webEvents1))
	assert.Equal(t, pageSize, len(mobileEvents1))

	// Validate pagination metadata
	webPagination := webPage1["pagination"].(map[string]interface{})
	mobilePagination := mobilePage1["pagination"].(map[string]interface{})

	assert.Equal(t, float64(1), webPagination["page"])
	assert.Equal(t, float64(1), mobilePagination["page"])
	assert.Equal(t, float64(pageSize), webPagination["limit"])
	assert.Equal(t, float64(pageSize), mobilePagination["limit"])
	assert.Equal(t, float64(3), webPagination["total_pages"]) // 25 events / 10 per page = 3 pages
	assert.Equal(t, float64(25), webPagination["total_items"])

	// Page 2 from different platforms
	webPage2Rec := s.SimulateWebOperation(t, "GET", fmt.Sprintf("/api/v1/events?page=2&limit=%d", pageSize), nil, creator.Token)
	mobilePage2Rec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/events?page=2&limit=%d", pageSize), nil, creator.Token)

	require.Equal(t, http.StatusOK, webPage2Rec.Code)
	require.Equal(t, http.StatusOK, mobilePage2Rec.Code)

	var webPage2, mobilePage2 map[string]interface{}
	json.Unmarshal(webPage2Rec.Body.Bytes(), &webPage2)
	json.Unmarshal(mobilePage2Rec.Body.Bytes(), &mobilePage2)

	webEvents2 := webPage2["events"].([]interface{})
	mobileEvents2 := mobilePage2["events"].([]interface{})

	assert.Equal(t, len(webEvents2), len(mobileEvents2))

	// Ensure no overlap between pages
	page1IDs := make(map[string]bool)
	for _, event := range webEvents1 {
		e := event.(map[string]interface{})
		page1IDs[e["id"].(string)] = true
	}

	for _, event := range webEvents2 {
		e := event.(map[string]interface{})
		assert.False(t, page1IDs[e["id"].(string)], "Page 2 should not contain events from page 1")
	}

	// Test cursor-based pagination if supported
	cursorReq := "/api/v1/events?cursor=start&limit=5"
	cursorRec := s.SimulateWebOperation(t, "GET", cursorReq, nil, creator.Token)

	if cursorRec.Code == http.StatusOK {
		var cursorResp map[string]interface{}
		json.Unmarshal(cursorRec.Body.Bytes(), &cursorResp)

		if nextCursor, ok := cursorResp["next_cursor"].(string); ok && nextCursor != "" {
			// Follow cursor from mobile
			mobileCursorRec := s.SimulateMobileOperation(t, "GET",
				fmt.Sprintf("/api/v1/events?cursor=%s&limit=5", nextCursor), nil, creator.Token)
			require.Equal(t, http.StatusOK, mobileCursorRec.Code)

			var mobileCursorResp map[string]interface{}
			json.Unmarshal(mobileCursorRec.Body.Bytes(), &mobileCursorResp)

			// Should get next set of results
			cursorEvents1 := cursorResp["events"].([]interface{})
			cursorEvents2 := mobileCursorResp["events"].([]interface{})

			// Verify no overlap
			for _, e1 := range cursorEvents1 {
				event1 := e1.(map[string]interface{})
				for _, e2 := range cursorEvents2 {
					event2 := e2.(map[string]interface{})
					assert.NotEqual(t, event1["id"], event2["id"], "Cursor pagination should not return duplicate events")
				}
			}
		}
	}
}

// Test 25: Concurrent Operations and Conflict Resolution
func (s *AdvancedScenariosTestSuite) TestConcurrentOperationsConflictResolution() {
	t := s.T()

	// Create users
	user1 := s.CreateTestUser(t, "concurrent-user1", "+15550005008", "<EMAIL>")
	user2 := s.CreateTestUser(t, "concurrent-user2", "+15550005009", "<EMAIL>")

	// Create shared organization
	orgID := s.CreateTestOrganization(t, "concurrent-org", user1.Token)

	// Add user2 as admin
	addUser2Req := map[string]interface{}{
		"user_id": user2.ID.String(),
		"role":    "admin",
	}
	rec := s.SimulateWebOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/members", orgID), addUser2Req, user1.Token)
	require.Equal(t, http.StatusCreated, rec.Code)

	// Test 1: Concurrent event updates
	eventID := s.CreateTestEvent(t, "concurrent-event", orgID, user1.Token)

	// Both users try to update the event simultaneously
	var wg sync.WaitGroup
	wg.Add(2)

	results := make(chan *httptest.ResponseRecorder, 2)

	go func() {
		defer wg.Done()
		updateReq := map[string]interface{}{
			"description": "Updated by user1 from web",
			"location":    "Web Location",
		}
		rec := s.SimulateWebOperation(t, "PUT", fmt.Sprintf("/api/v1/events/%s", eventID), updateReq, user1.Token)
		results <- rec
	}()

	go func() {
		defer wg.Done()
		updateReq := map[string]interface{}{
			"description": "Updated by user2 from mobile",
			"location":    "Mobile Location",
		}
		rec := s.SimulateMobileOperation(t, "PUT", fmt.Sprintf("/api/v1/events/%s", eventID), updateReq, user2.Token)
		results <- rec
	}()

	wg.Wait()
	close(results)

	// Collect results
	statusCodes := make([]int, 0)
	for rec := range results {
		statusCodes = append(statusCodes, rec.Code)
	}

	// At least one should succeed
	successCount := 0
	for _, code := range statusCodes {
		if code == http.StatusOK {
			successCount++
		}
	}
	assert.GreaterOrEqual(t, successCount, 1, "At least one concurrent update should succeed")

	// Check final state
	finalRec := s.SimulateWebOperation(t, "GET", fmt.Sprintf("/api/v1/events/%s", eventID), nil, user1.Token)
	require.Equal(t, http.StatusOK, finalRec.Code)

	var finalEvent map[string]interface{}
	json.Unmarshal(finalRec.Body.Bytes(), &finalEvent)

	// Description should be one of the two updates
	desc := finalEvent["description"].(string)
	assert.True(t, desc == "Updated by user1 from web" || desc == "Updated by user2 from mobile")

	// Test 2: Concurrent member additions
	newMembers := []UserData{
		s.CreateTestUser(t, "concurrent-member1", "+15550005010", "<EMAIL>"),
		s.CreateTestUser(t, "concurrent-member2", "+15550005011", "<EMAIL>"),
	}

	var wg2 sync.WaitGroup
	wg2.Add(2)

	results2 := make(chan *httptest.ResponseRecorder, 2)

	// Both admins try to add different members simultaneously
	go func() {
		defer wg2.Done()
		addReq := map[string]interface{}{
			"user_id": newMembers[0].ID.String(),
			"role":    "member",
		}
		rec := s.SimulateWebOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/members", orgID), addReq, user1.Token)
		results2 <- rec
	}()

	go func() {
		defer wg2.Done()
		addReq := map[string]interface{}{
			"user_id": newMembers[1].ID.String(),
			"role":    "member",
		}
		rec := s.SimulateMobileOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/members", orgID), addReq, user2.Token)
		results2 <- rec
	}()

	wg2.Wait()
	close(results2)

	// Both should succeed (different members)
	for rec := range results2 {
		assert.Equal(t, http.StatusCreated, rec.Code, "Both member additions should succeed")
	}

	// Verify both members were added
	membersRec := s.SimulateWebOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s/members", orgID), nil, user1.Token)
	require.Equal(t, http.StatusOK, membersRec.Code)

	var membersResp map[string]interface{}
	json.Unmarshal(membersRec.Body.Bytes(), &membersResp)

	members := membersResp["members"].([]interface{})
	memberIDs := make(map[string]bool)
	for _, m := range members {
		member := m.(map[string]interface{})
		memberIDs[member["user_id"].(string)] = true
	}

	assert.True(t, memberIDs[newMembers[0].ID.String()])
	assert.True(t, memberIDs[newMembers[1].ID.String()])

	// Test 3: Optimistic locking with version numbers
	// Create a resource that supports versioning
	resourceReq := map[string]interface{}{
		"title":             "Versioned Resource",
		"content":           "Initial content",
		"organization_id":   orgID.String(),
		"enable_versioning": true,
	}

	resourceRec := s.SimulateWebOperation(t, "POST", "/api/v1/resources", resourceReq, user1.Token)
	if resourceRec.Code == http.StatusCreated {
		var resourceResp map[string]interface{}
		json.Unmarshal(resourceRec.Body.Bytes(), &resourceResp)
		resourceID := resourceResp["id"].(string)
		version := resourceResp["version"].(float64)

		// Both users try to update with same version
		update1Req := map[string]interface{}{
			"content": "Updated by user1",
			"version": version,
		}
		update2Req := map[string]interface{}{
			"content": "Updated by user2",
			"version": version,
		}

		// First update should succeed
		update1Rec := s.SimulateWebOperation(t, "PUT", fmt.Sprintf("/api/v1/resources/%s", resourceID), update1Req, user1.Token)

		// Second update should fail with conflict
		update2Rec := s.SimulateMobileOperation(t, "PUT", fmt.Sprintf("/api/v1/resources/%s", resourceID), update2Req, user2.Token)

		if update1Rec.Code == http.StatusOK {
			assert.Equal(t, http.StatusConflict, update2Rec.Code, "Second update with stale version should fail")
		}
	}
}

// Test 26: Error Handling Consistency
func (s *AdvancedScenariosTestSuite) TestErrorHandlingConsistency() {
	t := s.T()

	// Create test user
	user := s.CreateTestUser(t, "error-test-user", "+15550005012", "<EMAIL>")
	orgID := s.CreateTestOrganization(t, "error-test-org", user.Token)

	// Test various error scenarios from both platforms
	errorScenarios := []struct {
		Name        string
		Method      string
		Endpoint    string
		Body        interface{}
		ExpectCode  int
		Description string
	}{
		{
			"Invalid UUID",
			"GET",
			"/api/v1/events/invalid-uuid",
			nil,
			http.StatusBadRequest,
			"Invalid UUID format should return 400",
		},
		{
			"Non-existent Resource",
			"GET",
			fmt.Sprintf("/api/v1/events/%s", uuid.New()),
			nil,
			http.StatusNotFound,
			"Non-existent resource should return 404",
		},
		{
			"Missing Required Fields",
			"POST",
			"/api/v1/events",
			map[string]interface{}{
				"description": "Missing title field",
			},
			http.StatusBadRequest,
			"Missing required fields should return 400",
		},
		{
			"Invalid Date Format",
			"POST",
			"/api/v1/events",
			map[string]interface{}{
				"title":           "Invalid Date Event",
				"start_date":      "not-a-date",
				"end_date":        "also-not-a-date",
				"organization_id": orgID.String(),
			},
			http.StatusBadRequest,
			"Invalid date format should return 400",
		},
		{
			"Exceeding Limits",
			"POST",
			"/api/v1/posts",
			map[string]interface{}{
				"title":           strings.Repeat("a", 1000), // Assuming 255 char limit
				"content":         "Content",
				"organization_id": orgID.String(),
			},
			http.StatusBadRequest,
			"Exceeding field limits should return 400",
		},
		{
			"Invalid Enum Value",
			"POST",
			"/api/v1/events",
			map[string]interface{}{
				"title":           "Invalid Type Event",
				"event_type":      "invalid-type",
				"start_date":      time.Now().Add(24 * time.Hour).Format(time.RFC3339),
				"end_date":        time.Now().Add(26 * time.Hour).Format(time.RFC3339),
				"organization_id": orgID.String(),
			},
			http.StatusBadRequest,
			"Invalid enum value should return 400",
		},
		{
			"Unauthorized Access",
			"DELETE",
			fmt.Sprintf("/api/v1/organizations/%s", orgID),
			nil,
			http.StatusForbidden,
			"Non-owner deleting org should return 403",
		},
	}

	// Test each scenario from both platforms
	for _, scenario := range errorScenarios {
		// Test from web
		webRec := s.SimulateWebOperation(t, scenario.Method, scenario.Endpoint, scenario.Body, user.Token)
		assert.Equal(t, scenario.ExpectCode, webRec.Code,
			"Web - %s: %s", scenario.Name, scenario.Description)

		// Test from mobile
		mobileRec := s.SimulateMobileOperation(t, scenario.Method, scenario.Endpoint, scenario.Body, user.Token)
		assert.Equal(t, scenario.ExpectCode, mobileRec.Code,
			"Mobile - %s: %s", scenario.Name, scenario.Description)

		// Verify error response format consistency
		if webRec.Code >= 400 {
			var webError, mobileError map[string]interface{}
			json.Unmarshal(webRec.Body.Bytes(), &webError)
			json.Unmarshal(mobileRec.Body.Bytes(), &mobileError)

			// Both should have error field
			assert.NotNil(t, webError["error"], "Web error response should have 'error' field")
			assert.NotNil(t, mobileError["error"], "Mobile error response should have 'error' field")

			// Error codes should match if present
			if webError["code"] != nil && mobileError["code"] != nil {
				assert.Equal(t, webError["code"], mobileError["code"],
					"Error codes should match between platforms")
			}
		}
	}

	// Test rate limiting consistency
	// Make rapid requests from both platforms
	endpoint := "/api/v1/events"
	requestCount := 20

	webResponses := make([]int, 0)
	mobileResponses := make([]int, 0)

	for i := 0; i < requestCount; i++ {
		webRec := s.SimulateWebOperation(t, "GET", endpoint, nil, user.Token)
		mobileRec := s.SimulateMobileOperation(t, "GET", endpoint, nil, user.Token)

		webResponses = append(webResponses, webRec.Code)
		mobileResponses = append(mobileResponses, mobileRec.Code)

		// Check if rate limited
		if webRec.Code == http.StatusTooManyRequests || mobileRec.Code == http.StatusTooManyRequests {
			// Verify rate limit headers
			webRetryAfter := webRec.Header().Get("Retry-After")
			mobileRetryAfter := mobileRec.Header().Get("Retry-After")

			if webRetryAfter != "" && mobileRetryAfter != "" {
				assert.Equal(t, webRetryAfter, mobileRetryAfter,
					"Rate limit retry-after headers should match")
			}
			break
		}
	}
}

func TestAdvancedScenariosSuite(t *testing.T) {
	suite.Run(t, new(AdvancedScenariosTestSuite))
}
