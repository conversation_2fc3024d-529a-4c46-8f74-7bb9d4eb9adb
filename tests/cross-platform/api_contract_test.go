package crossplatform

import (
	"encoding/json"
	"fmt"
	"net/http"
	"reflect"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"github.com/tidwall/gjson"
)

// APIContractTestSuite tests API contract consistency across platforms
type APIContractTestSuite struct {
	suite.Suite
	*CrossPlatformTestSuite
}

func (s *APIContractTestSuite) SetupSuite() {
	s.CrossPlatformTestSuite = SetupCrossPlatformTest(s.T())
}

func (s *APIContractTestSuite) TearDownSuite() {
	s.CleanupTestData(s.T())
	s.TeardownCrossPlatformTest(s.T())
}

// TestAPIResponseFormatConsistency validates response formats are identical
func (s *APIContractTestSuite) TestAPIResponseFormatConsistency() {
	t := s.T()

	// Create test user
	user := s.CreateTestUser(t, "test-api-format", fmt.Sprintf("+1555%07d", time.Now().UnixNano()%10000000), fmt.Sprintf("<EMAIL>", time.Now().UnixNano()))
	token := user.Token
	orgID := s.CreateTestOrganization(t, "test-api-format", token)

	// Test cases for different endpoints
	testCases := []struct {
		name     string
		method   string
		endpoint string
		body     interface{}
		fields   []string // Fields that must be present in response
	}{
		{
			name:     "User Profile",
			method:   "GET",
			endpoint: "/api/v1/users/me",
			body:     nil,
			fields:   []string{"id", "phone", "email", "platform_role", "created_at", "updated_at"},
		},
		{
			name:     "Organization Details",
			method:   "GET",
			endpoint: fmt.Sprintf("/api/v1/organizations/%s", orgID),
			body:     nil,
			fields:   []string{"id", "name", "description", "created_at", "updated_at", "member_count"},
		},
		{
			name:     "Create Event",
			method:   "POST",
			endpoint: fmt.Sprintf("/api/v1/organizations/%s/events", orgID),
			body: map[string]interface{}{
				"title":       "API Contract Test Event",
				"description": "Testing response format",
				"start_time":  time.Now().Add(24 * time.Hour).Format(time.RFC3339),
				"end_time":    time.Now().Add(25 * time.Hour).Format(time.RFC3339),
			},
			fields: []string{"id", "title", "description", "start_time", "end_time", "organization_id", "created_at"},
		},
		{
			name:     "List Events",
			method:   "GET",
			endpoint: fmt.Sprintf("/api/v1/organizations/%s/events", orgID),
			body:     nil,
			fields:   []string{"data", "pagination.total", "pagination.page", "pagination.limit"},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Make request from web
			webRec := s.SimulateWebOperation(t, tc.method, tc.endpoint, tc.body, token)
			s.Require().Equal(http.StatusOK, webRec.Code, "Web request should succeed")

			// Make request from mobile
			mobileRec := s.SimulateMobileOperation(t, tc.method, tc.endpoint, tc.body, token)
			s.Require().Equal(http.StatusOK, mobileRec.Code, "Mobile request should succeed")

			// Parse responses
			var webResp, mobileResp map[string]interface{}
			s.Require().NoError(json.Unmarshal(webRec.Body.Bytes(), &webResp))
			s.Require().NoError(json.Unmarshal(mobileRec.Body.Bytes(), &mobileResp))

			// Check all required fields are present
			for _, field := range tc.fields {
				webValue := gjson.Get(webRec.Body.String(), field)
				mobileValue := gjson.Get(mobileRec.Body.String(), field)

				s.Assert().True(webValue.Exists(), "Web response should have field: %s", field)
				s.Assert().True(mobileValue.Exists(), "Mobile response should have field: %s", field)

				// For non-timestamp fields, values should match
				if field != "created_at" && field != "updated_at" {
					s.Assert().Equal(webValue.Type.String(), mobileValue.Type.String(),
						"Field %s should have same type", field)
				}
			}

			// Validate response structure is identical
			s.validateResponseStructure(t, webResp, mobileResp)
		})
	}
}

// TestErrorResponseConsistency validates error responses are identical
func (s *APIContractTestSuite) TestErrorResponseConsistency() {
	t := s.T()

	user := s.CreateTestUser(t, "test-error-format", fmt.Sprintf("+1555%07d", time.Now().UnixNano()%10000000), fmt.Sprintf("<EMAIL>", time.Now().UnixNano()))
	token := user.Token

	// Test cases for different error scenarios
	testCases := []struct {
		name           string
		method         string
		endpoint       string
		body           interface{}
		expectedStatus int
		errorFields    []string
	}{
		{
			name:           "Not Found",
			method:         "GET",
			endpoint:       "/api/v1/organizations/non-existent-id",
			body:           nil,
			expectedStatus: http.StatusNotFound,
			errorFields:    []string{"error", "message"},
		},
		{
			name:           "Validation Error",
			method:         "POST",
			endpoint:       "/api/v1/organizations",
			body:           map[string]interface{}{"name": ""}, // Empty name
			expectedStatus: http.StatusBadRequest,
			errorFields:    []string{"error", "message", "details"},
		},
		{
			name:           "Unauthorized",
			method:         "GET",
			endpoint:       "/api/v1/users/me",
			body:           nil,
			expectedStatus: http.StatusUnauthorized,
			errorFields:    []string{"error", "message"},
		},
		{
			name:           "Forbidden",
			method:         "DELETE",
			endpoint:       "/api/v1/organizations/some-org-id",
			body:           nil,
			expectedStatus: http.StatusForbidden,
			errorFields:    []string{"error", "message"},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Use invalid token for unauthorized test
			testToken := token
			if tc.name == "Unauthorized" {
				testToken = "invalid-token"
			}

			// Make request from web
			webRec := s.SimulateWebOperation(t, tc.method, tc.endpoint, tc.body, testToken)
			s.Assert().Equal(tc.expectedStatus, webRec.Code, "Web should return expected status")

			// Make request from mobile
			mobileRec := s.SimulateMobileOperation(t, tc.method, tc.endpoint, tc.body, testToken)
			s.Assert().Equal(tc.expectedStatus, mobileRec.Code, "Mobile should return expected status")

			// Parse error responses
			var webErr, mobileErr map[string]interface{}
			s.Require().NoError(json.Unmarshal(webRec.Body.Bytes(), &webErr))
			s.Require().NoError(json.Unmarshal(mobileRec.Body.Bytes(), &mobileErr))

			// Check error fields
			for _, field := range tc.errorFields {
				s.Assert().Contains(webErr, field, "Web error should have field: %s", field)
				s.Assert().Contains(mobileErr, field, "Mobile error should have field: %s", field)
			}

			// Error structure should be identical
			s.validateResponseStructure(t, webErr, mobileErr)
		})
	}
}

// TestPaginationContractConsistency validates pagination works identically
func (s *APIContractTestSuite) TestPaginationContractConsistency() {
	t := s.T()

	user := s.CreateTestUser(t, "test-pagination-contract", fmt.Sprintf("+1555%07d", time.Now().UnixNano()%10000000), fmt.Sprintf("<EMAIL>", time.Now().UnixNano()))
	token := user.Token
	orgID := s.CreateTestOrganization(t, "test-pagination-contract", token)

	// Create multiple events for pagination
	for i := 0; i < 15; i++ {
		eventData := map[string]interface{}{
			"title":       fmt.Sprintf("Pagination Test Event %d", i),
			"description": "Testing pagination",
			"start_time":  time.Now().Add(time.Duration(i*24) * time.Hour).Format(time.RFC3339),
			"end_time":    time.Now().Add(time.Duration(i*24+1) * time.Hour).Format(time.RFC3339),
		}
		eventRec := s.SimulateWebOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/events", orgID), eventData, token)
		s.Require().Equal(http.StatusCreated, eventRec.Code)
	}

	// Test pagination parameters
	testCases := []struct {
		name   string
		params string
	}{
		{"Default pagination", ""},
		{"Custom page size", "?limit=5"},
		{"Second page", "?page=2&limit=5"},
		{"Sort order", "?sort=created_at&order=desc"},
		{"Combined params", "?page=1&limit=10&sort=title&order=asc"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			endpoint := fmt.Sprintf("/api/v1/organizations/%s/events%s", orgID, tc.params)

			// Request from both platforms
			webRec := s.SimulateWebOperation(t, "GET", endpoint, nil, token)
			mobileRec := s.SimulateMobileOperation(t, "GET", endpoint, nil, token)

			s.Require().Equal(http.StatusOK, webRec.Code)
			s.Require().Equal(http.StatusOK, mobileRec.Code)

			// Parse responses
			var webResp, mobileResp map[string]interface{}
			s.Require().NoError(json.Unmarshal(webRec.Body.Bytes(), &webResp))
			s.Require().NoError(json.Unmarshal(mobileRec.Body.Bytes(), &mobileResp))

			// Validate pagination structure
			s.Assert().Contains(webResp, "data", "Web response should have data field")
			s.Assert().Contains(webResp, "pagination", "Web response should have pagination field")
			s.Assert().Contains(mobileResp, "data", "Mobile response should have data field")
			s.Assert().Contains(mobileResp, "pagination", "Mobile response should have pagination field")

			// Validate pagination metadata
			webPagination := webResp["pagination"].(map[string]interface{})
			mobilePagination := mobileResp["pagination"].(map[string]interface{})

			paginationFields := []string{"total", "page", "limit", "total_pages"}
			for _, field := range paginationFields {
				s.Assert().Contains(webPagination, field, "Web pagination should have %s", field)
				s.Assert().Contains(mobilePagination, field, "Mobile pagination should have %s", field)
				s.Assert().Equal(webPagination[field], mobilePagination[field],
					"Pagination field %s should match", field)
			}

			// Data arrays should have same length
			webData := webResp["data"].([]interface{})
			mobileData := mobileResp["data"].([]interface{})
			s.Assert().Equal(len(webData), len(mobileData), "Data arrays should have same length")
		})
	}
}

// TestHeadersAndMetadataConsistency validates headers handling
func (s *APIContractTestSuite) TestHeadersAndMetadataConsistency() {
	t := s.T()

	user := s.CreateTestUser(t, "test-headers", fmt.Sprintf("+1555%07d", time.Now().UnixNano()%10000000), fmt.Sprintf("<EMAIL>", time.Now().UnixNano()))
	token := user.Token

	// Test required headers are accepted
	testCases := []struct {
		name           string
		webHeaders     map[string]string
		mobileHeaders  map[string]string
		endpoint       string
		expectedStatus int
	}{
		{
			name:       "Content-Type JSON",
			webHeaders: map[string]string{"Content-Type": "application/json"},
			mobileHeaders: map[string]string{
				"Content-Type": "application/json",
				"X-Platform":   "mobile",
			},
			endpoint:       "/api/v1/users/me",
			expectedStatus: http.StatusOK,
		},
		{
			name:       "Accept Header",
			webHeaders: map[string]string{"Accept": "application/json"},
			mobileHeaders: map[string]string{
				"Accept":     "application/json",
				"X-Platform": "mobile",
			},
			endpoint:       "/api/v1/users/me",
			expectedStatus: http.StatusOK,
		},
		{
			name:       "Custom Headers",
			webHeaders: map[string]string{"X-Request-ID": "web-123"},
			mobileHeaders: map[string]string{
				"X-Request-ID":  "mobile-456",
				"X-Platform":    "mobile",
				"X-App-Version": "1.0.0",
			},
			endpoint:       "/api/v1/users/me",
			expectedStatus: http.StatusOK,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Make requests with custom headers
			webRec := s.SimulateWebOperationWithHeaders(t, "GET", tc.endpoint, nil, token, tc.webHeaders)
			mobileRec := s.SimulateWebOperationWithHeaders(t, "GET", tc.endpoint, nil, token, tc.mobileHeaders)

			s.Assert().Equal(tc.expectedStatus, webRec.Code, "Web request status")
			s.Assert().Equal(tc.expectedStatus, mobileRec.Code, "Mobile request status")

			// Both should return valid JSON
			var webResp, mobileResp map[string]interface{}
			s.Assert().NoError(json.Unmarshal(webRec.Body.Bytes(), &webResp))
			s.Assert().NoError(json.Unmarshal(mobileRec.Body.Bytes(), &mobileResp))
		})
	}
}

// TestRateLimitingConsistency validates rate limiting is applied equally
func (s *APIContractTestSuite) TestRateLimitingConsistency() {
	t := s.T()

	user := s.CreateTestUser(t, "test-rate-limit", fmt.Sprintf("+1555%07d", time.Now().UnixNano()%10000000), fmt.Sprintf("<EMAIL>", time.Now().UnixNano()))
	token := user.Token

	// Make multiple rapid requests from both platforms
	endpoint := "/api/v1/users/me"
	requestCount := 20 // Assuming rate limit is lower than this

	webResponses := make([]int, 0, requestCount)
	mobileResponses := make([]int, 0, requestCount)

	// Rapid fire from web
	for i := 0; i < requestCount; i++ {
		rec := s.SimulateWebOperation(t, "GET", endpoint, nil, token)
		webResponses = append(webResponses, rec.Code)
	}

	// Rapid fire from mobile
	for i := 0; i < requestCount; i++ {
		rec := s.SimulateMobileOperation(t, "GET", endpoint, nil, token)
		mobileResponses = append(mobileResponses, rec.Code)
	}

	// Check if rate limiting kicked in
	webRateLimited := countStatus(webResponses, http.StatusTooManyRequests)
	mobileRateLimited := countStatus(mobileResponses, http.StatusTooManyRequests)

	// Both platforms should experience similar rate limiting
	// Allow some variance due to timing
	difference := abs(webRateLimited - mobileRateLimited)
	s.Assert().LessOrEqual(difference, 2, "Rate limiting should be similar across platforms")

	// If rate limited, check response format
	if webRateLimited > 0 {
		// Find a rate limited response
		for _, status := range webResponses {
			if status == http.StatusTooManyRequests {
				// Make one more request to check rate limit response format
				webRec := s.SimulateWebOperation(t, "GET", endpoint, nil, token)
				mobileRec := s.SimulateMobileOperation(t, "GET", endpoint, nil, token)

				if webRec.Code == http.StatusTooManyRequests {
					var webErr, mobileErr map[string]interface{}
					json.Unmarshal(webRec.Body.Bytes(), &webErr)
					json.Unmarshal(mobileRec.Body.Bytes(), &mobileErr)

					// Rate limit responses should have consistent format
					s.Assert().Contains(webErr, "error", "Rate limit response should have error")
					s.Assert().Contains(mobileErr, "error", "Rate limit response should have error")
				}
				break
			}
		}
	}
}

// Helper function to validate response structure consistency
func (s *APIContractTestSuite) validateResponseStructure(t *testing.T, web, mobile map[string]interface{}) {
	// Get all keys from both responses
	webKeys := getKeys(web)
	mobileKeys := getKeys(mobile)

	// Check if key sets match (ignoring order)
	s.Assert().ElementsMatch(webKeys, mobileKeys, "Response keys should match")

	// Recursively check nested structures
	for key, webValue := range web {
		mobileValue, exists := mobile[key]
		s.Assert().True(exists, "Mobile response should have key: %s", key)

		// Check types match
		webType := reflect.TypeOf(webValue)
		mobileType := reflect.TypeOf(mobileValue)
		s.Assert().Equal(webType, mobileType, "Types should match for key: %s", key)

		// Recursively check nested objects
		if webMap, ok := webValue.(map[string]interface{}); ok {
			if mobileMap, ok := mobileValue.(map[string]interface{}); ok {
				s.validateResponseStructure(t, webMap, mobileMap)
			}
		}
	}
}

// Helper to get map keys
func getKeys(m map[string]interface{}) []string {
	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	return keys
}

// Helper to count status codes
func countStatus(responses []int, status int) int {
	count := 0
	for _, resp := range responses {
		if resp == status {
			count++
		}
	}
	return count
}

// Helper for abs
func abs(n int) int {
	if n < 0 {
		return -n
	}
	return n
}

func TestAPIContractSuite(t *testing.T) {
	suite.Run(t, new(APIContractTestSuite))
}
