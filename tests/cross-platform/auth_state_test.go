package crossplatform

import (
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"github.com/tidwall/gjson"
)

// AuthenticationStateTestSuite tests authentication state management across platforms
type AuthenticationStateTestSuite struct {
	suite.Suite
	*CrossPlatformTestSuite
}

func (s *AuthenticationStateTestSuite) SetupSuite() {
	s.CrossPlatformTestSuite = SetupCrossPlatformTest(s.T())
}

func (s *AuthenticationStateTestSuite) TearDownSuite() {
	s.CleanupTestData(s.T())
	s.TeardownCrossPlatformTest(s.T())
}

// TestPKCEOAuthFlowConsistency validates PKCE OAuth flow works identically across platforms
func (s *AuthenticationStateTestSuite) TestPKCEOAuthFlowConsistency() {
	t := s.T()

	// Test data
	testPhone := CreateTestPhoneNumber()
	testEmail := fmt.Sprintf("<EMAIL>", RandomString(8))

	// Step 1: Initiate PKCE flow from web
	webInitiateData := map[string]interface{}{
		"phone":                 testPhone,
		"email":                 testEmail,
		"code_challenge":        "E9Melhoa2OwvFrEMTJguCHaoeK1t8URWbuGJSstw-cM",
		"code_challenge_method": "S256",
	}

	webRec := s.SimulateWebOperation(t, "POST", "/api/v1/authn/phone/check", webInitiateData, "")
	s.Require().Equal(http.StatusOK, webRec.Code)

	// Simulate SMS OTP verification
	otpData := map[string]interface{}{
		"phone": testPhone,
		"otp":   "123456", // In test mode, this is accepted
	}

	webVerifyRec := s.SimulateWebOperation(t, "POST", "/api/v1/authn/phone/otp/verify", otpData, "")
	s.Require().Equal(http.StatusOK, webVerifyRec.Code)

	webAuthCode := gjson.Get(webVerifyRec.Body.String(), "authorization_code").String()
	s.Require().NotEmpty(webAuthCode)

	// Step 2: Exchange auth code with PKCE verifier from web
	webExchangeData := map[string]interface{}{
		"authorization_code": webAuthCode,
		"code_verifier":      "dBjftJeZ4CVP-mB92K27uhbUJU1p1r_wW1gFWFOEjXk",
	}

	webTokenRec := s.SimulateWebOperation(t, "POST", "/api/v1/authn/token", webExchangeData, "")
	s.Require().Equal(http.StatusOK, webTokenRec.Code)

	var webTokenResp map[string]interface{}
	s.Require().NoError(json.Unmarshal(webTokenRec.Body.Bytes(), &webTokenResp))
	webAccessToken := webTokenResp["access_token"].(string)
	webRefreshToken := webTokenResp["refresh_token"].(string)

	// Step 3: Repeat the same flow from mobile
	mobileInitiateData := map[string]interface{}{
		"phone":                 testPhone,
		"email":                 testEmail,
		"code_challenge":        "different-challenge-for-mobile",
		"code_challenge_method": "S256",
	}

	mobileRec := s.SimulateMobileOperation(t, "POST", "/api/v1/authn/phone/check", mobileInitiateData, "")
	s.Require().Equal(http.StatusOK, mobileRec.Code)

	mobileVerifyRec := s.SimulateMobileOperation(t, "POST", "/api/v1/authn/phone/otp/verify", otpData, "")
	s.Require().Equal(http.StatusOK, mobileVerifyRec.Code)

	mobileAuthCode := gjson.Get(mobileVerifyRec.Body.String(), "authorization_code").String()
	s.Require().NotEmpty(mobileAuthCode)

	// Step 4: Exchange with different verifier
	mobileExchangeData := map[string]interface{}{
		"authorization_code": mobileAuthCode,
		"code_verifier":      "mobile-verifier-different-from-web",
	}

	mobileTokenRec := s.SimulateMobileOperation(t, "POST", "/api/v1/authn/token", mobileExchangeData, "")
	s.Require().Equal(http.StatusOK, mobileTokenRec.Code)

	var mobileTokenResp map[string]interface{}
	s.Require().NoError(json.Unmarshal(mobileTokenRec.Body.Bytes(), &mobileTokenResp))
	mobileAccessToken := mobileTokenResp["access_token"].(string)

	// Step 5: Validate both tokens access same user data
	webUserRec := s.SimulateWebOperation(t, "GET", "/api/v1/users/me", nil, webAccessToken)
	mobileUserRec := s.SimulateMobileOperation(t, "GET", "/api/v1/users/me", nil, mobileAccessToken)

	s.Require().Equal(http.StatusOK, webUserRec.Code)
	s.Require().Equal(http.StatusOK, mobileUserRec.Code)

	// Validate user data is consistent
	webUserID := gjson.Get(webUserRec.Body.String(), "id").String()
	mobileUserID := gjson.Get(mobileUserRec.Body.String(), "id").String()
	s.Assert().Equal(webUserID, mobileUserID, "User IDs should match across platforms")

	// Step 6: Test refresh token flow
	webRefreshData := map[string]interface{}{
		"refresh_token": webRefreshToken,
	}

	webRefreshRec := s.SimulateWebOperation(t, "POST", "/api/v1/authn/refresh", webRefreshData, "")
	s.Require().Equal(http.StatusOK, webRefreshRec.Code)

	// Validate new token works
	var webNewTokenResp map[string]interface{}
	s.Require().NoError(json.Unmarshal(webRefreshRec.Body.Bytes(), &webNewTokenResp))
	newWebToken := webNewTokenResp["access_token"].(string)

	webNewUserRec := s.SimulateWebOperation(t, "GET", "/api/v1/users/me", nil, newWebToken)
	s.Require().Equal(http.StatusOK, webNewUserRec.Code)
}

// TestSessionManagementAcrossPlatforms validates session consistency
func (s *AuthenticationStateTestSuite) TestSessionManagementAcrossPlatforms() {
	t := s.T()

	// Create test user
	user := s.CreateTestUser(t, "test-session", CreateTestPhoneNumber(), "<EMAIL>")

	// Step 1: Login from web
	webRec := s.SimulateWebOperation(t, "GET", "/api/v1/users/me", nil, user.Token)
	s.Require().Equal(http.StatusOK, webRec.Code)

	// Step 2: Simulate session activity from mobile
	mobileRec := s.SimulateMobileOperation(t, "GET", "/api/v1/users/me", nil, user.Token)
	s.Require().Equal(http.StatusOK, mobileRec.Code)

	// Step 3: Logout from web should invalidate mobile session too
	logoutData := map[string]interface{}{
		"refresh_token": "dummy-refresh-token", // Would be real in production
	}

	webLogoutRec := s.SimulateWebOperation(t, "POST", "/api/v1/authn/logout", logoutData, user.Token)
	s.Require().Equal(http.StatusOK, webLogoutRec.Code)

	// Step 4: Verify mobile can't access with same token (after logout propagation)
	time.Sleep(100 * time.Millisecond) // Allow logout to propagate

	mobileAfterLogoutRec := s.SimulateMobileOperation(t, "GET", "/api/v1/users/me", nil, user.Token)
	// Token should be invalidated
	s.Assert().Equal(http.StatusUnauthorized, mobileAfterLogoutRec.Code, "Mobile should not access after web logout")
}

// TestOrganizationSwitchingConsistency validates org switching across platforms
func (s *AuthenticationStateTestSuite) TestOrganizationSwitchingConsistency() {
	t := s.T()

	// Create test user
	user := s.CreateTestUser(t, "test-org-switch", CreateTestPhoneNumber(), "<EMAIL>")

	// Create two organizations
	org1 := s.CreateTestOrganization(t, "test-org-switch-1", user.Token)
	org2 := s.CreateTestOrganization(t, "test-org-switch-2", user.Token)

	// Step 1: Check initial active org from web
	webUserRec := s.SimulateWebOperation(t, "GET", "/api/v1/users/me", nil, user.Token)
	s.Require().Equal(http.StatusOK, webUserRec.Code)

	initialActiveOrg := gjson.Get(webUserRec.Body.String(), "active_organization.id").String()
	s.Assert().NotEmpty(initialActiveOrg)

	// Step 2: Switch to org2 from mobile
	switchData := map[string]interface{}{
		"organization_id": org2.String(),
	}

	mobileSwitchRec := s.SimulateMobileOperation(t, "POST", "/api/v1/auth/switch-org", switchData, user.Token)
	s.Require().Equal(http.StatusOK, mobileSwitchRec.Code)

	// Get new token after switch
	var switchResp map[string]interface{}
	s.Require().NoError(json.Unmarshal(mobileSwitchRec.Body.Bytes(), &switchResp))
	newToken := switchResp["access_token"].(string)

	// Step 3: Verify web sees the same active org
	webAfterSwitchRec := s.SimulateWebOperation(t, "GET", "/api/v1/users/me", nil, newToken)
	s.Require().Equal(http.StatusOK, webAfterSwitchRec.Code)

	webActiveOrg := gjson.Get(webAfterSwitchRec.Body.String(), "active_organization.id").String()
	s.Assert().Equal(org2.String(), webActiveOrg, "Web should see org2 as active after mobile switch")

	// Step 4: Create content in org2 from web
	eventData := map[string]interface{}{
		"title":       "Test Event in Org2",
		"description": "Created after org switch",
		"start_time":  time.Now().Add(24 * time.Hour).Format(time.RFC3339),
		"end_time":    time.Now().Add(25 * time.Hour).Format(time.RFC3339),
	}

	webEventRec := s.SimulateWebOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/events", org2.String()), eventData, newToken)
	s.Require().Equal(http.StatusCreated, webEventRec.Code)

	var eventResp map[string]interface{}
	s.Require().NoError(json.Unmarshal(webEventRec.Body.Bytes(), &eventResp))
	eventID := eventResp["id"].(string)

	// Step 5: Verify mobile sees the event in org2
	mobileEventRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s/events/%s", org2.String(), eventID), nil, newToken)
	s.Require().Equal(http.StatusOK, mobileEventRec.Code)

	// Step 6: Switch back to org1 from web
	switchBackData := map[string]interface{}{
		"organization_id": org1.String(),
	}

	webSwitchBackRec := s.SimulateWebOperation(t, "POST", "/api/v1/auth/switch-org", switchBackData, newToken)
	s.Require().Equal(http.StatusOK, webSwitchBackRec.Code)

	var switchBackResp map[string]interface{}
	s.Require().NoError(json.Unmarshal(webSwitchBackRec.Body.Bytes(), &switchBackResp))
	org1Token := switchBackResp["access_token"].(string)

	// Step 7: Verify mobile can't see org2 event with org1 token
	mobileForbiddenRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s/events/%s", org2.String(), eventID), nil, org1Token)
	s.Assert().Equal(http.StatusForbidden, mobileForbiddenRec.Code, "Should not access org2 content with org1 token")
}

// TestTokenRefreshConsistency validates token refresh works identically
func (s *AuthenticationStateTestSuite) TestTokenRefreshConsistency() {
	t := s.T()

	// Create test user and get tokens
	user := s.CreateTestUser(t, "test-refresh", CreateTestPhoneNumber(), "<EMAIL>")

	// Simulate getting refresh token (in real flow this comes from login)
	// For test purposes, we'll use the initial token as both access and refresh

	// Step 1: Use token from web
	webRec := s.SimulateWebOperation(t, "GET", "/api/v1/users/me", nil, user.Token)
	s.Require().Equal(http.StatusOK, webRec.Code)

	// Step 2: Refresh from mobile
	refreshData := map[string]interface{}{
		"refresh_token": user.Token, // In test mode, using access token as refresh
	}

	mobileRefreshRec := s.SimulateMobileOperation(t, "POST", "/api/v1/authn/refresh", refreshData, "")
	// Note: This might return 401 in test mode if refresh endpoint is strict
	// Adjust based on actual API behavior

	if mobileRefreshRec.Code == http.StatusOK {
		var refreshResp map[string]interface{}
		s.Require().NoError(json.Unmarshal(mobileRefreshRec.Body.Bytes(), &refreshResp))
		newToken := refreshResp["access_token"].(string)

		// Step 3: Verify new token works on both platforms
		webNewRec := s.SimulateWebOperation(t, "GET", "/api/v1/users/me", nil, newToken)
		mobileNewRec := s.SimulateMobileOperation(t, "GET", "/api/v1/users/me", nil, newToken)

		s.Assert().Equal(http.StatusOK, webNewRec.Code)
		s.Assert().Equal(http.StatusOK, mobileNewRec.Code)

		// Verify user data is consistent
		webUserID := gjson.Get(webNewRec.Body.String(), "id").String()
		mobileUserID := gjson.Get(mobileNewRec.Body.String(), "id").String()
		s.Assert().Equal(user.ID.String(), webUserID)
		s.Assert().Equal(user.ID.String(), mobileUserID)
	}
}

// TestConcurrentAuthStateChanges validates concurrent auth operations
func (s *AuthenticationStateTestSuite) TestConcurrentAuthStateChanges() {
	t := s.T()

	// Create test user
	user := s.CreateTestUser(t, "test-concurrent-auth", CreateTestPhoneNumber(), "<EMAIL>")

	// Create multiple organizations
	orgs := make([]uuid.UUID, 3)
	for i := 0; i < 3; i++ {
		org := s.CreateTestOrganization(t, fmt.Sprintf("test-concurrent-org-%d", i), user.Token)
		orgs[i] = org
	}

	// Run concurrent org switches from different platforms
	var wg sync.WaitGroup
	errors := make(chan error, 10)
	results := make(chan string, 10)

	// Web clients switching orgs
	for i := 0; i < 3; i++ {
		wg.Add(1)
		go func(orgIndex int) {
			defer wg.Done()

			switchData := map[string]interface{}{
				"organization_id": orgs[orgIndex].String(),
			}

			rec := s.SimulateWebOperation(t, "POST", "/api/v1/auth/switch-org", switchData, user.Token)
			if rec.Code != http.StatusOK {
				errors <- fmt.Errorf("web switch to org %d failed: %d", orgIndex, rec.Code)
				return
			}

			var resp map[string]interface{}
			if err := json.Unmarshal(rec.Body.Bytes(), &resp); err != nil {
				errors <- err
				return
			}

			newToken := resp["access_token"].(string)
			results <- newToken
		}(i)
	}

	// Mobile clients switching orgs
	for i := 0; i < 3; i++ {
		wg.Add(1)
		go func(orgIndex int) {
			defer wg.Done()

			switchData := map[string]interface{}{
				"organization_id": orgs[orgIndex].String(),
			}

			rec := s.SimulateMobileOperation(t, "POST", "/api/v1/auth/switch-org", switchData, user.Token)
			if rec.Code != http.StatusOK {
				errors <- fmt.Errorf("mobile switch to org %d failed: %d", orgIndex, rec.Code)
				return
			}

			var resp map[string]interface{}
			if err := json.Unmarshal(rec.Body.Bytes(), &resp); err != nil {
				errors <- err
				return
			}

			newToken := resp["access_token"].(string)
			results <- newToken
		}(i)
	}

	wg.Wait()
	close(errors)
	close(results)

	// Check for errors
	for err := range errors {
		t.Errorf("Concurrent operation error: %v", err)
	}

	// Verify all tokens are valid
	tokenCount := 0
	for newToken := range results {
		tokenCount++
		rec := s.SimulateWebOperation(t, "GET", "/api/v1/users/me", nil, newToken)
		s.Assert().Equal(http.StatusOK, rec.Code, "Token should be valid")
	}

	s.Assert().Equal(6, tokenCount, "Should have 6 successful token switches")
}

func TestAuthenticationStateSuite(t *testing.T) {
	suite.Run(t, new(AuthenticationStateTestSuite))
}
