package crossplatform

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// ContentManagementTestSuite tests content/post management across platforms
type ContentManagementTestSuite struct {
	suite.Suite
	*CrossPlatformTestSuite
}

func (s *ContentManagementTestSuite) SetupSuite() {
	s.CrossPlatformTestSuite = SetupCrossPlatformTest(s.T())
}

func (s *ContentManagementTestSuite) TearDownSuite() {
	s.CleanupTestData(s.T())
	s.TeardownCrossPlatformTest(s.T())
}

// Test 16: Post Creation and Publishing
func (s *ContentManagementTestSuite) TestPostCreationPublishing() {
	t := s.T()

	// Create content managers
	author1 := s.CreateTestUser(t, "author1", "+15550004001", "<EMAIL>")
	author2 := s.CreateTestUser(t, "author2", "+15550004002", "<EMAIL>")
	reader := s.CreateTestUser(t, "reader", "+15550004003", "<EMAIL>")

	// Create organization
	orgID := s.CreateTestOrganization(t, "content-org", author1.Token)

	// Add author2 as manager, reader as member
	addAuthor2Req := map[string]interface{}{
		"user_id": author2.ID.String(),
		"role":    "manager",
	}
	rec := s.SimulateWebOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/members", orgID), addAuthor2Req, author1.Token)
	require.Equal(t, http.StatusCreated, rec.Code)

	addReaderReq := map[string]interface{}{
		"user_id": reader.ID.String(),
		"role":    "member",
	}
	rec = s.SimulateMobileOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/members", orgID), addReaderReq, author1.Token)
	require.Equal(t, http.StatusCreated, rec.Code)

	// Author1 creates draft post from web
	webPostReq := map[string]interface{}{
		"title":           "Cross-Platform Content Test",
		"content":         "This is a test post created from the web platform.",
		"content_type":    "article",
		"organization_id": orgID.String(),
		"tags":            []string{"test", "cross-platform", "article"},
		"is_published":    false,
		"metadata": map[string]interface{}{
			"author":       author1.Email,
			"reading_time": "5 minutes",
			"featured":     true,
		},
	}

	webCreateRec := s.SimulateWebOperation(t, "POST", "/api/v1/posts", webPostReq, author1.Token)
	require.Equal(t, http.StatusCreated, webCreateRec.Code)

	var webPostResp map[string]interface{}
	err := json.Unmarshal(webCreateRec.Body.Bytes(), &webPostResp)
	require.NoError(t, err)
	postID := webPostResp["id"].(string)
	s.testData.Posts["test-post-1"] = uuid.MustParse(postID)

	// Author2 reviews and edits from mobile
	mobileEditReq := map[string]interface{}{
		"content": webPostReq["content"].(string) + "\n\nEdited by second author from mobile app.",
		"tags":    []string{"test", "cross-platform", "article", "reviewed"},
	}

	mobileEditRec := s.SimulateMobileOperation(t, "PUT", fmt.Sprintf("/api/v1/posts/%s", postID), mobileEditReq, author2.Token)
	require.Equal(t, http.StatusOK, mobileEditRec.Code)

	// Publish from web
	webPublishReq := map[string]interface{}{
		"is_published": true,
		"published_at": time.Now().Format(time.RFC3339),
	}

	webPublishRec := s.SimulateWebOperation(t, "PATCH", fmt.Sprintf("/api/v1/posts/%s/publish", postID), webPublishReq, author1.Token)
	require.Equal(t, http.StatusOK, webPublishRec.Code)

	// Verify published post is accessible from both platforms
	webReadRec := s.SimulateWebOperation(t, "GET", fmt.Sprintf("/api/v1/posts/%s", postID), nil, reader.Token)
	mobileReadRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/posts/%s", postID), nil, reader.Token)

	require.Equal(t, http.StatusOK, webReadRec.Code)
	require.Equal(t, http.StatusOK, mobileReadRec.Code)

	var webRead, mobileRead map[string]interface{}
	json.Unmarshal(webReadRec.Body.Bytes(), &webRead)
	json.Unmarshal(mobileReadRec.Body.Bytes(), &mobileRead)

	assert.Equal(t, true, webRead["is_published"])
	assert.Equal(t, true, mobileRead["is_published"])
	assert.Contains(t, mobileRead["content"], "Edited by second author")
}

// Test 17: Content Versioning and History
func (s *ContentManagementTestSuite) TestContentVersioningHistory() {
	t := s.T()

	// Create author
	author := s.CreateTestUser(t, "version-author", "+15550004004", "<EMAIL>")
	orgID := s.CreateTestOrganization(t, "version-org", author.Token)

	// Create initial post
	initialReq := map[string]interface{}{
		"title":             "Versioned Content",
		"content":           "Version 1: Initial content",
		"content_type":      "announcement",
		"organization_id":   orgID.String(),
		"is_published":      true,
		"enable_versioning": true,
	}

	createRec := s.SimulateWebOperation(t, "POST", "/api/v1/posts", initialReq, author.Token)
	require.Equal(t, http.StatusCreated, createRec.Code)

	var postResp map[string]interface{}
	json.Unmarshal(createRec.Body.Bytes(), &postResp)
	postID := postResp["id"].(string)

	// Make multiple edits from different platforms
	edits := []struct {
		platform string
		content  string
		summary  string
	}{
		{"mobile", "Version 2: Updated from mobile", "Mobile update"},
		{"web", "Version 3: Updated from web", "Web revision"},
		{"mobile", "Version 4: Final version from mobile", "Final edit"},
	}

	for _, edit := range edits {
		updateReq := map[string]interface{}{
			"content":         edit.content,
			"version_summary": edit.summary,
		}

		var rec *httptest.ResponseRecorder
		if edit.platform == "web" {
			rec = s.SimulateWebOperation(t, "PUT", fmt.Sprintf("/api/v1/posts/%s", postID), updateReq, author.Token)
		} else {
			rec = s.SimulateMobileOperation(t, "PUT", fmt.Sprintf("/api/v1/posts/%s", postID), updateReq, author.Token)
		}
		require.Equal(t, http.StatusOK, rec.Code)

		// Small delay to ensure version timestamps differ
		time.Sleep(100 * time.Millisecond)
	}

	// Get version history from both platforms
	webHistoryRec := s.SimulateWebOperation(t, "GET", fmt.Sprintf("/api/v1/posts/%s/versions", postID), nil, author.Token)
	mobileHistoryRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/posts/%s/versions", postID), nil, author.Token)

	if webHistoryRec.Code == http.StatusOK && mobileHistoryRec.Code == http.StatusOK {
		var webHistory, mobileHistory map[string]interface{}
		json.Unmarshal(webHistoryRec.Body.Bytes(), &webHistory)
		json.Unmarshal(mobileHistoryRec.Body.Bytes(), &mobileHistory)

		// Validate version consistency
		s.ValidateDataConsistency(t, webHistory, mobileHistory)

		versions := webHistory["versions"].([]interface{})
		assert.GreaterOrEqual(t, len(versions), 4) // Initial + 3 edits

		// Restore previous version from mobile
		secondVersion := versions[1].(map[string]interface{})
		versionID := secondVersion["id"].(string)

		restoreReq := map[string]interface{}{
			"version_id": versionID,
			"reason":     "Reverting to earlier version",
		}

		restoreRec := s.SimulateMobileOperation(t, "POST", fmt.Sprintf("/api/v1/posts/%s/restore", postID), restoreReq, author.Token)
		assert.Equal(t, http.StatusOK, restoreRec.Code)

		// Verify content is restored
		finalRec := s.SimulateWebOperation(t, "GET", fmt.Sprintf("/api/v1/posts/%s", postID), nil, author.Token)
		require.Equal(t, http.StatusOK, finalRec.Code)

		var finalPost map[string]interface{}
		json.Unmarshal(finalRec.Body.Bytes(), &finalPost)
		assert.Contains(t, finalPost["content"], "Version 2")
	}
}

// Test 18: Media Attachments and File Handling
func (s *ContentManagementTestSuite) TestMediaAttachmentsFileHandling() {
	t := s.T()

	// Create users
	creator := s.CreateTestUser(t, "media-creator", "+15550004005", "<EMAIL>")
	viewer := s.CreateTestUser(t, "media-viewer", "+15550004006", "<EMAIL>")

	// Create organization
	orgID := s.CreateTestOrganization(t, "media-org", creator.Token)

	// Add viewer as member
	addViewerReq := map[string]interface{}{
		"user_id": viewer.ID.String(),
		"role":    "member",
	}
	rec := s.SimulateWebOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/members", orgID), addViewerReq, creator.Token)
	require.Equal(t, http.StatusCreated, rec.Code)

	// Create post with media references
	postReq := map[string]interface{}{
		"title":           "Media-Rich Content",
		"content":         "Check out these images and documents!",
		"content_type":    "gallery",
		"organization_id": orgID.String(),
		"is_published":    true,
		"media": []map[string]interface{}{
			{
				"type":          "image",
				"url":           "/uploads/images/test-image-1.jpg",
				"caption":       "Test image from web upload",
				"alt_text":      "Description of test image",
				"uploaded_from": "web",
			},
			{
				"type":          "document",
				"url":           "/uploads/docs/test-document.pdf",
				"filename":      "test-document.pdf",
				"size":          1048576, // 1MB
				"uploaded_from": "web",
			},
		},
	}

	createRec := s.SimulateWebOperation(t, "POST", "/api/v1/posts", postReq, creator.Token)
	require.Equal(t, http.StatusCreated, createRec.Code)

	var postResp map[string]interface{}
	json.Unmarshal(createRec.Body.Bytes(), &postResp)
	postID := postResp["id"].(string)

	// Add more media from mobile
	mobileMediaReq := map[string]interface{}{
		"media": []map[string]interface{}{
			{
				"type":          "image",
				"url":           "/uploads/images/mobile-image-1.jpg",
				"caption":       "Added from mobile app",
				"alt_text":      "Mobile uploaded image",
				"uploaded_from": "mobile",
			},
			{
				"type":          "video",
				"url":           "/uploads/videos/mobile-video.mp4",
				"thumbnail":     "/uploads/videos/mobile-video-thumb.jpg",
				"duration":      120, // 2 minutes
				"uploaded_from": "mobile",
			},
		},
	}

	mobileAddRec := s.SimulateMobileOperation(t, "POST", fmt.Sprintf("/api/v1/posts/%s/media", postID), mobileMediaReq, creator.Token)
	require.Equal(t, http.StatusOK, mobileAddRec.Code)

	// Viewer accesses post with media from both platforms
	webViewRec := s.SimulateWebOperation(t, "GET", fmt.Sprintf("/api/v1/posts/%s", postID), nil, viewer.Token)
	mobileViewRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/posts/%s", postID), nil, viewer.Token)

	require.Equal(t, http.StatusOK, webViewRec.Code)
	require.Equal(t, http.StatusOK, mobileViewRec.Code)

	var webView, mobileView map[string]interface{}
	json.Unmarshal(webViewRec.Body.Bytes(), &webView)
	json.Unmarshal(mobileViewRec.Body.Bytes(), &mobileView)

	// Verify all media is present
	webMedia := webView["media"].([]interface{})
	mobileMedia := mobileView["media"].([]interface{})

	assert.Equal(t, 4, len(webMedia)) // 2 initial + 2 added
	assert.Equal(t, 4, len(mobileMedia))

	// Remove specific media item from web
	if len(webMedia) > 0 {
		firstMediaID := webMedia[0].(map[string]interface{})["id"].(string)

		removeReq := map[string]interface{}{
			"media_id": firstMediaID,
			"reason":   "Replacing with updated version",
		}

		removeRec := s.SimulateWebOperation(t, "DELETE", fmt.Sprintf("/api/v1/posts/%s/media/%s", postID, firstMediaID), removeReq, creator.Token)
		assert.Equal(t, http.StatusOK, removeRec.Code)

		// Verify removal from mobile view
		finalRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/posts/%s", postID), nil, viewer.Token)
		require.Equal(t, http.StatusOK, finalRec.Code)

		var finalView map[string]interface{}
		json.Unmarshal(finalRec.Body.Bytes(), &finalView)

		finalMedia := finalView["media"].([]interface{})
		assert.Equal(t, 3, len(finalMedia))
	}
}

// Test 19: Content Search and Filtering
func (s *ContentManagementTestSuite) TestContentSearchFiltering() {
	t := s.T()

	// Create author
	author := s.CreateTestUser(t, "search-author", "+15550004007", "<EMAIL>")
	orgID := s.CreateTestOrganization(t, "search-org", author.Token)

	// Create multiple posts with different attributes
	posts := []map[string]interface{}{
		{
			"title":           "Technology News Update",
			"content":         "Latest updates in artificial intelligence and machine learning.",
			"content_type":    "news",
			"tags":            []string{"tech", "ai", "ml", "news"},
			"is_published":    true,
			"organization_id": orgID.String(),
		},
		{
			"title":           "Community Event Announcement",
			"content":         "Join us for the annual technology conference next month.",
			"content_type":    "announcement",
			"tags":            []string{"event", "conference", "tech"},
			"is_published":    true,
			"organization_id": orgID.String(),
		},
		{
			"title":           "Tutorial: Getting Started with AI",
			"content":         "A comprehensive guide to understanding artificial intelligence basics.",
			"content_type":    "tutorial",
			"tags":            []string{"tutorial", "ai", "beginner"},
			"is_published":    true,
			"organization_id": orgID.String(),
		},
		{
			"title":           "Draft: Future Plans",
			"content":         "Internal planning document for next quarter.",
			"content_type":    "internal",
			"tags":            []string{"planning", "internal"},
			"is_published":    false,
			"organization_id": orgID.String(),
		},
	}

	// Create posts alternating between platforms
	createdPosts := make([]string, 0)
	for i, post := range posts {
		var rec *httptest.ResponseRecorder
		if i%2 == 0 {
			rec = s.SimulateWebOperation(t, "POST", "/api/v1/posts", post, author.Token)
		} else {
			rec = s.SimulateMobileOperation(t, "POST", "/api/v1/posts", post, author.Token)
		}
		require.Equal(t, http.StatusCreated, rec.Code)

		var resp map[string]interface{}
		json.Unmarshal(rec.Body.Bytes(), &resp)
		createdPosts = append(createdPosts, resp["id"].(string))
	}

	// Test search from web - keyword search
	webSearchRec := s.SimulateWebOperation(t, "GET", "/api/v1/posts?q=artificial+intelligence", nil, author.Token)
	require.Equal(t, http.StatusOK, webSearchRec.Code)

	var webSearchResp map[string]interface{}
	json.Unmarshal(webSearchRec.Body.Bytes(), &webSearchResp)

	webResults := webSearchResp["posts"].([]interface{})
	assert.GreaterOrEqual(t, len(webResults), 2) // Should find at least 2 posts with "artificial intelligence"

	// Test filter from mobile - by content type
	mobileFilterRec := s.SimulateMobileOperation(t, "GET", "/api/v1/posts?content_type=announcement", nil, author.Token)
	require.Equal(t, http.StatusOK, mobileFilterRec.Code)

	var mobileFilterResp map[string]interface{}
	json.Unmarshal(mobileFilterRec.Body.Bytes(), &mobileFilterResp)

	mobileResults := mobileFilterResp["posts"].([]interface{})
	assert.Equal(t, 1, len(mobileResults))

	// Test tag filtering from web
	tagFilterRec := s.SimulateWebOperation(t, "GET", "/api/v1/posts?tags=ai,tech", nil, author.Token)
	require.Equal(t, http.StatusOK, tagFilterRec.Code)

	var tagFilterResp map[string]interface{}
	json.Unmarshal(tagFilterRec.Body.Bytes(), &tagFilterResp)

	tagResults := tagFilterResp["posts"].([]interface{})
	assert.GreaterOrEqual(t, len(tagResults), 2)

	// Test combined filters from mobile
	combinedRec := s.SimulateMobileOperation(t, "GET", "/api/v1/posts?is_published=true&content_type=tutorial&tags=ai", nil, author.Token)
	require.Equal(t, http.StatusOK, combinedRec.Code)

	var combinedResp map[string]interface{}
	json.Unmarshal(combinedRec.Body.Bytes(), &combinedResp)

	combinedResults := combinedResp["posts"].([]interface{})
	assert.Equal(t, 1, len(combinedResults))

	// Verify draft posts are not visible to regular members
	member := s.CreateTestUser(t, "search-member", "+15550004008", "<EMAIL>")
	addMemberReq := map[string]interface{}{
		"user_id": member.ID.String(),
		"role":    "member",
	}
	s.SimulateWebOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/members", orgID), addMemberReq, author.Token)

	memberSearchRec := s.SimulateMobileOperation(t, "GET", "/api/v1/posts?q=planning", nil, member.Token)
	require.Equal(t, http.StatusOK, memberSearchRec.Code)

	var memberSearchResp map[string]interface{}
	json.Unmarshal(memberSearchRec.Body.Bytes(), &memberSearchResp)

	memberResults := memberSearchResp["posts"].([]interface{})
	assert.Equal(t, 0, len(memberResults)) // Draft post should not be visible
}

// Test 20: Content Scheduling and Automation
func (s *ContentManagementTestSuite) TestContentSchedulingAutomation() {
	t := s.T()

	// Create content manager
	manager := s.CreateTestUser(t, "schedule-manager", "+15550004009", "<EMAIL>")
	orgID := s.CreateTestOrganization(t, "schedule-org", manager.Token)

	// Schedule post for future publication from web
	futureTime := time.Now().Add(2 * time.Hour)
	scheduledReq := map[string]interface{}{
		"title":           "Scheduled Announcement",
		"content":         "This post will be published automatically.",
		"content_type":    "announcement",
		"organization_id": orgID.String(),
		"is_published":    false,
		"scheduled_at":    futureTime.Format(time.RFC3339),
		"auto_publish":    true,
		"notification_settings": map[string]interface{}{
			"notify_on_publish": true,
			"notify_channels":   []string{"email", "push"},
		},
	}

	webScheduleRec := s.SimulateWebOperation(t, "POST", "/api/v1/posts", scheduledReq, manager.Token)
	require.Equal(t, http.StatusCreated, webScheduleRec.Code)

	var scheduledResp map[string]interface{}
	json.Unmarshal(webScheduleRec.Body.Bytes(), &scheduledResp)
	scheduledID := scheduledResp["id"].(string)

	// Verify scheduled status from mobile
	mobileCheckRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/posts/%s", scheduledID), nil, manager.Token)
	require.Equal(t, http.StatusOK, mobileCheckRec.Code)

	var mobileCheck map[string]interface{}
	json.Unmarshal(mobileCheckRec.Body.Bytes(), &mobileCheck)

	assert.Equal(t, false, mobileCheck["is_published"])
	assert.Equal(t, "scheduled", mobileCheck["status"])
	assert.Equal(t, futureTime.Format(time.RFC3339), mobileCheck["scheduled_at"])

	// Update scheduled time from mobile
	newFutureTime := time.Now().Add(4 * time.Hour)
	rescheduleReq := map[string]interface{}{
		"scheduled_at": newFutureTime.Format(time.RFC3339),
		"reason":       "Adjusting publication time",
	}

	mobileRescheduleRec := s.SimulateMobileOperation(t, "PATCH", fmt.Sprintf("/api/v1/posts/%s/schedule", scheduledID), rescheduleReq, manager.Token)
	require.Equal(t, http.StatusOK, mobileRescheduleRec.Code)

	// Create recurring content schedule
	recurringReq := map[string]interface{}{
		"title_template":   "Weekly Newsletter - {{date}}",
		"content_template": "This week's updates for {{organization_name}}...",
		"content_type":     "newsletter",
		"organization_id":  orgID.String(),
		"schedule": map[string]interface{}{
			"type":      "recurring",
			"frequency": "weekly",
			"day":       "monday",
			"time":      "09:00",
			"timezone":  "America/New_York",
		},
		"auto_generate": true,
		"template_variables": map[string]string{
			"organization_name": "Schedule Test Org",
		},
	}

	recurringRec := s.SimulateWebOperation(t, "POST", "/api/v1/posts/templates", recurringReq, manager.Token)
	if recurringRec.Code == http.StatusCreated {
		var recurringResp map[string]interface{}
		json.Unmarshal(recurringRec.Body.Bytes(), &recurringResp)
		templateID := recurringResp["template_id"].(string)

		// View scheduled posts queue from mobile
		queueRec := s.SimulateMobileOperation(t, "GET", "/api/v1/posts/scheduled", nil, manager.Token)
		require.Equal(t, http.StatusOK, queueRec.Code)

		var queueResp map[string]interface{}
		json.Unmarshal(queueRec.Body.Bytes(), &queueResp)

		scheduledPosts := queueResp["scheduled_posts"].([]interface{})
		assert.GreaterOrEqual(t, len(scheduledPosts), 1)

		// Cancel recurring schedule from web
		cancelRec := s.SimulateWebOperation(t, "DELETE", fmt.Sprintf("/api/v1/posts/templates/%s", templateID), nil, manager.Token)
		assert.Equal(t, http.StatusOK, cancelRec.Code)
	}

	// Test immediate publication override
	publishNowReq := map[string]interface{}{
		"publish_now": true,
		"reason":      "Urgent announcement",
	}

	publishNowRec := s.SimulateMobileOperation(t, "POST", fmt.Sprintf("/api/v1/posts/%s/publish-now", scheduledID), publishNowReq, manager.Token)
	require.Equal(t, http.StatusOK, publishNowRec.Code)

	// Verify immediate publication
	finalCheckRec := s.SimulateWebOperation(t, "GET", fmt.Sprintf("/api/v1/posts/%s", scheduledID), nil, manager.Token)
	require.Equal(t, http.StatusOK, finalCheckRec.Code)

	var finalCheck map[string]interface{}
	json.Unmarshal(finalCheckRec.Body.Bytes(), &finalCheck)

	assert.Equal(t, true, finalCheck["is_published"])
	assert.Equal(t, "published", finalCheck["status"])
}

func TestContentManagementSuite(t *testing.T) {
	suite.Run(t, new(ContentManagementTestSuite))
}
