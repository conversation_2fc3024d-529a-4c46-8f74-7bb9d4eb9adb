package crossplatform

import (
	"testing"

	"github.com/stretchr/testify/suite"
)

// CrossPlatformTestRunner runs all cross-platform test suites
func TestCrossPlatformSuites(t *testing.T) {
	// Run all test suites
	suites := []suite.TestingSuite{
		// Original test suites (26 scenarios)
		new(OrganizationManagementTestSuite),
		new(EventLifecycleTestSuite),
		new(MemberPermissionsTestSuite),
		new(ContentManagementTestSuite),
		new(AdvancedScenariosTestSuite),

		// New test suites (15 additional scenarios)
		new(AuthenticationStateTestSuite),
		new(APIContractTestSuite),
		new(SyncStateTestSuite),
	}

	for _, s := range suites {
		suite.Run(t, s)
	}
}

// Individual test runners are also available in each test file
// This allows running specific test suites independently:
// - TestOrganizationManagementSuite
// - TestEventLifecycleSuite
// - TestMemberPermissionsSuite
// - TestContentManagementSuite
// - TestAdvancedScenariosSuite
// - TestAuthenticationStateSuite
// - TestAPIContractSuite
// - TestSyncStateSuite
