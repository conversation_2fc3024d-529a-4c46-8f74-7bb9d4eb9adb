package crossplatform

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// EventLifecycleTestSuite tests event lifecycle across platforms
type EventLifecycleTestSuite struct {
	suite.Suite
	*CrossPlatformTestSuite
}

func (s *EventLifecycleTestSuite) SetupSuite() {
	s.CrossPlatformTestSuite = SetupCrossPlatformTest(s.T())
}

func (s *EventLifecycleTestSuite) TearDownSuite() {
	s.CleanupTestData(s.T())
	s.TeardownCrossPlatformTest(s.T())
}

// Test 6: Event Creation and Publication Flow
func (s *EventLifecycleTestSuite) TestEventCreationPublicationFlow() {
	t := s.T()

	// Create user and organization
	user := s.CreateTestUser(t, "event-creator", "+15550002001", "<EMAIL>")
	orgID := s.CreateTestOrganization(t, "event-org", user.Token)

	// Create draft event from web
	webEventReq := map[string]interface{}{
		"title":                 "Cross Platform Event",
		"description":           "Event created from web as draft",
		"event_type":            "workshop",
		"start_date":            time.Now().Add(48 * time.Hour).Format(time.RFC3339),
		"end_date":              time.Now().Add(52 * time.Hour).Format(time.RFC3339),
		"registration_deadline": time.Now().Add(24 * time.Hour).Format(time.RFC3339),
		"max_participants":      30,
		"location":              "Virtual",
		"is_published":          false, // Start as draft
		"organization_id":       orgID.String(),
		"tags":                  []string{"workshop", "online", "tech"},
	}

	webCreateRec := s.SimulateWebOperation(t, "POST", "/api/v1/events", webEventReq, user.Token)
	require.Equal(t, http.StatusCreated, webCreateRec.Code)

	var webEventResp map[string]interface{}
	err := json.Unmarshal(webCreateRec.Body.Bytes(), &webEventResp)
	require.NoError(t, err)
	eventID := webEventResp["id"].(string)

	// Verify draft is visible from mobile
	mobileGetRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/events/%s", eventID), nil, user.Token)
	require.Equal(t, http.StatusOK, mobileGetRec.Code)

	var mobileEventResp map[string]interface{}
	err = json.Unmarshal(mobileGetRec.Body.Bytes(), &mobileEventResp)
	require.NoError(t, err)

	assert.Equal(t, false, mobileEventResp["is_published"])
	assert.Equal(t, "draft", mobileEventResp["status"])

	// Update event details from mobile
	mobileUpdateReq := map[string]interface{}{
		"description":      "Updated description from mobile app",
		"max_participants": 50,
		"requirements":     []string{"Laptop required", "Basic programming knowledge"},
	}

	mobileUpdateRec := s.SimulateMobileOperation(t, "PUT", fmt.Sprintf("/api/v1/events/%s", eventID), mobileUpdateReq, user.Token)
	require.Equal(t, http.StatusOK, mobileUpdateRec.Code)

	// Publish event from web
	webPublishReq := map[string]interface{}{
		"is_published": true,
	}

	webPublishRec := s.SimulateWebOperation(t, "PATCH", fmt.Sprintf("/api/v1/events/%s/publish", eventID), webPublishReq, user.Token)
	require.Equal(t, http.StatusOK, webPublishRec.Code)

	// Verify published status from both platforms
	webFinalRec := s.SimulateWebOperation(t, "GET", fmt.Sprintf("/api/v1/events/%s", eventID), nil, user.Token)
	mobileFinalRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/events/%s", eventID), nil, user.Token)

	require.Equal(t, http.StatusOK, webFinalRec.Code)
	require.Equal(t, http.StatusOK, mobileFinalRec.Code)

	var webFinal, mobileFinal map[string]interface{}
	json.Unmarshal(webFinalRec.Body.Bytes(), &webFinal)
	json.Unmarshal(mobileFinalRec.Body.Bytes(), &mobileFinal)

	assert.Equal(t, true, webFinal["is_published"])
	assert.Equal(t, true, mobileFinal["is_published"])
	assert.Equal(t, "published", webFinal["status"])
	assert.Equal(t, "published", mobileFinal["status"])
}

// Test 7: Event Registration Management
func (s *EventLifecycleTestSuite) TestEventRegistrationManagement() {
	t := s.T()

	// Create organizer and attendees
	organizer := s.CreateTestUser(t, "event-organizer", "+15550002002", "<EMAIL>")
	attendee1 := s.CreateTestUser(t, "attendee1", "+15550002003", "<EMAIL>")
	attendee2 := s.CreateTestUser(t, "attendee2", "+15550002004", "<EMAIL>")

	// Create organization and event
	orgID := s.CreateTestOrganization(t, "reg-test-org", organizer.Token)
	eventID := s.CreateTestEvent(t, "reg-test-event", orgID, organizer.Token)

	// Attendee1 registers from web
	webRegReq := map[string]interface{}{
		"user_id":              attendee1.ID.String(),
		"dietary_restrictions": "Vegetarian",
		"accessibility_needs":  "Wheelchair access",
		"emergency_contact":    "+15550002005",
		"additional_notes":     "Looking forward to the event",
	}

	webRegRec := s.SimulateWebOperation(t, "POST", fmt.Sprintf("/api/v1/events/%s/register", eventID), webRegReq, attendee1.Token)
	require.Equal(t, http.StatusCreated, webRegRec.Code)

	// Attendee2 registers from mobile
	mobileRegReq := map[string]interface{}{
		"user_id":              attendee2.ID.String(),
		"dietary_restrictions": "Gluten-free",
		"emergency_contact":    "+15550002006",
		"transport_needed":     true,
	}

	mobileRegRec := s.SimulateMobileOperation(t, "POST", fmt.Sprintf("/api/v1/events/%s/register", eventID), mobileRegReq, attendee2.Token)
	require.Equal(t, http.StatusCreated, mobileRegRec.Code)

	// Organizer views registrations from both platforms
	webRegListRec := s.SimulateWebOperation(t, "GET", fmt.Sprintf("/api/v1/events/%s/registrations", eventID), nil, organizer.Token)
	mobileRegListRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/events/%s/registrations", eventID), nil, organizer.Token)

	require.Equal(t, http.StatusOK, webRegListRec.Code)
	require.Equal(t, http.StatusOK, mobileRegListRec.Code)

	var webRegList, mobileRegList map[string]interface{}
	json.Unmarshal(webRegListRec.Body.Bytes(), &webRegList)
	json.Unmarshal(mobileRegListRec.Body.Bytes(), &mobileRegList)

	// Validate data consistency
	s.ValidateDataConsistency(t, webRegList, mobileRegList)

	registrations := webRegList["registrations"].([]interface{})
	assert.Equal(t, 2, len(registrations))

	// Organizer approves registration from mobile
	var reg1ID string
	for _, reg := range registrations {
		regMap := reg.(map[string]interface{})
		if regMap["user_id"] == attendee1.ID.String() {
			reg1ID = regMap["id"].(string)
			break
		}
	}

	approveReq := map[string]interface{}{
		"status": "confirmed",
		"notes":  "Registration confirmed",
	}

	mobileApproveRec := s.SimulateMobileOperation(t, "PUT", fmt.Sprintf("/api/v1/events/%s/registrations/%s", eventID, reg1ID), approveReq, organizer.Token)
	require.Equal(t, http.StatusOK, mobileApproveRec.Code)

	// Attendee1 cancels registration from web
	webCancelRec := s.SimulateWebOperation(t, "DELETE", fmt.Sprintf("/api/v1/events/%s/registrations/%s", eventID, reg1ID), nil, attendee1.Token)
	require.Equal(t, http.StatusOK, webCancelRec.Code)

	// Verify cancellation reflected in mobile
	mobileFinalListRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/events/%s/registrations", eventID), nil, organizer.Token)
	require.Equal(t, http.StatusOK, mobileFinalListRec.Code)

	var mobileFinalList map[string]interface{}
	json.Unmarshal(mobileFinalListRec.Body.Bytes(), &mobileFinalList)

	finalRegistrations := mobileFinalList["registrations"].([]interface{})
	assert.Equal(t, 1, len(finalRegistrations)) // Only attendee2 remains
}

// Test 8: Event Updates and Notifications
func (s *EventLifecycleTestSuite) TestEventUpdatesAndNotifications() {
	t := s.T()

	// Create users
	organizer := s.CreateTestUser(t, "notif-organizer", "+15550002007", "<EMAIL>")
	attendee := s.CreateTestUser(t, "notif-attendee", "+15550002008", "<EMAIL>")

	// Create event with notification settings
	orgID := s.CreateTestOrganization(t, "notif-org", organizer.Token)

	eventReq := map[string]interface{}{
		"title":                 "Notification Test Event",
		"description":           "Testing event update notifications",
		"event_type":            "meetup",
		"start_date":            time.Now().Add(72 * time.Hour).Format(time.RFC3339),
		"end_date":              time.Now().Add(74 * time.Hour).Format(time.RFC3339),
		"registration_deadline": time.Now().Add(48 * time.Hour).Format(time.RFC3339),
		"max_participants":      20,
		"location":              "Conference Room A",
		"is_published":          true,
		"organization_id":       orgID.String(),
		"notification_settings": map[string]interface{}{
			"send_updates":     true,
			"reminder_24h":     true,
			"reminder_1h":      true,
			"update_attendees": true,
		},
	}

	createRec := s.SimulateWebOperation(t, "POST", "/api/v1/events", eventReq, organizer.Token)
	require.Equal(t, http.StatusCreated, createRec.Code)

	var eventResp map[string]interface{}
	json.Unmarshal(createRec.Body.Bytes(), &eventResp)
	eventID := eventResp["id"].(string)

	// Attendee registers
	regReq := map[string]interface{}{
		"user_id": attendee.ID.String(),
	}
	regRec := s.SimulateMobileOperation(t, "POST", fmt.Sprintf("/api/v1/events/%s/register", eventID), regReq, attendee.Token)
	require.Equal(t, http.StatusCreated, regRec.Code)

	// Update event time from mobile
	timeUpdateReq := map[string]interface{}{
		"start_date":    time.Now().Add(96 * time.Hour).Format(time.RFC3339),
		"end_date":      time.Now().Add(98 * time.Hour).Format(time.RFC3339),
		"update_reason": "Venue availability change",
	}

	mobileUpdateRec := s.SimulateMobileOperation(t, "PUT", fmt.Sprintf("/api/v1/events/%s", eventID), timeUpdateReq, organizer.Token)
	require.Equal(t, http.StatusOK, mobileUpdateRec.Code)

	// Check notifications queue (simulated)
	webNotifsRec := s.SimulateWebOperation(t, "GET", "/api/v1/notifications/pending", nil, organizer.Token)
	require.Equal(t, http.StatusOK, webNotifsRec.Code)

	var notifications map[string]interface{}
	json.Unmarshal(webNotifsRec.Body.Bytes(), &notifications)

	// Update event location from web
	locationUpdateReq := map[string]interface{}{
		"location": "Virtual - Zoom Link",
		"location_details": map[string]string{
			"platform": "Zoom",
			"link":     "https://zoom.us/j/123456789",
			"passcode": "abc123",
		},
		"update_reason": "Moving to virtual format",
	}

	webUpdateRec := s.SimulateWebOperation(t, "PUT", fmt.Sprintf("/api/v1/events/%s", eventID), locationUpdateReq, organizer.Token)
	require.Equal(t, http.StatusOK, webUpdateRec.Code)

	// Verify update history
	historyRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/events/%s/history", eventID), nil, organizer.Token)
	require.Equal(t, http.StatusOK, historyRec.Code)

	var history map[string]interface{}
	json.Unmarshal(historyRec.Body.Bytes(), &history)

	updates := history["updates"].([]interface{})
	assert.GreaterOrEqual(t, len(updates), 2) // At least 2 updates
}

// Test 9: Event Cancellation Flow
func (s *EventLifecycleTestSuite) TestEventCancellationFlow() {
	t := s.T()

	// Create users
	organizer := s.CreateTestUser(t, "cancel-organizer", "+15550002009", "<EMAIL>")
	attendee1 := s.CreateTestUser(t, "cancel-attendee1", "+15550002010", "<EMAIL>")
	attendee2 := s.CreateTestUser(t, "cancel-attendee2", "+15550002011", "<EMAIL>")

	// Create and publish event
	orgID := s.CreateTestOrganization(t, "cancel-org", organizer.Token)
	eventID := s.CreateTestEvent(t, "cancel-event", orgID, organizer.Token)

	// Register attendees
	for _, attendee := range []UserData{attendee1, attendee2} {
		regReq := map[string]interface{}{
			"user_id": attendee.ID.String(),
		}
		regRec := s.SimulateWebOperation(t, "POST", fmt.Sprintf("/api/v1/events/%s/register", eventID), regReq, attendee.Token)
		require.Equal(t, http.StatusCreated, regRec.Code)
	}

	// Cancel event from mobile
	cancelReq := map[string]interface{}{
		"status":              "cancelled",
		"cancellation_reason": "Unforeseen circumstances",
		"notify_attendees":    true,
		"refund_policy":       "Full refund will be processed within 5 business days",
	}

	mobileCancelRec := s.SimulateMobileOperation(t, "PUT", fmt.Sprintf("/api/v1/events/%s/cancel", eventID), cancelReq, organizer.Token)
	require.Equal(t, http.StatusOK, mobileCancelRec.Code)

	// Verify event status from web
	webStatusRec := s.SimulateWebOperation(t, "GET", fmt.Sprintf("/api/v1/events/%s", eventID), nil, organizer.Token)
	require.Equal(t, http.StatusOK, webStatusRec.Code)

	var webStatus map[string]interface{}
	json.Unmarshal(webStatusRec.Body.Bytes(), &webStatus)

	assert.Equal(t, "cancelled", webStatus["status"])
	assert.Equal(t, "Unforeseen circumstances", webStatus["cancellation_reason"])

	// Verify attendees can see cancellation
	for _, attendee := range []UserData{attendee1, attendee2} {
		attendeeRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/events/%s", eventID), nil, attendee.Token)
		require.Equal(t, http.StatusOK, attendeeRec.Code)

		var attendeeView map[string]interface{}
		json.Unmarshal(attendeeRec.Body.Bytes(), &attendeeView)

		assert.Equal(t, "cancelled", attendeeView["status"])
	}

	// Verify registrations are marked as cancelled
	regListRec := s.SimulateWebOperation(t, "GET", fmt.Sprintf("/api/v1/events/%s/registrations", eventID), nil, organizer.Token)
	require.Equal(t, http.StatusOK, regListRec.Code)

	var regList map[string]interface{}
	json.Unmarshal(regListRec.Body.Bytes(), &regList)

	registrations := regList["registrations"].([]interface{})
	for _, reg := range registrations {
		regMap := reg.(map[string]interface{})
		assert.Equal(t, "cancelled", regMap["status"])
	}
}

// Test 10: Recurring Event Management
func (s *EventLifecycleTestSuite) TestRecurringEventManagement() {
	t := s.T()

	// Create organizer
	organizer := s.CreateTestUser(t, "recurring-organizer", "+15550002012", "<EMAIL>")
	orgID := s.CreateTestOrganization(t, "recurring-org", organizer.Token)

	// Create recurring event series from web
	recurringReq := map[string]interface{}{
		"title":            "Weekly Team Meeting",
		"description":      "Regular team sync meeting",
		"event_type":       "meeting",
		"start_date":       time.Now().Add(24 * time.Hour).Format(time.RFC3339),
		"end_date":         time.Now().Add(25 * time.Hour).Format(time.RFC3339),
		"max_participants": 15,
		"location":         "Meeting Room B",
		"is_published":     true,
		"organization_id":  orgID.String(),
		"recurrence": map[string]interface{}{
			"pattern":     "weekly",
			"interval":    1,
			"days":        []string{"monday"},
			"end_type":    "occurrences",
			"occurrences": 4,
		},
	}

	webCreateRec := s.SimulateWebOperation(t, "POST", "/api/v1/events/recurring", recurringReq, organizer.Token)
	require.Equal(t, http.StatusCreated, webCreateRec.Code)

	var seriesResp map[string]interface{}
	json.Unmarshal(webCreateRec.Body.Bytes(), &seriesResp)
	seriesID := seriesResp["series_id"].(string)

	// Get all events in series from mobile
	mobileSeriesRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/events/series/%s", seriesID), nil, organizer.Token)
	require.Equal(t, http.StatusOK, mobileSeriesRec.Code)

	var mobileSeries map[string]interface{}
	json.Unmarshal(mobileSeriesRec.Body.Bytes(), &mobileSeries)

	events := mobileSeries["events"].([]interface{})
	assert.Equal(t, 4, len(events)) // 4 occurrences

	// Update single occurrence from mobile
	firstEventID := events[0].(map[string]interface{})["id"].(string)

	singleUpdateReq := map[string]interface{}{
		"location":      "Virtual - Special Session",
		"update_type":   "single",
		"update_reason": "Room unavailable for this session",
	}

	mobileUpdateRec := s.SimulateMobileOperation(t, "PUT", fmt.Sprintf("/api/v1/events/%s", firstEventID), singleUpdateReq, organizer.Token)
	require.Equal(t, http.StatusOK, mobileUpdateRec.Code)

	// Update all future occurrences from web
	thirdEventID := events[2].(map[string]interface{})["id"].(string)

	futureUpdateReq := map[string]interface{}{
		"start_date":    time.Now().Add(25 * time.Hour).Format(time.RFC3339), // 1 hour later
		"end_date":      time.Now().Add(26 * time.Hour).Format(time.RFC3339),
		"update_type":   "future",
		"update_reason": "Team preference for later time",
	}

	webFutureRec := s.SimulateWebOperation(t, "PUT", fmt.Sprintf("/api/v1/events/%s", thirdEventID), futureUpdateReq, organizer.Token)
	require.Equal(t, http.StatusOK, webFutureRec.Code)

	// Verify updates across platforms
	finalSeriesRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/events/series/%s", seriesID), nil, organizer.Token)
	require.Equal(t, http.StatusOK, finalSeriesRec.Code)

	var finalSeries map[string]interface{}
	json.Unmarshal(finalSeriesRec.Body.Bytes(), &finalSeries)

	finalEvents := finalSeries["events"].([]interface{})

	// First event should have special location
	firstEvent := finalEvents[0].(map[string]interface{})
	assert.Equal(t, "Virtual - Special Session", firstEvent["location"])

	// Third and fourth events should have updated time
	for i := 2; i < 4; i++ {
		event := finalEvents[i].(map[string]interface{})
		startTime, _ := time.Parse(time.RFC3339, event["start_date"].(string))
		// Verify time is updated (checking hour difference)
		assert.Greater(t, startTime.Hour(), time.Now().Add(24*time.Hour).Hour())
	}
}

func TestEventLifecycleSuite(t *testing.T) {
	suite.Run(t, new(EventLifecycleTestSuite))
}
