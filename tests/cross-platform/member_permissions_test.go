package crossplatform

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// MemberPermissionsTestSuite tests member management and permissions across platforms
type MemberPermissionsTestSuite struct {
	suite.Suite
	*CrossPlatformTestSuite
}

func (s *MemberPermissionsTestSuite) SetupSuite() {
	s.CrossPlatformTestSuite = SetupCrossPlatformTest(s.T())
}

func (s *MemberPermissionsTestSuite) TearDownSuite() {
	s.CleanupTestData(s.T())
	s.TeardownCrossPlatformTest(s.T())
}

// Test 11: Role-Based Access Control
func (s *MemberPermissionsTestSuite) TestRoleBasedAccessControl() {
	t := s.T()

	// Create users with different roles
	owner := s.CreateTestUser(t, "rbac-owner", "+15550003001", "<EMAIL>")
	admin := s.CreateTestUser(t, "rbac-admin", "+15550003002", "<EMAIL>")
	manager := s.CreateTestUser(t, "rbac-manager", "+15550003003", "<EMAIL>")
	member := s.CreateTestUser(t, "rbac-member", "+15550003004", "<EMAIL>")

	// Owner creates organization
	orgID := s.CreateTestOrganization(t, "rbac-org", owner.Token)

	// Owner adds users with different roles
	roles := map[string]UserData{
		"admin":   admin,
		"manager": manager,
		"member":  member,
	}

	for role, user := range roles {
		addReq := map[string]interface{}{
			"user_id": user.ID.String(),
			"role":    role,
		}
		rec := s.SimulateWebOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/members", orgID), addReq, owner.Token)
		require.Equal(t, http.StatusCreated, rec.Code)
	}

	// Test permission levels

	// Admin can add new members (from mobile)
	newMember := s.CreateTestUser(t, "rbac-new", "+15550003005", "<EMAIL>")
	adminAddReq := map[string]interface{}{
		"user_id": newMember.ID.String(),
		"role":    "member",
	}
	adminAddRec := s.SimulateMobileOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/members", orgID), adminAddReq, admin.Token)
	assert.Equal(t, http.StatusCreated, adminAddRec.Code)

	// Manager cannot add members (from web)
	anotherMember := s.CreateTestUser(t, "rbac-another", "+15550003006", "<EMAIL>")
	managerAddReq := map[string]interface{}{
		"user_id": anotherMember.ID.String(),
		"role":    "member",
	}
	managerAddRec := s.SimulateWebOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/members", orgID), managerAddReq, manager.Token)
	assert.Equal(t, http.StatusForbidden, managerAddRec.Code)

	// Manager can create events (from mobile)
	eventReq := map[string]interface{}{
		"title":           "Manager Created Event",
		"description":     "Event created by manager role",
		"event_type":      "meeting",
		"start_date":      time.Now().Add(24 * time.Hour).Format(time.RFC3339),
		"end_date":        time.Now().Add(26 * time.Hour).Format(time.RFC3339),
		"organization_id": orgID.String(),
	}
	managerEventRec := s.SimulateMobileOperation(t, "POST", "/api/v1/events", eventReq, manager.Token)
	assert.Equal(t, http.StatusCreated, managerEventRec.Code)

	// Member cannot create events (from web)
	memberEventReq := map[string]interface{}{
		"title":           "Member Attempted Event",
		"description":     "Should fail",
		"event_type":      "meeting",
		"start_date":      time.Now().Add(48 * time.Hour).Format(time.RFC3339),
		"end_date":        time.Now().Add(50 * time.Hour).Format(time.RFC3339),
		"organization_id": orgID.String(),
	}
	memberEventRec := s.SimulateWebOperation(t, "POST", "/api/v1/events", memberEventReq, member.Token)
	assert.Equal(t, http.StatusForbidden, memberEventRec.Code)

	// Member can view organization details (from mobile)
	memberViewRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s", orgID), nil, member.Token)
	assert.Equal(t, http.StatusOK, memberViewRec.Code)

	// Admin can update organization settings (from web)
	adminUpdateReq := map[string]interface{}{
		"description": "Updated by admin",
	}
	adminUpdateRec := s.SimulateWebOperation(t, "PUT", fmt.Sprintf("/api/v1/organizations/%s", orgID), adminUpdateReq, admin.Token)
	assert.Equal(t, http.StatusOK, adminUpdateRec.Code)
}

// Test 12: Permission Inheritance and Propagation
func (s *MemberPermissionsTestSuite) TestPermissionInheritancePropagation() {
	t := s.T()

	// Create users
	owner := s.CreateTestUser(t, "inherit-owner", "+15550003007", "<EMAIL>")
	user1 := s.CreateTestUser(t, "inherit-user1", "+15550003008", "<EMAIL>")
	user2 := s.CreateTestUser(t, "inherit-user2", "+15550003009", "<EMAIL>")

	// Create parent organization
	parentOrgID := s.CreateTestOrganization(t, "parent-org", owner.Token)

	// Add user1 as admin to parent org
	addAdminReq := map[string]interface{}{
		"user_id": user1.ID.String(),
		"role":    "admin",
	}
	rec := s.SimulateWebOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/members", parentOrgID), addAdminReq, owner.Token)
	require.Equal(t, http.StatusCreated, rec.Code)

	// Create sub-organization/department (if supported)
	subOrgReq := map[string]interface{}{
		"name":                "Sub Department",
		"description":         "Department under parent org",
		"parent_org_id":       parentOrgID.String(),
		"inherit_permissions": true,
	}
	subOrgRec := s.SimulateMobileOperation(t, "POST", "/api/v1/organizations/departments", subOrgReq, owner.Token)

	// If sub-organizations are supported, test inheritance
	if subOrgRec.Code == http.StatusCreated {
		var subOrgResp map[string]interface{}
		json.Unmarshal(subOrgRec.Body.Bytes(), &subOrgResp)
		subOrgID := subOrgResp["id"].(string)

		// User1 (admin in parent) should have access to sub-org
		user1AccessRec := s.SimulateWebOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s", subOrgID), nil, user1.Token)
		assert.Equal(t, http.StatusOK, user1AccessRec.Code)

		// User2 (not in parent) should not have access
		user2AccessRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s", subOrgID), nil, user2.Token)
		assert.Equal(t, http.StatusForbidden, user2AccessRec.Code)
	}

	// Test resource-level permissions
	// Create a resource in the organization
	resourceReq := map[string]interface{}{
		"title":           "Restricted Resource",
		"description":     "Resource with specific permissions",
		"content":         "Confidential content",
		"organization_id": parentOrgID.String(),
		"permissions": map[string]interface{}{
			"view":   []string{"admin", "manager"},
			"edit":   []string{"admin"},
			"delete": []string{"owner"},
		},
	}

	resourceRec := s.SimulateWebOperation(t, "POST", "/api/v1/resources", resourceReq, owner.Token)
	if resourceRec.Code == http.StatusCreated {
		var resourceResp map[string]interface{}
		json.Unmarshal(resourceRec.Body.Bytes(), &resourceResp)
		resourceID := resourceResp["id"].(string)

		// Admin can view and edit
		adminViewRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/resources/%s", resourceID), nil, user1.Token)
		assert.Equal(t, http.StatusOK, adminViewRec.Code)

		adminEditReq := map[string]interface{}{
			"description": "Edited by admin",
		}
		adminEditRec := s.SimulateWebOperation(t, "PUT", fmt.Sprintf("/api/v1/resources/%s", resourceID), adminEditReq, user1.Token)
		assert.Equal(t, http.StatusOK, adminEditRec.Code)

		// Admin cannot delete (owner only)
		adminDeleteRec := s.SimulateMobileOperation(t, "DELETE", fmt.Sprintf("/api/v1/resources/%s", resourceID), nil, user1.Token)
		assert.Equal(t, http.StatusForbidden, adminDeleteRec.Code)
	}
}

// Test 13: Dynamic Permission Updates
func (s *MemberPermissionsTestSuite) TestDynamicPermissionUpdates() {
	t := s.T()

	// Create users
	owner := s.CreateTestUser(t, "dynamic-owner", "+15550003010", "<EMAIL>")
	user := s.CreateTestUser(t, "dynamic-user", "+15550003011", "<EMAIL>")

	// Create organization and add user as member
	orgID := s.CreateTestOrganization(t, "dynamic-org", owner.Token)

	addMemberReq := map[string]interface{}{
		"user_id": user.ID.String(),
		"role":    "member",
	}
	rec := s.SimulateWebOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/members", orgID), addMemberReq, owner.Token)
	require.Equal(t, http.StatusCreated, rec.Code)

	// Member tries to create event (should fail)
	eventReq := map[string]interface{}{
		"title":           "Member Event Attempt",
		"description":     "Should fail initially",
		"event_type":      "workshop",
		"start_date":      time.Now().Add(48 * time.Hour).Format(time.RFC3339),
		"end_date":        time.Now().Add(50 * time.Hour).Format(time.RFC3339),
		"organization_id": orgID.String(),
	}

	memberEventRec1 := s.SimulateMobileOperation(t, "POST", "/api/v1/events", eventReq, user.Token)
	assert.Equal(t, http.StatusForbidden, memberEventRec1.Code)

	// Owner upgrades member to manager
	upgradeReq := map[string]interface{}{
		"role": "manager",
	}
	upgradeRec := s.SimulateWebOperation(t, "PUT", fmt.Sprintf("/api/v1/organizations/%s/members/%s", orgID, user.ID), upgradeReq, owner.Token)
	require.Equal(t, http.StatusOK, upgradeRec.Code)

	// Wait for permission propagation
	s.WaitForEventualConsistency(t, func() bool {
		// Check if user can now create events
		testRec := s.SimulateMobileOperation(t, "POST", "/api/v1/events", eventReq, user.Token)
		return testRec.Code == http.StatusCreated
	}, 5*time.Second)

	// Manager can now create event
	eventReq["title"] = "Manager Event Success"
	memberEventRec2 := s.SimulateMobileOperation(t, "POST", "/api/v1/events", eventReq, user.Token)
	assert.Equal(t, http.StatusCreated, memberEventRec2.Code)

	var eventResp map[string]interface{}
	json.Unmarshal(memberEventRec2.Body.Bytes(), &eventResp)
	eventID := eventResp["id"].(string)

	// Owner downgrades manager back to member
	downgradeReq := map[string]interface{}{
		"role": "member",
	}
	downgradeRec := s.SimulateWebOperation(t, "PUT", fmt.Sprintf("/api/v1/organizations/%s/members/%s", orgID, user.ID), downgradeReq, owner.Token)
	require.Equal(t, http.StatusOK, downgradeRec.Code)

	// Member can no longer edit the event they created
	editEventReq := map[string]interface{}{
		"description": "Trying to edit as member",
	}
	memberEditRec := s.SimulateMobileOperation(t, "PUT", fmt.Sprintf("/api/v1/events/%s", eventID), editEventReq, user.Token)
	assert.Equal(t, http.StatusForbidden, memberEditRec.Code)

	// But can still view it
	memberViewRec := s.SimulateWebOperation(t, "GET", fmt.Sprintf("/api/v1/events/%s", eventID), nil, user.Token)
	assert.Equal(t, http.StatusOK, memberViewRec.Code)
}

// Test 14: Cross-Organization Permission Isolation
func (s *MemberPermissionsTestSuite) TestCrossOrganizationPermissionIsolation() {
	t := s.T()

	// Create users
	user1 := s.CreateTestUser(t, "cross-org-user1", "+15550003012", "<EMAIL>")
	user2 := s.CreateTestUser(t, "cross-org-user2", "+15550003013", "<EMAIL>")
	sharedUser := s.CreateTestUser(t, "shared-user", "+15550003014", "<EMAIL>")

	// Create two separate organizations
	org1ID := s.CreateTestOrganization(t, "isolation-org1", user1.Token)
	org2ID := s.CreateTestOrganization(t, "isolation-org2", user2.Token)

	// Add shared user to both orgs with different roles
	// Admin in org1
	addToOrg1Req := map[string]interface{}{
		"user_id": sharedUser.ID.String(),
		"role":    "admin",
	}
	rec1 := s.SimulateWebOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/members", org1ID), addToOrg1Req, user1.Token)
	require.Equal(t, http.StatusCreated, rec1.Code)

	// Member in org2
	addToOrg2Req := map[string]interface{}{
		"user_id": sharedUser.ID.String(),
		"role":    "member",
	}
	rec2 := s.SimulateMobileOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/members", org2ID), addToOrg2Req, user2.Token)
	require.Equal(t, http.StatusCreated, rec2.Code)

	// Create resources in each org
	org1ResourceReq := map[string]interface{}{
		"title":           "Org1 Private Resource",
		"description":     "Only for org1 members",
		"content":         "Org1 confidential data",
		"organization_id": org1ID.String(),
		"is_public":       false,
	}
	org1ResRec := s.SimulateWebOperation(t, "POST", "/api/v1/resources", org1ResourceReq, user1.Token)
	require.Equal(t, http.StatusCreated, org1ResRec.Code)

	var org1ResResp map[string]interface{}
	json.Unmarshal(org1ResRec.Body.Bytes(), &org1ResResp)
	org1ResID := org1ResResp["id"].(string)

	org2EventReq := map[string]interface{}{
		"title":           "Org2 Private Event",
		"description":     "Only for org2 members",
		"event_type":      "internal",
		"start_date":      time.Now().Add(24 * time.Hour).Format(time.RFC3339),
		"end_date":        time.Now().Add(26 * time.Hour).Format(time.RFC3339),
		"organization_id": org2ID.String(),
		"is_public":       false,
	}
	org2EventRec := s.SimulateMobileOperation(t, "POST", "/api/v1/events", org2EventReq, user2.Token)
	require.Equal(t, http.StatusCreated, org2EventRec.Code)

	var org2EventResp map[string]interface{}
	json.Unmarshal(org2EventRec.Body.Bytes(), &org2EventResp)
	org2EventID := org2EventResp["id"].(string)

	// Test isolation
	// User1 cannot access org2's event
	user1Org2EventRec := s.SimulateWebOperation(t, "GET", fmt.Sprintf("/api/v1/events/%s", org2EventID), nil, user1.Token)
	assert.Equal(t, http.StatusForbidden, user1Org2EventRec.Code)

	// User2 cannot access org1's resource
	user2Org1ResRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/resources/%s", org1ResID), nil, user2.Token)
	assert.Equal(t, http.StatusForbidden, user2Org1ResRec.Code)

	// Shared user has appropriate access based on role in each org
	// Can edit in org1 (admin)
	sharedEditOrg1Req := map[string]interface{}{
		"description": "Updated by shared user as admin",
	}
	sharedEditRec := s.SimulateWebOperation(t, "PUT", fmt.Sprintf("/api/v1/resources/%s", org1ResID), sharedEditOrg1Req, sharedUser.Token)
	assert.Equal(t, http.StatusOK, sharedEditRec.Code)

	// Cannot create event in org2 (only member)
	sharedOrg2EventReq := map[string]interface{}{
		"title":           "Shared User Event Attempt",
		"description":     "Should fail - only member role",
		"event_type":      "meeting",
		"start_date":      time.Now().Add(72 * time.Hour).Format(time.RFC3339),
		"end_date":        time.Now().Add(74 * time.Hour).Format(time.RFC3339),
		"organization_id": org2ID.String(),
	}
	sharedCreateRec := s.SimulateMobileOperation(t, "POST", "/api/v1/events", sharedOrg2EventReq, sharedUser.Token)
	assert.Equal(t, http.StatusForbidden, sharedCreateRec.Code)
}

// Test 15: Permission Delegation and Temporary Access
func (s *MemberPermissionsTestSuite) TestPermissionDelegationTemporaryAccess() {
	t := s.T()

	// Create users
	owner := s.CreateTestUser(t, "delegate-owner", "+15550003015", "<EMAIL>")
	admin := s.CreateTestUser(t, "delegate-admin", "+15550003016", "<EMAIL>")
	volunteer := s.CreateTestUser(t, "volunteer", "+15550003017", "<EMAIL>")
	guest := s.CreateTestUser(t, "guest", "+15550003018", "<EMAIL>")

	// Create organization
	orgID := s.CreateTestOrganization(t, "delegate-org", owner.Token)

	// Add admin
	addAdminReq := map[string]interface{}{
		"user_id": admin.ID.String(),
		"role":    "admin",
	}
	rec := s.SimulateWebOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/members", orgID), addAdminReq, owner.Token)
	require.Equal(t, http.StatusCreated, rec.Code)

	// Create event
	eventID := s.CreateTestEvent(t, "delegate-event", orgID, admin.Token)

	// Admin delegates temporary event management to volunteer
	delegateReq := map[string]interface{}{
		"user_id":       volunteer.ID.String(),
		"resource_id":   eventID.String(),
		"resource_type": "event",
		"permissions":   []string{"view", "edit", "manage_registrations"},
		"expires_at":    time.Now().Add(7 * 24 * time.Hour).Format(time.RFC3339), // 7 days
		"reason":        "Event coordination assistance",
	}

	delegateRec := s.SimulateMobileOperation(t, "POST", "/api/v1/permissions/delegate", delegateReq, admin.Token)
	if delegateRec.Code == http.StatusCreated {
		// Volunteer can now manage event
		volunteerEditReq := map[string]interface{}{
			"description": "Updated by volunteer with delegated access",
		}
		volunteerEditRec := s.SimulateWebOperation(t, "PUT", fmt.Sprintf("/api/v1/events/%s", eventID), volunteerEditReq, volunteer.Token)
		assert.Equal(t, http.StatusOK, volunteerEditRec.Code)

		// Volunteer can manage registrations
		regListRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/events/%s/registrations", eventID), nil, volunteer.Token)
		assert.Equal(t, http.StatusOK, regListRec.Code)
	}

	// Create guest pass for limited access
	guestPassReq := map[string]interface{}{
		"email":       guest.Email,
		"access_type": "event_attendee",
		"event_id":    eventID.String(),
		"expires_at":  time.Now().Add(30 * 24 * time.Hour).Format(time.RFC3339),
		"permissions": []string{"view_event", "register"},
	}

	guestPassRec := s.SimulateWebOperation(t, "POST", "/api/v1/organizations/guest-passes", guestPassReq, admin.Token)
	if guestPassRec.Code == http.StatusCreated {
		var guestPassResp map[string]interface{}
		json.Unmarshal(guestPassRec.Body.Bytes(), &guestPassResp)

		// Guest can view event
		guestViewRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/events/%s", eventID), nil, guest.Token)
		assert.Equal(t, http.StatusOK, guestViewRec.Code)

		// Guest can register
		guestRegReq := map[string]interface{}{
			"user_id": guest.ID.String(),
		}
		guestRegRec := s.SimulateWebOperation(t, "POST", fmt.Sprintf("/api/v1/events/%s/register", eventID), guestRegReq, guest.Token)
		assert.Equal(t, http.StatusCreated, guestRegRec.Code)

		// Guest cannot edit event
		guestEditReq := map[string]interface{}{
			"description": "Guest trying to edit",
		}
		guestEditRec := s.SimulateMobileOperation(t, "PUT", fmt.Sprintf("/api/v1/events/%s", eventID), guestEditReq, guest.Token)
		assert.Equal(t, http.StatusForbidden, guestEditRec.Code)
	}
}

func TestMemberPermissionsSuite(t *testing.T) {
	suite.Run(t, new(MemberPermissionsTestSuite))
}
