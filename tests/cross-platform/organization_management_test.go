package crossplatform

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// OrganizationManagementTestSuite tests organization management across platforms
type OrganizationManagementTestSuite struct {
	suite.Suite
	*CrossPlatformTestSuite
}

func (s *OrganizationManagementTestSuite) SetupSuite() {
	s.CrossPlatformTestSuite = SetupCrossPlatformTest(s.T())
}

func (s *OrganizationManagementTestSuite) TearDownSuite() {
	s.CleanupTestData(s.T())
	s.TeardownCrossPlatformTest(s.T())
}

// Test 1: Organization Creation Consistency
func (s *OrganizationManagementTestSuite) TestOrganizationCreationConsistency() {
	t := s.T()

	// Create users for web and mobile
	webUser := s.CreateTestUser(t, "web-org-creator", "+15550001001", "<EMAIL>")
	mobileUser := s.CreateTestUser(t, "mobile-org-creator", "+15550001002", "<EMAIL>")

	// Web creates organization
	webOrgReq := map[string]interface{}{
		"name":        "Web Created Organization",
		"description": "Organization created from web platform",
		"email":       "<EMAIL>",
		"phone":       "+15550001003",
		"website":     "https://web-org.example.com",
		"address":     "123 Web Street",
		"city":        "Web City",
		"country":     "Web Country",
	}

	webRec := s.SimulateWebOperation(t, "POST", "/api/v1/organizations", webOrgReq, webUser.Token)
	require.Equal(t, http.StatusCreated, webRec.Code)

	var webOrgResp map[string]interface{}
	err := json.Unmarshal(webRec.Body.Bytes(), &webOrgResp)
	require.NoError(t, err)

	webOrgID := webOrgResp["id"].(string)

	// Mobile creates organization
	mobileOrgReq := map[string]interface{}{
		"name":        "Mobile Created Organization",
		"description": "Organization created from mobile platform",
		"email":       "<EMAIL>",
		"phone":       "+15550001004",
		"website":     "https://mobile-org.example.com",
		"address":     "456 Mobile Avenue",
		"city":        "Mobile City",
		"country":     "Mobile Country",
	}

	mobileRec := s.SimulateMobileOperation(t, "POST", "/api/v1/organizations", mobileOrgReq, mobileUser.Token)
	require.Equal(t, http.StatusCreated, mobileRec.Code)

	var mobileOrgResp map[string]interface{}
	err = json.Unmarshal(mobileRec.Body.Bytes(), &mobileOrgResp)
	require.NoError(t, err)

	mobileOrgID := mobileOrgResp["id"].(string)

	// Verify both organizations are accessible from both platforms
	// Web user accesses mobile-created org
	webAccessRec := s.SimulateWebOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s", mobileOrgID), nil, webUser.Token)
	assert.Equal(t, http.StatusOK, webAccessRec.Code)

	// Mobile user accesses web-created org
	mobileAccessRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s", webOrgID), nil, mobileUser.Token)
	assert.Equal(t, http.StatusOK, mobileAccessRec.Code)
}

// Test 2: Organization Update Synchronization
func (s *OrganizationManagementTestSuite) TestOrganizationUpdateSynchronization() {
	t := s.T()

	// Create user and organization
	user := s.CreateTestUser(t, "org-updater", "+15550001005", "<EMAIL>")
	orgID := s.CreateTestOrganization(t, "update-test", user.Token)

	// Web updates organization
	webUpdateReq := map[string]interface{}{
		"description": "Updated from web platform",
		"website":     "https://updated-web.example.com",
		"social_media": map[string]string{
			"twitter":  "@webupdate",
			"facebook": "webupdateorg",
		},
	}

	webUpdateRec := s.SimulateWebOperation(t, "PUT", fmt.Sprintf("/api/v1/organizations/%s", orgID), webUpdateReq, user.Token)
	require.Equal(t, http.StatusOK, webUpdateRec.Code)

	// Mobile reads updated organization
	mobileReadRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s", orgID), nil, user.Token)
	require.Equal(t, http.StatusOK, mobileReadRec.Code)

	var mobileReadResp map[string]interface{}
	err := json.Unmarshal(mobileReadRec.Body.Bytes(), &mobileReadResp)
	require.NoError(t, err)

	// Verify updates are reflected
	assert.Equal(t, "Updated from web platform", mobileReadResp["description"])
	assert.Equal(t, "https://updated-web.example.com", mobileReadResp["website"])

	// Mobile updates organization
	mobileUpdateReq := map[string]interface{}{
		"description": "Updated again from mobile platform",
		"phone":       "+15550001006",
	}

	mobileUpdateRec := s.SimulateMobileOperation(t, "PUT", fmt.Sprintf("/api/v1/organizations/%s", orgID), mobileUpdateReq, user.Token)
	require.Equal(t, http.StatusOK, mobileUpdateRec.Code)

	// Web reads mobile updates
	webReadRec := s.SimulateWebOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s", orgID), nil, user.Token)
	require.Equal(t, http.StatusOK, webReadRec.Code)

	var webReadResp map[string]interface{}
	err = json.Unmarshal(webReadRec.Body.Bytes(), &webReadResp)
	require.NoError(t, err)

	assert.Equal(t, "Updated again from mobile platform", webReadResp["description"])
	assert.Equal(t, "+15550001006", webReadResp["phone"])
}

// Test 3: Organization Member Management
func (s *OrganizationManagementTestSuite) TestOrganizationMemberManagement() {
	t := s.T()

	// Create owner and organization
	owner := s.CreateTestUser(t, "org-owner", "+15550001007", "<EMAIL>")
	orgID := s.CreateTestOrganization(t, "member-test", owner.Token)

	// Create members to add
	member1 := s.CreateTestUser(t, "member1", "+15550001008", "<EMAIL>")
	member2 := s.CreateTestUser(t, "member2", "+15550001009", "<EMAIL>")

	// Web adds member1
	webAddMemberReq := map[string]interface{}{
		"user_id": member1.ID.String(),
		"role":    "member",
	}

	webAddRec := s.SimulateWebOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/members", orgID), webAddMemberReq, owner.Token)
	require.Equal(t, http.StatusCreated, webAddRec.Code)

	// Mobile adds member2
	mobileAddMemberReq := map[string]interface{}{
		"user_id": member2.ID.String(),
		"role":    "manager",
	}

	mobileAddRec := s.SimulateMobileOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/members", orgID), mobileAddMemberReq, owner.Token)
	require.Equal(t, http.StatusCreated, mobileAddRec.Code)

	// Verify member list from both platforms
	webMembersRec := s.SimulateWebOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s/members", orgID), nil, owner.Token)
	require.Equal(t, http.StatusOK, webMembersRec.Code)

	mobileMembersRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s/members", orgID), nil, owner.Token)
	require.Equal(t, http.StatusOK, mobileMembersRec.Code)

	var webMembers, mobileMembers map[string]interface{}
	err := json.Unmarshal(webMembersRec.Body.Bytes(), &webMembers)
	require.NoError(t, err)
	err = json.Unmarshal(mobileMembersRec.Body.Bytes(), &mobileMembers)
	require.NoError(t, err)

	// Validate consistency
	s.ValidateDataConsistency(t, webMembers, mobileMembers)

	// Update member role from mobile
	mobileUpdateRoleReq := map[string]interface{}{
		"role": "admin",
	}

	mobileUpdateRec := s.SimulateMobileOperation(t, "PUT", fmt.Sprintf("/api/v1/organizations/%s/members/%s", orgID, member1.ID), mobileUpdateRoleReq, owner.Token)
	require.Equal(t, http.StatusOK, mobileUpdateRec.Code)

	// Verify role update from web
	webMemberRec := s.SimulateWebOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s/members/%s", orgID, member1.ID), nil, owner.Token)
	require.Equal(t, http.StatusOK, webMemberRec.Code)

	var webMemberResp map[string]interface{}
	err = json.Unmarshal(webMemberRec.Body.Bytes(), &webMemberResp)
	require.NoError(t, err)

	assert.Equal(t, "admin", webMemberResp["role"])
}

// Test 4: Organization Settings and Preferences
func (s *OrganizationManagementTestSuite) TestOrganizationSettingsSync() {
	t := s.T()

	// Create user and organization
	user := s.CreateTestUser(t, "settings-manager", "+15550001010", "<EMAIL>")
	orgID := s.CreateTestOrganization(t, "settings-test", user.Token)

	// Update settings from web
	webSettingsReq := map[string]interface{}{
		"settings": map[string]interface{}{
			"notifications": map[string]bool{
				"email_enabled": true,
				"sms_enabled":   false,
			},
			"privacy": map[string]string{
				"member_visibility": "public",
				"event_visibility":  "members_only",
			},
			"features": map[string]bool{
				"events_enabled":    true,
				"resources_enabled": true,
				"posts_enabled":     true,
			},
		},
	}

	webSettingsRec := s.SimulateWebOperation(t, "PUT", fmt.Sprintf("/api/v1/organizations/%s/settings", orgID), webSettingsReq, user.Token)
	require.Equal(t, http.StatusOK, webSettingsRec.Code)

	// Read settings from mobile
	mobileSettingsRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s/settings", orgID), nil, user.Token)
	require.Equal(t, http.StatusOK, mobileSettingsRec.Code)

	var mobileSettings map[string]interface{}
	err := json.Unmarshal(mobileSettingsRec.Body.Bytes(), &mobileSettings)
	require.NoError(t, err)

	// Update partial settings from mobile
	mobileSettingsUpdateReq := map[string]interface{}{
		"settings": map[string]interface{}{
			"notifications": map[string]bool{
				"push_enabled": true,
			},
			"theme": map[string]string{
				"primary_color":   "#007AFF",
				"secondary_color": "#FF3B30",
			},
		},
	}

	mobileUpdateRec := s.SimulateMobileOperation(t, "PATCH", fmt.Sprintf("/api/v1/organizations/%s/settings", orgID), mobileSettingsUpdateReq, user.Token)
	require.Equal(t, http.StatusOK, mobileUpdateRec.Code)

	// Verify merged settings from web
	webFinalRec := s.SimulateWebOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s/settings", orgID), nil, user.Token)
	require.Equal(t, http.StatusOK, webFinalRec.Code)

	var webFinalSettings map[string]interface{}
	err = json.Unmarshal(webFinalRec.Body.Bytes(), &webFinalSettings)
	require.NoError(t, err)

	// Should have both web and mobile settings merged
	settings := webFinalSettings["settings"].(map[string]interface{})
	notifications := settings["notifications"].(map[string]interface{})
	assert.Equal(t, true, notifications["email_enabled"])
	assert.Equal(t, true, notifications["push_enabled"])
}

// Test 5: Organization Deletion and Cascade Effects
func (s *OrganizationManagementTestSuite) TestOrganizationDeletionCascade() {
	t := s.T()

	// Create owner and organization
	owner := s.CreateTestUser(t, "delete-org-owner", "+15550001011", "<EMAIL>")
	orgID := s.CreateTestOrganization(t, "delete-test", owner.Token)

	// Add member
	member := s.CreateTestUser(t, "delete-org-member", "+15550001012", "<EMAIL>")
	addMemberReq := map[string]interface{}{
		"user_id": member.ID.String(),
		"role":    "member",
	}
	rec := s.SimulateWebOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/members", orgID), addMemberReq, owner.Token)
	require.Equal(t, http.StatusCreated, rec.Code)

	// Create event in organization
	eventReq := map[string]interface{}{
		"title":           "Delete Test Event",
		"description":     "Event to test cascade deletion",
		"event_type":      "community",
		"start_date":      time.Now().Add(24 * time.Hour).Format(time.RFC3339),
		"end_date":        time.Now().Add(26 * time.Hour).Format(time.RFC3339),
		"organization_id": orgID.String(),
	}

	eventRec := s.SimulateMobileOperation(t, "POST", "/api/v1/events", eventReq, owner.Token)
	require.Equal(t, http.StatusCreated, eventRec.Code)

	var eventResp map[string]interface{}
	err := json.Unmarshal(eventRec.Body.Bytes(), &eventResp)
	require.NoError(t, err)
	eventID := eventResp["id"].(string)

	// Delete organization from web
	deleteRec := s.SimulateWebOperation(t, "DELETE", fmt.Sprintf("/api/v1/organizations/%s", orgID), nil, owner.Token)
	require.Equal(t, http.StatusOK, deleteRec.Code)

	// Verify organization is not accessible from mobile
	mobileGetRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s", orgID), nil, owner.Token)
	assert.Equal(t, http.StatusNotFound, mobileGetRec.Code)

	// Verify event is also deleted (cascade)
	eventGetRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/events/%s", eventID), nil, owner.Token)
	assert.Equal(t, http.StatusNotFound, eventGetRec.Code)

	// Verify member no longer has access
	memberOrgRec := s.SimulateWebOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s", orgID), nil, member.Token)
	assert.Equal(t, http.StatusNotFound, memberOrgRec.Code)
}

func TestOrganizationManagementSuite(t *testing.T) {
	suite.Run(t, new(OrganizationManagementTestSuite))
}
