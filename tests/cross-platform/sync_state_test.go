package crossplatform

import (
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"github.com/tidwall/gjson"
)

// SyncStateTestSuite tests data synchronization and offline/online transitions
type SyncStateTestSuite struct {
	suite.Suite
	*CrossPlatformTestSuite
}

func (s *SyncStateTestSuite) SetupSuite() {
	s.CrossPlatformTestSuite = SetupCrossPlatformTest(s.T())
}

func (s *SyncStateTestSuite) TearDownSuite() {
	s.CleanupTestData(s.T())
	s.TeardownCrossPlatformTest(s.T())
}

// TestOfflineOnlineTransitions simulates offline/online state changes
func (s *SyncStateTestSuite) TestOfflineOnlineTransitions() {
	t := s.T()

	user := s.CreateTestUser(t, "test-offline-sync", CreateTestPhoneNumber(), "<EMAIL>")
	org := s.CreateTestOrganization(t, "test-offline-sync", user.Token)

	// Step 1: Create initial data while "online"
	eventData := map[string]interface{}{
		"title":       "Pre-Offline Event",
		"description": "Created before going offline",
		"start_time":  time.Now().Add(24 * time.Hour).Format(time.RFC3339),
		"end_time":    time.Now().Add(25 * time.Hour).Format(time.RFC3339),
	}

	webRec := s.SimulateWebOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/events", org.String()), eventData, user.Token)
	s.Require().Equal(http.StatusCreated, webRec.Code)

	var eventResp map[string]interface{}
	s.Require().NoError(json.Unmarshal(webRec.Body.Bytes(), &eventResp))
	eventID := eventResp["id"].(string)

	// Step 2: Mobile goes "offline" - simulate by capturing state
	mobileStateRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s/events", org.String()), nil, user.Token)
	s.Require().Equal(http.StatusOK, mobileStateRec.Code)

	var offlineState map[string]interface{}
	s.Require().NoError(json.Unmarshal(mobileStateRec.Body.Bytes(), &offlineState))
	offlineEventCount := len(offlineState["data"].([]interface{}))

	// Step 3: Web makes changes while mobile is "offline"
	updateData := map[string]interface{}{
		"title":       "Updated While Mobile Offline",
		"description": "This was changed when mobile had no connection",
	}

	webUpdateRec := s.SimulateWebOperation(t, "PATCH", fmt.Sprintf("/api/v1/organizations/%s/events/%s", org.String(), eventID), updateData, user.Token)
	s.Require().Equal(http.StatusOK, webUpdateRec.Code)

	// Create new event while mobile offline
	newEventData := map[string]interface{}{
		"title":       "Created While Mobile Offline",
		"description": "Mobile doesn't know about this yet",
		"start_time":  time.Now().Add(48 * time.Hour).Format(time.RFC3339),
		"end_time":    time.Now().Add(49 * time.Hour).Format(time.RFC3339),
	}

	webNewRec := s.SimulateWebOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/events", org.String()), newEventData, user.Token)
	s.Require().Equal(http.StatusCreated, webNewRec.Code)

	// Step 4: Mobile comes back "online" and syncs
	mobileSyncRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s/events", org.String()), nil, user.Token)
	s.Require().Equal(http.StatusOK, mobileSyncRec.Code)

	var syncedState map[string]interface{}
	s.Require().NoError(json.Unmarshal(mobileSyncRec.Body.Bytes(), &syncedState))
	syncedEventCount := len(syncedState["data"].([]interface{}))

	// Verify mobile sees the new event
	s.Assert().Equal(offlineEventCount+1, syncedEventCount, "Mobile should see new event after sync")

	// Verify mobile sees the updated event
	mobileEventRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s/events/%s", org.String(), eventID), nil, user.Token)
	s.Require().Equal(http.StatusOK, mobileEventRec.Code)

	updatedTitle := gjson.Get(mobileEventRec.Body.String(), "title").String()
	s.Assert().Equal("Updated While Mobile Offline", updatedTitle, "Mobile should see updated title")
}

// TestDataSynchronizationConflicts tests conflict resolution
func (s *SyncStateTestSuite) TestDataSynchronizationConflicts() {
	t := s.T()

	user := s.CreateTestUser(t, "test-sync-conflicts", CreateTestPhoneNumber(), "<EMAIL>")
	org := s.CreateTestOrganization(t, "test-sync-conflicts", user.Token)

	// Create initial event
	eventData := map[string]interface{}{
		"title":       "Conflict Test Event",
		"description": "Will be updated from both platforms",
		"start_time":  time.Now().Add(24 * time.Hour).Format(time.RFC3339),
		"end_time":    time.Now().Add(25 * time.Hour).Format(time.RFC3339),
	}

	createRec := s.SimulateWebOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/events", org.String()), eventData, user.Token)
	s.Require().Equal(http.StatusCreated, createRec.Code)

	var eventResp map[string]interface{}
	s.Require().NoError(json.Unmarshal(createRec.Body.Bytes(), &eventResp))
	eventID := eventResp["id"].(string)

	// Simulate concurrent updates from both platforms
	var wg sync.WaitGroup
	results := make(chan int, 2)

	// Web update
	wg.Add(1)
	go func() {
		defer wg.Done()
		webUpdate := map[string]interface{}{
			"title": "Updated from Web",
		}
		rec := s.SimulateWebOperation(t, "PATCH", fmt.Sprintf("/api/v1/organizations/%s/events/%s", org.String(), eventID), webUpdate, user.Token)
		results <- rec.Code
	}()

	// Mobile update (slight delay to increase conflict chance)
	wg.Add(1)
	go func() {
		defer wg.Done()
		time.Sleep(10 * time.Millisecond)
		mobileUpdate := map[string]interface{}{
			"title": "Updated from Mobile",
		}
		rec := s.SimulateMobileOperation(t, "PATCH", fmt.Sprintf("/api/v1/organizations/%s/events/%s", org.String(), eventID), mobileUpdate, user.Token)
		results <- rec.Code
	}()

	wg.Wait()
	close(results)

	// Check results - at least one should succeed
	successCount := 0
	for code := range results {
		if code == http.StatusOK {
			successCount++
		}
	}

	s.Assert().GreaterOrEqual(successCount, 1, "At least one update should succeed")

	// Verify final state is consistent across platforms
	webFinalRec := s.SimulateWebOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s/events/%s", org.String(), eventID), nil, user.Token)
	mobileFinalRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s/events/%s", org.String(), eventID), nil, user.Token)

	s.Require().Equal(http.StatusOK, webFinalRec.Code)
	s.Require().Equal(http.StatusOK, mobileFinalRec.Code)

	webTitle := gjson.Get(webFinalRec.Body.String(), "title").String()
	mobileTitle := gjson.Get(mobileFinalRec.Body.String(), "title").String()

	s.Assert().Equal(webTitle, mobileTitle, "Final state should be consistent")
	s.Assert().True(webTitle == "Updated from Web" || webTitle == "Updated from Mobile", "Title should be one of the updates")
}

// TestPushNotificationConsistency tests push notification delivery
func (s *SyncStateTestSuite) TestPushNotificationConsistency() {
	t := s.T()

	// Create test users
	sender := s.CreateTestUser(t, "test-notif-sender", CreateTestPhoneNumber(), "<EMAIL>")
	receiver := s.CreateTestUser(t, "test-notif-receiver", CreateTestPhoneNumber(), "<EMAIL>")

	// Create organization and add receiver
	org := s.CreateTestOrganization(t, "test-notifications", sender.Token)

	// Add receiver to organization
	inviteData := map[string]interface{}{
		"user_id": receiver.ID.String(),
		"role":    "member",
	}
	inviteRec := s.SimulateWebOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/members", org.String()), inviteData, sender.Token)
	s.Require().Equal(http.StatusCreated, inviteRec.Code)

	// Simulate device registration for push notifications
	webDeviceData := map[string]interface{}{
		"device_token": "web-push-token-123",
		"platform":     "web",
		"device_info": map[string]interface{}{
			"browser": "Chrome",
			"os":      "Windows",
		},
	}

	mobileDeviceData := map[string]interface{}{
		"device_token": "mobile-push-token-456",
		"platform":     "mobile",
		"device_info": map[string]interface{}{
			"os":      "iOS",
			"version": "15.0",
		},
	}

	// Register devices (assuming endpoint exists)
	webDeviceRec := s.SimulateWebOperation(t, "POST", "/api/v1/devices/register", webDeviceData, receiver.Token)
	mobileDeviceRec := s.SimulateMobileOperation(t, "POST", "/api/v1/devices/register", mobileDeviceData, receiver.Token)

	// These might return 404 if endpoint doesn't exist, which is ok for this test
	_ = webDeviceRec
	_ = mobileDeviceRec

	// Create event that should trigger notifications
	eventData := map[string]interface{}{
		"title":          "New Event Notification Test",
		"description":    "This should notify all members",
		"start_time":     time.Now().Add(2 * time.Hour).Format(time.RFC3339), // Soon enough to trigger reminder
		"end_time":       time.Now().Add(3 * time.Hour).Format(time.RFC3339),
		"notify_members": true, // If supported by API
	}

	eventRec := s.SimulateWebOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/events", org.String()), eventData, sender.Token)
	s.Require().Equal(http.StatusCreated, eventRec.Code)

	var eventResp map[string]interface{}
	s.Require().NoError(json.Unmarshal(eventRec.Body.Bytes(), &eventResp))
	eventID := eventResp["id"].(string)

	// Check notification status (if API supports it)
	// This is a mock check - actual implementation would check notification service
	time.Sleep(100 * time.Millisecond) // Allow time for async notification processing

	// Both platforms should be able to see the event
	webEventRec := s.SimulateWebOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s/events/%s", org.String(), eventID), nil, receiver.Token)
	mobileEventRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s/events/%s", org.String(), eventID), nil, receiver.Token)

	s.Assert().Equal(http.StatusOK, webEventRec.Code, "Web should see event")
	s.Assert().Equal(http.StatusOK, mobileEventRec.Code, "Mobile should see event")

	// In a real test, we would verify:
	// 1. Push notification was sent to both registered devices
	// 2. Notification content is consistent
	// 3. Deep links work correctly on both platforms
	// 4. Notification preferences are respected
}

// TestRealtimeDataSync tests real-time synchronization
func (s *SyncStateTestSuite) TestRealtimeDataSync() {
	t := s.T()

	user := s.CreateTestUser(t, "test-realtime-sync", CreateTestPhoneNumber(), "<EMAIL>")
	org := s.CreateTestOrganization(t, "test-realtime", user.Token)

	// Create initial post
	postData := map[string]interface{}{
		"title":   "Realtime Sync Test",
		"content": "Initial content",
		"type":    "announcement",
	}

	createRec := s.SimulateWebOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/posts", org.String()), postData, user.Token)
	s.Require().Equal(http.StatusCreated, createRec.Code)

	var postResp map[string]interface{}
	s.Require().NoError(json.Unmarshal(createRec.Body.Bytes(), &postResp))
	postID := postResp["id"].(string)

	// Simulate rapid updates from web
	updates := []string{
		"Update 1 from web",
		"Update 2 from web",
		"Update 3 from web",
	}

	for i, content := range updates {
		updateData := map[string]interface{}{
			"content": content,
		}

		rec := s.SimulateWebOperation(t, "PATCH", fmt.Sprintf("/api/v1/organizations/%s/posts/%s", org.String(), postID), updateData, user.Token)
		s.Assert().Equal(http.StatusOK, rec.Code, "Update %d should succeed", i+1)

		// Small delay between updates
		time.Sleep(50 * time.Millisecond)

		// Mobile should see the update immediately
		mobileRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s/posts/%s", org.String(), postID), nil, user.Token)
		s.Require().Equal(http.StatusOK, mobileRec.Code)

		mobileContent := gjson.Get(mobileRec.Body.String(), "content").String()
		s.Assert().Equal(content, mobileContent, "Mobile should see update %d", i+1)
	}
}

// TestBulkDataSynchronization tests syncing large amounts of data
func (s *SyncStateTestSuite) TestBulkDataSynchronization() {
	t := s.T()

	user := s.CreateTestUser(t, "test-bulk-sync", CreateTestPhoneNumber(), "<EMAIL>")
	org := s.CreateTestOrganization(t, "test-bulk-sync", user.Token)

	// Create multiple items from web
	itemCount := 50
	createdIDs := make([]string, 0, itemCount)

	for i := 0; i < itemCount; i++ {
		postData := map[string]interface{}{
			"title":   fmt.Sprintf("Bulk Item %d", i),
			"content": fmt.Sprintf("Content for item %d", i),
			"type":    "update",
		}

		rec := s.SimulateWebOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/posts", org.String()), postData, user.Token)
		s.Require().Equal(http.StatusCreated, rec.Code)

		var resp map[string]interface{}
		json.Unmarshal(rec.Body.Bytes(), &resp)
		createdIDs = append(createdIDs, resp["id"].(string))
	}

	// Mobile fetches all data
	mobileBulkRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s/posts?limit=100", org.String()), nil, user.Token)
	s.Require().Equal(http.StatusOK, mobileBulkRec.Code)

	var mobileBulkResp map[string]interface{}
	s.Require().NoError(json.Unmarshal(mobileBulkRec.Body.Bytes(), &mobileBulkResp))

	mobileItems := mobileBulkResp["data"].([]interface{})
	s.Assert().GreaterOrEqual(len(mobileItems), itemCount, "Mobile should see all created items")

	// Verify data integrity
	mobileIDs := make(map[string]bool)
	for _, item := range mobileItems {
		itemMap := item.(map[string]interface{})
		mobileIDs[itemMap["id"].(string)] = true
	}

	// Check all created items are visible
	for _, id := range createdIDs {
		s.Assert().True(mobileIDs[id], "Mobile should see item %s", id)
	}

	// Test pagination sync
	pageSize := 10
	for page := 1; page <= 3; page++ {
		webPageRec := s.SimulateWebOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s/posts?page=%d&limit=%d", org.String(), page, pageSize), nil, user.Token)
		mobilePageRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s/posts?page=%d&limit=%d", org.String(), page, pageSize), nil, user.Token)

		s.Require().Equal(http.StatusOK, webPageRec.Code)
		s.Require().Equal(http.StatusOK, mobilePageRec.Code)

		var webPage, mobilePage map[string]interface{}
		json.Unmarshal(webPageRec.Body.Bytes(), &webPage)
		json.Unmarshal(mobilePageRec.Body.Bytes(), &mobilePage)

		webData := webPage["data"].([]interface{})
		mobileData := mobilePage["data"].([]interface{})

		s.Assert().Equal(len(webData), len(mobileData), "Page %d should have same item count", page)
	}
}

// TestFileSynchronization tests file upload/download consistency
func (s *SyncStateTestSuite) TestFileSynchronization() {
	t := s.T()

	user := s.CreateTestUser(t, "test-file-sync", CreateTestPhoneNumber(), "<EMAIL>")
	org := s.CreateTestOrganization(t, "test-file-sync", user.Token)

	// Simulate file upload from web
	// Note: Actual file upload would use multipart form data
	fileMetadata := map[string]interface{}{
		"filename":     "test-document.pdf",
		"size":         1024 * 50, // 50KB
		"content_type": "application/pdf",
		"purpose":      "event_attachment",
	}

	// Request upload URL (if API supports it)
	webUploadRec := s.SimulateWebOperation(t, "POST", fmt.Sprintf("/api/v1/organizations/%s/files/upload", org.String()), fileMetadata, user.Token)

	if webUploadRec.Code == http.StatusOK || webUploadRec.Code == http.StatusCreated {
		var uploadResp map[string]interface{}
		json.Unmarshal(webUploadRec.Body.Bytes(), &uploadResp)

		// In real scenario, we would upload to the presigned URL
		// For now, we'll assume the file was uploaded

		fileID := ""
		if id, ok := uploadResp["file_id"].(string); ok {
			fileID = id
		} else if id, ok := uploadResp["id"].(string); ok {
			fileID = id
		}

		if fileID != "" {
			// Mobile should be able to access the file metadata
			mobileFileRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s/files/%s", org.String(), fileID), nil, user.Token)

			if mobileFileRec.Code == http.StatusOK {
				var mobileFileResp map[string]interface{}
				json.Unmarshal(mobileFileRec.Body.Bytes(), &mobileFileResp)

				// Verify file metadata matches
				s.Assert().Equal(fileMetadata["filename"], mobileFileResp["filename"])
				s.Assert().Equal(fileMetadata["content_type"], mobileFileResp["content_type"])
			}
		}
	}

	// Test file listing synchronization
	webFilesRec := s.SimulateWebOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s/files", org.String()), nil, user.Token)
	mobileFilesRec := s.SimulateMobileOperation(t, "GET", fmt.Sprintf("/api/v1/organizations/%s/files", org.String()), nil, user.Token)

	if webFilesRec.Code == http.StatusOK && mobileFilesRec.Code == http.StatusOK {
		var webFiles, mobileFiles map[string]interface{}
		json.Unmarshal(webFilesRec.Body.Bytes(), &webFiles)
		json.Unmarshal(mobileFilesRec.Body.Bytes(), &mobileFiles)

		// Both should see the same files
		if webData, ok := webFiles["data"].([]interface{}); ok {
			if mobileData, ok := mobileFiles["data"].([]interface{}); ok {
				s.Assert().Equal(len(webData), len(mobileData), "File count should match")
			}
		}
	}
}

func TestSyncStateSuite(t *testing.T) {
	suite.Run(t, new(SyncStateTestSuite))
}
