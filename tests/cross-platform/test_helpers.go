package crossplatform

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/tests/integration"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
)

// CrossPlatformTestSuite provides shared test infrastructure
type CrossPlatformTestSuite struct {
	apiSuite *integration.APITestSuite
	testData TestData
}

// TestData holds test data for cross-platform validation
type TestData struct {
	Organizations map[string]uuid.UUID
	Users         map[string]UserData
	Events        map[string]uuid.UUID
	Posts         map[string]uuid.UUID
	Resources     map[string]uuid.UUID
}

// UserData holds user-specific test data
type UserData struct {
	ID    uuid.UUID
	Token string
	Phone string
	Email string
}

// SetupCrossPlatformTest initializes the test suite
func SetupCrossPlatformTest(t *testing.T) *CrossPlatformTestSuite {
	apiSuite := integration.SetupAPITest(t)

	return &CrossPlatformTestSuite{
		apiSuite: apiSuite,
		testData: TestData{
			Organizations: make(map[string]uuid.UUID),
			Users:         make(map[string]UserData),
			Events:        make(map[string]uuid.UUID),
			Posts:         make(map[string]uuid.UUID),
			Resources:     make(map[string]uuid.UUID),
		},
	}
}

// TeardownCrossPlatformTest cleans up the test suite
func (s *CrossPlatformTestSuite) TeardownCrossPlatformTest(t *testing.T) {
	s.apiSuite.TeardownAPITest(t)
}

// CreateTestUser creates a test user and stores their data
func (s *CrossPlatformTestSuite) CreateTestUser(t *testing.T, key, phone, email string) UserData {
	// Register user
	regInitiateReq := map[string]interface{}{
		"phone_number":   phone,
		"email":          email,
		"first_name":     "Test",
		"last_name":      key,
		"code_challenge": fmt.Sprintf("test-challenge-%s", key),
		"state":          fmt.Sprintf("test-state-%s", key),
		"preferred_lang": "en",
	}

	rec, err := s.apiSuite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/register/phone/initiate", regInitiateReq)
	require.NoError(t, err)
	require.Equal(t, http.StatusOK, rec.Code)

	// Verify registration
	regVerifyReq := map[string]interface{}{
		"phone_number":  phone,
		"otp_code":      "123456",
		"code_verifier": fmt.Sprintf("test-verifier-%s", key),
		"state":         regInitiateReq["state"],
	}

	rec, err = s.apiSuite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/register/phone/verify", regVerifyReq)
	require.NoError(t, err)
	require.Equal(t, http.StatusOK, rec.Code)

	var authResponse map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &authResponse)
	require.NoError(t, err)

	token := authResponse["access_token"].(string)

	// Get user profile to get ID
	rec, err = s.apiSuite.MakeRequest("GET", "/api/v1/users/profile", nil, token)
	require.NoError(t, err)

	var profileResponse map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &profileResponse)
	require.NoError(t, err)

	userID, err := uuid.Parse(profileResponse["id"].(string))
	require.NoError(t, err)

	userData := UserData{
		ID:    userID,
		Token: token,
		Phone: phone,
		Email: email,
	}

	s.testData.Users[key] = userData
	return userData
}

// CreateTestOrganization creates a test organization
func (s *CrossPlatformTestSuite) CreateTestOrganization(t *testing.T, key string, userToken string) uuid.UUID {
	orgCreateReq := map[string]interface{}{
		"name":        fmt.Sprintf("Cross Platform Test Org %s", key),
		"description": fmt.Sprintf("Test organization for cross-platform scenario %s", key),
		"email":       fmt.Sprintf("<EMAIL>", key),
		"phone":       fmt.Sprintf("+155500%04d", len(s.testData.Organizations)+1000),
		"address":     "123 Test Street",
		"city":        "Test City",
		"country":     "Test Country",
	}

	rec, err := s.apiSuite.MakeRequest("POST", "/api/v1/organizations", orgCreateReq, userToken)
	require.NoError(t, err)
	require.Equal(t, http.StatusCreated, rec.Code)

	var orgResponse map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &orgResponse)
	require.NoError(t, err)

	orgID, err := uuid.Parse(orgResponse["id"].(string))
	require.NoError(t, err)

	s.testData.Organizations[key] = orgID
	return orgID
}

// CreateTestEvent creates a test event
func (s *CrossPlatformTestSuite) CreateTestEvent(t *testing.T, key string, orgID uuid.UUID, userToken string) uuid.UUID {
	eventCreateReq := map[string]interface{}{
		"title":                 fmt.Sprintf("Cross Platform Test Event %s", key),
		"description":           fmt.Sprintf("Test event for cross-platform scenario %s", key),
		"event_type":            "community",
		"start_date":            time.Now().Add(72 * time.Hour).Format(time.RFC3339),
		"end_date":              time.Now().Add(76 * time.Hour).Format(time.RFC3339),
		"registration_deadline": time.Now().Add(48 * time.Hour).Format(time.RFC3339),
		"max_participants":      50,
		"location":              "Test Event Location",
		"is_published":          true,
		"organization_id":       orgID.String(),
	}

	rec, err := s.apiSuite.MakeRequest("POST", "/api/v1/events", eventCreateReq, userToken)
	require.NoError(t, err)
	require.Equal(t, http.StatusCreated, rec.Code)

	var eventResponse map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &eventResponse)
	require.NoError(t, err)

	eventID, err := uuid.Parse(eventResponse["id"].(string))
	require.NoError(t, err)

	s.testData.Events[key] = eventID
	return eventID
}

// SimulateWebOperation simulates an operation from the web platform
func (s *CrossPlatformTestSuite) SimulateWebOperation(t *testing.T, method, path string, body interface{}, token string) *httptest.ResponseRecorder {
	rec, err := s.apiSuite.MakeRequest(method, path, body, token)
	require.NoError(t, err)
	return rec
}

// SimulateMobileOperation simulates an operation from the mobile platform
// In a real implementation, this might add specific headers or use different endpoints
func (s *CrossPlatformTestSuite) SimulateMobileOperation(t *testing.T, method, path string, body interface{}, token string) *httptest.ResponseRecorder {
	// Add mobile-specific headers
	req, err := http.NewRequest(method, path, nil)
	require.NoError(t, err)

	if body != nil {
		jsonBody, err := json.Marshal(body)
		require.NoError(t, err)
		req.Body = io.NopCloser(bytes.NewReader(jsonBody))
		req.Header.Set("Content-Type", "application/json")
	}

	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("X-Platform", "mobile")
	req.Header.Set("X-App-Version", "1.0.0")

	// Use APITestSuite's MakeRequest instead of direct ServeHTTP
	rec, err := s.apiSuite.MakeUnauthenticatedRequest(req.Method, req.URL.Path, nil)
	if err != nil {
		// Handle error appropriately for tests
		rec = httptest.NewRecorder()
		rec.WriteHeader(http.StatusInternalServerError)
	}

	return rec
}

// ValidateDataConsistency validates that data is consistent across platforms
func (s *CrossPlatformTestSuite) ValidateDataConsistency(t *testing.T, webResponse, mobileResponse interface{}) {
	webJSON, err := json.Marshal(webResponse)
	require.NoError(t, err)

	mobileJSON, err := json.Marshal(mobileResponse)
	require.NoError(t, err)

	require.JSONEq(t, string(webJSON), string(mobileJSON), "Data should be consistent between web and mobile platforms")
}

// CleanupTestData performs cleanup with retry logic
func (s *CrossPlatformTestSuite) CleanupTestData(t *testing.T) {
	// Clean up in reverse order of dependencies

	// Delete events
	for key, eventID := range s.testData.Events {
		rec, err := s.apiSuite.MakeAuthenticatedRequest("DELETE", fmt.Sprintf("/api/v1/events/%s", eventID), nil)
		if err == nil && rec.Code != http.StatusOK && rec.Code != http.StatusNotFound {
			t.Logf("Failed to delete event %s: %d", key, rec.Code)
		}
	}

	// Delete posts
	for key, postID := range s.testData.Posts {
		rec, err := s.apiSuite.MakeAuthenticatedRequest("DELETE", fmt.Sprintf("/api/v1/posts/%s", postID), nil)
		if err == nil && rec.Code != http.StatusOK && rec.Code != http.StatusNotFound {
			t.Logf("Failed to delete post %s: %d", key, rec.Code)
		}
	}

	// Delete resources
	for key, resourceID := range s.testData.Resources {
		rec, err := s.apiSuite.MakeAuthenticatedRequest("DELETE", fmt.Sprintf("/api/v1/resources/%s", resourceID), nil)
		if err == nil && rec.Code != http.StatusOK && rec.Code != http.StatusNotFound {
			t.Logf("Failed to delete resource %s: %d", key, rec.Code)
		}
	}

	// Note: Organizations and users typically can't be deleted via API
	// They would need to be cleaned up by database cleanup or admin endpoints
}

// WaitForEventualConsistency waits for eventual consistency with timeout
func (s *CrossPlatformTestSuite) WaitForEventualConsistency(t *testing.T, checkFunc func() bool, timeout time.Duration) {
	deadline := time.Now().Add(timeout)
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()

	for {
		if checkFunc() {
			return
		}

		select {
		case <-ticker.C:
			if time.Now().After(deadline) {
				t.Fatal("Timeout waiting for eventual consistency")
			}
		}
	}
}

// SimulateWebOperationWithHeaders simulates a web operation with custom headers
func (s *CrossPlatformTestSuite) SimulateWebOperationWithHeaders(t *testing.T, method, path string, body interface{}, token string, headers map[string]string) *httptest.ResponseRecorder {
	req, err := http.NewRequest(method, path, nil)
	require.NoError(t, err)

	if body != nil {
		jsonBody, err := json.Marshal(body)
		require.NoError(t, err)
		req.Body = io.NopCloser(bytes.NewReader(jsonBody))
		req.Header.Set("Content-Type", "application/json")
	}

	if token != "" {
		req.Header.Set("Authorization", "Bearer "+token)
	}

	// Add custom headers
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	// Use APITestSuite's MakeRequest instead of direct ServeHTTP
	rec, err := s.apiSuite.MakeUnauthenticatedRequest(req.Method, req.URL.Path, nil)
	if err != nil {
		// Handle error appropriately for tests
		rec = httptest.NewRecorder()
		rec.WriteHeader(http.StatusInternalServerError)
	}

	return rec
}

// CreateTestPhoneNumber generates a unique test phone number
func CreateTestPhoneNumber() string {
	// Generate a random phone number for testing
	return fmt.Sprintf("+155588%05d", rand.Intn(100000))
}

// RandomString generates a random string of the specified length
func RandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}
