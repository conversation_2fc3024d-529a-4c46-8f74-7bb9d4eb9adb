package e2e

import (
	"context"
	"fmt"
	"net/http"
	"sync"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/tests/integration"
)

// ConcurrentOperationsTestSuite tests system behavior under concurrent access scenarios
type ConcurrentOperationsTestSuite struct {
	suite.Suite
	apiSuite *integration.APITestSuite
	dbPool   *pgxpool.Pool
}

// SetupSuite initializes the test suite
func (s *ConcurrentOperationsTestSuite) SetupSuite() {
	s.apiSuite = integration.SetupAPITest(s.T())

	// Setup direct database connection for concurrent testing
	ctx := context.Background()
	pool, err := pgxpool.New(ctx, s.apiSuite.GetDBConnString())
	require.NoError(s.T(), err)
	s.dbPool = pool
}

// TearDownSuite cleans up the test suite
func (s *ConcurrentOperationsTestSuite) TearDownSuite() {
	if s.dbPool != nil {
		s.dbPool.Close()
	}
	s.apiSuite.TeardownAPITest(s.T())
}

// TestConcurrentEventRegistrations tests concurrent event registration with capacity limits
func (s *ConcurrentOperationsTestSuite) TestConcurrentEventRegistrations() {
	t := s.T()

	// Setup: Create event with limited capacity
	eventID := s.createTestEvent("Concurrent Registration Test", 5) // Max 5 participants

	// Create 10 users who will try to register concurrently
	users := make([]testUser, 10)
	for i := 0; i < 10; i++ {
		phone := fmt.Sprintf("+155500200%d", i)
		email := fmt.Sprintf("<EMAIL>", i)
		token := s.createTestUser(phone, email)
		userID := s.getUserIDFromToken(token)

		users[i] = testUser{
			ID:    userID,
			Token: token,
			Phone: phone,
			Email: email,
		}
	}

	// Concurrent registration attempt
	var wg sync.WaitGroup
	results := make([]registrationResult, 10)

	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()

			regReq := map[string]interface{}{
				"user_id":           users[index].ID,
				"emergency_contact": fmt.Sprintf("Emergency %d - +155500300%d", index, index),
				"additional_notes":  fmt.Sprintf("Concurrent registration test %d", index),
			}

			rec, err := s.apiSuite.MakeRequest("POST", fmt.Sprintf("/api/v1/events/%s/register", eventID), regReq, users[index].Token)

			results[index] = registrationResult{
				UserIndex:  index,
				StatusCode: rec.Code,
				Error:      err,
				Success:    rec.Code == http.StatusCreated,
			}
		}(i)
	}

	wg.Wait()

	// Verify results
	successCount := 0
	failureCount := 0

	for _, result := range results {
		if result.Success {
			successCount++
		} else {
			failureCount++
		}
	}

	t.Logf("Concurrent registration results: %d successful, %d failed", successCount, failureCount)

	// Assertions
	assert.Equal(t, 5, successCount, "Exactly 5 registrations should succeed (event capacity)")
	assert.Equal(t, 5, failureCount, "Exactly 5 registrations should fail")

	// Verify database consistency
	dbCount := s.countEventRegistrations(eventID)
	assert.Equal(t, 5, dbCount, "Database should have exactly 5 registrations")

	// Verify no duplicate registrations
	duplicates := s.findDuplicateRegistrations(eventID)
	assert.Equal(t, 0, duplicates, "Should have no duplicate registrations")
}

// TestConcurrentVolunteerApplications tests concurrent volunteer applications
func (s *ConcurrentOperationsTestSuite) TestConcurrentVolunteerApplications() {
	t := s.T()

	// Setup: Create event requiring volunteers
	eventID := s.createTestEvent("Concurrent Volunteer Test", 50)

	// Create 5 users who will apply as volunteers concurrently
	users := make([]testUser, 5)
	for i := 0; i < 5; i++ {
		phone := fmt.Sprintf("+155500250%d", i)
		email := fmt.Sprintf("<EMAIL>", i)
		token := s.createTestUser(phone, email)
		userID := s.getUserIDFromToken(token)

		users[i] = testUser{
			ID:    userID,
			Token: token,
			Phone: phone,
			Email: email,
		}
	}

	// Concurrent volunteer applications
	var wg sync.WaitGroup
	results := make([]registrationResult, 5)

	for i := 0; i < 5; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()

			// First apply as organization volunteer
			volAppReq := map[string]interface{}{
				"user_id":      users[index].ID,
				"motivation":   fmt.Sprintf("Concurrent volunteer test %d", index),
				"availability": "Weekends",
				"skills":       []string{"Event Management", "Communication"},
				"experience":   "2 years",
				"references":   fmt.Sprintf("Reference %d", index),
			}

			rec1, err1 := s.apiSuite.MakeRequest("POST", "/api/v1/volunteers/apply", volAppReq, users[index].Token)

			// Then apply as event volunteer
			eventVolReq := map[string]interface{}{
				"role":         "Event Setup",
				"availability": "Full event duration",
				"notes":        fmt.Sprintf("Event volunteer %d", index),
			}

			rec2, err2 := s.apiSuite.MakeRequest("POST", fmt.Sprintf("/api/v1/events/%s/volunteer", eventID), eventVolReq, users[index].Token)

			// Consider success if either application succeeds
			success := (rec1.Code == http.StatusCreated) || (rec2.Code == http.StatusCreated)

			results[index] = registrationResult{
				UserIndex:  index,
				StatusCode: rec1.Code, // Use org volunteer application status
				Error:      err1,
				Success:    success,
			}

			if err2 != nil {
				t.Logf("Event volunteer application error for user %d: %v", index, err2)
			}
		}(i)
	}

	wg.Wait()

	// Verify results
	successCount := 0
	for _, result := range results {
		if result.Success {
			successCount++
		}
	}

	t.Logf("Concurrent volunteer applications: %d successful", successCount)

	// All volunteer applications should succeed (no capacity limit)
	assert.Equal(t, 5, successCount, "All volunteer applications should succeed")

	// Verify database consistency
	for _, user := range users {
		hasVolApp := s.userHasVolunteerApplication(user.ID)
		assert.True(t, hasVolApp, fmt.Sprintf("User %s should have volunteer application", user.ID))
	}
}

// TestConcurrentUserUpdates tests concurrent user profile updates
func (s *ConcurrentOperationsTestSuite) TestConcurrentUserUpdates() {
	t := s.T()

	// Setup: Create user
	userToken := s.createTestUser("+1555002100", "<EMAIL>")
	userID := s.getUserIDFromToken(userToken)

	// Concurrent profile updates
	var wg sync.WaitGroup
	updates := []map[string]interface{}{
		{"first_name": "Updated1", "bio": "Bio update 1"},
		{"last_name": "Updated2", "bio": "Bio update 2"},
		{"first_name": "Updated3", "last_name": "Updated3"},
		{"bio": "Bio update 4", "preferred_lang": "en"},
		{"first_name": "Final", "last_name": "User", "bio": "Final bio"},
	}

	results := make([]registrationResult, 5)

	for i := 0; i < 5; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()

			rec, err := s.apiSuite.MakeRequest("PUT", "/api/v1/users/profile", updates[index], userToken)

			results[index] = registrationResult{
				UserIndex:  index,
				StatusCode: rec.Code,
				Error:      err,
				Success:    rec.Code == http.StatusOK,
			}
		}(i)
	}

	wg.Wait()

	// Verify all updates succeeded
	successCount := 0
	for _, result := range results {
		if result.Success {
			successCount++
		}
	}

	t.Logf("Concurrent profile updates: %d successful", successCount)
	assert.Equal(t, 5, successCount, "All profile updates should succeed")

	// Verify final profile state is consistent
	rec, err := s.apiSuite.MakeRequest("GET", "/api/v1/users/profile", nil, userToken)
	require.NoError(t, err)

	var profileResponse payloads.UserProfileResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &profileResponse)

	// Profile should have valid values (exact values depend on which update was last)
	assert.NotEmpty(t, profileResponse.DisplayName, "Display name should not be empty")
	assert.Equal(t, userID, profileResponse.ID, "User ID should remain consistent")
}

// TestConcurrentEventCreation tests concurrent event creation by admins
func (s *ConcurrentOperationsTestSuite) TestConcurrentEventCreation() {
	t := s.T()

	// Concurrent event creation
	var wg sync.WaitGroup
	eventCount := 3
	results := make([]eventCreationResult, eventCount)

	for i := 0; i < eventCount; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()

			eventCreateReq := map[string]interface{}{
				"title":                 fmt.Sprintf("Concurrent Event %d", index),
				"description":           fmt.Sprintf("Event created concurrently %d", index),
				"event_type":            "community",
				"start_date":            time.Now().Add(time.Duration(72+index*24) * time.Hour).Format(time.RFC3339),
				"end_date":              time.Now().Add(time.Duration(76+index*24) * time.Hour).Format(time.RFC3339),
				"registration_deadline": time.Now().Add(time.Duration(48+index*24) * time.Hour).Format(time.RFC3339),
				"max_participants":      50,
				"location":              fmt.Sprintf("Location %d", index),
				"is_published":          true,
			}

			rec, err := s.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/events", eventCreateReq)

			var eventResponse payloads.EventResponse
			if rec.Code == http.StatusCreated {
				s.apiSuite.AssertJSONResponse(s.T(), rec, http.StatusCreated, &eventResponse)
			}

			results[index] = eventCreationResult{
				Index:      index,
				StatusCode: rec.Code,
				Error:      err,
				Success:    rec.Code == http.StatusCreated,
				EventID:    eventResponse.ID,
			}
		}(i)
	}

	wg.Wait()

	// Verify all events were created successfully
	successCount := 0
	for _, result := range results {
		if result.Success {
			successCount++
		}
	}

	t.Logf("Concurrent event creation: %d successful", successCount)
	assert.Equal(t, eventCount, successCount, "All event creations should succeed")

	// Verify events exist in database
	for _, result := range results {
		if result.Success {
			exists := s.eventExists(result.EventID)
			assert.True(t, exists, fmt.Sprintf("Event %s should exist in database", result.EventID))
		}
	}
}

// TestConcurrentNotificationSending tests concurrent notification operations
func (s *ConcurrentOperationsTestSuite) TestConcurrentNotificationSending() {
	t := s.T()

	// Setup: Create users and event
	userCount := 5
	users := make([]testUser, userCount)
	for i := 0; i < userCount; i++ {
		phone := fmt.Sprintf("+155500220%d", i)
		email := fmt.Sprintf("<EMAIL>", i)
		token := s.createTestUser(phone, email)
		userID := s.getUserIDFromToken(token)

		users[i] = testUser{
			ID:    userID,
			Token: token,
			Phone: phone,
			Email: email,
		}
	}

	// Create event
	eventID := s.createTestEvent("Notification Test Event", 50)

	// Register all users for the event
	for _, user := range users {
		regReq := map[string]interface{}{
			"user_id":           user.ID,
			"emergency_contact": "Emergency Contact",
		}
		rec, err := s.apiSuite.MakeRequest("POST", fmt.Sprintf("/api/v1/events/%s/register", eventID), regReq, user.Token)
		require.NoError(t, err)
		assert.Equal(t, http.StatusCreated, rec.Code)
	}

	// Concurrent notification sending (simulated by creating announcements)
	var wg sync.WaitGroup
	notificationCount := 3
	results := make([]registrationResult, notificationCount)

	for i := 0; i < notificationCount; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()

			postCreateReq := map[string]interface{}{
				"title":             fmt.Sprintf("Concurrent Announcement %d", index),
				"content":           fmt.Sprintf("This is announcement %d sent concurrently", index),
				"content_type":      "announcement",
				"tags":              []string{"announcement", "concurrent"},
				"send_notification": true,
				"is_published":      true,
			}

			rec, err := s.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/posts", postCreateReq)

			results[index] = registrationResult{
				UserIndex:  index,
				StatusCode: rec.Code,
				Error:      err,
				Success:    rec.Code == http.StatusCreated,
			}
		}(i)
	}

	wg.Wait()

	// Verify all announcements were created
	successCount := 0
	for _, result := range results {
		if result.Success {
			successCount++
		}
	}

	t.Logf("Concurrent notifications: %d successful", successCount)
	assert.Equal(t, notificationCount, successCount, "All announcements should be created")
}

// TestConcurrentResourceAccess tests concurrent resource file operations
func (s *ConcurrentOperationsTestSuite) TestConcurrentResourceAccess() {
	t := s.T()

	// Setup: Create resource
	resourceCreateReq := map[string]interface{}{
		"title":       "Concurrent Access Resource",
		"description": "Resource for testing concurrent access",
		"category":    "documentation",
		"content":     "Initial content",
		"is_public":   true,
		"tags":        []string{"concurrent", "test"},
	}
	rec, err := s.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/resources", resourceCreateReq)
	require.NoError(t, err)

	var resourceResponse payloads.ResourceResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusCreated, &resourceResponse)
	resourceID := resourceResponse.ID

	// Create users for concurrent access
	userCount := 5
	users := make([]testUser, userCount)
	for i := 0; i < userCount; i++ {
		phone := fmt.Sprintf("+155500230%d", i)
		email := fmt.Sprintf("<EMAIL>", i)
		token := s.createTestUser(phone, email)
		userID := s.getUserIDFromToken(token)

		users[i] = testUser{
			ID:    userID,
			Token: token,
			Phone: phone,
			Email: email,
		}
	}

	// Concurrent resource access
	var wg sync.WaitGroup
	results := make([]registrationResult, userCount)

	for i := 0; i < userCount; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()

			// Concurrent read access
			rec, err := s.apiSuite.MakeRequest("GET", fmt.Sprintf("/api/v1/resources/%s", resourceID), nil, users[index].Token)

			results[index] = registrationResult{
				UserIndex:  index,
				StatusCode: rec.Code,
				Error:      err,
				Success:    rec.Code == http.StatusOK,
			}
		}(i)
	}

	wg.Wait()

	// Verify all access attempts succeeded
	successCount := 0
	for _, result := range results {
		if result.Success {
			successCount++
		}
	}

	t.Logf("Concurrent resource access: %d successful", successCount)
	assert.Equal(t, userCount, successCount, "All resource access attempts should succeed")
}

// Helper types and methods

type testUser struct {
	ID    uuid.UUID
	Token string
	Phone string
	Email string
}

type registrationResult struct {
	UserIndex  int
	StatusCode int
	Error      error
	Success    bool
}

type eventCreationResult struct {
	Index      int
	StatusCode int
	Error      error
	Success    bool
	EventID    uuid.UUID
}

// Database helper methods

func (s *ConcurrentOperationsTestSuite) countEventRegistrations(eventID uuid.UUID) int {
	var count int
	err := s.dbPool.QueryRow(context.Background(),
		"SELECT COUNT(*) FROM event_registrations WHERE event_id = $1", eventID).Scan(&count)
	if err != nil {
		s.T().Logf("Error counting registrations: %v", err)
		return 0
	}
	return count
}

func (s *ConcurrentOperationsTestSuite) findDuplicateRegistrations(eventID uuid.UUID) int {
	var count int
	err := s.dbPool.QueryRow(context.Background(), `
		SELECT COUNT(*) FROM (
			SELECT user_id, COUNT(*) 
			FROM event_registrations 
			WHERE event_id = $1 
			GROUP BY user_id 
			HAVING COUNT(*) > 1
		) duplicates
	`, eventID).Scan(&count)
	if err != nil {
		s.T().Logf("Error counting duplicates: %v", err)
		return 0
	}
	return count
}

func (s *ConcurrentOperationsTestSuite) userHasVolunteerApplication(userID uuid.UUID) bool {
	var exists bool
	err := s.dbPool.QueryRow(context.Background(),
		"SELECT EXISTS(SELECT 1 FROM volunteer_applications WHERE user_id = $1)", userID).Scan(&exists)
	if err != nil {
		s.T().Logf("Error checking volunteer application: %v", err)
		return false
	}
	return exists
}

func (s *ConcurrentOperationsTestSuite) eventExists(eventID uuid.UUID) bool {
	var exists bool
	err := s.dbPool.QueryRow(context.Background(),
		"SELECT EXISTS(SELECT 1 FROM events WHERE id = $1)", eventID).Scan(&exists)
	if err != nil {
		s.T().Logf("Error checking event existence: %v", err)
		return false
	}
	return exists
}

// Test setup helper methods

func (s *ConcurrentOperationsTestSuite) createTestUser(phoneNumber, email string) string {
	regInitiateReq := map[string]interface{}{
		"phone_number":   phoneNumber,
		"email":          email,
		"first_name":     "Test",
		"last_name":      "User",
		"code_challenge": "test-challenge-" + uuid.New().String(),
		"state":          "test-state-" + uuid.New().String(),
		"preferred_lang": "en",
	}
	rec, err := s.apiSuite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/register/phone/initiate", regInitiateReq)
	require.NoError(s.T(), err)

	regVerifyReq := map[string]interface{}{
		"phone_number":  phoneNumber,
		"otp_code":      "123456",
		"code_verifier": "test-verifier-" + uuid.New().String(),
		"state":         regInitiateReq["state"],
	}
	rec, err = s.apiSuite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/register/phone/verify", regVerifyReq)
	require.NoError(s.T(), err)

	var authResponse map[string]interface{}
	s.apiSuite.AssertJSONResponse(s.T(), rec, http.StatusOK, &authResponse)

	userToken, ok := authResponse["access_token"].(string)
	require.True(s.T(), ok)

	return userToken
}

func (s *ConcurrentOperationsTestSuite) getUserIDFromToken(token string) uuid.UUID {
	rec, err := s.apiSuite.MakeRequest("GET", "/api/v1/users/profile", nil, token)
	require.NoError(s.T(), err)

	var profileResponse payloads.UserProfileResponse
	s.apiSuite.AssertJSONResponse(s.T(), rec, http.StatusOK, &profileResponse)

	return profileResponse.ID
}

func (s *ConcurrentOperationsTestSuite) createTestEvent(title string, maxParticipants int) uuid.UUID {
	eventCreateReq := map[string]interface{}{
		"title":                 title,
		"description":           "Event for concurrent testing",
		"event_type":            "community",
		"start_date":            time.Now().Add(48 * time.Hour).Format(time.RFC3339),
		"end_date":              time.Now().Add(52 * time.Hour).Format(time.RFC3339),
		"registration_deadline": time.Now().Add(24 * time.Hour).Format(time.RFC3339),
		"max_participants":      maxParticipants,
		"location":              "Test Location",
		"is_published":          true,
	}
	rec, err := s.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/events", eventCreateReq)
	require.NoError(s.T(), err)

	var eventResponse payloads.EventResponse
	s.apiSuite.AssertJSONResponse(s.T(), rec, http.StatusCreated, &eventResponse)

	return eventResponse.ID
}

// TestConcurrentOperationsSuite runs the concurrent operations test suite
func TestConcurrentOperationsSuite(t *testing.T) {
	suite.Run(t, new(ConcurrentOperationsTestSuite))
}
