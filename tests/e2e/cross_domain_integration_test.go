package e2e

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/tests/integration"
)

// CrossDomainIntegrationTestSuite tests interactions between different service domains
type CrossDomainIntegrationTestSuite struct {
	suite.Suite
	apiSuite *integration.APITestSuite
}

// SetupSuite initializes the test suite
func (s *CrossDomainIntegrationTestSuite) SetupSuite() {
	s.apiSuite = integration.SetupAPITest(s.T())
}

// TearDownSuite cleans up the test suite
func (s *CrossDomainIntegrationTestSuite) TearDownSuite() {
	s.apiSuite.TeardownAPITest(s.T())
}

// TestAuthenticationUserManagementIntegration tests auth and user service integration
func (s *CrossDomainIntegrationTestSuite) TestAuthenticationUserManagementIntegration() {
	t := s.T()

	phoneNumber := "+1555000001"
	email := "<EMAIL>"

	// 1. Authentication Domain: Register user
	regInitiateReq := map[string]interface{}{
		"phone_number":   phoneNumber,
		"email":          email,
		"first_name":     "Cross",
		"last_name":      "Domain",
		"code_challenge": "test-challenge-cd1",
		"state":          "test-state-cd1",
		"preferred_lang": "en",
	}
	rec, err := s.apiSuite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/register/phone/initiate", regInitiateReq)
	require.NoError(t, err)
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, nil)

	regVerifyReq := map[string]interface{}{
		"phone_number":  phoneNumber,
		"otp_code":      "123456",
		"code_verifier": "test-verifier-cd1",
		"state":         "test-state-cd1",
	}
	rec, err = s.apiSuite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/register/phone/verify", regVerifyReq)
	require.NoError(t, err)

	var authResponse map[string]interface{}
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &authResponse)

	userToken, ok := authResponse["access_token"].(string)
	require.True(t, ok)

	// 2. User Management Domain: Get profile created by auth
	rec, err = s.apiSuite.MakeRequest("GET", "/api/v1/users/profile", nil, userToken)
	require.NoError(t, err)

	var profileResponse payloads.UserProfileResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &profileResponse)
	assert.Equal(t, email, *profileResponse.Email)
	assert.Equal(t, phoneNumber, *profileResponse.Phone)

	userID := profileResponse.ID

	// 3. User Management Domain: Update profile
	updateReq := map[string]interface{}{
		"first_name": "UpdatedCross",
		"last_name":  "UpdatedDomain",
		"bio":        "Updated by user management domain",
	}
	rec, err = s.apiSuite.MakeRequest("PUT", "/api/v1/users/profile", updateReq, userToken)
	require.NoError(t, err)
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, nil)

	// 4. Authentication Domain: Phone change flow using updated user
	newPhoneNumber := "+1555000002"
	phoneChangeReq := map[string]interface{}{
		"new_phone_number": newPhoneNumber,
		"code_challenge":   "phone-change-challenge",
		"state":            "phone-change-state",
	}
	rec, err = s.apiSuite.MakeRequest("POST", "/api/v1/authn/phone/change/initiate", phoneChangeReq, userToken)
	require.NoError(t, err)
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, nil)

	phoneChangeVerifyReq := map[string]interface{}{
		"new_phone_number": newPhoneNumber,
		"otp_code":         "123456",
		"code_verifier":    "phone-change-verifier",
		"state":            "phone-change-state",
	}
	rec, err = s.apiSuite.MakeRequest("POST", "/api/v1/authn/phone/change/verify", phoneChangeVerifyReq, userToken)
	require.NoError(t, err)

	var phoneChangeResponse map[string]interface{}
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &phoneChangeResponse)

	// 5. User Management Domain: Verify phone number was updated
	rec, err = s.apiSuite.MakeRequest("GET", "/api/v1/users/profile", nil, userToken)
	require.NoError(t, err)
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &profileResponse)
	assert.Equal(t, newPhoneNumber, *profileResponse.Phone)
	assert.Equal(t, "UpdatedCross", profileResponse.DisplayName) // Profile updates should persist

	t.Logf("Successfully tested auth-user domain integration for user %s", userID)
}

// TestEventVolunteerRegistrationIntegration tests event, volunteer, and registration domain integration
func (s *CrossDomainIntegrationTestSuite) TestEventVolunteerRegistrationIntegration() {
	t := s.T()

	// Setup: Create user
	userToken := s.createTestUser("+1555000003", "<EMAIL>")
	userID := s.getUserIDFromToken(userToken)

	// 1. Volunteer Domain: Apply as organization volunteer
	volunteerAppReq := map[string]interface{}{
		"user_id":      userID,
		"motivation":   "Want to help with events",
		"availability": "Weekends",
		"skills":       []string{"Event Management", "First Aid"},
		"experience":   "3 years volunteering",
		"references":   "Reference Person - <EMAIL>",
	}
	rec, err := s.apiSuite.MakeRequest("POST", "/api/v1/volunteers/apply", volunteerAppReq, userToken)
	require.NoError(t, err)

	var volunteerResponse payloads.VolunteerApplicationResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusCreated, &volunteerResponse)

	// Admin approves volunteer application
	approveReq := map[string]interface{}{
		"status":   "approved",
		"feedback": "Excellent background for event volunteering",
	}
	rec, err = s.apiSuite.MakeAuthenticatedRequest("PUT", fmt.Sprintf("/api/v1/volunteers/applications/%s", volunteerResponse.ID), approveReq)
	require.NoError(t, err)

	// 2. Event Domain: Create event requiring volunteers
	eventCreateReq := map[string]interface{}{
		"title":                  "Community Volunteer Event",
		"description":            "Event requiring volunteer coordination",
		"event_type":             "community",
		"start_date":             time.Now().Add(72 * time.Hour).Format(time.RFC3339),
		"end_date":               time.Now().Add(76 * time.Hour).Format(time.RFC3339),
		"registration_deadline":  time.Now().Add(48 * time.Hour).Format(time.RFC3339),
		"max_participants":       100,
		"location":               "Community Event Center",
		"volunteers_needed":      10,
		"volunteer_requirements": []string{"Setup", "Registration", "Cleanup"},
		"is_published":           true,
	}
	rec, err = s.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/events", eventCreateReq)
	require.NoError(t, err)

	var eventResponse payloads.EventResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusCreated, &eventResponse)
	eventID := eventResponse.ID

	// 3. Event-Volunteer Integration: Apply as event volunteer
	eventVolunteerReq := map[string]interface{}{
		"role":         "Registration",
		"availability": "Full event duration",
		"notes":        "Ready to help with registration desk",
	}
	rec, err = s.apiSuite.MakeRequest("POST", fmt.Sprintf("/api/v1/events/%s/volunteer", eventID), eventVolunteerReq, userToken)
	require.NoError(t, err)

	var eventVolunteerResponse payloads.EventVolunteerApplicationResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusCreated, &eventVolunteerResponse)

	// Admin approves event volunteer
	approveEventVolReq := map[string]interface{}{
		"status":   "approved",
		"feedback": "Perfect for registration role",
	}
	rec, err = s.apiSuite.MakeAuthenticatedRequest("PUT", fmt.Sprintf("/api/v1/events/%s/volunteers/%s", eventID, eventVolunteerResponse.ID), approveEventVolReq)
	require.NoError(t, err)

	// 4. Registration Domain: Register as participant (same user, different role)
	regEventReq := map[string]interface{}{
		"user_id":              userID,
		"dietary_restrictions": "Vegetarian",
		"emergency_contact":    "Emergency Contact - +1555000004",
		"additional_notes":     "Also volunteering for this event",
	}
	rec, err = s.apiSuite.MakeRequest("POST", fmt.Sprintf("/api/v1/events/%s/register", eventID), regEventReq, userToken)
	require.NoError(t, err)

	var registrationResponse payloads.EventRegistrationResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusCreated, &registrationResponse)

	// 5. Cross-Domain Verification: User should appear in both volunteer and participant lists
	// Check volunteer list
	rec, err = s.apiSuite.MakeAuthenticatedRequest("GET", fmt.Sprintf("/api/v1/events/%s/volunteers", eventID), nil)
	require.NoError(t, err)

	var volunteersResponse map[string]interface{}
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &volunteersResponse)

	volunteers, ok := volunteersResponse["volunteers"].([]interface{})
	require.True(t, ok)
	assert.Equal(t, 1, len(volunteers))

	// Check participant list
	rec, err = s.apiSuite.MakeAuthenticatedRequest("GET", fmt.Sprintf("/api/v1/events/%s/registrations", eventID), nil)
	require.NoError(t, err)

	var participantsResponse map[string]interface{}
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &participantsResponse)

	participants, ok := participantsResponse["registrations"].([]interface{})
	require.True(t, ok)
	assert.Equal(t, 1, len(participants))

	// 6. Event Statistics Integration: Verify counts include both roles
	rec, err = s.apiSuite.MakeAuthenticatedRequest("GET", fmt.Sprintf("/api/v1/events/%s/statistics", eventID), nil)
	require.NoError(t, err)

	var statsResponse map[string]interface{}
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &statsResponse)

	assert.Equal(t, float64(1), statsResponse["total_registrations"])
	assert.Equal(t, float64(1), statsResponse["volunteer_applications"])

	t.Logf("Successfully tested event-volunteer-registration integration for event %s", eventID)
}

// TestContentPostEventIntegration tests content management and event integration
func (s *CrossDomainIntegrationTestSuite) TestContentPostEventIntegration() {
	t := s.T()

	// 1. Event Domain: Create event
	eventCreateReq := map[string]interface{}{
		"title":                 "Content Integration Event",
		"description":           "Event for testing content integration",
		"event_type":            "community",
		"start_date":            time.Now().Add(96 * time.Hour).Format(time.RFC3339),
		"end_date":              time.Now().Add(100 * time.Hour).Format(time.RFC3339),
		"registration_deadline": time.Now().Add(72 * time.Hour).Format(time.RFC3339),
		"max_participants":      50,
		"location":              "Content Center",
		"is_published":          true,
	}
	rec, err := s.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/events", eventCreateReq)
	require.NoError(t, err)

	var eventResponse payloads.EventResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusCreated, &eventResponse)
	eventID := eventResponse.ID

	// 2. Content Domain: Create post about the event
	postCreateReq := map[string]interface{}{
		"title":            "Upcoming Community Event",
		"content":          fmt.Sprintf("Join us for our amazing event: %s", eventResponse.Title),
		"content_type":     "announcement",
		"tags":             []string{"event", "community", "announcement"},
		"related_event_id": eventID,
		"is_published":     true,
		"scheduled_at":     time.Now().Add(1 * time.Hour).Format(time.RFC3339),
	}
	rec, err = s.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/posts", postCreateReq)
	require.NoError(t, err)

	var postResponse payloads.PostResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusCreated, &postResponse)
	postID := postResponse.ID

	// 3. Content-Event Integration: Verify post is linked to event
	rec, err = s.apiSuite.MakeRequest("GET", fmt.Sprintf("/api/v1/posts/%s", postID), nil, s.apiSuite.GetUserToken())
	require.NoError(t, err)
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &postResponse)

	// Note: RelatedEventID field might not exist in current PostResponse structure
	// This would need to be added to support event-post linking
	assert.Equal(t, postCreateReq["title"], postResponse.Title)

	// 4. Event Content Listing: Get posts related to event
	rec, err = s.apiSuite.MakeRequest("GET", fmt.Sprintf("/api/v1/events/%s/posts", eventID), nil, s.apiSuite.GetUserToken())
	require.NoError(t, err)

	var eventPostsResponse map[string]interface{}
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &eventPostsResponse)

	relatedPosts, ok := eventPostsResponse["posts"].([]interface{})
	require.True(t, ok)
	assert.Equal(t, 1, len(relatedPosts))

	// 5. Tag System Integration: Find posts by event tags
	rec, err = s.apiSuite.MakeRequest("GET", "/api/v1/posts?tags=event,community", nil, s.apiSuite.GetUserToken())
	require.NoError(t, err)

	var taggedPostsResponse map[string]interface{}
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &taggedPostsResponse)

	taggedPosts, ok := taggedPostsResponse["posts"].([]interface{})
	require.True(t, ok)
	assert.Greater(t, len(taggedPosts), 0)

	// 6. Scheduled Content Integration: Update event should trigger content update
	eventUpdateReq := map[string]interface{}{
		"title":       "Updated Content Integration Event",
		"description": "Updated event description for content integration",
	}
	rec, err = s.apiSuite.MakeAuthenticatedRequest("PUT", fmt.Sprintf("/api/v1/events/%s", eventID), eventUpdateReq)
	require.NoError(t, err)
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, nil)

	// Verify event update
	rec, err = s.apiSuite.MakeRequest("GET", fmt.Sprintf("/api/v1/events/%s", eventID), nil, s.apiSuite.GetUserToken())
	require.NoError(t, err)
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &eventResponse)
	assert.Equal(t, "Updated Content Integration Event", eventResponse.Title)

	t.Logf("Successfully tested content-event integration for event %s and post %s", eventID, postID)
}

// TestResourceOrganizationIntegration tests resource management and organization integration
func (s *CrossDomainIntegrationTestSuite) TestResourceOrganizationIntegration() {
	t := s.T()

	// 1. Organization Domain: Get current organization details
	rec, err := s.apiSuite.MakeAuthenticatedRequest("GET", fmt.Sprintf("/api/v1/organizations/%s", s.apiSuite.GetOrgID()), nil)
	require.NoError(t, err)

	var orgResponse payloads.OrganizationResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &orgResponse)

	// 2. Resource Domain: Create organization resource
	resourceCreateReq := map[string]interface{}{
		"title":       "Organization Handbook",
		"description": "Complete guide for organization members",
		"category":    "documentation",
		"content":     "This is the organization handbook content...",
		"is_public":   true,
		"tags":        []string{"handbook", "documentation", "guide"},
	}
	rec, err = s.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/resources", resourceCreateReq)
	require.NoError(t, err)

	var resourceResponse payloads.ResourceResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusCreated, &resourceResponse)
	resourceID := resourceResponse.ID

	// 3. Organization-Resource Integration: Verify resource belongs to organization
	rec, err = s.apiSuite.MakeRequest("GET", fmt.Sprintf("/api/v1/resources/%s", resourceID), nil, s.apiSuite.GetUserToken())
	require.NoError(t, err)
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &resourceResponse)

	assert.Equal(t, s.apiSuite.GetOrgID(), resourceResponse.OrganizationID.String())

	// 4. Organization Resource Listing: Get all resources for organization
	rec, err = s.apiSuite.MakeAuthenticatedRequest("GET", fmt.Sprintf("/api/v1/organizations/%s/resources", s.apiSuite.GetOrgID()), nil)
	require.NoError(t, err)

	var orgResourcesResponse map[string]interface{}
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &orgResourcesResponse)

	resources, ok := orgResourcesResponse["resources"].([]interface{})
	require.True(t, ok)
	assert.Greater(t, len(resources), 0)

	// 5. Resource Access Control: Different organization shouldn't access resource
	// Create second organization
	org2CreateReq := map[string]interface{}{
		"name":        "Resource Test Org 2",
		"description": "Second org for resource access testing",
		"email":       "<EMAIL>",
		"phone":       "+1555000005",
	}
	rec, err = s.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/organizations", org2CreateReq)
	require.NoError(t, err)

	var org2Response payloads.OrganizationResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusCreated, &org2Response)

	// Create user in second organization
	user2Token := s.createTestUserInOrg("+1555000006", "<EMAIL>", org2Response.ID.String())

	// Try to access first org's resource from second org
	rec, err = s.apiSuite.MakeRequest("GET", fmt.Sprintf("/api/v1/resources/%s", resourceID), nil, user2Token)
	require.NoError(t, err)

	// Should either return 403 Forbidden or 404 Not Found (depending on access control strategy)
	assert.True(t, rec.Code == http.StatusForbidden || rec.Code == http.StatusNotFound,
		"Cross-organization resource access should be denied")

	// 6. Resource Statistics Integration: Update organization statistics
	rec, err = s.apiSuite.MakeAuthenticatedRequest("GET", fmt.Sprintf("/api/v1/organizations/%s/statistics", s.apiSuite.GetOrgID()), nil)
	require.NoError(t, err)

	var orgStatsResponse map[string]interface{}
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &orgStatsResponse)

	// Verify resource count is included in org statistics
	resourceCount, ok := orgStatsResponse["total_resources"].(float64)
	require.True(t, ok)
	assert.Greater(t, resourceCount, float64(0))

	t.Logf("Successfully tested resource-organization integration for resource %s", resourceID)
}

// TestNotificationIntegration tests notification system integration across domains
func (s *CrossDomainIntegrationTestSuite) TestNotificationIntegration() {
	t := s.T()

	// Setup: Create user
	userToken := s.createTestUser("+1555000007", "<EMAIL>")
	userID := s.getUserIDFromToken(userToken)

	// 1. Event Domain: Create event with notifications enabled
	eventCreateReq := map[string]interface{}{
		"title":                 "Notification Test Event",
		"description":           "Event for testing notification integration",
		"event_type":            "community",
		"start_date":            time.Now().Add(120 * time.Hour).Format(time.RFC3339),
		"end_date":              time.Now().Add(124 * time.Hour).Format(time.RFC3339),
		"registration_deadline": time.Now().Add(96 * time.Hour).Format(time.RFC3339),
		"max_participants":      25,
		"location":              "Notification Center",
		"send_reminders":        true,
		"reminder_schedule":     []string{"24h", "1h"},
		"is_published":          true,
	}
	rec, err := s.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/events", eventCreateReq)
	require.NoError(t, err)

	var eventResponse payloads.EventResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusCreated, &eventResponse)
	eventID := eventResponse.ID

	// 2. Registration Domain: Register for event (should trigger notification)
	regEventReq := map[string]interface{}{
		"user_id":           userID,
		"emergency_contact": "Emergency - +1555000008",
		"additional_notes":  "Testing notification flow",
	}
	rec, err = s.apiSuite.MakeRequest("POST", fmt.Sprintf("/api/v1/events/%s/register", eventID), regEventReq, userToken)
	require.NoError(t, err)

	var registrationResponse payloads.EventRegistrationResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusCreated, &registrationResponse)

	// 3. Notification System: Check pending notifications
	rec, err = s.apiSuite.MakeAuthenticatedRequest("GET", "/api/v1/notifications/pending", nil)
	require.NoError(t, err)

	var notificationsResponse map[string]interface{}
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &notificationsResponse)

	notifications, ok := notificationsResponse["notifications"].([]interface{})
	require.True(t, ok)

	// Should have registration confirmation notification
	found := false
	for _, notification := range notifications {
		notifMap, ok := notification.(map[string]interface{})
		require.True(t, ok)

		if notifMap["type"] == "event_registration_confirmation" {
			found = true
			assert.Equal(t, userID.String(), notifMap["user_id"])
			assert.Equal(t, eventID.String(), notifMap["event_id"])
		}
	}
	assert.True(t, found, "Should have registration confirmation notification")

	// 4. Content Domain: Create announcement (should trigger notification)
	postCreateReq := map[string]interface{}{
		"title":             "Important Announcement",
		"content":           "This is an important announcement for all members",
		"content_type":      "announcement",
		"tags":              []string{"announcement", "important"},
		"send_notification": true,
		"is_published":      true,
	}
	rec, err = s.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/posts", postCreateReq)
	require.NoError(t, err)

	var postResponse payloads.PostResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusCreated, &postResponse)

	// 5. User Notification Preferences Integration
	// Update user notification preferences
	notifPrefsReq := map[string]interface{}{
		"email_notifications":        true,
		"sms_notifications":          true,
		"event_reminders":            true,
		"announcement_notifications": true,
		"volunteer_updates":          false,
	}
	rec, err = s.apiSuite.MakeRequest("PUT", "/api/v1/users/notification-preferences", notifPrefsReq, userToken)
	require.NoError(t, err)
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, nil)

	// 6. Scheduled Job Integration: Check reminder jobs
	rec, err = s.apiSuite.MakeAuthenticatedRequest("GET", "/api/v1/jobs/scheduled", nil)
	require.NoError(t, err)

	var jobsResponse map[string]interface{}
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &jobsResponse)

	jobs, ok := jobsResponse["jobs"].([]interface{})
	require.True(t, ok)

	// Should have scheduled reminder jobs for the event
	reminderJobFound := false
	for _, job := range jobs {
		jobMap, ok := job.(map[string]interface{})
		require.True(t, ok)

		if jobMap["type"] == "event_reminder" && jobMap["event_id"] == eventID.String() {
			reminderJobFound = true
		}
	}
	assert.True(t, reminderJobFound, "Should have scheduled reminder job")

	t.Logf("Successfully tested notification integration across domains for event %s", eventID)
}

// Helper methods

func (s *CrossDomainIntegrationTestSuite) createTestUser(phoneNumber, email string) string {
	regInitiateReq := map[string]interface{}{
		"phone_number":   phoneNumber,
		"email":          email,
		"first_name":     "Test",
		"last_name":      "User",
		"code_challenge": "test-challenge-" + uuid.New().String(),
		"state":          "test-state-" + uuid.New().String(),
		"preferred_lang": "en",
	}
	rec, err := s.apiSuite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/register/phone/initiate", regInitiateReq)
	require.NoError(s.T(), err)

	regVerifyReq := map[string]interface{}{
		"phone_number":  phoneNumber,
		"otp_code":      "123456",
		"code_verifier": "test-verifier-" + uuid.New().String(),
		"state":         regInitiateReq["state"],
	}
	rec, err = s.apiSuite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/register/phone/verify", regVerifyReq)
	require.NoError(s.T(), err)

	var authResponse map[string]interface{}
	s.apiSuite.AssertJSONResponse(s.T(), rec, http.StatusOK, &authResponse)

	userToken, ok := authResponse["access_token"].(string)
	require.True(s.T(), ok)

	return userToken
}

func (s *CrossDomainIntegrationTestSuite) createTestUserInOrg(phoneNumber, email, orgID string) string {
	// This would involve creating a user and then switching to specific organization
	// For simplicity, using the same flow as createTestUser
	// In real implementation, you'd have organization-specific registration
	return s.createTestUser(phoneNumber, email)
}

func (s *CrossDomainIntegrationTestSuite) getUserIDFromToken(token string) uuid.UUID {
	rec, err := s.apiSuite.MakeRequest("GET", "/api/v1/users/profile", nil, token)
	require.NoError(s.T(), err)

	var profileResponse payloads.UserProfileResponse
	s.apiSuite.AssertJSONResponse(s.T(), rec, http.StatusOK, &profileResponse)

	return profileResponse.ID
}

// TestCrossDomainIntegrationSuite runs the cross-domain integration test suite
func TestCrossDomainIntegrationSuite(t *testing.T) {
	suite.Run(t, new(CrossDomainIntegrationTestSuite))
}
