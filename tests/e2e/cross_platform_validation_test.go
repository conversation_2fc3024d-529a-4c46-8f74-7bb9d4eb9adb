package e2e

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/internal/testutil"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/utils"
	"Membership-SAAS-System-Backend/tests/integration"
)

// CrossPlatformValidationTestSuite tests data consistency across web, mobile, and API platforms
type CrossPlatformValidationTestSuite struct {
	suite.Suite
	apiSuite *integration.APITestSuite
}

// SetupSuite initializes the test suite
func (s *CrossPlatformValidationTestSuite) SetupSuite() {
	s.apiSuite = integration.SetupAPITest(s.T())
}

// TearDownSuite cleans up the test suite
func (s *CrossPlatformValidationTestSuite) TearDownSuite() {
	s.apiSuite.TeardownAPITest(s.T())
}

// TestUserDataConsistencyAcrossPlatforms tests that user data is consistent across all platforms
func (s *CrossPlatformValidationTestSuite) TestUserDataConsistencyAcrossPlatforms() {
	t := s.T()

	// Create user (simulating mobile app registration)
	phoneNumber := "+1234567880"
	userToken, err := s.createTestUser(phoneNumber, "Mobile App")
	require.NoError(t, err)

	// Get user profile from API (simulating mobile app request)
	rec, err := s.apiSuite.MakeRequest("GET", "/api/v1/users/me", nil, userToken)
	require.NoError(t, err)

	var mobileProfile payloads.UserProfileResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &mobileProfile)

	// Update profile (simulating web app update)
	updateReq := map[string]interface{}{
		"display_name": "Updated via Web",
	}
	rec, err = s.apiSuite.MakeRequest("PATCH", "/api/v1/users/me", updateReq, userToken)
	require.NoError(t, err)
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, nil)

	// Verify update from API (simulating mobile app checking for updates)
	rec, err = s.apiSuite.MakeRequest("GET", "/api/v1/users/me", nil, userToken)
	require.NoError(t, err)

	var updatedProfile payloads.UserProfileResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &updatedProfile)

	// Validate consistency
	assert.Equal(t, mobileProfile.ID, updatedProfile.ID, "User ID should remain consistent")
	assert.Equal(t, "Updated via Web", updatedProfile.DisplayName, "Display name should be updated")
	assert.Equal(t, phoneNumber, *updatedProfile.Phone, "Phone number should remain consistent")

	t.Log("User data is consistent across platforms")
}

// TestEventDataConsistencyAcrossPlatforms tests event data consistency
func (s *CrossPlatformValidationTestSuite) TestEventDataConsistencyAcrossPlatforms() {
	t := s.T()

	// Create event (simulating web admin panel)
	orgID := s.apiSuite.GetOrgID()
	eventCreateReq := map[string]interface{}{
		"title":                 "Cross-Platform Event",
		"jsonContent":           json.RawMessage(`{"blocks":[{"type":"paragraph","data":{"text":"Event for cross-platform testing"}}]}`),
		"location_type":         "physical",
		"location_full_address": "123 Cross Platform St",
		"start_time":            time.Now().Add(72 * time.Hour).Format(time.RFC3339),
		"end_time":              time.Now().Add(76 * time.Hour).Format(time.RFC3339),
		"participant_limit":     100,
		"status":                "published",
		"tags":                  []string{"cross-platform", "test"},
	}
	eventURL := fmt.Sprintf("/api/v1/organizations/%s/events", orgID)
	rec, err := s.apiSuite.MakeAuthenticatedRequest("POST", eventURL, eventCreateReq)
	require.NoError(t, err)

	var eventResponse payloads.EventResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusCreated, &eventResponse)
	eventID := eventResponse.ID

	// Get event details from API (simulating mobile app)
	userToken, err := s.createTestUser("+1234567881", "Mobile User")
	require.NoError(t, err)

	rec, err = s.apiSuite.MakeRequest("GET", fmt.Sprintf("/api/v1/events/%s", eventID), nil, userToken)
	require.NoError(t, err)

	var mobileEventResponse payloads.EventResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &mobileEventResponse)

	// Validate consistency
	assert.Equal(t, eventResponse.ID, mobileEventResponse.ID, "Event ID should be consistent")
	assert.Equal(t, eventResponse.Title, mobileEventResponse.Title, "Event title should be consistent")
	assert.Equal(t, eventResponse.LocationType, mobileEventResponse.LocationType, "Location type should be consistent")
	assert.Equal(t, eventResponse.StartTime, mobileEventResponse.StartTime, "Start time should be consistent")
	assert.Equal(t, eventResponse.ParticipantLimit, mobileEventResponse.ParticipantLimit, "Participant limit should be consistent")
	assert.Equal(t, eventResponse.Status, mobileEventResponse.Status, "Status should be consistent")

	t.Log("Event data is consistent across platforms")
}

// TestRegistrationFlowAcrossPlatforms tests event registration consistency
func (s *CrossPlatformValidationTestSuite) TestRegistrationFlowAcrossPlatforms() {
	t := s.T()

	// Create event
	eventID := s.createTestEvent("Registration Test Event", 50)

	// Create user via mobile
	mobileUserToken, err := s.createTestUser("+1234567882", "Mobile App")
	require.NoError(t, err)

	// Register for event (simulating mobile app)
	regReq := map[string]interface{}{
		"event_id":             eventID.String(),
		"dietary_restrictions": "Vegetarian",
		"emergency_contact":    "Emergency Contact - +1234567883",
		"additional_notes":     "Registered from mobile app",
	}
	rec, err := s.apiSuite.MakeRequest("POST", "/api/v1/me/event-registrations", regReq, mobileUserToken)
	require.NoError(t, err)

	var regResponse payloads.EventRegistrationResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusCreated, &regResponse)
	registrationID := regResponse.ID

	// Check registration from API (simulating web app)
	rec, err = s.apiSuite.MakeRequest("GET", fmt.Sprintf("/api/v1/me/event-registrations/%s", registrationID), nil, mobileUserToken)
	require.NoError(t, err)

	var webRegResponse payloads.EventRegistrationResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &webRegResponse)

	// Validate consistency
	assert.Equal(t, regResponse.ID, webRegResponse.ID, "Registration ID should be consistent")
	assert.Equal(t, regResponse.EventID, webRegResponse.EventID, "Event ID should be consistent")
	assert.Equal(t, regResponse.UserID, webRegResponse.UserID, "User ID should be consistent")
	assert.Equal(t, regResponse.Status, webRegResponse.Status, "Status should be consistent")

	// Check event statistics (simulating admin panel)
	rec, err = s.apiSuite.MakeAuthenticatedRequest("GET", fmt.Sprintf("/api/v1/events/%s/statistics/registrations", eventID), nil)
	require.NoError(t, err)

	var statsResponse map[string]interface{}
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &statsResponse)

	// Verify registration count
	if totalReg, ok := statsResponse["total_registrations"].(float64); ok {
		assert.Equal(t, float64(1), totalReg, "Should have 1 registration")
	} else if regCount, ok := statsResponse["registered_count"].(float64); ok {
		assert.Equal(t, float64(1), regCount, "Should have 1 registration")
	}

	t.Log("Registration data is consistent across platforms")
}

// TestOrganizationDataConsistencyAcrossPlatforms tests organization data consistency
func (s *CrossPlatformValidationTestSuite) TestOrganizationDataConsistencyAcrossPlatforms() {
	t := s.T()

	// Get organization details from API (simulating mobile app)
	orgID := s.apiSuite.GetOrgID()
	userToken, err := s.createTestUser("+1234567884", "Mobile User")
	require.NoError(t, err)

	rec, err := s.apiSuite.MakeRequest("GET", fmt.Sprintf("/api/v1/organizations/%s", orgID), nil, userToken)
	require.NoError(t, err)

	var mobileOrgResponse payloads.OrganizationResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &mobileOrgResponse)

	// Update organization (simulating web admin)
	updateOrgReq := map[string]interface{}{
		"description": "Updated organization description from web",
	}
	rec, err = s.apiSuite.MakeAuthenticatedRequest("PATCH", fmt.Sprintf("/api/v1/organizations/%s", orgID), updateOrgReq)
	require.NoError(t, err)
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, nil)

	// Verify update from API (simulating mobile app refresh)
	rec, err = s.apiSuite.MakeRequest("GET", fmt.Sprintf("/api/v1/organizations/%s", orgID), nil, userToken)
	require.NoError(t, err)

	var updatedOrgResponse payloads.OrganizationResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &updatedOrgResponse)

	// Validate consistency
	assert.Equal(t, mobileOrgResponse.ID, updatedOrgResponse.ID, "Organization ID should be consistent")
	assert.Equal(t, mobileOrgResponse.Name, updatedOrgResponse.Name, "Organization name should remain consistent")
	assert.Equal(t, "Updated organization description from web", *updatedOrgResponse.Description, "Description should be updated")

	t.Log("Organization data is consistent across platforms")
}

// TestNotificationConsistencyAcrossPlatforms tests notification delivery across platforms
func (s *CrossPlatformValidationTestSuite) TestNotificationConsistencyAcrossPlatforms() {
	t := s.T()

	// Create users for different platforms
	webUserToken, err := s.createTestUser("+1234567885", "Web User")
	require.NoError(t, err)
	mobileUserToken, err := s.createTestUser("+1234567886", "Mobile User")
	require.NoError(t, err)

	// Create announcement (simulating admin action)
	orgID := s.apiSuite.GetOrgID()
	postCreateReq := map[string]interface{}{
		"title":             "Cross-Platform Announcement",
		"jsonContent":       json.RawMessage(`{"blocks":[{"type":"paragraph","data":{"text":"Important announcement for all platforms"}}]}`),
		"content_type":      "announcement",
		"tags":              []string{"announcement", "cross-platform"},
		"send_notification": true,
		"status":            "published",
	}
	postURL := fmt.Sprintf("/api/v1/organizations/%s/posts", orgID)
	rec, err := s.apiSuite.MakeAuthenticatedRequest("POST", postURL, postCreateReq)
	require.NoError(t, err)

	var postResponse payloads.PostResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusCreated, &postResponse)

	// Get posts from both user perspectives
	// Web user
	rec, err = s.apiSuite.MakeRequest("GET", "/api/v1/posts?content_type=announcement", nil, webUserToken)
	require.NoError(t, err)

	var webPostsResponse map[string]interface{}
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &webPostsResponse)

	webPosts, ok := webPostsResponse["posts"].([]interface{})
	require.True(t, ok)
	assert.Greater(t, len(webPosts), 0, "Web user should see announcement")

	// Mobile user
	rec, err = s.apiSuite.MakeRequest("GET", "/api/v1/posts?content_type=announcement", nil, mobileUserToken)
	require.NoError(t, err)

	var mobilePostsResponse map[string]interface{}
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &mobilePostsResponse)

	mobilePosts, ok := mobilePostsResponse["posts"].([]interface{})
	require.True(t, ok)
	assert.Greater(t, len(mobilePosts), 0, "Mobile user should see announcement")

	// Verify both see the same announcement
	assert.Equal(t, len(webPosts), len(mobilePosts), "Both platforms should see same number of announcements")

	t.Log("Notification/announcement data is consistent across platforms")
}

// Helper methods

func (s *CrossPlatformValidationTestSuite) createTestUser(phoneNumber, platform string) (string, error) {
	// Generate PKCE values
	codeVerifier := "test-verifier-" + uuid.New().String()
	codeChallenge := utils.GenerateS256Challenge(codeVerifier)
	state := "test-state-" + uuid.New().String()

	// Step 1: Check phone
	checkReq := map[string]interface{}{
		"phone": phoneNumber,
	}
	rec, err := s.apiSuite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/phone/check", checkReq)
	if err != nil {
		return "", err
	}
	s.apiSuite.AssertJSONResponse(s.T(), rec, http.StatusOK, nil)

	// Step 2: Initiate registration
	initiateReq := map[string]interface{}{
		"phone":                 phoneNumber,
		"client_id":             "test-client",
		"code_challenge":        codeChallenge,
		"code_challenge_method": "S256",
		"state":                 state,
		"phone_otp_channel":     "sms",
	}
	rec, err = s.apiSuite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/register/phone/initiate", initiateReq)
	if err != nil {
		return "", err
	}

	// Extract OTP SID from response for dynamic OTP
	var initiateResp map[string]interface{}
	s.apiSuite.AssertJSONResponse(s.T(), rec, http.StatusOK, &initiateResp)

	// Get dynamic OTP from mock service if available
	otp := testutil.GenerateTestOTP() // Default fallback
	if _, ok := initiateResp["otp_sid"].(string); ok {
		// In test mode, OTP is always 123456
		otp = "123456"
	}

	// Step 3: Verify registration
	verifyReq := map[string]interface{}{
		"state":                  state,
		"otp":                    otp,
		"code_verifier":          codeVerifier,
		"display_name":           fmt.Sprintf("Cross-Platform Test User - %s", platform),
		"interface_language":     "en",
		"communication_language": "en",
	}
	rec, err = s.apiSuite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/register/phone/verify", verifyReq)
	if err != nil {
		return "", err
	}

	var authResponse map[string]interface{}
	s.apiSuite.AssertJSONResponse(s.T(), rec, http.StatusCreated, &authResponse)

	token, ok := authResponse["access_token"].(string)
	if !ok {
		return "", fmt.Errorf("no access token in response")
	}

	return token, nil
}

func (s *CrossPlatformValidationTestSuite) createTestEvent(title string, maxParticipants int) uuid.UUID {
	orgID := s.apiSuite.GetOrgID()
	eventCreateReq := map[string]interface{}{
		"title":                 title,
		"jsonContent":           json.RawMessage(`{"blocks":[{"type":"paragraph","data":{"text":"Event for cross-platform validation"}}]}`),
		"location_type":         "physical",
		"location_full_address": "Cross-Platform Test Location",
		"start_time":            time.Now().Add(48 * time.Hour).Format(time.RFC3339),
		"end_time":              time.Now().Add(52 * time.Hour).Format(time.RFC3339),
		"participant_limit":     maxParticipants,
		"status":                "published",
	}
	eventURL := fmt.Sprintf("/api/v1/organizations/%s/events", orgID)
	rec, err := s.apiSuite.MakeAuthenticatedRequest("POST", eventURL, eventCreateReq)
	require.NoError(s.T(), err)

	var eventResponse payloads.EventResponse
	s.apiSuite.AssertJSONResponse(s.T(), rec, http.StatusCreated, &eventResponse)

	return eventResponse.ID
}

// TestCrossPlatformValidationSuite runs the cross-platform validation test suite
func TestCrossPlatformValidationSuite(t *testing.T) {
	suite.Run(t, new(CrossPlatformValidationTestSuite))
}
