package e2e

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	// "Membership-SAAS-System-Backend/internal/config" // Temporarily commented out
	"Membership-SAAS-System-Backend/tests/integration"
)

// RateLimitingTestSuite tests rate limiting functionality
type RateLimitingTestSuite struct {
	suite.Suite
	apiSuite    *integration.APITestSuite
	testServer  *httptest.Server
	serverHost  string
	wsHelper    *WebSocketTestHelper
	originalEnv map[string]string
	mu          sync.Mutex
}

// SetupSuite runs once before all tests
func (s *RateLimitingTestSuite) SetupSuite() {
	// Set environment for test mode
	s.T().Setenv("GO_ENV", "test")
	s.T().Setenv("TESTING", "true")

	s.apiSuite = integration.SetupAPITest(s.T())

	// Initialize WebSocket test helper
	s.wsHelper = NewWebSocketTestHelper(s.T())

	// Start a real test server once for all tests
	s.testServer, s.serverHost = s.apiSuite.StartTestServer()

	// Save original environment variables
	s.originalEnv = make(map[string]string)
	s.originalEnv["RATE_LIMIT_ENABLED"] = os.Getenv("RATE_LIMIT_ENABLED")
	s.originalEnv["RATE_LIMIT_SKIP_IN_TESTS"] = os.Getenv("RATE_LIMIT_SKIP_IN_TESTS")
	s.originalEnv["RATE_LIMIT_PER_MINUTE"] = os.Getenv("RATE_LIMIT_PER_MINUTE")
	s.originalEnv["GO_ENV"] = os.Getenv("GO_ENV")
}

// TearDownSuite runs once after all tests
func (s *RateLimitingTestSuite) TearDownSuite() {
	// Restore original environment variables
	for key, value := range s.originalEnv {
		if value != "" {
			os.Setenv(key, value)
		} else {
			os.Unsetenv(key)
		}
	}

	// Clean up WebSocket helper
	if s.wsHelper != nil {
		s.wsHelper.Cleanup()
	}

	// Clean up test server
	if s.testServer != nil {
		s.testServer.Close()
	}

	// Clean up API suite
	if s.apiSuite != nil {
		s.apiSuite.TeardownAPITest(s.T())
	}
}

// Helper function to enable rate limiting for a specific test
func (s *RateLimitingTestSuite) withRateLimitingEnabled(t *testing.T, ratePerMinute int, fn func()) {
	s.mu.Lock()
	defer s.mu.Unlock()

	// Save current values
	oldEnabled := os.Getenv("RATE_LIMIT_ENABLED")
	oldSkipInTests := os.Getenv("RATE_LIMIT_SKIP_IN_TESTS")
	oldGoEnv := os.Getenv("GO_ENV")
	oldRate := os.Getenv("RATE_LIMIT_PER_MINUTE")

	// Enable rate limiting
	os.Setenv("RATE_LIMIT_ENABLED", "true")
	os.Setenv("RATE_LIMIT_SKIP_IN_TESTS", "false")
	os.Setenv("GO_ENV", "production") // Force production mode
	os.Setenv("RATE_LIMIT_PER_MINUTE", fmt.Sprintf("%d", ratePerMinute))

	// Ensure cleanup happens
	defer func() {
		if oldEnabled != "" {
			os.Setenv("RATE_LIMIT_ENABLED", oldEnabled)
		} else {
			os.Unsetenv("RATE_LIMIT_ENABLED")
		}

		if oldSkipInTests != "" {
			os.Setenv("RATE_LIMIT_SKIP_IN_TESTS", oldSkipInTests)
		} else {
			os.Unsetenv("RATE_LIMIT_SKIP_IN_TESTS")
		}

		if oldGoEnv != "" {
			os.Setenv("GO_ENV", oldGoEnv)
		} else {
			os.Unsetenv("GO_ENV")
		}

		if oldRate != "" {
			os.Setenv("RATE_LIMIT_PER_MINUTE", oldRate)
		} else {
			os.Unsetenv("RATE_LIMIT_PER_MINUTE")
		}
	}()

	// Execute the test function
	fn()
}

// TestBasicRateLimiting tests that rate limiting works for general endpoints
func (s *RateLimitingTestSuite) TestBasicRateLimiting() {
	t := s.T()

	// Skip if rate limiting tests are disabled
	if os.Getenv("SKIP_RATE_LIMIT_TESTS") == "true" {
		t.Skip("Rate limiting tests disabled")
	}

	s.withRateLimitingEnabled(t, 5, func() {
		// Create a test endpoint that doesn't require authentication
		endpoint := "/api/v1/health"

		// Track successful and rate limited requests
		successCount := 0
		rateLimitedCount := 0

		// Make 10 requests rapidly
		for i := 0; i < 10; i++ {
			resp, err := s.makeRequest(http.MethodGet, endpoint, nil, nil)
			require.NoError(t, err)

			if resp.StatusCode == http.StatusOK {
				successCount++
			} else if resp.StatusCode == http.StatusTooManyRequests {
				rateLimitedCount++

				// Verify rate limit headers
				retryAfter := resp.Header.Get("Retry-After")
				assert.NotEmpty(t, retryAfter, "Retry-After header should be present")

				remaining := resp.Header.Get("X-RateLimit-Remaining")
				assert.NotEmpty(t, remaining, "X-RateLimit-Remaining header should be present")
			}
		}

		// Should have some successful requests and some rate limited
		assert.Greater(t, successCount, 0, "Should have at least one successful request")
		assert.Greater(t, rateLimitedCount, 0, "Should have at least one rate limited request")
		assert.Equal(t, 10, successCount+rateLimitedCount, "Total requests should be 10")
	})
}

// TestAuthEndpointRateLimiting tests stricter rate limiting on authentication endpoints
func (s *RateLimitingTestSuite) TestAuthEndpointRateLimiting() {
	t := s.T()

	s.withRateLimitingEnabled(t, 10, func() {
		// Test phone check endpoint (should have stricter limits)
		endpoint := "/api/v1/authn/phone/check"

		successCount := 0
		rateLimitedCount := 0

		// Make rapid requests to auth endpoint
		for i := 0; i < 15; i++ {
			payload := map[string]string{
				"phone": fmt.Sprintf("+1555000%04d", i),
			}

			resp, err := s.makeRequest(http.MethodPost, endpoint, payload, nil)
			require.NoError(t, err)

			if resp.StatusCode == http.StatusOK || resp.StatusCode == http.StatusBadRequest {
				successCount++
			} else if resp.StatusCode == http.StatusTooManyRequests {
				rateLimitedCount++
			}
		}

		// Auth endpoints should have stricter rate limiting
		assert.Greater(t, rateLimitedCount, 0, "Auth endpoint should be rate limited")
		t.Logf("Auth endpoint: %d successful, %d rate limited", successCount, rateLimitedCount)
	})
}

// TestIPBasedRateLimiting tests that rate limiting is per IP address
func (s *RateLimitingTestSuite) TestIPBasedRateLimiting() {
	t := s.T()

	s.withRateLimitingEnabled(t, 5, func() {
		endpoint := "/api/v1/health"

		// Test with first IP
		ip1Results := s.testIPRateLimit(t, endpoint, "*************", 10)
		assert.Greater(t, ip1Results.rateLimited, 0, "First IP should be rate limited")

		// Test with second IP - should have its own limit
		ip2Results := s.testIPRateLimit(t, endpoint, "*************", 10)
		assert.Greater(t, ip2Results.success, 0, "Second IP should have successful requests")

		// Both IPs should experience rate limiting independently
		t.Logf("IP1: %d successful, %d rate limited", ip1Results.success, ip1Results.rateLimited)
		t.Logf("IP2: %d successful, %d rate limited", ip2Results.success, ip2Results.rateLimited)
	})
}

// TestExemptIPConfiguration tests that exempt IPs bypass rate limiting
func (s *RateLimitingTestSuite) TestExemptIPConfiguration() {
	t := s.T()

	// Set exempt IPs
	oldExemptIPs := os.Getenv("RATE_LIMIT_EXEMPT_IPS")
	os.Setenv("RATE_LIMIT_EXEMPT_IPS", "********,127.0.0.1")
	defer func() {
		if oldExemptIPs != "" {
			os.Setenv("RATE_LIMIT_EXEMPT_IPS", oldExemptIPs)
		} else {
			os.Unsetenv("RATE_LIMIT_EXEMPT_IPS")
		}
	}()

	s.withRateLimitingEnabled(t, 5, func() {
		endpoint := "/api/v1/health"

		// Test with exempt IP - should never be rate limited
		results := s.testIPRateLimit(t, endpoint, "********", 20)
		assert.Equal(t, 20, results.success, "Exempt IP should not be rate limited")
		assert.Equal(t, 0, results.rateLimited, "Exempt IP should have no rate limited requests")
	})
}

// TestRateLimitReset tests that rate limits reset after the time window
func (s *RateLimitingTestSuite) TestRateLimitReset() {
	t := s.T()

	// Skip this test in CI as it takes time
	if os.Getenv("CI") == "true" {
		t.Skip("Skipping time-based test in CI")
	}

	s.withRateLimitingEnabled(t, 5, func() {
		endpoint := "/api/v1/health"

		// First, exhaust the rate limit
		results1 := s.testIPRateLimit(t, endpoint, "*************", 10)
		assert.Greater(t, results1.rateLimited, 0, "Should be rate limited")

		// Wait for rate limit window to reset (assuming 1 minute window)
		t.Log("Waiting 61 seconds for rate limit to reset...")
		time.Sleep(61 * time.Second)

		// Try again - should work
		results2 := s.testIPRateLimit(t, endpoint, "*************", 5)
		assert.Greater(t, results2.success, 0, "Should have successful requests after reset")
	})
}

// TestConcurrentRateLimiting tests rate limiting under concurrent load
func (s *RateLimitingTestSuite) TestConcurrentRateLimiting() {
	t := s.T()

	s.withRateLimitingEnabled(t, 10, func() {
		endpoint := "/api/v1/health"

		var wg sync.WaitGroup
		results := struct {
			success     int
			rateLimited int
			mu          sync.Mutex
		}{}

		// Launch 20 concurrent requests
		for i := 0; i < 20; i++ {
			wg.Add(1)
			go func(index int) {
				defer wg.Done()

				// Small random delay to spread requests
				time.Sleep(time.Duration(index*10) * time.Millisecond)

				resp, err := s.makeRequestWithIP(http.MethodGet, endpoint, nil, nil, "************")
				if err != nil {
					return
				}

				results.mu.Lock()
				defer results.mu.Unlock()

				if resp.StatusCode == http.StatusOK {
					results.success++
				} else if resp.StatusCode == http.StatusTooManyRequests {
					results.rateLimited++
				}
			}(i)
		}

		wg.Wait()

		// Should have a mix of successful and rate limited requests
		assert.Greater(t, results.success, 0, "Should have successful requests")
		assert.Greater(t, results.rateLimited, 0, "Should have rate limited requests")
		assert.Equal(t, 20, results.success+results.rateLimited, "All requests should be accounted for")

		t.Logf("Concurrent test: %d successful, %d rate limited", results.success, results.rateLimited)
	})
}

// TestWebSocketRateLimiting tests rate limiting for WebSocket connections
func (s *RateLimitingTestSuite) TestWebSocketRateLimiting() {
	t := s.T()

	// Create test user for WebSocket auth
	testUser := s.createTestUser(t)

	s.withRateLimitingEnabled(t, 5, func() {
		successCount := 0
		failureCount := 0

		// Try to create multiple WebSocket connections rapidly
		for i := 0; i < 10; i++ {
			conn, err := s.wsHelper.CreateAuthenticatedConnection(t, s.getWebSocketURL(), testUser.AccessToken)
			if err == nil {
				successCount++
				conn.Close()
			} else {
				failureCount++
				// WebSocket connection failures due to rate limiting
				t.Logf("WebSocket connection failed: %v", err)
			}
		}

		// Should have some successful connections and some failures
		assert.Greater(t, successCount, 0, "Should have some successful WebSocket connections")
		assert.Greater(t, failureCount, 0, "Should have some rate limited WebSocket connections")

		t.Logf("WebSocket connections: %d successful, %d failed", successCount, failureCount)
	})
}

// Helper types and methods

type rateLimitResults struct {
	success     int
	rateLimited int
}

func (s *RateLimitingTestSuite) testIPRateLimit(t *testing.T, endpoint string, ip string, requests int) rateLimitResults {
	results := rateLimitResults{}

	for i := 0; i < requests; i++ {
		resp, err := s.makeRequestWithIP(http.MethodGet, endpoint, nil, nil, ip)
		require.NoError(t, err)

		if resp.StatusCode == http.StatusOK {
			results.success++
		} else if resp.StatusCode == http.StatusTooManyRequests {
			results.rateLimited++
		}
	}

	return results
}

func (s *RateLimitingTestSuite) makeRequestWithIP(method, endpoint string, payload interface{}, headers map[string]string, ip string) (*http.Response, error) {
	if headers == nil {
		headers = make(map[string]string)
	}
	headers["X-Real-IP"] = ip
	headers["X-Forwarded-For"] = ip

	return s.makeRequest(method, endpoint, payload, headers)
}

// makeRequest is a helper method to make HTTP requests to the test server
func (s *RateLimitingTestSuite) makeRequest(method, endpoint string, payload interface{}, headers map[string]string) (*http.Response, error) {
	var body []byte
	var err error

	if payload != nil {
		body, err = json.Marshal(payload)
		if err != nil {
			return nil, err
		}
	}

	url := fmt.Sprintf("%s%s", s.serverHost, endpoint)
	req, err := http.NewRequest(method, url, bytes.NewReader(body))
	if err != nil {
		return nil, err
	}

	// Set default headers
	req.Header.Set("Content-Type", "application/json")

	// Set custom headers
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	return client.Do(req)
}

// createTestUser creates a test user for authenticated tests
func (s *RateLimitingTestSuite) createTestUser(t *testing.T) *integration.TestUserResponse {
	phone := fmt.Sprintf("+1555%07d", time.Now().UnixNano()%10000000)
	user, err := s.apiSuite.CreateTestUser("member", phone)
	require.NoError(t, err)
	return user
}

// getWebSocketURL returns the WebSocket URL for the test server
func (s *RateLimitingTestSuite) getWebSocketURL() string {
	return fmt.Sprintf("ws://%s/ws", s.serverHost)
}

// TestSkipRateLimitingInTestMode verifies rate limiting is disabled in test mode
func (s *RateLimitingTestSuite) TestSkipRateLimitingInTestMode() {
	t := s.T()

	// Ensure we're in test mode
	os.Setenv("GO_ENV", "test")
	os.Setenv("RATE_LIMIT_SKIP_IN_TESTS", "true")
	defer func() {
		os.Unsetenv("GO_ENV")
		os.Unsetenv("RATE_LIMIT_SKIP_IN_TESTS")
	}()

	// Verify rate limiting is disabled
	// In test mode with RATE_LIMIT_SKIP_IN_TESTS=true, rate limiting should be disabled
	// This is verified by making many requests without rate limiting

	// Make many rapid requests - none should be rate limited
	endpoint := "/api/v1/health"
	for i := 0; i < 50; i++ {
		resp, err := s.makeRequest(http.MethodGet, endpoint, nil, nil)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, resp.StatusCode, "Request %d should not be rate limited in test mode", i)
	}
}

// Test runner
func TestRateLimitingSuite(t *testing.T) {
	// Only run if explicitly requested or in comprehensive test mode
	if os.Getenv("RUN_RATE_LIMIT_TESTS") != "true" {
		t.Skip("Rate limiting tests disabled. Set RUN_RATE_LIMIT_TESTS=true to run")
	}

	suite.Run(t, new(RateLimitingTestSuite))
}
