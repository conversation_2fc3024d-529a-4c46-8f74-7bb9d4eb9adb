package e2e

import (
	"testing"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
)

// generateEnhancedTestJWT creates a test JWT token with the enhanced format
func generateEnhancedTestJWT(t *testing.T, userID, platformRole string, activeOrgID string, orgRole string) string {
	// Build the active org structure
	var activeOrg interface{}
	if activeOrgID != "" && orgRole != "" {
		activeOrg = map[string]interface{}{
			"id":   activeOrgID,
			"role": orgRole,
		}
	}

	claims := jwt.MapClaims{
		"user_id":       userID,
		"platform_role": platformRole,
		"active_org":    activeOrg,
		"exp":           time.Now().Add(time.Hour).Unix(),
		"iat":           time.Now().Unix(),
		"nbf":           time.Now().Unix(),
		"iss":           "MembershipSaas",
		"sub":           userID,
	}

	// Add platform_role to claims
	claims["platform_role"] = platformRole

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte("test-secret-key-for-testing"))
	require.NoError(t, err)

	return tokenString
}

// generateAdminTestJWT creates a test JWT token for an admin user
func generateAdminTestJWT(t *testing.T) string {
	// Use the superadmin user ID from migrations
	superAdminID := "00000000-0000-0000-0000-000000000001"
	defaultOrgID := "00000000-0000-0000-0000-000000000002"

	return generateEnhancedTestJWT(t, superAdminID, "super_admin", defaultOrgID, "owner")
}

// generateUserTestJWT creates a test JWT token for a regular user
func generateUserTestJWT(t *testing.T, userID string, orgID string) string {
	if userID == "" {
		userID = uuid.New().String()
	}
	if orgID == "" {
		orgID = "00000000-0000-0000-0000-000000000002" // Default org
	}

	return generateEnhancedTestJWT(t, userID, "user", orgID, "member")
}
