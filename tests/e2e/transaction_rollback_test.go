package e2e

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/tests/integration"
)

// TransactionRollbackTestSuite tests database transaction integrity and rollback behavior
type TransactionRollbackTestSuite struct {
	suite.Suite
	apiSuite *integration.APITestSuite
	dbPool   *pgxpool.Pool
}

// SetupSuite initializes the test suite
func (s *TransactionRollbackTestSuite) SetupSuite() {
	s.apiSuite = integration.SetupAPITest(s.T())

	// Setup direct database connection for transaction testing
	ctx := context.Background()
	pool, err := pgxpool.New(ctx, s.apiSuite.GetDBConnString())
	require.NoError(s.T(), err)
	s.dbPool = pool
}

// TearDownSuite cleans up the test suite
func (s *TransactionRollbackTestSuite) TearDownSuite() {
	if s.dbPool != nil {
		s.dbPool.Close()
	}
	s.apiSuite.TeardownAPITest(s.T())
}

// TestEventRegistrationTransactionRollback tests rollback when event registration fails mid-transaction
func (s *TransactionRollbackTestSuite) TestEventRegistrationTransactionRollback() {
	t := s.T()

	// Setup: Create user and event
	userToken := s.createTestUser("+1555001001", "<EMAIL>")
	userID := s.getUserIDFromToken(userToken)

	eventID := s.createTestEvent("Transaction Test Event", 1) // Max 1 participant

	// Step 1: First user registers successfully
	regReq := map[string]interface{}{
		"user_id":           userID,
		"emergency_contact": "Emergency - +1555001002",
	}
	rec, err := s.apiSuite.MakeRequest("POST", fmt.Sprintf("/api/v1/events/%s/register", eventID), regReq, userToken)
	require.NoError(t, err)
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusCreated, nil)

	// Verify registration exists
	count := s.countEventRegistrations(eventID)
	assert.Equal(t, 1, count, "Should have 1 registration")

	// Step 2: Create second user
	user2Token := s.createTestUser("+1555001003", "<EMAIL>")
	user2ID := s.getUserIDFromToken(user2Token)

	// Step 3: Second user attempts to register (should fail due to capacity)
	reg2Req := map[string]interface{}{
		"user_id":           user2ID,
		"emergency_contact": "Emergency - +1555001004",
	}
	rec, err = s.apiSuite.MakeRequest("POST", fmt.Sprintf("/api/v1/events/%s/register", eventID), reg2Req, user2Token)
	require.NoError(t, err)

	// Should return conflict or bad request
	assert.True(t, rec.Code == http.StatusConflict || rec.Code == http.StatusBadRequest,
		"Registration should fail when event is full")

	// Step 4: Verify no partial data was committed
	count = s.countEventRegistrations(eventID)
	assert.Equal(t, 1, count, "Should still have exactly 1 registration")

	// Verify second user has no registration
	reg2Exists := s.userHasRegistration(user2ID, eventID)
	assert.False(t, reg2Exists, "Second user should not have registration")

	// Verify first user's registration is still intact
	reg1Exists := s.userHasRegistration(userID, eventID)
	assert.True(t, reg1Exists, "First user's registration should remain")
}

// TestVolunteerApplicationTransactionRollback tests rollback in volunteer application workflow
func (s *TransactionRollbackTestSuite) TestVolunteerApplicationTransactionRollback() {
	t := s.T()
	ctx := context.Background()

	// Setup: Create user
	userToken := s.createTestUser("+1555001005", "<EMAIL>")
	userID := s.getUserIDFromToken(userToken)

	// Step 1: Check initial volunteer application count
	initialCount := s.countVolunteerApplications(userID)

	// Step 2: Simulate partial volunteer application creation
	// We'll use direct database manipulation to test rollback
	tx, err := s.dbPool.Begin(ctx)
	require.NoError(t, err)

	// Insert volunteer application
	appID := uuid.New()
	_, err = tx.Exec(ctx, `
		INSERT INTO volunteer_applications (id, user_id, organization_id, motivation, availability, status, created_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7)
	`, appID, userID, s.apiSuite.GetOrgID(), "Test motivation", "Weekends", "pending", time.Now())
	require.NoError(t, err)

	// Simulate error condition (e.g., invalid skills data)
	_, err = tx.Exec(ctx, `
		INSERT INTO volunteer_skills (application_id, skill_name)
		VALUES ($1, $2)
	`, appID, "") // Empty skill name should cause constraint violation

	// This should fail, triggering rollback
	if err != nil {
		tx.Rollback(ctx)
	} else {
		tx.Commit(ctx)
	}

	// Step 3: Verify rollback occurred
	finalCount := s.countVolunteerApplications(userID)
	assert.Equal(t, initialCount, finalCount, "Volunteer application count should be unchanged after rollback")

	// Verify the application doesn't exist
	appExists := s.volunteerApplicationExists(appID)
	assert.False(t, appExists, "Volunteer application should not exist after rollback")
}

// TestEventCreationTransactionRollback tests rollback when event creation fails
func (s *TransactionRollbackTestSuite) TestEventCreationTransactionRollback() {
	t := s.T()

	// Step 1: Count initial events
	initialCount := s.countEvents()

	// Step 2: Attempt to create event with invalid data that should cause rollback
	invalidEventReq := map[string]interface{}{
		"title":                 "Invalid Event",
		"description":           "Event with invalid data",
		"event_type":            "invalid_type", // Invalid event type
		"start_date":            "invalid-date", // Invalid date format
		"end_date":              time.Now().Add(24 * time.Hour).Format(time.RFC3339),
		"registration_deadline": time.Now().Add(12 * time.Hour).Format(time.RFC3339),
		"max_participants":      -1, // Invalid negative number
		"location":              "", // Empty required field
		"is_published":          true,
	}

	rec, err := s.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/events", invalidEventReq)
	require.NoError(t, err)

	// Should return validation error
	assert.Equal(t, http.StatusBadRequest, rec.Code, "Invalid event should return bad request")

	// Step 3: Verify no event was created
	finalCount := s.countEvents()
	assert.Equal(t, initialCount, finalCount, "Event count should be unchanged after failed creation")
}

// TestUserRegistrationTransactionRollback tests rollback in user registration process
func (s *TransactionRollbackTestSuite) TestUserRegistrationTransactionRollback() {
	t := s.T()

	phoneNumber := "+1555001007"
	email := "<EMAIL>"

	// Step 1: Count initial users
	initialUserCount := s.countUsers()
	initialOTPCount := s.countOTPAttempts(phoneNumber)

	// Step 2: Initiate registration
	regInitiateReq := map[string]interface{}{
		"phone_number":   phoneNumber,
		"email":          email,
		"first_name":     "Rollback",
		"last_name":      "Test",
		"code_challenge": "test-challenge-rollback",
		"state":          "test-state-rollback",
		"preferred_lang": "en",
	}
	rec, err := s.apiSuite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/register/phone/initiate", regInitiateReq)
	require.NoError(t, err)
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, nil)

	// Verify OTP attempt was created
	otpCount := s.countOTPAttempts(phoneNumber)
	assert.Greater(t, otpCount, initialOTPCount, "OTP attempt should be created")

	// Step 3: Attempt verification with invalid data that should cause rollback
	invalidVerifyReq := map[string]interface{}{
		"phone_number":  phoneNumber,
		"otp_code":      "000000", // Invalid OTP
		"code_verifier": "wrong-verifier",
		"state":         "wrong-state",
	}
	rec, err = s.apiSuite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/register/phone/verify", invalidVerifyReq)
	require.NoError(t, err)

	// Should return unauthorized or bad request
	assert.True(t, rec.Code == http.StatusUnauthorized || rec.Code == http.StatusBadRequest,
		"Invalid OTP verification should fail")

	// Step 4: Verify no user was created despite OTP initiation
	finalUserCount := s.countUsers()
	assert.Equal(t, initialUserCount, finalUserCount, "User count should be unchanged after failed verification")

	// Verify user doesn't exist
	userExists := s.userExistsByPhone(phoneNumber)
	assert.False(t, userExists, "User should not exist after failed verification")
}

// TestPaymentTransactionRollback tests rollback in payment processing scenarios
func (s *TransactionRollbackTestSuite) TestPaymentTransactionRollback() {
	t := s.T()
	ctx := context.Background()

	// Setup: Create user and paid event
	userToken := s.createTestUser("+1555001009", "<EMAIL>")
	userID := s.getUserIDFromToken(userToken)

	eventID := s.createPaidTestEvent("Paid Event", 50, 25.00) // $25 event

	// Step 1: Register for event
	regReq := map[string]interface{}{
		"user_id":           userID,
		"emergency_contact": "Emergency - +1555001010",
	}
	rec, err := s.apiSuite.MakeRequest("POST", fmt.Sprintf("/api/v1/events/%s/register", eventID), regReq, userToken)
	require.NoError(t, err)
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusCreated, nil)

	// Step 2: Simulate payment transaction rollback
	tx, err := s.dbPool.Begin(ctx)
	require.NoError(t, err)

	// Update registration to processing payment
	_, err = tx.Exec(ctx, `
		UPDATE event_registrations 
		SET payment_status = 'processing', payment_method = 'credit_card'
		WHERE user_id = $1 AND event_id = $2
	`, userID, eventID)
	require.NoError(t, err)

	// Simulate payment processor failure (rollback)
	// In real scenario, this would be external payment gateway failure
	err = tx.Rollback(ctx)
	require.NoError(t, err)

	// Step 3: Verify payment status was rolled back
	paymentStatus := s.getRegistrationPaymentStatus(userID, eventID)
	assert.Equal(t, "pending", paymentStatus, "Payment status should be rolled back to pending")

	// Verify registration still exists but payment is not processed
	regExists := s.userHasRegistration(userID, eventID)
	assert.True(t, regExists, "Registration should still exist")
}

// TestConcurrentTransactionRollback tests rollback behavior under concurrent operations
func (s *TransactionRollbackTestSuite) TestConcurrentTransactionRollback() {
	t := s.T()

	// Setup: Create event with limited capacity
	eventID := s.createTestEvent("Concurrent Rollback Test", 2) // Max 2 participants

	// Step 1: Create multiple users concurrently
	user1Token := s.createTestUser("+1555001011", "<EMAIL>")
	user2Token := s.createTestUser("+1555001012", "<EMAIL>")
	user3Token := s.createTestUser("+1555001013", "<EMAIL>")

	user1ID := s.getUserIDFromToken(user1Token)
	user2ID := s.getUserIDFromToken(user2Token)
	user3ID := s.getUserIDFromToken(user3Token)

	// Step 2: Simulate concurrent registration attempts
	done := make(chan struct{}, 3)
	results := make([]int, 3)

	// Goroutine 1
	go func() {
		defer func() { done <- struct{}{} }()
		regReq := map[string]interface{}{
			"user_id":           user1ID,
			"emergency_contact": "Emergency 1",
		}
		rec, _ := s.apiSuite.MakeRequest("POST", fmt.Sprintf("/api/v1/events/%s/register", eventID), regReq, user1Token)
		results[0] = rec.Code
	}()

	// Goroutine 2
	go func() {
		defer func() { done <- struct{}{} }()
		regReq := map[string]interface{}{
			"user_id":           user2ID,
			"emergency_contact": "Emergency 2",
		}
		rec, _ := s.apiSuite.MakeRequest("POST", fmt.Sprintf("/api/v1/events/%s/register", eventID), regReq, user2Token)
		results[1] = rec.Code
	}()

	// Goroutine 3
	go func() {
		defer func() { done <- struct{}{} }()
		regReq := map[string]interface{}{
			"user_id":           user3ID,
			"emergency_contact": "Emergency 3",
		}
		rec, _ := s.apiSuite.MakeRequest("POST", fmt.Sprintf("/api/v1/events/%s/register", eventID), regReq, user3Token)
		results[2] = rec.Code
	}()

	// Wait for all goroutines
	for i := 0; i < 3; i++ {
		<-done
	}

	// Step 3: Verify transaction integrity
	finalCount := s.countEventRegistrations(eventID)
	assert.LessOrEqual(t, finalCount, 2, "Should not exceed event capacity")

	// Count successful registrations
	successCount := 0
	for _, code := range results {
		if code == http.StatusCreated {
			successCount++
		}
	}

	assert.Equal(t, finalCount, successCount, "Database count should match successful API responses")
	assert.LessOrEqual(t, successCount, 2, "Successful registrations should not exceed capacity")
}

// Helper methods for database verification

func (s *TransactionRollbackTestSuite) countEventRegistrations(eventID uuid.UUID) int {
	var count int
	err := s.dbPool.QueryRow(context.Background(),
		"SELECT COUNT(*) FROM event_registrations WHERE event_id = $1", eventID).Scan(&count)
	if err != nil {
		s.T().Logf("Error counting registrations: %v", err)
		return 0
	}
	return count
}

func (s *TransactionRollbackTestSuite) countVolunteerApplications(userID uuid.UUID) int {
	var count int
	err := s.dbPool.QueryRow(context.Background(),
		"SELECT COUNT(*) FROM volunteer_applications WHERE user_id = $1", userID).Scan(&count)
	if err != nil {
		s.T().Logf("Error counting volunteer applications: %v", err)
		return 0
	}
	return count
}

func (s *TransactionRollbackTestSuite) countEvents() int {
	var count int
	err := s.dbPool.QueryRow(context.Background(), "SELECT COUNT(*) FROM events").Scan(&count)
	if err != nil {
		s.T().Logf("Error counting events: %v", err)
		return 0
	}
	return count
}

func (s *TransactionRollbackTestSuite) countUsers() int {
	var count int
	err := s.dbPool.QueryRow(context.Background(), "SELECT COUNT(*) FROM users").Scan(&count)
	if err != nil {
		s.T().Logf("Error counting users: %v", err)
		return 0
	}
	return count
}

func (s *TransactionRollbackTestSuite) countOTPAttempts(phoneNumber string) int {
	var count int
	err := s.dbPool.QueryRow(context.Background(),
		"SELECT COUNT(*) FROM otp_attempts WHERE phone_number = $1", phoneNumber).Scan(&count)
	if err != nil {
		s.T().Logf("Error counting OTP attempts: %v", err)
		return 0
	}
	return count
}

func (s *TransactionRollbackTestSuite) userHasRegistration(userID, eventID uuid.UUID) bool {
	var exists bool
	err := s.dbPool.QueryRow(context.Background(),
		"SELECT EXISTS(SELECT 1 FROM event_registrations WHERE user_id = $1 AND event_id = $2)",
		userID, eventID).Scan(&exists)
	if err != nil {
		s.T().Logf("Error checking registration: %v", err)
		return false
	}
	return exists
}

func (s *TransactionRollbackTestSuite) volunteerApplicationExists(appID uuid.UUID) bool {
	var exists bool
	err := s.dbPool.QueryRow(context.Background(),
		"SELECT EXISTS(SELECT 1 FROM volunteer_applications WHERE id = $1)", appID).Scan(&exists)
	if err != nil {
		s.T().Logf("Error checking volunteer application: %v", err)
		return false
	}
	return exists
}

func (s *TransactionRollbackTestSuite) userExistsByPhone(phoneNumber string) bool {
	var exists bool
	err := s.dbPool.QueryRow(context.Background(),
		"SELECT EXISTS(SELECT 1 FROM users WHERE phone_number = $1)", phoneNumber).Scan(&exists)
	if err != nil {
		s.T().Logf("Error checking user existence: %v", err)
		return false
	}
	return exists
}

func (s *TransactionRollbackTestSuite) getRegistrationPaymentStatus(userID, eventID uuid.UUID) string {
	var status string
	err := s.dbPool.QueryRow(context.Background(),
		"SELECT payment_status FROM event_registrations WHERE user_id = $1 AND event_id = $2",
		userID, eventID).Scan(&status)
	if err != nil {
		s.T().Logf("Error getting payment status: %v", err)
		return ""
	}
	return status
}

// Helper methods for test setup

func (s *TransactionRollbackTestSuite) createTestUser(phoneNumber, email string) string {
	regInitiateReq := map[string]interface{}{
		"phone_number":   phoneNumber,
		"email":          email,
		"first_name":     "Test",
		"last_name":      "User",
		"code_challenge": "test-challenge-" + uuid.New().String(),
		"state":          "test-state-" + uuid.New().String(),
		"preferred_lang": "en",
	}
	rec, err := s.apiSuite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/register/phone/initiate", regInitiateReq)
	require.NoError(s.T(), err)

	regVerifyReq := map[string]interface{}{
		"phone_number":  phoneNumber,
		"otp_code":      "123456",
		"code_verifier": "test-verifier-" + uuid.New().String(),
		"state":         regInitiateReq["state"],
	}
	rec, err = s.apiSuite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/register/phone/verify", regVerifyReq)
	require.NoError(s.T(), err)

	var authResponse map[string]interface{}
	s.apiSuite.AssertJSONResponse(s.T(), rec, http.StatusOK, &authResponse)

	userToken, ok := authResponse["access_token"].(string)
	require.True(s.T(), ok)

	return userToken
}

func (s *TransactionRollbackTestSuite) getUserIDFromToken(token string) uuid.UUID {
	rec, err := s.apiSuite.MakeRequest("GET", "/api/v1/users/profile", nil, token)
	require.NoError(s.T(), err)

	var profileResponse payloads.UserProfileResponse
	s.apiSuite.AssertJSONResponse(s.T(), rec, http.StatusOK, &profileResponse)

	return profileResponse.ID
}

func (s *TransactionRollbackTestSuite) createTestEvent(title string, maxParticipants int) uuid.UUID {
	eventCreateReq := map[string]interface{}{
		"title":                 title,
		"description":           "Event for transaction testing",
		"event_type":            "community",
		"start_date":            time.Now().Add(48 * time.Hour).Format(time.RFC3339),
		"end_date":              time.Now().Add(52 * time.Hour).Format(time.RFC3339),
		"registration_deadline": time.Now().Add(24 * time.Hour).Format(time.RFC3339),
		"max_participants":      maxParticipants,
		"location":              "Test Location",
		"is_published":          true,
	}
	rec, err := s.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/events", eventCreateReq)
	require.NoError(s.T(), err)

	var eventResponse payloads.EventResponse
	s.apiSuite.AssertJSONResponse(s.T(), rec, http.StatusCreated, &eventResponse)

	return eventResponse.ID
}

func (s *TransactionRollbackTestSuite) createPaidTestEvent(title string, maxParticipants int, price float64) uuid.UUID {
	eventCreateReq := map[string]interface{}{
		"title":                 title,
		"description":           "Paid event for transaction testing",
		"event_type":            "community",
		"start_date":            time.Now().Add(48 * time.Hour).Format(time.RFC3339),
		"end_date":              time.Now().Add(52 * time.Hour).Format(time.RFC3339),
		"registration_deadline": time.Now().Add(24 * time.Hour).Format(time.RFC3339),
		"max_participants":      maxParticipants,
		"location":              "Test Location",
		"registration_fee":      price,
		"requires_payment":      true,
		"is_published":          true,
	}
	rec, err := s.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/events", eventCreateReq)
	require.NoError(s.T(), err)

	var eventResponse payloads.EventResponse
	s.apiSuite.AssertJSONResponse(s.T(), rec, http.StatusCreated, &eventResponse)

	return eventResponse.ID
}

// TestTransactionRollbackSuite runs the transaction rollback test suite
func TestTransactionRollbackSuite(t *testing.T) {
	suite.Run(t, new(TransactionRollbackTestSuite))
}
