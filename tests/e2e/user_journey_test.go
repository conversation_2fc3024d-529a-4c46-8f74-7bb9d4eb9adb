package e2e

import (
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/utils"
	"Membership-SAAS-System-Backend/tests/integration"
)

// UserJourneyTestSuite tests complete end-to-end user journeys
type UserJourneyTestSuite struct {
	suite.Suite
	apiSuite *integration.APITestSuite
}

// SetupSuite initializes the test suite
func (s *UserJourneyTestSuite) SetupSuite() {
	s.apiSuite = integration.SetupAPITest(s.T())
}

// TearDownSuite cleans up the test suite
func (s *UserJourneyTestSuite) TearDownSuite() {
	s.apiSuite.TeardownAPITest(s.T())
}

// TestCompleteUserRegistrationToEventParticipation tests the full user lifecycle
func (s *UserJourneyTestSuite) TestCompleteUserRegistrationToEventParticipation() {
	t := s.T()

	// Phase 1: User Registration
	phoneNumber := "+1234567890"

	// Step 1: Check if phone number exists
	checkReq := map[string]interface{}{
		"phone": phoneNumber,
	}
	rec, err := s.apiSuite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/phone/check", checkReq)
	require.NoError(t, err)
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, nil)

	// Step 2: Initiate registration
	codeVerifier := "test-verifier-" + uuid.New().String()
	codeChallenge := utils.GenerateS256Challenge(codeVerifier)
	state := "test-state-" + uuid.New().String()

	regInitiateReq := map[string]interface{}{
		"phone":                 phoneNumber,
		"client_id":             "test-client",
		"code_challenge":        codeChallenge,
		"code_challenge_method": "S256",
		"state":                 state,
		"phone_otp_channel":     "sms",
	}
	rec, err = s.apiSuite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/register/phone/initiate", regInitiateReq)
	require.NoError(t, err)
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, nil)

	// Step 3: Verify registration with OTP
	regVerifyReq := map[string]interface{}{
		"state":                  state,
		"otp":                    "123456", // Test OTP
		"code_verifier":          codeVerifier,
		"display_name":           "Test User",
		"interface_language":     "en",
		"communication_language": "en",
	}
	rec, err = s.apiSuite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/register/phone/verify", regVerifyReq)
	require.NoError(t, err)

	var authResponse map[string]interface{}
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusCreated, &authResponse)

	// Extract user token from response
	userToken, ok := authResponse["access_token"].(string)
	require.True(t, ok, "Should receive access token")
	require.NotEmpty(t, userToken, "Access token should not be empty")

	// Phase 2: Profile Management
	// Step 4: Get user profile
	rec, err = s.apiSuite.MakeRequest("GET", "/api/v1/users/me", nil, userToken)
	require.NoError(t, err)

	var profileResponse payloads.UserProfileResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &profileResponse)
	assert.Equal(t, "Test User", profileResponse.DisplayName)

	userID := profileResponse.ID

	// Step 5: Update profile
	updateReq := map[string]interface{}{
		"display_name": "Updated Test User",
		"bio":          "Test user biography",
	}
	rec, err = s.apiSuite.MakeRequest("PATCH", "/api/v1/users/me", updateReq, userToken)
	require.NoError(t, err)
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, nil)

	// Phase 3: Event Discovery and Registration
	// Step 6: Create an event (using admin token)
	eventCreateReq := map[string]interface{}{
		"title":                 "Test Community Event",
		"jsonContent":           json.RawMessage(`{"blocks":[{"type":"paragraph","data":{"text":"A test event for user journey testing"}}]}`),
		"location_type":         "physical",
		"location_full_address": "Test Community Center",
		"start_time":            time.Now().Add(24 * time.Hour).Format(time.RFC3339),
		"end_time":              time.Now().Add(26 * time.Hour).Format(time.RFC3339),
		"participant_limit":     100,
		"status":                "published",
	}
	orgID := s.apiSuite.GetOrgID()
	eventURL := fmt.Sprintf("/api/v1/organizations/%s/events", orgID)
	rec, err = s.apiSuite.MakeAuthenticatedRequest("POST", eventURL, eventCreateReq)
	require.NoError(t, err)

	var eventResponse payloads.EventResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusCreated, &eventResponse)
	eventID := eventResponse.ID

	// Step 7: List available events (user perspective)
	rec, err = s.apiSuite.MakeRequest("GET", "/api/v1/events?status=published", nil, userToken)
	require.NoError(t, err)

	var eventsListResponse map[string]interface{}
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &eventsListResponse)

	events, ok := eventsListResponse["events"].([]interface{})
	require.True(t, ok, "Should receive events list")
	assert.Greater(t, len(events), 0, "Should have at least one event")

	// Step 8: Get event details
	rec, err = s.apiSuite.MakeRequest("GET", fmt.Sprintf("/api/v1/events/%s", eventID), nil, userToken)
	require.NoError(t, err)
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &eventResponse)
	assert.Equal(t, "Test Community Event", eventResponse.Title)

	// Step 9: Register for event
	regEventReq := map[string]interface{}{
		"event_id":             eventID.String(),
		"dietary_restrictions": "None",
		"emergency_contact":    "John Doe - +1234567891",
		"additional_notes":     "Looking forward to participating",
	}
	rec, err = s.apiSuite.MakeRequest("POST", "/api/v1/me/event-registrations", regEventReq, userToken)
	require.NoError(t, err)

	var registrationResponse payloads.EventRegistrationResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusCreated, &registrationResponse)
	assert.Equal(t, userID, registrationResponse.UserID)
	assert.Equal(t, eventID, registrationResponse.EventID)

	// Step 10: Check registration status
	registrationID := registrationResponse.ID
	rec, err = s.apiSuite.MakeRequest("GET", fmt.Sprintf("/api/v1/me/event-registrations/%s", registrationID), nil, userToken)
	require.NoError(t, err)
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &registrationResponse)
	assert.Equal(t, "registered", string(registrationResponse.Status))

	// Phase 4: Event Participation
	// Step 11: Get user's registered events
	rec, err = s.apiSuite.MakeRequest("GET", "/api/v1/me/event-registrations", nil, userToken)
	require.NoError(t, err)

	var userRegistrations []payloads.EventRegistrationResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &userRegistrations)
	assert.Equal(t, 1, len(userRegistrations), "Should have one event registration")

	// Phase 4.5: Event Check-in
	// Step 12: Generate scanner JWT token for event (admin operation)
	scannerJWT, err := s.generateScannerToken(eventID)
	require.NoError(t, err)

	// Step 13: Perform check-in using scanner token
	checkInReq := map[string]interface{}{
		"registration_id": registrationID.String(),
	}
	rec, err = s.apiSuite.MakeRequest("POST", "/api/v1/scanner/checkin", checkInReq, scannerJWT)
	require.NoError(t, err)

	// Check-in should succeed
	assert.Equal(t, http.StatusOK, rec.Code)
	var checkInResponse map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &checkInResponse)
	require.NoError(t, err)
	assert.Equal(t, "success", checkInResponse["status"])

	// Verify check-in status
	rec, err = s.apiSuite.MakeRequest("GET", fmt.Sprintf("/api/v1/me/event-registrations/%s", registrationID), nil, userToken)
	require.NoError(t, err)
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &registrationResponse)
	assert.Equal(t, "checked_in", string(registrationResponse.Status))

	// Phase 5: Post-Event Activities
	// Step 14: Get event statistics (admin view)
	rec, err = s.apiSuite.MakeAuthenticatedRequest("GET", fmt.Sprintf("/api/v1/events/%s/statistics/registrations", eventID), nil)
	require.NoError(t, err)

	var statsResponse map[string]interface{}
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &statsResponse)

	// Verify statistics with check-in
	// Check for different possible field names in response
	if val, ok := statsResponse["total_registrations"]; ok {
		assert.Equal(t, float64(1), val, "Should have 1 registration")
	} else if val, ok := statsResponse["registered_count"]; ok {
		assert.Equal(t, float64(1), val, "Should have 1 registration")
	}

	// Verify check-in statistics
	if val, ok := statsResponse["checked_in_count"]; ok {
		assert.Equal(t, float64(1), val, "Should have 1 check-in")
	} else if val, ok := statsResponse["total_checked_in"]; ok {
		assert.Equal(t, float64(1), val, "Should have 1 check-in")
	}

	// Phase 6: Event Cancellation
	// Step 15: Test event registration cancellation
	// First, create a new event with future cancellation deadline
	futureEventReq := map[string]interface{}{
		"title":                 "Future Event for Cancellation Test",
		"jsonContent":           json.RawMessage(`{"blocks":[{"type":"paragraph","data":{"text":"Event for testing cancellation"}}]}`),
		"location_type":         "physical",
		"location_full_address": "Test Location",
		"start_time":            time.Now().Add(7 * 24 * time.Hour).Format(time.RFC3339), // 7 days from now
		"end_time":              time.Now().Add(7 * 24 * time.Hour).Add(2 * time.Hour).Format(time.RFC3339),
		"participant_limit":     50,
		"status":                "published",
		"cancellation_deadline": time.Now().Add(5 * 24 * time.Hour).Format(time.RFC3339), // 5 days from now
	}

	rec, err = s.apiSuite.MakeAuthenticatedRequest("POST", fmt.Sprintf("/api/v1/organizations/%s/events", orgID), futureEventReq)
	require.NoError(t, err)

	var futureEventResponse payloads.EventResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusCreated, &futureEventResponse)
	futureEventID := futureEventResponse.ID

	// Register for the future event
	regFutureReq := map[string]interface{}{
		"event_id":             futureEventID.String(),
		"dietary_restrictions": "None",
		"emergency_contact":    "Test Contact",
	}
	rec, err = s.apiSuite.MakeRequest("POST", "/api/v1/me/event-registrations", regFutureReq, userToken)
	require.NoError(t, err)

	var futureRegResponse payloads.EventRegistrationResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusCreated, &futureRegResponse)
	futureRegID := futureRegResponse.ID

	// Cancel the registration
	cancelReq := map[string]interface{}{
		"reason": "Unable to attend due to schedule conflict",
	}
	rec, err = s.apiSuite.MakeRequest("POST", fmt.Sprintf("/api/v1/me/event-registrations/%s/cancel", futureRegID), cancelReq, userToken)
	require.NoError(t, err)

	// Cancellation should succeed
	assert.Equal(t, http.StatusOK, rec.Code)

	// Verify cancellation status
	rec, err = s.apiSuite.MakeRequest("GET", fmt.Sprintf("/api/v1/me/event-registrations/%s", futureRegID), nil, userToken)
	require.NoError(t, err)
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &futureRegResponse)
	assert.Equal(t, "cancelled", string(futureRegResponse.Status))
}

// generateScannerToken generates a scanner JWT token for event check-in
func (s *UserJourneyTestSuite) generateScannerToken(eventID uuid.UUID) (string, error) {
	// Scanner tokens are typically generated by the backend
	// For testing, we'll use the admin token to request a scanner token

	scannerReq := map[string]interface{}{
		"event_id":    eventID.String(),
		"scanner_id":  uuid.New().String(),
		"valid_until": time.Now().Add(24 * time.Hour).Format(time.RFC3339),
	}

	rec, err := s.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/events/scanner-token", scannerReq)
	if err != nil {
		return "", err
	}

	if rec.Code != http.StatusOK && rec.Code != http.StatusCreated {
		// If scanner token endpoint doesn't exist, generate a mock scanner token
		// In real implementation, this would be a properly signed JWT with scanner permissions
		return "mock-scanner-token-" + eventID.String(), nil
	}

	var tokenResponse map[string]interface{}
	if err := json.Unmarshal(rec.Body.Bytes(), &tokenResponse); err != nil {
		return "", err
	}

	token, ok := tokenResponse["token"].(string)
	if !ok {
		return "", fmt.Errorf("invalid scanner token response")
	}

	return token, nil
}

// TestVolunteerWorkflow tests the complete volunteer application and management workflow
func (s *UserJourneyTestSuite) TestVolunteerWorkflow() {
	t := s.T()

	// Use existing user token for volunteer workflow
	userToken := s.apiSuite.GetUserToken()

	// Step 1: Apply as organization volunteer
	volunteerAppReq := map[string]interface{}{
		"user_id":      uuid.New().String(),
		"motivation":   "I want to help the community",
		"availability": "Weekends and evenings",
		"skills":       []string{"Event Management", "Communication"},
		"experience":   "2 years of volunteer experience",
		"references":   "Jane Smith - <EMAIL>",
	}
	rec, err := s.apiSuite.MakeRequest("POST", "/api/v1/volunteers/apply", volunteerAppReq, userToken)
	require.NoError(t, err)

	var volunteerResponse payloads.VolunteerApplicationResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusCreated, &volunteerResponse)
	assert.Equal(t, "pending", volunteerResponse.Status)

	applicationID := volunteerResponse.ID

	// Step 2: Admin reviews and approves application
	approveReq := map[string]interface{}{
		"status":   "approved",
		"feedback": "Great motivation and experience. Welcome to our team!",
	}
	rec, err = s.apiSuite.MakeAuthenticatedRequest("PUT", fmt.Sprintf("/api/v1/volunteers/applications/%s", applicationID), approveReq)
	require.NoError(t, err)
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, nil)

	// Step 3: Create an event requiring volunteers
	eventCreateReq := map[string]interface{}{
		"title":                  "Volunteer Event",
		"description":            "Event requiring volunteer assistance",
		"event_type":             "community",
		"start_date":             time.Now().Add(48 * time.Hour).Format(time.RFC3339),
		"end_date":               time.Now().Add(50 * time.Hour).Format(time.RFC3339),
		"registration_deadline":  time.Now().Add(24 * time.Hour).Format(time.RFC3339),
		"max_participants":       50,
		"location":               "Community Center",
		"volunteers_needed":      5,
		"volunteer_requirements": []string{"Event Setup", "Registration Desk"},
		"is_published":           true,
	}
	rec, err = s.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/events", eventCreateReq)
	require.NoError(t, err)

	var eventResponse payloads.EventResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusCreated, &eventResponse)
	eventID := eventResponse.ID

	// Step 4: Apply as event volunteer
	eventVolunteerReq := map[string]interface{}{
		"role":         "Registration Desk",
		"availability": "Full event duration",
		"notes":        "Happy to help with registration",
	}
	rec, err = s.apiSuite.MakeRequest("POST", fmt.Sprintf("/api/v1/events/%s/volunteer", eventID), eventVolunteerReq, userToken)
	require.NoError(t, err)

	var eventVolunteerResponse payloads.EventVolunteerApplicationResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusCreated, &eventVolunteerResponse)
	assert.Equal(t, "pending", eventVolunteerResponse.Status)

	// Step 5: Admin approves event volunteer application
	approveEventVolunteerReq := map[string]interface{}{
		"status":   "approved",
		"feedback": "Perfect fit for registration desk role",
	}
	rec, err = s.apiSuite.MakeAuthenticatedRequest("PUT", fmt.Sprintf("/api/v1/events/%s/volunteers/%s", eventID, eventVolunteerResponse.ID), approveEventVolunteerReq)
	require.NoError(t, err)
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, nil)

	// Step 6: Check volunteer dashboard
	rec, err = s.apiSuite.MakeRequest("GET", "/api/v1/volunteers/dashboard", nil, userToken)
	require.NoError(t, err)

	var dashboardResponse map[string]interface{}
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &dashboardResponse)

	upcomingEvents, ok := dashboardResponse["upcoming_events"].([]interface{})
	require.True(t, ok, "Should receive upcoming events")
	assert.Equal(t, 1, len(upcomingEvents), "Should have one upcoming volunteer event")
}

// TestMultiOrganizationWorkflow tests user interaction across multiple organizations
func (s *UserJourneyTestSuite) TestMultiOrganizationWorkflow() {
	t := s.T()

	userToken := s.apiSuite.GetUserToken()

	// Step 1: Create a second organization
	org2CreateReq := map[string]interface{}{
		"name":        "Second Test Organization",
		"description": "Another organization for testing",
		"website":     "https://org2.example.com",
		"email":       "<EMAIL>",
		"phone":       "+1234567892",
		"address":     "456 Second St",
		"city":        "Test City",
		"country":     "Test Country",
	}
	rec, err := s.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/organizations", org2CreateReq)
	require.NoError(t, err)

	var org2Response payloads.OrganizationResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusCreated, &org2Response)
	org2ID := org2Response.ID

	// Step 2: Switch user to second organization
	switchOrgReq := map[string]interface{}{
		"organization_id": org2ID,
	}
	rec, err = s.apiSuite.MakeRequest("POST", "/api/v1/users/switch-organization", switchOrgReq, userToken)
	require.NoError(t, err)

	var switchResponse map[string]interface{}
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &switchResponse)

	newToken, ok := switchResponse["access_token"].(string)
	require.True(t, ok, "Should receive new access token")

	// Step 3: Create event in second organization
	eventCreateReq := map[string]interface{}{
		"title":                 "Org2 Event",
		"description":           "Event in second organization",
		"event_type":            "community",
		"start_date":            time.Now().Add(72 * time.Hour).Format(time.RFC3339),
		"end_date":              time.Now().Add(74 * time.Hour).Format(time.RFC3339),
		"registration_deadline": time.Now().Add(48 * time.Hour).Format(time.RFC3339),
		"max_participants":      30,
		"location":              "Org2 Center",
		"is_published":          true,
	}
	rec, err = s.apiSuite.MakeRequest("POST", "/api/v1/events", eventCreateReq, newToken)
	require.NoError(t, err)

	var eventResponse payloads.EventResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusCreated, &eventResponse)

	// Step 4: Verify organization isolation
	// User should only see events from current organization
	rec, err = s.apiSuite.MakeRequest("GET", "/api/v1/events", nil, newToken)
	require.NoError(t, err)

	var eventsResponse map[string]interface{}
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, &eventsResponse)

	events, ok := eventsResponse["events"].([]interface{})
	require.True(t, ok, "Should receive events list")

	// All events should belong to org2
	for _, event := range events {
		eventMap, ok := event.(map[string]interface{})
		require.True(t, ok, "Event should be a map")
		assert.Equal(t, org2ID.String(), eventMap["organization_id"], "Event should belong to org2")
	}
}

// TestErrorRecoveryWorkflow tests system behavior during error conditions
func (s *UserJourneyTestSuite) TestErrorRecoveryWorkflow() {
	t := s.T()

	// Test 1: Invalid phone number registration
	invalidPhoneReq := map[string]interface{}{
		"phone_number": "invalid-phone",
	}
	rec, err := s.apiSuite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/phone/check", invalidPhoneReq)
	require.NoError(t, err)
	s.apiSuite.AssertErrorResponse(t, rec, http.StatusBadRequest, "invalid phone number")

	// Test 2: Duplicate registration attempt
	phoneNumber := "+1234567893"

	// First registration
	regReq := map[string]interface{}{
		"phone_number":   phoneNumber,
		"email":          "<EMAIL>",
		"first_name":     "Duplicate",
		"last_name":      "User",
		"code_challenge": "challenge1",
		"state":          "state1",
	}
	rec, err = s.apiSuite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/register/phone/initiate", regReq)
	require.NoError(t, err)
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, nil)

	// Verify first registration
	verifyReq := map[string]interface{}{
		"phone_number":  phoneNumber,
		"otp_code":      "123456",
		"code_verifier": "verifier1",
		"state":         "state1",
	}
	rec, err = s.apiSuite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/register/phone/verify", verifyReq)
	require.NoError(t, err)
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusOK, nil)

	// Attempt duplicate registration
	rec, err = s.apiSuite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/register/phone/initiate", regReq)
	require.NoError(t, err)
	s.apiSuite.AssertErrorResponse(t, rec, http.StatusConflict, "already exists")

	// Test 3: Event registration past deadline
	pastEventReq := map[string]interface{}{
		"title":                 "Past Deadline Event",
		"description":           "Event with past registration deadline",
		"event_type":            "community",
		"start_date":            time.Now().Add(72 * time.Hour).Format(time.RFC3339),
		"end_date":              time.Now().Add(74 * time.Hour).Format(time.RFC3339),
		"registration_deadline": time.Now().Add(-1 * time.Hour).Format(time.RFC3339), // Past deadline
		"max_participants":      10,
		"location":              "Test Location",
		"is_published":          true,
	}
	rec, err = s.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/events", pastEventReq)
	require.NoError(t, err)

	var eventResponse payloads.EventResponse
	s.apiSuite.AssertJSONResponse(t, rec, http.StatusCreated, &eventResponse)

	// Try to register for event past deadline
	regEventReq := map[string]interface{}{
		"user_id": uuid.New().String(),
	}
	rec, err = s.apiSuite.MakeRequest("POST", fmt.Sprintf("/api/v1/events/%s/register", eventResponse.ID), regEventReq, s.apiSuite.GetUserToken())
	require.NoError(t, err)
	s.apiSuite.AssertErrorResponse(t, rec, http.StatusBadRequest, "registration deadline")
}

// TestUserJourneySuite runs the user journey test suite
func TestUserJourneySuite(t *testing.T) {
	suite.Run(t, new(UserJourneyTestSuite))
}
