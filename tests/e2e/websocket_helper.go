package e2e

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"sync"
	"testing"
	"time"

	"github.com/gorilla/websocket"
	"github.com/stretchr/testify/require"
)

// WebSocketTestHelper provides utilities for WebSocket testing
type WebSocketTestHelper struct {
	t           *testing.T
	connections []*websocket.Conn
	mu          sync.Mutex
}

// NewWebSocketTestHelper creates a new WebSocket test helper
func NewWebSocketTestHelper(t *testing.T) *WebSocketTestHelper {
	return &WebSocketTestHelper{
		t:           t,
		connections: make([]*websocket.Conn, 0),
	}
}

// CreateAuthenticatedConnection creates an authenticated WebSocket connection
func (h *WebSocketTestHelper) CreateAuthenticatedConnection(t *testing.T, baseURL, token string) (*websocket.Conn, error) {
	// Parse base URL and convert to WebSocket scheme
	u, err := url.Parse(baseURL)
	if err != nil {
		return nil, fmt.Errorf("invalid base URL: %w", err)
	}

	// Convert http/https to ws/wss
	switch u.Scheme {
	case "http":
		u.Scheme = "ws"
	case "https":
		u.Scheme = "wss"
	default:
		// Assume it's already a WebSocket URL
	}

	// Add WebSocket path if not present
	if u.Path == "" || u.Path == "/" {
		u.Path = "/api/v1/ws"
	}

	// Add authentication header
	headers := http.Header{}
	if token != "" {
		headers.Set("Authorization", "Bearer "+token)
	}

	// Configure dialer with timeout
	dialer := websocket.DefaultDialer
	dialer.HandshakeTimeout = 10 * time.Second

	// Connect to WebSocket
	conn, resp, err := dialer.Dial(u.String(), headers)
	if err != nil {
		if resp != nil {
			return nil, fmt.Errorf("WebSocket connection failed with status %d: %w", resp.StatusCode, err)
		}
		return nil, fmt.Errorf("WebSocket connection failed: %w", err)
	}

	// Track connection for cleanup
	h.mu.Lock()
	h.connections = append(h.connections, conn)
	h.mu.Unlock()

	return conn, nil
}

// SendMessage sends a message through the WebSocket connection
func (h *WebSocketTestHelper) SendMessage(conn *websocket.Conn, msgType string, payload interface{}) error {
	message := map[string]interface{}{
		"type":    msgType,
		"payload": payload,
	}

	return conn.WriteJSON(message)
}

// ReadMessage reads a message from the WebSocket connection with timeout
func (h *WebSocketTestHelper) ReadMessage(conn *websocket.Conn, timeout time.Duration) (map[string]interface{}, error) {
	// Set read deadline
	conn.SetReadDeadline(time.Now().Add(timeout))

	var message map[string]interface{}
	err := conn.ReadJSON(&message)
	if err != nil {
		return nil, err
	}

	return message, nil
}

// WaitForMessage waits for a specific message type with timeout
func (h *WebSocketTestHelper) WaitForMessage(conn *websocket.Conn, msgType string, timeout time.Duration) (map[string]interface{}, error) {
	deadline := time.Now().Add(timeout)

	for time.Now().Before(deadline) {
		// Try to read a message
		msg, err := h.ReadMessage(conn, 1*time.Second)
		if err != nil {
			// Timeout or error, continue waiting
			continue
		}

		// Check if this is the message we're waiting for
		if messageType, ok := msg["type"].(string); ok && messageType == msgType {
			return msg, nil
		}
	}

	return nil, fmt.Errorf("timeout waiting for message type: %s", msgType)
}

// SubscribeToChannels subscribes to WebSocket channels
func (h *WebSocketTestHelper) SubscribeToChannels(conn *websocket.Conn, channels []string) error {
	return h.SendMessage(conn, "subscribe", map[string]interface{}{
		"channels": channels,
	})
}

// ListenForNotifications listens for notifications in the background
func (h *WebSocketTestHelper) ListenForNotifications(conn *websocket.Conn, ctx context.Context) <-chan map[string]interface{} {
	notifications := make(chan map[string]interface{}, 10)

	go func() {
		defer close(notifications)

		for {
			select {
			case <-ctx.Done():
				return
			default:
				// Try to read a message
				msg, err := h.ReadMessage(conn, 1*time.Second)
				if err != nil {
					// Ignore timeout errors, continue listening
					continue
				}

				// Check if it's a notification
				if msgType, ok := msg["type"].(string); ok {
					switch msgType {
					case "notification", "announcement", "reminder", "event_update":
						select {
						case notifications <- msg:
						case <-ctx.Done():
							return
						}
					}
				}
			}
		}
	}()

	return notifications
}

// Cleanup closes all WebSocket connections
func (h *WebSocketTestHelper) Cleanup() {
	h.mu.Lock()
	defer h.mu.Unlock()

	for _, conn := range h.connections {
		if conn != nil {
			conn.Close()
		}
	}

	h.connections = h.connections[:0]
}

// AssertPingPong tests WebSocket ping/pong functionality
func (h *WebSocketTestHelper) AssertPingPong(t *testing.T, conn *websocket.Conn) {
	// Send ping
	err := h.SendMessage(conn, "ping", map[string]interface{}{
		"timestamp": time.Now().Unix(),
	})
	require.NoError(t, err)

	// Wait for pong
	msg, err := h.WaitForMessage(conn, "pong", 5*time.Second)
	require.NoError(t, err)
	require.NotNil(t, msg)
}

// CreateMockWebSocketServer creates a mock WebSocket server for testing when real WebSocket is not available
func (h *WebSocketTestHelper) CreateMockWebSocketServer() *http.Server {
	mux := http.NewServeMux()

	upgrader := websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true // Allow all origins in tests
		},
	}

	mux.HandleFunc("/api/v1/ws", func(w http.ResponseWriter, r *http.Request) {
		// Check authorization
		authHeader := r.Header.Get("Authorization")
		if authHeader == "" {
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}

		// Upgrade to WebSocket
		conn, err := upgrader.Upgrade(w, r, nil)
		if err != nil {
			return
		}
		defer conn.Close()

		// Simple echo server for testing
		for {
			var msg map[string]interface{}
			err := conn.ReadJSON(&msg)
			if err != nil {
				break
			}

			// Handle different message types
			msgType, _ := msg["type"].(string)
			switch msgType {
			case "ping":
				// Respond with pong
				conn.WriteJSON(map[string]interface{}{
					"type":    "pong",
					"payload": msg["payload"],
				})
			case "subscribe":
				// Acknowledge subscription
				conn.WriteJSON(map[string]interface{}{
					"type": "subscription_confirmed",
					"payload": map[string]interface{}{
						"channels": msg["payload"],
					},
				})
			default:
				// Echo back
				conn.WriteJSON(msg)
			}
		}
	})

	server := &http.Server{
		Addr:    ":0", // Random port
		Handler: mux,
	}

	return server
}
