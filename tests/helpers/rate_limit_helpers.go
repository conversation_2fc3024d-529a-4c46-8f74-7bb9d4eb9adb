package helpers

import (
	"fmt"
	"os"
	"sync"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/internal/config"
)

// RateLimitTestHelper provides utilities for testing rate limiting
type RateLimitTestHelper struct {
	mu          sync.Mutex
	originalEnv map[string]string
}

// NewRateLimitTestHelper creates a new rate limit test helper
func NewRateLimitTestHelper() *RateLimitTestHelper {
	return &RateLimitTestHelper{
		originalEnv: make(map[string]string),
	}
}

// EnableRateLimiting temporarily enables rate limiting for a test
func (h *RateLimitTestHelper) EnableRateLimiting(t *testing.T, ratePerMinute int) func() {
	h.mu.Lock()
	defer h.mu.Unlock()

	// Save current environment
	h.originalEnv["RATE_LIMIT_ENABLED"] = os.Getenv("RATE_LIMIT_ENABLED")
	h.originalEnv["RATE_LIMIT_SKIP_IN_TESTS"] = os.Getenv("RATE_LIMIT_SKIP_IN_TESTS")
	h.originalEnv["GO_ENV"] = os.Getenv("GO_ENV")
	h.originalEnv["RATE_LIMIT_PER_MINUTE"] = os.Getenv("RATE_LIMIT_PER_MINUTE")

	// Enable rate limiting
	os.Setenv("RATE_LIMIT_ENABLED", "true")
	os.Setenv("RATE_LIMIT_SKIP_IN_TESTS", "false")
	os.Setenv("GO_ENV", "production")
	os.Setenv("RATE_LIMIT_PER_MINUTE", fmt.Sprintf("%d", ratePerMinute))

	// Return cleanup function
	return func() {
		h.mu.Lock()
		defer h.mu.Unlock()

		// Restore original environment
		for key, value := range h.originalEnv {
			if value != "" {
				os.Setenv(key, value)
			} else {
				os.Unsetenv(key)
			}
		}
	}
}

// DisableRateLimiting temporarily disables rate limiting for a test
func (h *RateLimitTestHelper) DisableRateLimiting(t *testing.T) func() {
	h.mu.Lock()
	defer h.mu.Unlock()

	// Save current environment
	h.originalEnv["RATE_LIMIT_ENABLED"] = os.Getenv("RATE_LIMIT_ENABLED")
	h.originalEnv["RATE_LIMIT_SKIP_IN_TESTS"] = os.Getenv("RATE_LIMIT_SKIP_IN_TESTS")
	h.originalEnv["GO_ENV"] = os.Getenv("GO_ENV")

	// Disable rate limiting
	os.Setenv("RATE_LIMIT_ENABLED", "false")
	os.Setenv("RATE_LIMIT_SKIP_IN_TESTS", "true")
	os.Setenv("GO_ENV", "test")

	// Return cleanup function
	return func() {
		h.mu.Lock()
		defer h.mu.Unlock()

		// Restore original environment
		for key, value := range h.originalEnv {
			if value != "" {
				os.Setenv(key, value)
			} else {
				os.Unsetenv(key)
			}
		}
	}
}

// SetAuthEndpointLimits configures rate limits for auth endpoints
func (h *RateLimitTestHelper) SetAuthEndpointLimits(t *testing.T, maxRequests int, window time.Duration) func() {
	h.mu.Lock()
	defer h.mu.Unlock()

	// Save current environment
	h.originalEnv["RATE_LIMIT_AUTH_MAX"] = os.Getenv("RATE_LIMIT_AUTH_MAX")
	h.originalEnv["RATE_LIMIT_AUTH_WINDOW"] = os.Getenv("RATE_LIMIT_AUTH_WINDOW")

	// Set auth endpoint limits
	os.Setenv("RATE_LIMIT_AUTH_MAX", fmt.Sprintf("%d", maxRequests))
	os.Setenv("RATE_LIMIT_AUTH_WINDOW", window.String())

	// Return cleanup function
	return func() {
		h.mu.Lock()
		defer h.mu.Unlock()

		// Restore original environment
		if val, ok := h.originalEnv["RATE_LIMIT_AUTH_MAX"]; ok {
			if val != "" {
				os.Setenv("RATE_LIMIT_AUTH_MAX", val)
			} else {
				os.Unsetenv("RATE_LIMIT_AUTH_MAX")
			}
		}

		if val, ok := h.originalEnv["RATE_LIMIT_AUTH_WINDOW"]; ok {
			if val != "" {
				os.Setenv("RATE_LIMIT_AUTH_WINDOW", val)
			} else {
				os.Unsetenv("RATE_LIMIT_AUTH_WINDOW")
			}
		}
	}
}

// SetExemptIPs configures IPs that are exempt from rate limiting
func (h *RateLimitTestHelper) SetExemptIPs(t *testing.T, ips []string) func() {
	h.mu.Lock()
	defer h.mu.Unlock()

	// Save current environment
	h.originalEnv["RATE_LIMIT_EXEMPT_IPS"] = os.Getenv("RATE_LIMIT_EXEMPT_IPS")

	// Set exempt IPs
	if len(ips) > 0 {
		os.Setenv("RATE_LIMIT_EXEMPT_IPS", joinIPs(ips))
	} else {
		os.Unsetenv("RATE_LIMIT_EXEMPT_IPS")
	}

	// Return cleanup function
	return func() {
		h.mu.Lock()
		defer h.mu.Unlock()

		// Restore original environment
		if val, ok := h.originalEnv["RATE_LIMIT_EXEMPT_IPS"]; ok {
			if val != "" {
				os.Setenv("RATE_LIMIT_EXEMPT_IPS", val)
			} else {
				os.Unsetenv("RATE_LIMIT_EXEMPT_IPS")
			}
		}
	}
}

// WithRateLimiting runs a test function with rate limiting enabled
func (h *RateLimitTestHelper) WithRateLimiting(t *testing.T, ratePerMinute int, fn func()) {
	cleanup := h.EnableRateLimiting(t, ratePerMinute)
	defer cleanup()
	fn()
}

// WithoutRateLimiting runs a test function with rate limiting disabled
func (h *RateLimitTestHelper) WithoutRateLimiting(t *testing.T, fn func()) {
	cleanup := h.DisableRateLimiting(t)
	defer cleanup()
	fn()
}

// VerifyRateLimitingEnabled checks if rate limiting should be applied
func (h *RateLimitTestHelper) VerifyRateLimitingEnabled(t *testing.T) bool {
	cfg := config.Load()
	// Rate limiting is enabled if RateLimitPerMinute > 0
	return cfg.Security.RateLimitPerMinute > 0
}

// ShouldSkipRateLimitTests returns true if rate limit tests should be skipped
func ShouldSkipRateLimitTests() bool {
	// Skip if explicitly disabled
	if os.Getenv("SKIP_RATE_LIMIT_TESTS") == "true" {
		return true
	}

	// Skip in CI unless explicitly enabled
	if os.Getenv("CI") == "true" && os.Getenv("RUN_RATE_LIMIT_TESTS") != "true" {
		return true
	}

	return false
}

// Helper function to join IPs with commas
func joinIPs(ips []string) string {
	result := ""
	for i, ip := range ips {
		if i > 0 {
			result += ","
		}
		result += ip
	}
	return result
}

// RateLimitTestConfig holds configuration for rate limit testing
type RateLimitTestConfig struct {
	RatePerMinute   int
	BurstSize       int
	TestDuration    time.Duration
	ConcurrentUsers int
	ExemptIPs       []string
	AuthMaxRequests int
	AuthWindow      time.Duration
}

// DefaultRateLimitTestConfig returns default configuration for testing
func DefaultRateLimitTestConfig() RateLimitTestConfig {
	return RateLimitTestConfig{
		RatePerMinute:   10,
		BurstSize:       10,
		TestDuration:    30 * time.Second,
		ConcurrentUsers: 5,
		ExemptIPs:       []string{},
		AuthMaxRequests: 5,
		AuthWindow:      5 * time.Minute,
	}
}

// ApplyConfig applies the rate limit test configuration
func (h *RateLimitTestHelper) ApplyConfig(t *testing.T, cfg RateLimitTestConfig) func() {
	cleanups := []func(){}

	// Enable rate limiting with specified rate
	cleanup1 := h.EnableRateLimiting(t, cfg.RatePerMinute)
	cleanups = append(cleanups, cleanup1)

	// Set auth endpoint limits if specified
	if cfg.AuthMaxRequests > 0 {
		cleanup2 := h.SetAuthEndpointLimits(t, cfg.AuthMaxRequests, cfg.AuthWindow)
		cleanups = append(cleanups, cleanup2)
	}

	// Set exempt IPs if specified
	if len(cfg.ExemptIPs) > 0 {
		cleanup3 := h.SetExemptIPs(t, cfg.ExemptIPs)
		cleanups = append(cleanups, cleanup3)
	}

	// Return combined cleanup function
	return func() {
		for i := len(cleanups) - 1; i >= 0; i-- {
			cleanups[i]()
		}
	}
}
