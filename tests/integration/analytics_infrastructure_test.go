package integration

import (
	"context"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/services/analytics"
	"Membership-SAAS-System-Backend/internal/testutil"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

type AnalyticsInfrastructureTestSuite struct {
	testutil.BaseServiceTestSuite
	analyticsService analytics.AnalyticsService
}

func TestAnalyticsInfrastructureTestSuite(t *testing.T) {
	suite.Run(t, new(AnalyticsInfrastructureTestSuite))
}

func (s *AnalyticsInfrastructureTestSuite) SetupTest() {
	s.BaseServiceTestSuite.SetupTest()
	s.analyticsService = analytics.NewAnalyticsService(s.MockStore)
}

// Time-Series Data Tests

func (s *AnalyticsInfrastructureTestSuite) TestTimeSeriesEventTracking() {
	t := s.T()
	ctx := context.Background()

	// Create test organization and user
	org := s.CreateTestOrganization()
	user := s.CreateTestUser()

	// Test various event types with time-series data
	eventTypes := []string{
		"user_login", "user_logout", "event_created", "post_viewed",
		"resource_downloaded", "notification_sent",
	}

	baseTime := time.Now().Add(-24 * time.Hour)
	events := make([]analytics.AnalyticsEvent, 0)

	// Generate time-series events over 24 hours
	for i, eventType := range eventTypes {
		for hour := 0; hour < 24; hour++ {
			timestamp := baseTime.Add(time.Duration(hour) * time.Hour)

			entityID := uuid.New()
			ipAddress := "***********"
			userAgent := "Test-Agent/1.0"
			sessionID := uuid.New()

			event := analytics.AnalyticsEvent{
				EventType:      eventType,
				EventName:      eventType + "_test",
				EventTimestamp: timestamp,
				UserID:         &user.ID,
				OrganizationID: &org.ID,
				EntityType:     &eventType,
				EntityID:       &entityID,
				Properties: map[string]interface{}{
					"hour":     hour,
					"day":      timestamp.Format("2006-01-02"),
					"sequence": i*24 + hour,
				},
				IPAddress: &ipAddress,
				UserAgent: &userAgent,
				SessionID: &sessionID,
			}
			events = append(events, event)
		}
	}

	// Test batch tracking for performance
	start := time.Now()
	err := s.analyticsService.BatchTrackEvents(ctx, events)
	duration := time.Since(start)

	require.NoError(t, err)
	require.Less(t, duration, 5*time.Second, "Batch insert should complete within 5 seconds")

	// Verify events were stored correctly with partitioning
	queryParams := analytics.EventQueryParams{
		StartTime:      baseTime,
		EndTime:        baseTime.Add(25 * time.Hour),
		OrganizationID: &org.ID,
		Limit:          1000,
	}

	retrievedEvents, err := s.analyticsService.GetEventsByTimeRange(ctx, queryParams)
	require.NoError(t, err)
	require.Len(t, retrievedEvents, len(events), "All events should be retrievable")

	// Test time-based queries for performance
	start = time.Now()
	counts, err := s.analyticsService.GetEventCountsByType(ctx, baseTime, baseTime.Add(12*time.Hour), &org.ID)
	duration = time.Since(start)

	require.NoError(t, err)
	require.Less(t, duration, 2*time.Second, "Time-range queries should be fast")
	require.NotEmpty(t, counts, "Should get event counts")
}

func (s *AnalyticsInfrastructureTestSuite) TestPartitioningPerformance() {
	t := s.T()
	ctx := context.Background()

	// Create test data across multiple months
	org := s.CreateTestOrganization()
	user := s.CreateTestUser()

	// Generate events across 3 months to test partitioning
	baseTime := time.Now().Add(-3 * 30 * 24 * time.Hour) // 3 months ago
	eventsPerMonth := 1000

	for month := 0; month < 3; month++ {
		monthStart := baseTime.Add(time.Duration(month) * 30 * 24 * time.Hour)

		events := make([]analytics.AnalyticsEvent, 0, eventsPerMonth)
		for i := 0; i < eventsPerMonth; i++ {
			timestamp := monthStart.Add(time.Duration(i) * time.Minute)

			event := analytics.AnalyticsEvent{
				EventType:      "performance_metric",
				EventName:      "partition_test",
				EventTimestamp: timestamp,
				UserID:         &user.ID,
				OrganizationID: &org.ID,
				Properties: map[string]interface{}{
					"month":    month,
					"sequence": i,
					"value":    i * 10,
				},
			}
			events = append(events, event)
		}

		// Track events for this month
		err := s.analyticsService.BatchTrackEvents(ctx, events)
		require.NoError(t, err)
	}

	// Test query performance across partitions
	start := time.Now()
	allEvents, err := s.analyticsService.GetEventsByTimeRange(ctx, analytics.EventQueryParams{
		StartTime:      baseTime,
		EndTime:        time.Now(),
		OrganizationID: &org.ID,
		Limit:          5000,
	})
	duration := time.Since(start)

	require.NoError(t, err)
	require.Less(t, duration, 3*time.Second, "Cross-partition queries should be efficient")
	require.GreaterOrEqual(t, len(allEvents), 3000, "Should retrieve events from all partitions")

	// Test single partition query performance
	singleMonthStart := baseTime.Add(30 * 24 * time.Hour)
	singleMonthEnd := baseTime.Add(60 * 24 * time.Hour)

	start = time.Now()
	monthEvents, err := s.analyticsService.GetEventsByTimeRange(ctx, analytics.EventQueryParams{
		StartTime:      singleMonthStart,
		EndTime:        singleMonthEnd,
		OrganizationID: &org.ID,
		Limit:          2000,
	})
	duration = time.Since(start)

	require.NoError(t, err)
	require.Less(t, duration, 1*time.Second, "Single partition queries should be very fast")
	require.GreaterOrEqual(t, len(monthEvents), 900, "Should retrieve events from single partition")
}

// Aggregation Tests

func (s *AnalyticsInfrastructureTestSuite) TestUserAnalyticsAggregation() {
	t := s.T()
	ctx := context.Background()

	org := s.CreateTestOrganization()
	user := s.CreateTestUser()

	// Track various user activities
	activities := []struct {
		eventType string
		count     int
	}{
		{"user_login", 15},
		{"event_registered", 5},
		{"event_checked_in", 3},
		{"post_created", 8},
		{"post_viewed", 25},
		{"resource_downloaded", 12},
	}

	// Create events for aggregation
	baseTime := time.Now().Add(-7 * 24 * time.Hour)
	for _, activity := range activities {
		for i := 0; i < activity.count; i++ {
			timestamp := baseTime.Add(time.Duration(i) * time.Hour)

			event := analytics.AnalyticsEvent{
				EventType:      activity.eventType,
				EventName:      activity.eventType + "_test",
				EventTimestamp: timestamp,
				UserID:         &user.ID,
				OrganizationID: &org.ID,
				Properties: map[string]interface{}{
					"test_sequence": i,
				},
			}

			err := s.analyticsService.TrackEvent(ctx, event)
			require.NoError(t, err)
		}
	}

	// Wait for async updates to complete
	time.Sleep(100 * time.Millisecond)

	// Test user analytics aggregation
	userAnalytics, err := s.analyticsService.GetUserAnalytics(ctx, user.ID, &org.ID)
	require.NoError(t, err)
	require.NotNil(t, userAnalytics)

	// Verify aggregated counters
	require.Equal(t, 15, userAnalytics.TotalLogins, "Login count should be aggregated")
	require.Equal(t, 5, userAnalytics.TotalEventsRegistered, "Registration count should be aggregated")
	require.Equal(t, 3, userAnalytics.TotalEventsAttended, "Check-in count should be aggregated")
	require.Equal(t, 8, userAnalytics.TotalPostsCreated, "Post creation count should be aggregated")
	require.Equal(t, 25, userAnalytics.TotalPostsViewed, "Post view count should be aggregated")
	require.Equal(t, 12, userAnalytics.TotalResourcesAccessed, "Resource access count should be aggregated")

	// Test engagement score calculation
	require.Greater(t, userAnalytics.EngagementScore, 0.0, "Engagement score should be calculated")
	require.NotNil(t, userAnalytics.LastActivityAt, "Last activity should be recorded")

	// Test counter increment functionality
	err = s.analyticsService.IncrementUserCounter(ctx, user.ID, &org.ID, "total_logins")
	require.NoError(t, err)

	// Verify increment
	updatedAnalytics, err := s.analyticsService.GetUserAnalytics(ctx, user.ID, &org.ID)
	require.NoError(t, err)
	require.Equal(t, 16, updatedAnalytics.TotalLogins, "Counter should be incremented")
}

func (s *AnalyticsInfrastructureTestSuite) TestOrganizationAnalyticsAggregation() {
	t := s.T()
	ctx := context.Background()

	org := s.CreateTestOrganization()
	users := make([]db.User, 10)
	for i := 0; i < 10; i++ {
		users[i] = s.CreateTestUser()
		// Add users to organization
		s.AddUserToOrganization(users[i].ID, org.ID, "member")
	}

	// Generate diverse activities for organization analytics
	baseTime := time.Now().Add(-30 * 24 * time.Hour)

	// Event creation activities
	for i := 0; i < 20; i++ {
		event := analytics.AnalyticsEvent{
			EventType:      "event_created",
			EventName:      "event_creation",
			EventTimestamp: baseTime.Add(time.Duration(i) * time.Hour),
			UserID:         &users[i%len(users)].ID,
			OrganizationID: &org.ID,
		}
		err := s.analyticsService.TrackEvent(ctx, event)
		require.NoError(t, err)
	}

	// Event registration activities
	for i := 0; i < 50; i++ {
		event := analytics.AnalyticsEvent{
			EventType:      "event_registered",
			EventName:      "event_registration",
			EventTimestamp: baseTime.Add(time.Duration(i) * time.Hour),
			UserID:         &users[i%len(users)].ID,
			OrganizationID: &org.ID,
		}
		err := s.analyticsService.TrackEvent(ctx, event)
		require.NoError(t, err)
	}

	// User login activities for different time windows
	now := time.Now()
	for i, user := range users {
		// Recent activity (1 day)
		if i < 8 {
			event := analytics.AnalyticsEvent{
				EventType:      "user_login",
				EventName:      "recent_login",
				EventTimestamp: now.Add(-12 * time.Hour),
				UserID:         &user.ID,
				OrganizationID: &org.ID,
			}
			err := s.analyticsService.TrackEvent(ctx, event)
			require.NoError(t, err)
		}

		// Weekly activity (7 days)
		if i < 9 {
			event := analytics.AnalyticsEvent{
				EventType:      "user_login",
				EventName:      "weekly_login",
				EventTimestamp: now.Add(-3 * 24 * time.Hour),
				UserID:         &user.ID,
				OrganizationID: &org.ID,
			}
			err := s.analyticsService.TrackEvent(ctx, event)
			require.NoError(t, err)
		}
	}

	// Calculate active members for organization
	activeMembers, err := s.analyticsService.CalculateOrganizationActiveMembers(ctx, org.ID)
	require.NoError(t, err)
	require.NotNil(t, activeMembers)

	// Test active member calculations
	require.Equal(t, 8, activeMembers.Active1d, "1-day active members should be correct")
	require.Equal(t, 9, activeMembers.Active7d, "7-day active members should be correct")
	require.GreaterOrEqual(t, activeMembers.Active30d, 9, "30-day active members should include all")

	// Test organization analytics retrieval
	orgAnalytics, err := s.analyticsService.GetOrganizationAnalytics(ctx, org.ID)
	require.NoError(t, err)
	require.NotNil(t, orgAnalytics)

	// Verify organization metrics
	require.Equal(t, len(users), orgAnalytics.TotalMembers, "Total members should match")
	require.Equal(t, 8, orgAnalytics.ActiveMembers1d, "1-day active members should match")
	require.Equal(t, 9, orgAnalytics.ActiveMembers7d, "7-day active members should match")
}

func (s *AnalyticsInfrastructureTestSuite) TestDailySnapshotsGeneration() {
	t := s.T()
	ctx := context.Background()

	org := s.CreateTestOrganization()
	user := s.CreateTestUser()

	// Generate activities for a specific day
	targetDate := time.Now().Add(-1 * 24 * time.Hour)
	dayStart := time.Date(targetDate.Year(), targetDate.Month(), targetDate.Day(), 0, 0, 0, 0, targetDate.Location())
	dayEnd := dayStart.Add(24 * time.Hour)

	// Activities for the target day
	activities := []struct {
		eventType string
		count     int
	}{
		{"user_login", 5},
		{"event_created", 2},
		{"event_registered", 8},
		{"event_checked_in", 6},
		{"post_created", 3},
		{"post_viewed", 15},
		{"resource_created", 1},
		{"resource_downloaded", 4},
	}

	for _, activity := range activities {
		for i := 0; i < activity.count; i++ {
			timestamp := dayStart.Add(time.Duration(i) * time.Hour)

			event := analytics.AnalyticsEvent{
				EventType:      activity.eventType,
				EventName:      activity.eventType + "_snapshot_test",
				EventTimestamp: timestamp,
				UserID:         &user.ID,
				OrganizationID: &org.ID,
			}

			err := s.analyticsService.TrackEvent(ctx, event)
			require.NoError(t, err)
		}
	}

	// Generate daily snapshot
	err := s.analyticsService.GenerateDailySnapshot(ctx, dayStart, &org.ID)
	require.NoError(t, err)

	// Retrieve and verify snapshot
	snapshots, err := s.analyticsService.GetDailySnapshots(ctx, dayStart, dayEnd, &org.ID)
	require.NoError(t, err)
	require.Len(t, snapshots, 1, "Should have one snapshot for the day")

	snapshot := snapshots[0]
	require.Equal(t, dayStart.Format("2006-01-02"), snapshot.SnapshotDate.Format("2006-01-02"))
	require.Equal(t, org.ID, snapshot.OrganizationID)
	require.Equal(t, 1, snapshot.DailyActiveUsers, "Should count unique active users")
	require.Equal(t, 2, snapshot.EventsCreated, "Should count events created")
	require.Equal(t, 8, snapshot.EventRegistrations, "Should count event registrations")
	require.Equal(t, 6, snapshot.EventCheckIns, "Should count check-ins")
	require.Equal(t, 3, snapshot.PostsCreated, "Should count posts created")
	require.Equal(t, 15, snapshot.PostViews, "Should count post views")
	require.Equal(t, 1, snapshot.ResourcesCreated, "Should count resources created")
	require.Equal(t, 4, snapshot.ResourceDownloads, "Should count resource downloads")
}

// Performance Tests

func (s *AnalyticsInfrastructureTestSuite) TestHighVolumeEventIngestion() {
	t := s.T()
	ctx := context.Background()

	if testing.Short() {
		t.Skip("Skipping high-volume test in short mode")
	}

	org := s.CreateTestOrganization()
	user := s.CreateTestUser()

	// Test ingesting 10,000 events in batches
	totalEvents := 10000
	batchSize := 1000
	batches := totalEvents / batchSize

	overallStart := time.Now()

	for batch := 0; batch < batches; batch++ {
		events := make([]analytics.AnalyticsEvent, batchSize)
		baseTime := time.Now().Add(-time.Duration(batch) * time.Hour)

		for i := 0; i < batchSize; i++ {
			events[i] = analytics.AnalyticsEvent{
				EventType:      "performance_metric",
				EventName:      "high_volume_test",
				EventTimestamp: baseTime.Add(time.Duration(i) * time.Second),
				UserID:         &user.ID,
				OrganizationID: &org.ID,
				Properties: map[string]interface{}{
					"batch":    batch,
					"sequence": i,
					"value":    batch*batchSize + i,
				},
			}
		}

		batchStart := time.Now()
		err := s.analyticsService.BatchTrackEvents(ctx, events)
		batchDuration := time.Since(batchStart)

		require.NoError(t, err)
		require.Less(t, batchDuration, 2*time.Second, "Each batch should complete within 2 seconds")
	}

	overallDuration := time.Since(overallStart)
	eventsPerSecond := float64(totalEvents) / overallDuration.Seconds()

	t.Logf("Ingested %d events in %v (%.2f events/second)", totalEvents, overallDuration, eventsPerSecond)
	require.Greater(t, eventsPerSecond, 1000.0, "Should handle at least 1000 events per second")

	// Verify all events were stored
	count, err := s.analyticsService.GetEventCountsByType(ctx,
		time.Now().Add(-time.Duration(batches+1)*time.Hour),
		time.Now(), &org.ID)
	require.NoError(t, err)

	var totalStored int64
	for _, c := range count {
		if c.EventType == "performance_metric" {
			totalStored = c.EventCount
			break
		}
	}
	require.Equal(t, totalEvents, totalStored, "All events should be stored")
}

func (s *AnalyticsInfrastructureTestSuite) TestConcurrentAnalyticsOperations() {
	t := s.T()
	ctx := context.Background()

	org := s.CreateTestOrganization()
	users := make([]db.User, 5)
	for i := 0; i < 5; i++ {
		users[i] = s.CreateTestUser()
	}

	// Test concurrent event tracking
	concurrentGoroutines := 10
	eventsPerGoroutine := 100
	errors := make(chan error, concurrentGoroutines)

	start := time.Now()

	for g := 0; g < concurrentGoroutines; g++ {
		go func(goroutineID int) {
			for i := 0; i < eventsPerGoroutine; i++ {
				event := analytics.AnalyticsEvent{
					EventType:      "user_login",
					EventName:      "concurrent_test",
					EventTimestamp: time.Now(),
					UserID:         &users[i%len(users)].ID,
					OrganizationID: &org.ID,
					Properties: map[string]interface{}{
						"goroutine": goroutineID,
						"sequence":  i,
					},
				}

				if err := s.analyticsService.TrackEvent(ctx, event); err != nil {
					errors <- err
					return
				}
			}
			errors <- nil
		}(g)
	}

	// Wait for all goroutines to complete
	for i := 0; i < concurrentGoroutines; i++ {
		err := <-errors
		require.NoError(t, err, "Concurrent operations should not fail")
	}

	duration := time.Since(start)
	totalEvents := concurrentGoroutines * eventsPerGoroutine
	t.Logf("Concurrent test: %d events in %v", totalEvents, duration)

	// Verify all events were tracked
	time.Sleep(500 * time.Millisecond) // Wait for async processing

	for _, user := range users {
		analytics, err := s.analyticsService.GetUserAnalytics(ctx, user.ID, &org.ID)
		require.NoError(t, err)
		require.Greater(t, analytics.TotalLogins, 0, "User should have login events tracked")
	}
}

// Feature Usage Tests

func (s *AnalyticsInfrastructureTestSuite) TestFeatureUsageTracking() {
	t := s.T()
	ctx := context.Background()

	org := s.CreateTestOrganization()
	user := s.CreateTestUser()

	features := []string{
		"event_management", "post_creation", "resource_sharing",
		"member_directory", "notifications", "analytics_dashboard",
	}

	// Track feature usage with different contexts
	for i, feature := range features {
		for usage := 0; usage < i+1; usage++ {
			context := map[string]interface{}{
				"page":     "/dashboard/" + feature,
				"usage":    usage,
				"browser":  "Chrome",
				"platform": "web",
			}

			err := s.analyticsService.TrackFeatureUsage(ctx, feature, user.ID, &org.ID, context)
			require.NoError(t, err)
		}
	}

	// Get feature usage statistics
	startDate := time.Now().Add(-24 * time.Hour)
	endDate := time.Now().Add(1 * time.Hour)

	stats, err := s.analyticsService.GetFeatureUsageStats(ctx, &org.ID, &startDate, &endDate)
	require.NoError(t, err)
	require.Len(t, stats, len(features), "Should track all features")

	// Verify usage counts
	for i, stat := range stats {
		expectedCount := i + 1 // Based on our test data
		require.Equal(t, features[i], stat.FeatureName)
		require.Equal(t, expectedCount, stat.TotalUsage)
		require.Equal(t, 1, stat.UniqueUsers)
	}
}

// Helper methods for test data creation

func (s *AnalyticsInfrastructureTestSuite) CreateTestOrganization() db.Organization {
	desc := "Organization for analytics testing"
	org, err := s.MockStore.CreateOrganization(context.Background(), db.CreateOrganizationParams{
		Name:        "Analytics Test Org " + uuid.New().String()[:8],
		Description: &desc,
		Status:      "active",
	})
	require.NoError(s.T(), err)
	return org
}

func (s *AnalyticsInfrastructureTestSuite) CreateTestUser() db.User {
	// Use the base test suite's CreateTestUser method
	return s.BaseServiceTestSuite.CreateTestUser()
}

func (s *AnalyticsInfrastructureTestSuite) AddUserToOrganization(userID, orgID uuid.UUID, role string) {
	_, err := s.MockStore.AddUserToOrganization(context.Background(), db.AddUserToOrganizationParams{
		UserID:               userID,
		OrganizationID:       orgID,
		Role:                 role,
		NotificationsEnabled: true,
	})
	require.NoError(s.T(), err)
}
