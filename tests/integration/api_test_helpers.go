package integration

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/internal/config"
	"Membership-SAAS-System-Backend/internal/server"
	testingtesting "Membership-SAAS-System-Backend/internal/testing"
	"Membership-SAAS-System-Backend/internal/utils"

	"github.com/golang-jwt/jwt/v5"
	"github.com/golang-migrate/migrate/v4"
	_ "github.com/golang-migrate/migrate/v4/database/postgres"
	_ "github.com/golang-migrate/migrate/v4/source/file"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/require"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/modules/postgres"
	"github.com/testcontainers/testcontainers-go/wait"
)

// generateTestJWT creates a test JWT token with enhanced format
func generateTestJWT(t *testing.T, email, role string) string {
	// Use actual user IDs that exist in the database
	userID := "00000000-0000-0000-0000-000000000001" // Default superadmin
	defaultOrgID := "00000000-0000-0000-0000-000000000002"

	// Determine platform role and org role based on input role
	var platformRole, orgRole string
	if role == "admin" {
		platformRole = "super_admin"
		orgRole = "owner"
	} else {
		platformRole = "user"
		orgRole = "member"
		// For regular users, we should use a different ID
		if email == "<EMAIL>" {
			userID = uuid.New().String()
		}
	}

	// Build the active org structure
	activeOrg := map[string]interface{}{
		"id":   defaultOrgID,
		"role": orgRole,
	}

	claims := jwt.MapClaims{
		"user_id":       userID,
		"platform_role": platformRole,
		"active_org":    activeOrg,
		"exp":           time.Now().Add(time.Hour).Unix(),
		"iat":           time.Now().Unix(),
		"nbf":           time.Now().Unix(),
		"iss":           "MembershipSaas",
		"sub":           userID,
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte("test-secret-key-for-testing"))
	require.NoError(t, err)

	return tokenString
}

// createTestConfig creates a test configuration for integration tests
func createTestConfig(dbConnString string) *config.Config {
	// Create config directly without using Load() to avoid migration issues
	cfg := &config.Config{
		Database: config.DatabaseConfig{
			URL:               dbConnString,
			AutoResetDB:       false,
			MigrationsPath:    "", // Empty to skip migrations
			MaxRetries:        5,
			RetryDelay:        2 * time.Second,
			MaxConnections:    10,
			MinConnections:    2,
			MaxConnIdleTime:   30 * time.Minute,
			MaxConnLifetime:   1 * time.Hour,
			HealthCheckPeriod: 1 * time.Minute,
		},
		Server: config.ServerConfig{
			Port:    "8081",
			Address: "0.0.0.0:8081",
		},
		JWT: config.JWTConfig{
			AccessTokenSecret:  "test-secret-key-for-testing",
			RefreshTokenSecret: "test-refresh-secret-key-for-testing",
			AccessTokenTTL:     15 * time.Minute,
			RefreshTokenTTL:    7 * 24 * time.Hour,
		},
		Twilio: config.TwilioConfig{
			AccountSID: "test-account-sid",
			AuthToken:  "test-auth-token",
			FromPhone:  "+**********",
		},
		App: config.AppConfig{
			BaseURL:   "http://localhost:8081",
			UploadDir: "./test-uploads",
		},
		Storage: config.StorageConfig{
			Backend:       "local",
			LocalBasePath: "./test-uploads",
			LocalBaseURL:  "http://localhost:8081/uploads",
		},
	}

	return cfg
}

// APITestSuite provides common infrastructure for API integration tests
type APITestSuite struct {
	e            *echo.Echo
	dbContainer  *postgres.PostgresContainer
	dbConnString string
	adminToken   string
	userToken    string
	orgID        string
	ctx          context.Context
	Server       *server.Server
}

// SetupAPITest initializes the test environment with a real database and server
func SetupAPITest(t *testing.T) *APITestSuite {
	// Reset all metrics to prevent duplicate registration
	testingtesting.ResetAllMetrics()

	// Set up JWT environment variables BEFORE anything else
	// This is crucial because the token package uses sync.Once to load config
	t.Setenv("ACCESS_TOKEN_SECRET", "test-secret-key-for-testing")
	t.Setenv("REFRESH_TOKEN_SECRET", "test-refresh-secret-key-for-testing")
	t.Setenv("ACCESS_TOKEN_DURATION", "15m")
	t.Setenv("REFRESH_TOKEN_DURATION", "168h") // 7 days

	// Enable mock mode for OTP/notifications in tests
	t.Setenv("MOCK_TWILIO_OTP", "true")
	t.Setenv("MOCK_TWILIO_NOTIFICATIONS", "true")

	ctx := context.Background()

	// Start PostgreSQL container
	dbContainer, err := postgres.Run(ctx,
		"postgres:16-alpine",
		postgres.WithDatabase("testdb"),
		postgres.WithUsername("testuser"),
		postgres.WithPassword("testpass"),
		testcontainers.WithWaitStrategy(
			wait.ForLog("database system is ready to accept connections").
				WithOccurrence(2),
		),
	)
	require.NoError(t, err)

	// Get database connection string
	dbConnString, err := dbContainer.ConnectionString(ctx, "sslmode=disable")
	require.NoError(t, err)

	// Load database schema using migrations
	migrationPath := "file://../../db/migrations"
	m, err := migrate.New(migrationPath, dbConnString)
	require.NoError(t, err, "Failed to create migrate instance")

	err = m.Up()
	if err != nil && !errors.Is(err, migrate.ErrNoChange) {
		require.NoError(t, err, "Failed to run migrations")
	}
	m.Close()
	t.Log("Database schema loaded successfully")

	// Create test configuration
	testConfig := createTestConfig(dbConnString)

	// Create server with proper route registration
	srv, err := server.New(testConfig)
	require.NoError(t, err)

	// Generate test tokens
	adminToken := generateTestJWT(t, "<EMAIL>", "admin")
	userToken := generateTestJWT(t, "<EMAIL>", "member")

	return &APITestSuite{
		e:            srv.Echo(),
		dbContainer:  dbContainer,
		dbConnString: dbConnString,
		adminToken:   adminToken,
		userToken:    userToken,
		orgID:        "00000000-0000-0000-0000-000000000002", // Default organization from migrations
		ctx:          ctx,
		Server:       srv,
	}
}

// TeardownAPITest cleans up test resources
func (suite *APITestSuite) TeardownAPITest(t *testing.T) {
	if suite.dbContainer != nil {
		err := suite.dbContainer.Terminate(suite.ctx)
		require.NoError(t, err)
	}
}

// MakeRequest creates and executes an HTTP request with optional authentication
func (suite *APITestSuite) MakeRequest(method, path string, body interface{}, token string) (*httptest.ResponseRecorder, error) {
	var reqBody []byte
	var err error

	if body != nil {
		reqBody, err = json.Marshal(body)
		if err != nil {
			return nil, err
		}
	}

	req := httptest.NewRequest(method, path, bytes.NewBuffer(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

	if token != "" {
		req.Header.Set(echo.HeaderAuthorization, fmt.Sprintf("Bearer %s", token))
	}

	rec := httptest.NewRecorder()
	suite.e.ServeHTTP(rec, req)

	return rec, nil
}

// MakeAuthenticatedRequest makes a request with admin authentication
func (suite *APITestSuite) MakeAuthenticatedRequest(method, path string, body interface{}) (*httptest.ResponseRecorder, error) {
	return suite.MakeRequest(method, path, body, suite.adminToken)
}

// MakeUserRequest makes a request with user authentication
func (suite *APITestSuite) MakeUserRequest(method, path string, body interface{}) (*httptest.ResponseRecorder, error) {
	return suite.MakeRequest(method, path, body, suite.userToken)
}

// MakeUnauthenticatedRequest makes a request without authentication
func (suite *APITestSuite) MakeUnauthenticatedRequest(method, path string, body interface{}) (*httptest.ResponseRecorder, error) {
	return suite.MakeRequest(method, path, body, "")
}

// AssertJSONResponse validates HTTP response status and optionally parses JSON response
func (suite *APITestSuite) AssertJSONResponse(t *testing.T, rec *httptest.ResponseRecorder, expectedStatus int, target interface{}) {
	if rec.Code != expectedStatus {
		t.Logf("Response body: %s", rec.Body.String())
	}
	require.Equal(t, expectedStatus, rec.Code)

	if target != nil && rec.Body.Len() > 0 {
		err := json.Unmarshal(rec.Body.Bytes(), target)
		require.NoError(t, err)
	}
}

// AssertErrorResponse validates error response format
func (suite *APITestSuite) AssertErrorResponse(t *testing.T, rec *httptest.ResponseRecorder, expectedStatus int, expectedError string) {
	require.Equal(t, expectedStatus, rec.Code)

	if expectedError != "" {
		body := rec.Body.String()
		require.Contains(t, body, expectedError)
	}
}

// TestAuthFlow represents a complete authentication flow test
type TestAuthFlow struct {
	suite *APITestSuite
	t     *testing.T
}

// NewTestAuthFlow creates a new authentication flow tester
func (suite *APITestSuite) NewTestAuthFlow(t *testing.T) *TestAuthFlow {
	return &TestAuthFlow{
		suite: suite,
		t:     t,
	}
}

// TestPhoneOTPFlow tests the complete phone OTP authentication flow
func (flow *TestAuthFlow) TestPhoneOTPFlow(phoneNumber string) {
	// Generate valid PKCE values
	codeVerifier := "test-verifier-for-phone-otp"
	codeChallenge := utils.GenerateS256Challenge(codeVerifier)

	// Step 1: Check phone number
	checkReq := map[string]interface{}{
		"phone": phoneNumber,
	}
	rec, err := flow.suite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/phone/check", checkReq)
	require.NoError(flow.t, err)
	flow.suite.AssertJSONResponse(flow.t, rec, http.StatusOK, nil)

	// Step 2: Initiate OTP
	initiateReq := map[string]interface{}{
		"phone":                 phoneNumber,
		"client_id":             "test-client",
		"code_challenge":        codeChallenge,
		"code_challenge_method": "S256",
		"state":                 "test-state-phone-otp",
	}
	rec, err = flow.suite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/phone/otp/initiate", initiateReq)
	require.NoError(flow.t, err)
	flow.suite.AssertJSONResponse(flow.t, rec, http.StatusOK, nil)

	// Step 3: Verify OTP (using test OTP)
	verifyReq := map[string]interface{}{
		"otp":           "123456", // Test OTP
		"code_verifier": codeVerifier,
		"state":         "test-state-phone-otp",
	}
	rec, err = flow.suite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/phone/otp/verify", verifyReq)
	require.NoError(flow.t, err)
	flow.suite.AssertJSONResponse(flow.t, rec, http.StatusOK, nil)
}

// TestStaffLoginFlow tests the staff login authentication flow
func (flow *TestAuthFlow) TestStaffLoginFlow(email, password string) {
	// Generate valid PKCE values
	codeVerifier := "test-verifier-for-staff-login"
	codeChallenge := utils.GenerateS256Challenge(codeVerifier)

	// Step 1: Initiate staff login (no password in this step)
	initiateReq := map[string]interface{}{
		"email":                 email,
		"client_id":             "test-client",
		"code_challenge":        codeChallenge,
		"code_challenge_method": "S256",
		"state":                 "test-state-staff-login",
	}
	rec, err := flow.suite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/staff/login/initiate", initiateReq)
	require.NoError(flow.t, err)
	flow.suite.AssertJSONResponse(flow.t, rec, http.StatusCreated, nil)

	// Step 2: Verify staff login (password provided in this step)
	verifyReq := map[string]interface{}{
		"email":         email,
		"password":      password,
		"code_verifier": codeVerifier,
		"state":         "test-state-staff-login",
	}
	rec, err = flow.suite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/staff/login/verify", verifyReq)
	require.NoError(flow.t, err)
	flow.suite.AssertJSONResponse(flow.t, rec, http.StatusOK, nil)
}

// FileUploadTest provides utilities for testing file upload endpoints
type FileUploadTest struct {
	suite *APITestSuite
	t     *testing.T
}

// NewFileUploadTest creates a new file upload tester
func (suite *APITestSuite) NewFileUploadTest(t *testing.T) *FileUploadTest {
	return &FileUploadTest{
		suite: suite,
		t:     t,
	}
}

// TestEventMediaUpload tests event media file upload
func (upload *FileUploadTest) TestEventMediaUpload(eventID string, fileContent []byte, filename string) {
	// Create multipart form data
	body := &bytes.Buffer{}
	// Note: This is a simplified version. In real implementation,
	// you'd use multipart/form-data encoding

	req := httptest.NewRequest("POST", fmt.Sprintf("/api/v1/events/%s/media", eventID), body)
	req.Header.Set("Content-Type", "multipart/form-data")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", upload.suite.adminToken))

	rec := httptest.NewRecorder()
	upload.suite.e.ServeHTTP(rec, req)

	require.Equal(upload.t, http.StatusCreated, rec.Code)
}

// CleanupTestData cleans up test data from the database
func (suite *APITestSuite) CleanupTestData(t *testing.T) {
	// Reset the superadmin password to the default value used in migrations
	// This is critical for test isolation as some tests modify the password

	// Use the service container from the server to access the database
	if suite.Server != nil && suite.Server.ServiceContainer() != nil {
		ctx := context.Background()
		container := suite.Server.ServiceContainer()

		// Get the database pool
		pool := container.DB.Pool()
		if pool == nil {
			t.Logf("Warning: Database pool is nil")
			return
		}

		// Reset superadmin password to '123456' (the bcrypt hash from migration)
		defaultPasswordHash := "$2a$12$v6MjHc6me1Iidicch8oFD.eIqbzpaRmi26pPgy9v7PxnBzgEeVb7S"

		// Update the password for superadmin using raw SQL
		_, err := pool.Exec(ctx,
			"UPDATE users SET hashed_password = $1 WHERE id = $2",
			defaultPasswordHash,
			uuid.MustParse("00000000-0000-0000-0000-000000000001"), // Superadmin ID from migration
		)
		if err != nil {
			t.Logf("Warning: Failed to reset superadmin password: %v", err)
		}

		// Clean up any test-created users (those with test phone numbers)
		testPhonePatterns := []string{
			"+**********%",
			"+1987654321%",
			"+1555123456%",
			"+1234567892%",
			"+1234567893%",
		}

		for _, pattern := range testPhonePatterns {
			// Note: We can't directly delete users due to foreign key constraints
			// Instead, we'll mark them as deleted or leave them for the container cleanup
			t.Logf("Skipping cleanup of users with phone pattern: %s (will be cleaned with container)", pattern)
		}
	}
}

// GetDBConnString returns the database connection string for direct DB access
func (suite *APITestSuite) GetDBConnString() string {
	return suite.dbConnString
}

// GetUserToken returns the user token for authenticated requests
func (suite *APITestSuite) GetUserToken() string {
	return suite.userToken
}

// GetOrgID returns the organization ID
func (suite *APITestSuite) GetOrgID() string {
	return suite.orgID
}

// MakeRawRequest executes a raw HTTP request for testing malformed content
func (suite *APITestSuite) MakeRawRequest(req *http.Request) *httptest.ResponseRecorder {
	rec := httptest.NewRecorder()
	suite.e.ServeHTTP(rec, req)
	return rec
}

// TestUserResponse represents a test user creation response
type TestUserResponse struct {
	User        *UserProfileResponse
	AccessToken string
}

// CreateTestUser creates a test user for OWASP security tests
func (suite *APITestSuite) CreateTestUser(role, phone string) (*TestUserResponse, error) {
	// Generate PKCE values
	codeVerifier := fmt.Sprintf("test-verifier-%s", phone)
	codeChallenge := utils.GenerateS256Challenge(codeVerifier)

	// Step 1: Initiate registration
	initiateReq := map[string]interface{}{
		"phone_number":          phone,
		"display_name":          fmt.Sprintf("Test %s User", role),
		"client_id":             "test-client",
		"code_challenge":        codeChallenge,
		"code_challenge_method": "S256",
		"state":                 fmt.Sprintf("test-state-%s", role),
		"preferred_lang":        "en",
	}

	rec, err := suite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/register/phone/initiate", initiateReq)
	if err != nil {
		return nil, err
	}
	if rec.Code != http.StatusOK && rec.Code != http.StatusCreated {
		return nil, fmt.Errorf("registration initiate failed: %d - %s", rec.Code, rec.Body.String())
	}

	// Step 2: Verify OTP
	verifyReq := map[string]interface{}{
		"otp":           "123456", // Test OTP
		"code_verifier": codeVerifier,
		"state":         fmt.Sprintf("test-state-%s", role),
	}

	rec, err = suite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/register/phone/verify", verifyReq)
	if err != nil {
		return nil, err
	}
	if rec.Code != http.StatusOK {
		return nil, fmt.Errorf("registration verify failed: %d - %s", rec.Code, rec.Body.String())
	}

	// Parse response to get access token
	var authResponse map[string]interface{}
	if err := json.Unmarshal(rec.Body.Bytes(), &authResponse); err != nil {
		return nil, err
	}

	accessToken, ok := authResponse["access_token"].(string)
	if !ok {
		return nil, fmt.Errorf("no access token in response")
	}

	// Get user profile
	rec, err = suite.MakeRequest("GET", "/api/v1/users/profile", nil, accessToken)
	if err != nil {
		return nil, err
	}

	var userProfile UserProfileResponse
	if err := json.Unmarshal(rec.Body.Bytes(), &userProfile); err != nil {
		return nil, err
	}

	return &TestUserResponse{
		User:        &userProfile,
		AccessToken: accessToken,
	}, nil
}

// TestOrganizationResponse represents a test organization
type TestOrganizationResponse struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
}

// CreateTestOrganization creates a test organization for OWASP security tests
func (suite *APITestSuite) CreateTestOrganization(name, token string) (*TestOrganizationResponse, error) {
	orgCreateReq := map[string]interface{}{
		"name":        name,
		"description": fmt.Sprintf("Test organization %s", name),
		"email":       fmt.Sprintf("%<EMAIL>", name),
		"phone":       fmt.Sprintf("+1555%07d", time.Now().UnixNano()%10000000),
		"address":     "123 Test Street",
		"city":        "Test City",
		"country":     "Test Country",
	}

	rec, err := suite.MakeRequest("POST", "/api/v1/organizations", orgCreateReq, token)
	if err != nil {
		return nil, err
	}
	if rec.Code != http.StatusCreated && rec.Code != http.StatusOK {
		return nil, fmt.Errorf("organization creation failed: %d - %s", rec.Code, rec.Body.String())
	}

	var orgResponse TestOrganizationResponse
	if err := json.Unmarshal(rec.Body.Bytes(), &orgResponse); err != nil {
		return nil, err
	}

	return &orgResponse, nil
}

// MakeAuthenticatedRequestWithToken is an alias for MakeRequest to match OWASP test expectations
func (suite *APITestSuite) MakeAuthenticatedRequestWithToken(method, path string, body interface{}, token string) (*httptest.ResponseRecorder, error) {
	return suite.MakeRequest(method, path, body, token)
}

// StartTestServer starts a test HTTP server and returns the server instance and URL
func (suite *APITestSuite) StartTestServer() (*httptest.Server, string) {
	// Create a test server using the Echo instance
	testServer := httptest.NewServer(suite.e)

	// Return both the server instance and its URL
	return testServer, testServer.URL
}
