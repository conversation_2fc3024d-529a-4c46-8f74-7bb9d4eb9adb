package integration

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/netip"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/services"
	"Membership-SAAS-System-Backend/internal/token"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// Helper functions
func stringPtr(s string) *string {
	return &s
}

func timePtr(t time.Time) *time.Time {
	return &t
}

func TestAuditLogEndpoints(t *testing.T) {
	// Setup test suite
	apiSuite := SetupAPITest(t)
	defer apiSuite.TeardownAPITest(t)

	ctx := context.Background()

	// Get the store from the server
	store := apiSuite.Server.ServiceContainer().Store

	// Create test users with different roles
	// For super admin, we'll use the seeded user
	superAdminID := uuid.MustParse("00000000-0000-0000-0000-000000000001") // Seeded super admin
	defaultOrgID := uuid.MustParse("00000000-0000-0000-0000-000000000002") // Default org

	// Create a staff user
	staffUser, err := store.CreateStaffUserWithEmailPassword(ctx, db.CreateStaffUserWithEmailPasswordParams{
		DisplayName:                 "Test Staff User",
		Email:                       stringPtr("<EMAIL>"),
		EmailVerifiedAt:             timePtr(time.Now()),
		HashedPassword:              stringPtr("hashed_password"),
		InterfaceLanguage:           "en",
		CommunicationLanguage:       "en",
		EnableAppNotifications:      true,
		EnableWhatsappNotifications: true,
		EnableSmsNotifications:      true,
		EnableEmailNotifications:    true,
		PhoneOtpChannel:             "whatsapp",
	})
	require.NoError(t, err)

	// Create a regular user
	regularPhone := "+1234567892"
	regularUser, err := store.CreateUserWithPhone(ctx, db.CreateUserWithPhoneParams{
		Phone:                       &regularPhone,
		DisplayName:                 "Test Regular User",
		PhoneVerifiedAt:             timePtr(time.Now()),
		InterfaceLanguage:           "en",
		CommunicationLanguage:       "en",
		EnableAppNotifications:      true,
		EnableWhatsappNotifications: true,
		EnableSmsNotifications:      true,
		EnableEmailNotifications:    true,
		PhoneOtpChannel:             "whatsapp",
	})
	require.NoError(t, err)

	// Create test organization owner
	ownerPhone := "+1234567893"
	orgOwner, err := store.CreateUserWithPhone(ctx, db.CreateUserWithPhoneParams{
		Phone:                       &ownerPhone,
		DisplayName:                 "Test Org Owner",
		PhoneVerifiedAt:             timePtr(time.Now()),
		InterfaceLanguage:           "en",
		CommunicationLanguage:       "en",
		EnableAppNotifications:      true,
		EnableWhatsappNotifications: true,
		EnableSmsNotifications:      true,
		EnableEmailNotifications:    true,
		PhoneOtpChannel:             "whatsapp",
	})
	require.NoError(t, err)

	// Create test organization
	orgDesc := "Test organization for audit log tests"
	org, err := store.CreateOrganization(ctx, db.CreateOrganizationParams{
		Name:         "Test Organization for Audit",
		Description:  &orgDesc,
		OwnerUserID:  orgOwner.ID,
		IsDefaultOrg: false,
		Status:       "active",
	})
	require.NoError(t, err)

	// Add members to organization
	_, err = store.AddUserToOrganization(ctx, db.AddUserToOrganizationParams{
		UserID:               regularUser.ID,
		OrganizationID:       org.ID,
		Role:                 "member",
		IsActive:             true,
		NotificationsEnabled: true,
	})
	require.NoError(t, err)

	// Create an organization admin
	adminPhone := "+1234567894"
	orgAdmin, err := store.CreateUserWithPhone(ctx, db.CreateUserWithPhoneParams{
		Phone:                       &adminPhone,
		DisplayName:                 "Test Org Admin",
		PhoneVerifiedAt:             timePtr(time.Now()),
		InterfaceLanguage:           "en",
		CommunicationLanguage:       "en",
		EnableAppNotifications:      true,
		EnableWhatsappNotifications: true,
		EnableSmsNotifications:      true,
		EnableEmailNotifications:    true,
		PhoneOtpChannel:             "whatsapp",
	})
	require.NoError(t, err)

	_, err = store.AddUserToOrganization(ctx, db.AddUserToOrganizationParams{
		UserID:               orgAdmin.ID,
		OrganizationID:       org.ID,
		Role:                 "admin",
		IsActive:             true,
		NotificationsEnabled: true,
	})
	require.NoError(t, err)

	// Create some audit logs for testing
	// Note: We directly create audit logs in the database to bypass async logging in tests
	ipAddr1, _ := netip.ParseAddr("***********")
	ipAddr2, _ := netip.ParseAddr("***********")
	userAgent := "Test Agent"

	// Platform-level audit logs
	for i := 0; i < 5; i++ {
		details, _ := json.Marshal(map[string]interface{}{"method": "phone", "index": i})
		_, err := store.CreateEnhancedAuditLog(ctx, db.CreateEnhancedAuditLogParams{
			UserID:       superAdminID,
			Action:       services.AuditActionLogin,
			Category:     db.AuditCategoryAuthentication,
			Severity:     db.NullAuditSeverity{AuditSeverity: db.AuditSeverityInfo, Valid: true},
			ResourceType: "user",
			Details:      details,
			IpAddress:    &ipAddr1,
			UserAgent:    &userAgent,
		})
		require.NoError(t, err)
	}

	// Organization-specific audit logs
	for i := 0; i < 3; i++ {
		details, _ := json.Marshal(map[string]interface{}{"event_name": fmt.Sprintf("Event %d", i)})
		_, err := store.CreateEnhancedAuditLog(ctx, db.CreateEnhancedAuditLogParams{
			UserID:         orgOwner.ID,
			OrganizationID: &org.ID,
			Category:       db.AuditCategoryContentManagement,
			Severity:       db.NullAuditSeverity{AuditSeverity: db.AuditSeverityInfo, Valid: true},
			Action:         services.AuditActionEventCreate,
			ResourceType:   "event",
			ResourceID:     &org.ID, // Using org ID as resource ID for testing
			Details:        details,
			IpAddress:      &ipAddr2,
			UserAgent:      &userAgent,
		})
		require.NoError(t, err)
	}

	// Helper function to generate test JWT token
	generateToken := func(userID uuid.UUID, platformRole string, activeOrg *token.OrgContext) string {
		claims := &token.EnhancedClaims{
			UserID:       userID,
			PlatformRole: platformRole,
			ActiveOrg:    activeOrg,
			RegisteredClaims: jwt.RegisteredClaims{
				ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
				IssuedAt:  jwt.NewNumericDate(time.Now()),
				NotBefore: jwt.NewNumericDate(time.Now()),
				Issuer:    "MembershipSaas",
				Subject:   userID.String(),
			},
		}

		jwtToken := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
		tokenString, err := jwtToken.SignedString([]byte("test-secret-key-for-testing"))
		require.NoError(t, err)
		return tokenString
	}

	t.Run("GetPlatformAuditLogs", func(t *testing.T) {
		t.Run("SuperAdminAccess", func(t *testing.T) {
			// Generate super admin token
			superAdminToken := generateToken(superAdminID, "super_admin", &token.OrgContext{
				ID:   defaultOrgID.String(),
				Role: "owner",
			})

			// Make request
			rec, err := apiSuite.MakeRequest("GET", "/api/v1/audit-logs?page=1&page_size=10", nil, superAdminToken)
			require.NoError(t, err)

			// Debug: print response if not OK
			if rec.Code != http.StatusOK {
				t.Logf("Response code: %d, body: %s", rec.Code, rec.Body.String())
			}
			assert.Equal(t, http.StatusOK, rec.Code)

			// Parse response
			var result payloads.PaginatedAuditLogsResponse
			err = json.Unmarshal(rec.Body.Bytes(), &result)
			require.NoError(t, err)

			t.Logf("Got %d audit logs, total: %d", len(result.Data), result.Pagination.TotalItems)
			assert.GreaterOrEqual(t, len(result.Data), 5)
			assert.GreaterOrEqual(t, result.Pagination.TotalItems, 8)
		})

		t.Run("StaffAccess", func(t *testing.T) {
			// Generate staff token
			staffToken := generateToken(staffUser.ID, "staff", &token.OrgContext{
				ID:   defaultOrgID.String(),
				Role: "admin",
			})

			// Make request
			rec, err := apiSuite.MakeRequest("GET", "/api/v1/audit-logs?page=1&page_size=10", nil, staffToken)
			require.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)

			// Parse response
			var result payloads.PaginatedAuditLogsResponse
			err = json.Unmarshal(rec.Body.Bytes(), &result)
			require.NoError(t, err)

			assert.GreaterOrEqual(t, len(result.Data), 5)
		})

		t.Run("RegularUserDenied", func(t *testing.T) {
			// Generate regular user token
			regularToken := generateToken(regularUser.ID, "user", &token.OrgContext{
				ID:   defaultOrgID.String(),
				Role: "member",
			})

			// Make request
			rec, err := apiSuite.MakeRequest("GET", "/api/v1/audit-logs", nil, regularToken)
			require.NoError(t, err)
			assert.Equal(t, http.StatusForbidden, rec.Code)
		})

		t.Run("WithFilters", func(t *testing.T) {
			// Generate super admin token
			superAdminToken := generateToken(superAdminID, "super_admin", &token.OrgContext{
				ID:   defaultOrgID.String(),
				Role: "owner",
			})

			// Test action filter
			rec, err := apiSuite.MakeRequest("GET", "/api/v1/audit-logs?action=login&page=1&page_size=10", nil, superAdminToken)
			require.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)

			var result payloads.PaginatedAuditLogsResponse
			err = json.Unmarshal(rec.Body.Bytes(), &result)
			require.NoError(t, err)

			// All results should have login action
			for _, log := range result.Data {
				assert.Equal(t, services.AuditActionLogin, log.Action)
			}
		})
	})

	t.Run("GetAuditLogByID", func(t *testing.T) {
		// Generate super admin token
		superAdminToken := generateToken(superAdminID, "super_admin", &token.OrgContext{
			ID:   defaultOrgID.String(),
			Role: "owner",
		})

		// First, get an audit log ID
		rec, err := apiSuite.MakeRequest("GET", "/api/v1/audit-logs?page=1&page_size=1", nil, superAdminToken)
		require.NoError(t, err)
		require.Equal(t, http.StatusOK, rec.Code)

		var listResult payloads.PaginatedAuditLogsResponse
		err = json.Unmarshal(rec.Body.Bytes(), &listResult)
		require.NoError(t, err)
		require.NotEmpty(t, listResult.Data)

		logID := listResult.Data[0].ID

		t.Run("SuperAdminAccess", func(t *testing.T) {
			rec, err := apiSuite.MakeRequest("GET", fmt.Sprintf("/api/v1/audit-logs/%s", logID), nil, superAdminToken)
			require.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)

			var result payloads.AuditLogDetailResponse
			err = json.Unmarshal(rec.Body.Bytes(), &result)
			require.NoError(t, err)

			assert.Equal(t, logID, result.ID)
			assert.NotEmpty(t, result.Action)
		})

		t.Run("RegularUserDenied", func(t *testing.T) {
			regularToken := generateToken(regularUser.ID, "user", &token.OrgContext{
				ID:   defaultOrgID.String(),
				Role: "member",
			})
			rec, err := apiSuite.MakeRequest("GET", fmt.Sprintf("/api/v1/audit-logs/%s", logID), nil, regularToken)
			require.NoError(t, err)
			assert.Equal(t, http.StatusForbidden, rec.Code)
		})

		t.Run("InvalidID", func(t *testing.T) {
			rec, err := apiSuite.MakeRequest("GET", "/api/v1/audit-logs/invalid-uuid", nil, superAdminToken)
			require.NoError(t, err)
			assert.Equal(t, http.StatusBadRequest, rec.Code)
		})

		t.Run("NotFound", func(t *testing.T) {
			nonExistentID := uuid.New()
			rec, err := apiSuite.MakeRequest("GET", fmt.Sprintf("/api/v1/audit-logs/%s", nonExistentID), nil, superAdminToken)
			require.NoError(t, err)
			assert.Equal(t, http.StatusNotFound, rec.Code)
		})
	})

	t.Run("GetOrganizationAuditLogs", func(t *testing.T) {
		t.Run("SuperAdminAccess", func(t *testing.T) {
			superAdminToken := generateToken(superAdminID, "super_admin", &token.OrgContext{
				ID:   defaultOrgID.String(),
				Role: "owner",
			})
			rec, err := apiSuite.MakeRequest("GET", fmt.Sprintf("/api/v1/organizations/%s/audit-logs?page=1&page_size=10", org.ID), nil, superAdminToken)
			require.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)

			var result payloads.PaginatedAuditLogsResponse
			err = json.Unmarshal(rec.Body.Bytes(), &result)
			require.NoError(t, err)

			assert.GreaterOrEqual(t, len(result.Data), 3)
			// All results should belong to the organization
			for _, log := range result.Data {
				assert.Equal(t, org.ID, *log.OrganizationID)
			}
		})

		t.Run("OrgOwnerAccess", func(t *testing.T) {
			// Generate token with org context
			ownerToken := generateToken(orgOwner.ID, "user", &token.OrgContext{
				ID:   org.ID.String(),
				Role: "owner",
			})

			rec, err := apiSuite.MakeRequest("GET", fmt.Sprintf("/api/v1/organizations/%s/audit-logs?page=1&page_size=10", org.ID), nil, ownerToken)
			require.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)

			var result payloads.PaginatedAuditLogsResponse
			err = json.Unmarshal(rec.Body.Bytes(), &result)
			require.NoError(t, err)

			assert.GreaterOrEqual(t, len(result.Data), 3)
		})

		t.Run("OrgAdminAccess", func(t *testing.T) {
			// Generate token with org context
			adminToken := generateToken(orgAdmin.ID, "user", &token.OrgContext{
				ID:   org.ID.String(),
				Role: "admin",
			})

			rec, err := apiSuite.MakeRequest("GET", fmt.Sprintf("/api/v1/organizations/%s/audit-logs?page=1&page_size=10", org.ID), nil, adminToken)
			require.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})

		t.Run("OrgMemberDenied", func(t *testing.T) {
			// Generate token with org context
			memberToken := generateToken(regularUser.ID, "user", &token.OrgContext{
				ID:   org.ID.String(),
				Role: "member",
			})

			rec, err := apiSuite.MakeRequest("GET", fmt.Sprintf("/api/v1/organizations/%s/audit-logs", org.ID), nil, memberToken)
			require.NoError(t, err)
			assert.Equal(t, http.StatusForbidden, rec.Code)
		})

		t.Run("NonMemberDenied", func(t *testing.T) {
			// Create another user not in the organization
			otherPhone := "+1234567895"
			otherUser, err := store.CreateUserWithPhone(ctx, db.CreateUserWithPhoneParams{
				Phone:                       &otherPhone,
				DisplayName:                 "Other User",
				PhoneVerifiedAt:             timePtr(time.Now()),
				InterfaceLanguage:           "en",
				CommunicationLanguage:       "en",
				EnableAppNotifications:      true,
				EnableWhatsappNotifications: true,
				EnableSmsNotifications:      true,
				EnableEmailNotifications:    true,
				PhoneOtpChannel:             "whatsapp",
			})
			require.NoError(t, err)

			otherToken := generateToken(otherUser.ID, "user", &token.OrgContext{
				ID:   defaultOrgID.String(),
				Role: "member",
			})

			rec, err := apiSuite.MakeRequest("GET", fmt.Sprintf("/api/v1/organizations/%s/audit-logs", org.ID), nil, otherToken)
			require.NoError(t, err)
			assert.Equal(t, http.StatusForbidden, rec.Code)
		})

		t.Run("StaffDenied", func(t *testing.T) {
			// Staff users cannot access organization audit logs
			staffToken := generateToken(staffUser.ID, "staff", &token.OrgContext{
				ID:   defaultOrgID.String(),
				Role: "admin",
			})
			rec, err := apiSuite.MakeRequest("GET", fmt.Sprintf("/api/v1/organizations/%s/audit-logs", org.ID), nil, staffToken)
			require.NoError(t, err)
			assert.Equal(t, http.StatusForbidden, rec.Code)
		})
	})

	t.Run("SearchAuditLogs", func(t *testing.T) {
		t.Run("SuperAdminOnlyAccess", func(t *testing.T) {
			superAdminToken := generateToken(superAdminID, "super_admin", &token.OrgContext{
				ID:   defaultOrgID.String(),
				Role: "owner",
			})

			searchReq := payloads.AuditLogSearchRequest{
				Filters: payloads.AuditLogFilters{
					Actions: []string{services.AuditActionLogin},
				},
				Pagination: &payloads.AuditLogPagination{
					Page:     1,
					PageSize: 10,
				},
			}

			rec, err := apiSuite.MakeRequest("POST", "/api/v1/audit-logs/search", searchReq, superAdminToken)
			require.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)

			var result payloads.PaginatedAuditLogsResponse
			err = json.Unmarshal(rec.Body.Bytes(), &result)
			require.NoError(t, err)

			// All results should match the search criteria
			for _, log := range result.Data {
				assert.Equal(t, services.AuditActionLogin, log.Action)
			}
		})

		t.Run("StaffDenied", func(t *testing.T) {
			// Staff users cannot use advanced search
			staffToken := generateToken(staffUser.ID, "staff", &token.OrgContext{
				ID:   defaultOrgID.String(),
				Role: "admin",
			})

			searchReq := payloads.AuditLogSearchRequest{
				Filters: payloads.AuditLogFilters{
					Actions: []string{services.AuditActionLogin},
				},
			}

			rec, err := apiSuite.MakeRequest("POST", "/api/v1/audit-logs/search", searchReq, staffToken)
			require.NoError(t, err)
			assert.Equal(t, http.StatusForbidden, rec.Code)
		})

		t.Run("RegularUserDenied", func(t *testing.T) {
			regularToken := generateToken(regularUser.ID, "user", &token.OrgContext{
				ID:   defaultOrgID.String(),
				Role: "member",
			})

			searchReq := payloads.AuditLogSearchRequest{
				Filters: payloads.AuditLogFilters{
					Actions: []string{services.AuditActionLogin},
				},
			}

			rec, err := apiSuite.MakeRequest("POST", "/api/v1/audit-logs/search", searchReq, regularToken)
			require.NoError(t, err)
			assert.Equal(t, http.StatusForbidden, rec.Code)
		})
	})

	t.Run("Pagination", func(t *testing.T) {
		superAdminToken := generateToken(superAdminID, "super_admin", &token.OrgContext{
			ID:   defaultOrgID.String(),
			Role: "owner",
		})

		// Test first page
		rec, err := apiSuite.MakeRequest("GET", "/api/v1/audit-logs?page=1&page_size=2", nil, superAdminToken)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, rec.Code)

		var page1 payloads.PaginatedAuditLogsResponse
		err = json.Unmarshal(rec.Body.Bytes(), &page1)
		require.NoError(t, err)

		assert.Equal(t, 2, len(page1.Data))
		assert.Equal(t, 1, page1.Pagination.CurrentPage)
		assert.Equal(t, 2, page1.Pagination.Limit)
		assert.Greater(t, page1.Pagination.TotalPages, 1)

		// Test second page
		rec, err = apiSuite.MakeRequest("GET", "/api/v1/audit-logs?page=2&page_size=2", nil, superAdminToken)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, rec.Code)

		var page2 payloads.PaginatedAuditLogsResponse
		err = json.Unmarshal(rec.Body.Bytes(), &page2)
		require.NoError(t, err)

		assert.Equal(t, 2, len(page2.Data))
		assert.Equal(t, 2, page2.Pagination.CurrentPage)

		// Ensure different data on different pages
		if len(page1.Data) > 0 && len(page2.Data) > 0 {
			assert.NotEqual(t, page1.Data[0].ID, page2.Data[0].ID)
		}
	})
}
