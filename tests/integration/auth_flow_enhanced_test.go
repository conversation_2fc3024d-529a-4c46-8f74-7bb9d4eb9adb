package integration

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/internal/authorization"
	"Membership-SAAS-System-Backend/internal/utils"

	"github.com/golang-jwt/jwt/v5"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestEnhancedAuthenticationFlows tests all three PKCE flows with new JWT format
func TestEnhancedAuthenticationFlows(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration tests in short mode")
	}

	// Set up API test suite
	suite := SetupAPITest(t)
	defer suite.TeardownAPITest(t)

	t.Run("PhoneOTPWithJWTValidation", func(t *testing.T) {
		testPhoneOTPFlowWithJWTValidation(t, suite)
	})

	t.Run("StaffLoginWithJWTValidation", func(t *testing.T) {
		testStaffLoginFlowWithJWTValidation(t, suite)
	})

	t.Run("UserRegistrationWithJWTValidation", func(t *testing.T) {
		testUserRegistrationFlowWithJWTValidation(t, suite)
	})
}

// testPhoneOTPFlowWithJWTValidation tests phone OTP flow and validates JWT format
func testPhoneOTPFlowWithJWTValidation(t *testing.T, suite *APITestSuite) {
	// Generate PKCE values
	codeVerifier := "test-verifier-phone-otp-enhanced"
	codeChallenge := utils.GenerateS256Challenge(codeVerifier)
	phoneNumber := "+1234567892" // Use a different number to avoid conflicts

	// Step 1: Check phone number
	checkReq := map[string]interface{}{
		"phone": phoneNumber,
	}
	rec, err := suite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/phone/check", checkReq)
	require.NoError(t, err)

	if rec.Code != http.StatusOK {
		t.Logf("Phone check returned status %d: %s", rec.Code, rec.Body.String())
		// Might be expected if endpoint doesn't exist
		if rec.Code == http.StatusNotFound {
			t.Skip("Phone check endpoint not found")
			return
		}
	}

	// Step 2: Initiate OTP
	initiateReq := map[string]interface{}{
		"phone":                 phoneNumber,
		"client_id":             "test-client-enhanced",
		"code_challenge":        codeChallenge,
		"code_challenge_method": "S256",
		"state":                 "test-state-phone-otp-enhanced",
	}
	rec, err = suite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/phone/otp/initiate", initiateReq)
	require.NoError(t, err)

	if rec.Code != http.StatusOK && rec.Code != http.StatusCreated {
		t.Logf("OTP initiate returned status %d: %s", rec.Code, rec.Body.String())
		if rec.Code == http.StatusNotFound {
			t.Skip("OTP initiate endpoint not found")
			return
		}
	}

	// Step 3: Verify OTP
	verifyReq := map[string]interface{}{
		"otp":           "123456", // Mock OTP
		"code_verifier": codeVerifier,
		"state":         "test-state-phone-otp-enhanced",
	}
	rec, err = suite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/phone/otp/verify", verifyReq)
	require.NoError(t, err)

	if rec.Code == http.StatusOK {
		// Parse response and validate JWT
		var response struct {
			AccessToken  string `json:"access_token"`
			RefreshToken string `json:"refresh_token"`
			User         struct {
				ID           string `json:"id"`
				DisplayName  string `json:"display_name"`
				PlatformRole string `json:"platform_role"`
			} `json:"user"`
		}

		err := json.Unmarshal(rec.Body.Bytes(), &response)
		require.NoError(t, err)
		require.NotEmpty(t, response.AccessToken)

		// Validate JWT structure
		validateJWTFormat(t, response.AccessToken, authorization.PlatformRoleUser)

		t.Logf("Phone OTP flow completed successfully with user: %+v", response.User)
	} else {
		t.Logf("OTP verify returned status %d: %s", rec.Code, rec.Body.String())
		// This might be expected if the flow isn't fully implemented
		assert.Contains(t, []int{http.StatusNotFound, http.StatusBadRequest, http.StatusUnauthorized}, rec.Code)
	}
}

// testStaffLoginFlowWithJWTValidation tests staff login flow and validates JWT format
func testStaffLoginFlowWithJWTValidation(t *testing.T, suite *APITestSuite) {
	// Generate PKCE values
	codeVerifier := "test-verifier-staff-login-enhanced"
	codeChallenge := utils.GenerateS256Challenge(codeVerifier)
	email := "<EMAIL>"
	password := "123456"

	// Step 1: Initiate staff login
	initiateReq := map[string]interface{}{
		"email":                 email,
		"client_id":             "test-client-staff-enhanced",
		"code_challenge":        codeChallenge,
		"code_challenge_method": "S256",
		"state":                 "test-state-staff-login-enhanced",
	}
	rec, err := suite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/staff/login/initiate", initiateReq)
	require.NoError(t, err)

	if rec.Code != http.StatusOK && rec.Code != http.StatusCreated {
		t.Logf("Staff login initiate returned status %d: %s", rec.Code, rec.Body.String())
		if rec.Code == http.StatusNotFound {
			t.Skip("Staff login initiate endpoint not found")
			return
		}
	}

	// Step 2: Verify staff login
	verifyReq := map[string]interface{}{
		"email":         email,
		"password":      password,
		"code_verifier": codeVerifier,
		"state":         "test-state-staff-login-enhanced",
	}
	rec, err = suite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/staff/login/verify", verifyReq)
	require.NoError(t, err)

	if rec.Code == http.StatusOK {
		// Parse response and validate JWT
		var response struct {
			AccessToken  string `json:"access_token"`
			RefreshToken string `json:"refresh_token"`
			User         struct {
				ID           string `json:"id"`
				DisplayName  string `json:"display_name"`
				PlatformRole string `json:"platform_role"`
			} `json:"user"`
		}

		err := json.Unmarshal(rec.Body.Bytes(), &response)
		require.NoError(t, err)
		require.NotEmpty(t, response.AccessToken)

		// Validate JWT structure - should be super_<NAME_EMAIL>
		validateJWTFormat(t, response.AccessToken, authorization.PlatformRoleSuperAdmin)

		t.Logf("Staff login flow completed successfully with user: %+v", response.User)
	} else {
		t.Logf("Staff login verify returned status %d: %s", rec.Code, rec.Body.String())
		// This might be expected if the flow isn't fully implemented
		assert.Contains(t, []int{http.StatusNotFound, http.StatusBadRequest, http.StatusUnauthorized}, rec.Code)
	}
}

// testUserRegistrationFlowWithJWTValidation tests user registration flow and validates JWT format
func testUserRegistrationFlowWithJWTValidation(t *testing.T, suite *APITestSuite) {
	// Generate PKCE values
	codeVerifier := "test-verifier-registration-enhanced"
	codeChallenge := utils.GenerateS256Challenge(codeVerifier)
	phoneNumber := "+1234567893" // Use a different number

	// Step 1: Initiate registration
	initiateReq := map[string]interface{}{
		"phone":                 phoneNumber,
		"display_name":          "Test Registration User",
		"client_id":             "test-client-registration-enhanced",
		"code_challenge":        codeChallenge,
		"code_challenge_method": "S256",
		"state":                 "test-state-registration-enhanced",
	}
	rec, err := suite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/register/phone/initiate", initiateReq)
	require.NoError(t, err)

	if rec.Code != http.StatusOK && rec.Code != http.StatusCreated {
		t.Logf("Registration initiate returned status %d: %s", rec.Code, rec.Body.String())
		if rec.Code == http.StatusNotFound {
			t.Skip("Registration initiate endpoint not found")
			return
		}
	}

	// Step 2: Verify registration
	verifyReq := map[string]interface{}{
		"otp":           "123456", // Mock OTP
		"code_verifier": codeVerifier,
		"state":         "test-state-registration-enhanced",
	}
	rec, err = suite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/register/phone/verify", verifyReq)
	require.NoError(t, err)

	if rec.Code == http.StatusOK || rec.Code == http.StatusCreated {
		// Parse response and validate JWT
		var response struct {
			AccessToken  string `json:"access_token"`
			RefreshToken string `json:"refresh_token"`
			User         struct {
				ID           string `json:"id"`
				DisplayName  string `json:"display_name"`
				PlatformRole string `json:"platform_role"`
			} `json:"user"`
		}

		err := json.Unmarshal(rec.Body.Bytes(), &response)
		require.NoError(t, err)
		require.NotEmpty(t, response.AccessToken)

		// Validate JWT structure - should be 'member' for regular registration
		validateJWTFormat(t, response.AccessToken, authorization.PlatformRoleUser)

		t.Logf("Registration flow completed successfully with user: %+v", response.User)
	} else {
		t.Logf("Registration verify returned status %d: %s", rec.Code, rec.Body.String())
		// This might be expected if the flow isn't fully implemented
		assert.Contains(t, []int{http.StatusNotFound, http.StatusBadRequest, http.StatusConflict}, rec.Code)
	}
}

// validateJWTFormat validates that a JWT token has the correct new format
func validateJWTFormat(t *testing.T, tokenString, expectedPlatformRole string) {
	// Parse token
	parsedToken, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// Validate signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte("test-secret-key-for-testing"), nil
	})
	require.NoError(t, err)
	require.True(t, parsedToken.Valid)

	// Validate claims structure
	claims, ok := parsedToken.Claims.(jwt.MapClaims)
	require.True(t, ok)

	// Check required fields exist
	require.Contains(t, claims, "user_id", "JWT should contain user_id")
	require.Contains(t, claims, "platform_role", "JWT should contain platform_role")
	require.Contains(t, claims, "active_org", "JWT should contain active_org")
	require.Contains(t, claims, "exp", "JWT should contain exp")

	// Validate user_id format
	userIDStr, ok := claims["user_id"].(string)
	require.True(t, ok, "user_id should be string")
	require.NotEmpty(t, userIDStr, "user_id should not be empty")

	// Validate platform_role
	platformRole, ok := claims["platform_role"].(string)
	require.True(t, ok, "platform_role should be string")
	if expectedPlatformRole != "" {
		assert.Equal(t, expectedPlatformRole, platformRole, "platform_role should match expected")
	}
	validRoles := []string{authorization.PlatformRoleSuperAdmin, authorization.PlatformRoleStaff, authorization.PlatformRoleUser}
	assert.Contains(t, validRoles, platformRole, "platform_role should be valid")

	// Validate active_org structure
	activeOrg, ok := claims["active_org"].(map[string]interface{})
	require.True(t, ok, "active_org should be object")
	require.Contains(t, activeOrg, "id", "active_org should contain id")
	require.Contains(t, activeOrg, "role", "active_org should contain role")

	// Validate organization ID format
	orgIDStr, ok := activeOrg["id"].(string)
	require.True(t, ok, "active_org.id should be string")
	require.NotEmpty(t, orgIDStr, "active_org.id should not be empty")

	// Validate organization role
	orgRole, ok := activeOrg["role"].(string)
	require.True(t, ok, "active_org.role should be string")
	validOrgRoles := []string{authorization.OrgRoleOwner, authorization.OrgRoleAdmin, authorization.OrgRoleStaff, authorization.OrgRoleMember}
	assert.Contains(t, validOrgRoles, orgRole, "active_org.role should be valid")

	// Validate expiration
	exp, ok := claims["exp"].(float64)
	require.True(t, ok, "exp should be number")
	assert.Greater(t, exp, float64(time.Now().Unix()), "token should not be expired")

	// Ensure platform_role is present
	assert.Contains(t, claims, "platform_role", "JWT should contain platform_role field")

	t.Logf("JWT validation passed - platform_role: %s, org_role: %s", platformRole, orgRole)
}

// TestTokenRefreshWithNewFormat tests token refresh with new JWT format
func TestTokenRefreshWithNewFormat(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration tests in short mode")
	}

	// Set up API test suite
	suite := SetupAPITest(t)
	defer suite.TeardownAPITest(t)

	// First, we need a refresh token. For this test, we'll create one manually
	// In a real scenario, this would come from a login flow

	// Create a refresh token request (this endpoint might not exist yet)
	refreshReq := map[string]interface{}{
		"refresh_token": "mock-refresh-token-for-testing",
	}

	rec, err := suite.MakeUnauthenticatedRequest("POST", "/api/v1/auth/refresh", refreshReq)
	require.NoError(t, err)

	if rec.Code == http.StatusOK {
		var response struct {
			AccessToken string `json:"access_token"`
		}

		err := json.Unmarshal(rec.Body.Bytes(), &response)
		require.NoError(t, err)
		require.NotEmpty(t, response.AccessToken)

		// Validate that the refreshed token has the new format
		validateJWTFormat(t, response.AccessToken, "")

		t.Log("Token refresh with new format successful")
	} else {
		t.Logf("Token refresh returned status %d: %s", rec.Code, rec.Body.String())
		// This is expected if the endpoint doesn't exist yet
		assert.Contains(t, []int{http.StatusNotFound, http.StatusBadRequest, http.StatusUnauthorized}, rec.Code)
	}
}

// TestOrganizationSwitchingWithRoles tests organization switching preserves roles correctly
func TestOrganizationSwitchingWithRoles(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration tests in short mode")
	}

	// Set up API test suite
	suite := SetupAPITest(t)
	defer suite.TeardownAPITest(t)

	// Test switching organizations and validating role changes
	orgID := suite.orgID

	switchReq := bytes.NewBufferString("")
	req := httptest.NewRequest(http.MethodPost, fmt.Sprintf("/api/v1/auth/switch-org/%s", orgID), switchReq)
	req.Header.Set(echo.HeaderAuthorization, "Bearer "+suite.adminToken)
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)
	rec := httptest.NewRecorder()

	suite.e.ServeHTTP(rec, req)

	if rec.Code == http.StatusOK {
		var response struct {
			AccessToken string `json:"access_token"`
		}

		err := json.Unmarshal(rec.Body.Bytes(), &response)
		require.NoError(t, err)
		require.NotEmpty(t, response.AccessToken)

		// Validate the new token
		validateJWTFormat(t, response.AccessToken, "")

		// Parse and check that the active_org was updated
		parsedToken, err := jwt.Parse(response.AccessToken, func(token *jwt.Token) (interface{}, error) {
			return []byte("test-secret-key-for-testing"), nil
		})
		require.NoError(t, err)

		claims, ok := parsedToken.Claims.(jwt.MapClaims)
		require.True(t, ok)

		activeOrg := claims["active_org"].(map[string]interface{})
		assert.Equal(t, orgID, activeOrg["id"].(string))

		t.Logf("Organization switching successful with role: %s", activeOrg["role"].(string))
	} else {
		t.Logf("Organization switching returned status %d: %s", rec.Code, rec.Body.String())
		// This might be expected if the endpoint isn't implemented yet
		assert.Contains(t, []int{http.StatusNotFound, http.StatusForbidden, http.StatusInternalServerError}, rec.Code)
	}
}
