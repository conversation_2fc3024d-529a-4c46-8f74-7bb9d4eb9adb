package integration

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/db"
	authn "Membership-SAAS-System-Backend/internal/authentication"
	"Membership-SAAS-System-Backend/internal/config"
	"Membership-SAAS-System-Backend/internal/server"
	"Membership-SAAS-System-Backend/internal/testutil"
	"Membership-SAAS-System-Backend/internal/twilio_service"
	"Membership-SAAS-System-Backend/internal/utils/auth"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestAuthenticationFlows(t *testing.T) {
	// Set up test environment
	cfg := config.Load()
	cfg.Twilio.MockOTP = true

	// Create test database with schema
	testDB := testutil.SetupTestDatabase(t)
	defer testDB.Cleanup()
	testDB.LoadSchema(t)

	// Override the database configuration to use test database
	cfg.Database.URL = testDB.URL

	// Create test server
	srv, err := server.New(cfg)
	require.NoError(t, err)

	// Get the store from server's service container
	store := srv.ServiceContainer().Store

	// Set up echo instance with routes
	e := srv.Echo()

	t.Run("Phone OTP Login Flow", func(t *testing.T) {
		ctx := context.Background()

		// Create test user
		userParams := db.CreateUserWithPhoneParams{
			Phone:                       testutil.ToPtr("+1234567890"),
			PhoneVerifiedAt:             testutil.ToPtr(time.Now()),
			DisplayName:                 "Test User",
			InterfaceLanguage:           "en",
			CommunicationLanguage:       "en",
			EnableAppNotifications:      true,
			EnableWhatsappNotifications: false,
			EnableSmsNotifications:      true,
			EnableEmailNotifications:    true,
			PhoneOtpChannel:             "sms",
		}
		user, err := store.CreateUserWithPhone(ctx, userParams)
		require.NoError(t, err)

		// Step 1: Check phone exists
		checkReq := authn.CheckPhoneRequest{
			Phone: *user.Phone,
		}
		checkBody, _ := json.Marshal(checkReq)

		req := httptest.NewRequest(http.MethodPost, "/api/v1/authn/phone/check", bytes.NewReader(checkBody))
		req.Header.Set("Content-Type", "application/json")
		rec := httptest.NewRecorder()

		e.ServeHTTP(rec, req)
		assert.Equal(t, http.StatusOK, rec.Code)

		var checkResp authn.CheckPhoneResponse
		err = json.Unmarshal(rec.Body.Bytes(), &checkResp)
		require.NoError(t, err)
		assert.True(t, checkResp.Exists)

		// Step 2: Initiate OTP
		initiateReq := authn.InitiatePhoneOTPRequest{
			Phone:               *user.Phone,
			ClientID:            "test-client",
			CodeChallenge:       "test-challenge",
			CodeChallengeMethod: "S256",
			State:               "test-state",
		}
		initiateBody, _ := json.Marshal(initiateReq)

		req = httptest.NewRequest(http.MethodPost, "/api/v1/authn/phone/otp/initiate", bytes.NewReader(initiateBody))
		req.Header.Set("Content-Type", "application/json")
		rec = httptest.NewRecorder()

		e.ServeHTTP(rec, req)
		assert.Equal(t, http.StatusOK, rec.Code)

		var initiateResp authn.InitiatePhoneOTPResponse
		err = json.Unmarshal(rec.Body.Bytes(), &initiateResp)
		require.NoError(t, err)
		assert.NotEmpty(t, initiateResp.State)

		// Step 3: Verify OTP
		verifyReq := authn.VerifyPhoneOTPRequest{
			State:        initiateResp.State,
			OTP:          twilio_service.MockOTPCode, // Use the constant from twilio service
			CodeVerifier: "test-verifier",
		}
		verifyBody, _ := json.Marshal(verifyReq)

		req = httptest.NewRequest(http.MethodPost, "/api/v1/authn/phone/otp/verify", bytes.NewReader(verifyBody))
		req.Header.Set("Content-Type", "application/json")
		rec = httptest.NewRecorder()

		e.ServeHTTP(rec, req)
		assert.Equal(t, http.StatusOK, rec.Code)

		var verifyResp authn.VerifyPhoneOTPResponse
		err = json.Unmarshal(rec.Body.Bytes(), &verifyResp)
		require.NoError(t, err)
		assert.NotEmpty(t, verifyResp.AccessToken)
		assert.NotEmpty(t, verifyResp.RefreshToken)
	})

	t.Run("Registration Flow", func(t *testing.T) {
		ctx := context.Background()

		// Step 1: Initiate registration
		registerReq := authn.InitiatePhoneRegistrationRequest{
			Phone:               "+9876543210",
			ClientID:            "test-client",
			CodeChallenge:       "test-challenge",
			CodeChallengeMethod: "S256",
			State:               "test-state",
		}
		registerBody, _ := json.Marshal(registerReq)

		req := httptest.NewRequest(http.MethodPost, "/api/v1/authn/register/phone/initiate", bytes.NewReader(registerBody))
		req.Header.Set("Content-Type", "application/json")
		rec := httptest.NewRecorder()

		e.ServeHTTP(rec, req)
		assert.Equal(t, http.StatusOK, rec.Code)

		var registerResp authn.InitiatePhoneRegistrationResponse
		err := json.Unmarshal(rec.Body.Bytes(), &registerResp)
		require.NoError(t, err)
		assert.NotEmpty(t, registerResp.State)

		// Step 2: Verify registration
		verifyRegReq := authn.VerifyPhoneRegistrationRequest{
			State:        registerResp.State,
			OTP:          twilio_service.MockOTPCode,
			CodeVerifier: "test-verifier",
			DisplayName:  "Test User",
		}
		verifyRegBody, _ := json.Marshal(verifyRegReq)

		req = httptest.NewRequest(http.MethodPost, "/api/v1/authn/register/phone/verify", bytes.NewReader(verifyRegBody))
		req.Header.Set("Content-Type", "application/json")
		rec = httptest.NewRecorder()

		e.ServeHTTP(rec, req)
		assert.Equal(t, http.StatusOK, rec.Code)

		var verifyRegResp authn.VerifyPhoneRegistrationResponse
		err = json.Unmarshal(rec.Body.Bytes(), &verifyRegResp)
		require.NoError(t, err)
		assert.NotEmpty(t, verifyRegResp.AccessToken)
		assert.NotEmpty(t, verifyRegResp.RefreshToken)

		// Verify user was created
		createdUser, err := store.GetUserByPhone(ctx, &registerReq.Phone)
		require.NoError(t, err)
		assert.Equal(t, verifyRegReq.DisplayName, createdUser.DisplayName)
	})

	t.Run("Staff Login Flow", func(t *testing.T) {
		ctx := context.Background()

		// Create staff user with phone first, then update with email
		staffParams := db.CreateUserWithPhoneParams{
			Phone:                       testutil.ToPtr("+3333333333"),
			PhoneVerifiedAt:             testutil.ToPtr(time.Now()),
			DisplayName:                 "Staff User",
			InterfaceLanguage:           "en",
			CommunicationLanguage:       "en",
			EnableAppNotifications:      true,
			EnableWhatsappNotifications: false,
			EnableSmsNotifications:      true,
			EnableEmailNotifications:    true,
			PhoneOtpChannel:             "sms",
		}
		staffUser, err := store.CreateUserWithPhone(ctx, staffParams)
		require.NoError(t, err)

		// Update user with email and platform role
		staffEmail := "<EMAIL>"
		now := time.Now()
		staffUser, err = store.UpdateUserEmailAndVerificationStatus(ctx, db.UpdateUserEmailAndVerificationStatusParams{
			ID:              staffUser.ID,
			Email:           &staffEmail,
			EmailVerifiedAt: &now,
		})
		require.NoError(t, err)

		// Update platform role to staff
		err = store.UpdateUserPlatformRole(ctx, db.UpdateUserPlatformRoleParams{
			ID:           staffUser.ID,
			PlatformRole: "staff",
		})
		require.NoError(t, err)

		// Retrieve updated user
		staffUser, err = store.GetUserByID(ctx, staffUser.ID)
		require.NoError(t, err)

		// Set password for staff user
		hashedPassword, err := auth.HashPassword("securepassword123")
		require.NoError(t, err)
		err = store.UpdateUserPassword(ctx, db.UpdateUserPasswordParams{
			ID:             staffUser.ID,
			HashedPassword: &hashedPassword,
		})
		require.NoError(t, err)

		// Step 1: Initiate staff login
		staffLoginReq := authn.InitiateStaffLoginRequest{
			Email:               *staffUser.Email,
			ClientID:            "test-client",
			CodeChallenge:       "test-challenge",
			CodeChallengeMethod: "S256",
			State:               "test-state",
		}
		staffLoginBody, _ := json.Marshal(staffLoginReq)

		req := httptest.NewRequest(http.MethodPost, "/api/v1/authn/staff/login/initiate", bytes.NewReader(staffLoginBody))
		req.Header.Set("Content-Type", "application/json")
		rec := httptest.NewRecorder()

		e.ServeHTTP(rec, req)
		assert.Equal(t, http.StatusOK, rec.Code)

		var staffLoginResp authn.InitiateStaffLoginResponse
		err = json.Unmarshal(rec.Body.Bytes(), &staffLoginResp)
		require.NoError(t, err)
		assert.NotEmpty(t, staffLoginResp.State)

		// Step 2: Verify staff login
		verifyStaffReq := authn.VerifyStaffLoginRequest{
			State:        staffLoginResp.State,
			Email:        *staffUser.Email,
			Password:     "securepassword123",
			CodeVerifier: "test-verifier",
		}
		verifyStaffBody, _ := json.Marshal(verifyStaffReq)

		req = httptest.NewRequest(http.MethodPost, "/api/v1/authn/staff/login/verify", bytes.NewReader(verifyStaffBody))
		req.Header.Set("Content-Type", "application/json")
		rec = httptest.NewRecorder()

		e.ServeHTTP(rec, req)
		assert.Equal(t, http.StatusOK, rec.Code)

		var verifyStaffResp authn.VerifyStaffLoginResponse
		err = json.Unmarshal(rec.Body.Bytes(), &verifyStaffResp)
		require.NoError(t, err)
		assert.NotEmpty(t, verifyStaffResp.AccessToken)
		assert.NotEmpty(t, verifyStaffResp.RefreshToken)
	})

	t.Run("OTP Rate Limiting", func(t *testing.T) {
		ctx := context.Background()

		// Create test user
		userParams := db.CreateUserWithPhoneParams{
			Phone:                       testutil.ToPtr("+1111111111"),
			PhoneVerifiedAt:             testutil.ToPtr(time.Now()),
			DisplayName:                 "Rate Limit Test User",
			InterfaceLanguage:           "en",
			CommunicationLanguage:       "en",
			EnableAppNotifications:      true,
			EnableWhatsappNotifications: false,
			EnableSmsNotifications:      true,
			EnableEmailNotifications:    true,
			PhoneOtpChannel:             "sms",
		}
		user, err := store.CreateUserWithPhone(ctx, userParams)
		require.NoError(t, err)

		// Initiate OTP
		initiateReq := authn.InitiatePhoneOTPRequest{
			Phone:               *user.Phone,
			ClientID:            "test-client",
			CodeChallenge:       "test-challenge",
			CodeChallengeMethod: "S256",
			State:               "test-state",
		}
		initiateBody, _ := json.Marshal(initiateReq)

		req := httptest.NewRequest(http.MethodPost, "/api/v1/authn/phone/otp/initiate", bytes.NewReader(initiateBody))
		req.Header.Set("Content-Type", "application/json")
		rec := httptest.NewRecorder()

		e.ServeHTTP(rec, req)
		assert.Equal(t, http.StatusOK, rec.Code)

		var initiateResp authn.InitiatePhoneOTPResponse
		err = json.Unmarshal(rec.Body.Bytes(), &initiateResp)
		require.NoError(t, err)

		// Try wrong OTP multiple times
		for i := 0; i < 5; i++ {
			verifyReq := authn.VerifyPhoneOTPRequest{
				State:        initiateResp.State,
				OTP:          "wrong-otp",
				CodeVerifier: "test-verifier",
			}
			verifyBody, _ := json.Marshal(verifyReq)

			req = httptest.NewRequest(http.MethodPost, "/api/v1/authn/phone/otp/verify", bytes.NewReader(verifyBody))
			req.Header.Set("Content-Type", "application/json")
			rec = httptest.NewRecorder()

			e.ServeHTTP(rec, req)

			if i < 4 {
				assert.Equal(t, http.StatusUnauthorized, rec.Code)
			} else {
				// After 5 attempts, should be locked out
				assert.Equal(t, http.StatusTooManyRequests, rec.Code)
			}
		}
	})

	t.Run("Token Refresh Flow", func(t *testing.T) {
		ctx := context.Background()

		// Create test user and login
		userParams := db.CreateUserWithPhoneParams{
			Phone:                       testutil.ToPtr("+2222222222"),
			PhoneVerifiedAt:             testutil.ToPtr(time.Now()),
			DisplayName:                 "Token Refresh Test User",
			InterfaceLanguage:           "en",
			CommunicationLanguage:       "en",
			EnableAppNotifications:      true,
			EnableWhatsappNotifications: false,
			EnableSmsNotifications:      true,
			EnableEmailNotifications:    true,
			PhoneOtpChannel:             "sms",
		}
		user, err := store.CreateUserWithPhone(ctx, userParams)
		require.NoError(t, err)

		// Get tokens through login flow
		accessToken, refreshToken := performLoginFlow(t, e, *user.Phone, twilio_service.MockOTPCode)

		// Wait a moment to ensure different token timestamps
		time.Sleep(100 * time.Millisecond)

		// Use refresh token to get new tokens
		refreshReq := authn.RefreshTokenRequest{
			RefreshToken: refreshToken,
		}
		refreshBody, _ := json.Marshal(refreshReq)

		req := httptest.NewRequest(http.MethodPost, "/api/v1/authn/token/refresh", bytes.NewReader(refreshBody))
		req.Header.Set("Content-Type", "application/json")
		rec := httptest.NewRecorder()

		e.ServeHTTP(rec, req)
		assert.Equal(t, http.StatusOK, rec.Code)

		var refreshResp authn.TokenRefreshAPIResponse
		err = json.Unmarshal(rec.Body.Bytes(), &refreshResp)
		require.NoError(t, err)
		assert.NotEmpty(t, refreshResp.AccessToken)
		assert.NotEmpty(t, refreshResp.RefreshToken)
		assert.NotEqual(t, accessToken, refreshResp.AccessToken)
		assert.NotEqual(t, refreshToken, refreshResp.RefreshToken)
	})
}

// Helper function to perform login and return tokens
func performLoginFlow(t *testing.T, e *echo.Echo, phone, otp string) (accessToken, refreshToken string) {
	// Initiate OTP
	initiateReq := authn.InitiatePhoneOTPRequest{
		Phone:               phone,
		ClientID:            "test-client",
		CodeChallenge:       "test-challenge",
		CodeChallengeMethod: "S256",
		State:               "test-state",
	}
	initiateBody, _ := json.Marshal(initiateReq)

	req := httptest.NewRequest(http.MethodPost, "/api/v1/authn/phone/otp/initiate", bytes.NewReader(initiateBody))
	req.Header.Set("Content-Type", "application/json")
	rec := httptest.NewRecorder()

	e.ServeHTTP(rec, req)
	require.Equal(t, http.StatusOK, rec.Code)

	var initiateResp authn.InitiatePhoneOTPResponse
	err := json.Unmarshal(rec.Body.Bytes(), &initiateResp)
	require.NoError(t, err)

	// Verify OTP
	verifyReq := authn.VerifyPhoneOTPRequest{
		State:        initiateResp.State,
		OTP:          otp,
		CodeVerifier: "test-verifier",
	}
	verifyBody, _ := json.Marshal(verifyReq)

	req = httptest.NewRequest(http.MethodPost, "/api/v1/authn/phone/otp/verify", bytes.NewReader(verifyBody))
	req.Header.Set("Content-Type", "application/json")
	rec = httptest.NewRecorder()

	e.ServeHTTP(rec, req)
	require.Equal(t, http.StatusOK, rec.Code)

	var verifyResp authn.VerifyPhoneOTPResponse
	err = json.Unmarshal(rec.Body.Bytes(), &verifyResp)
	require.NoError(t, err)

	return verifyResp.AccessToken, verifyResp.RefreshToken
}
