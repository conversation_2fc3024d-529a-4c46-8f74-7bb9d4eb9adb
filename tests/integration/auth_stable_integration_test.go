package integration

import (
	"context"
	"testing"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/authorization"
	"Membership-SAAS-System-Backend/internal/testutil"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// AuthStableIntegrationTestSuite demonstrates the new stable testing approach
// This shows how to migrate from the problematic auth_integration_test.go
// to use transaction isolation and unique data generation
type AuthStableIntegrationTestSuite struct {
	suite.Suite
	testDB *testutil.TestDatabase
}

func (suite *AuthStableIntegrationTestSuite) SetupSuite() {
	// Set up the test database ONCE for the entire suite
	// Individual tests will use transactions for isolation
	suite.testDB, _ = testutil.SetupTestDatabaseWithSchema(suite.T())
}

func (suite *AuthStableIntegrationTestSuite) TearDownSuite() {
	// Clean up the test database container
	if suite.testDB != nil {
		suite.testDB.Cleanup()
	}
}

// NO SetupTest or TearDownTest needed!
// Each test runs in its own transaction and is automatically isolated

// TestPhoneAuthenticationFlow_Stable demonstrates stable phone auth testing
func (suite *AuthStableIntegrationTestSuite) TestPhoneAuthenticationFlow_Stable() {
	testutil.RunInTransaction(suite.T(), suite.testDB, func(ctx context.Context, queries *db.Queries) {
		factory := testutil.NewUniqueTestDataFactory(suite.T(), queries, ctx)

		// Create a unique user for this test - no conflicts possible
		user, err := factory.CreateUniqueUser()
		require.NoError(suite.T(), err)

		// Verify user has unique phone number
		require.NotNil(suite.T(), user.Phone)
		require.NotEqual(suite.T(), "+1234567890", *user.Phone) // Not hardcoded!

		suite.T().Logf("Created user with unique phone: %s", *user.Phone)

		// This test can run 1000 times without conflicts because:
		// 1. Each run gets a unique phone number
		// 2. Each run is isolated in its own transaction
		// 3. All data is automatically cleaned up
	})
}

// TestStaffLoginFlow_Stable demonstrates stable staff login testing
func (suite *AuthStableIntegrationTestSuite) TestStaffLoginFlow_Stable() {
	testutil.RunInTransaction(suite.T(), suite.testDB, func(ctx context.Context, queries *db.Queries) {
		factory := testutil.NewUniqueTestDataFactory(suite.T(), queries, ctx)

		// Create unique staff user with password - no shared state!
		hashedPassword := "$2a$12$v6MjHc6me1Iidicch8oFD.eIqbzpaRmi26pPgy9v7PxnBzgEeVb7S" // "123456"
		staffUser, org, _, err := factory.CreateCompleteStaffTestSetup(hashedPassword)
		require.NoError(suite.T(), err)

		// Verify staff user setup
		require.Equal(suite.T(), authorization.PlatformRoleStaff, staffUser.PlatformRole)
		require.NotNil(suite.T(), staffUser.Email)
		require.NotNil(suite.T(), staffUser.HashedPassword)
		require.Equal(suite.T(), hashedPassword, *staffUser.HashedPassword)

		// Test staff user can be found by unique email
		foundUser, err := queries.GetUserByEmail(ctx, staffUser.Email)
		require.NoError(suite.T(), err)
		require.Equal(suite.T(), staffUser.ID, foundUser.ID)

		suite.T().Logf("Created staff user: %s in org: %s",
			*staffUser.Email, org.Name)

		// Key insight: This staff user is COMPLETELY isolated from other tests
		// Unlike the shared superadmin, this user can be modified safely
	})
}

// TestPasswordChangeFlow_Stable demonstrates stable password change testing
func (suite *AuthStableIntegrationTestSuite) TestPasswordChangeFlow_Stable() {
	testutil.RunInTransaction(suite.T(), suite.testDB, func(ctx context.Context, queries *db.Queries) {
		factory := testutil.NewUniqueTestDataFactory(suite.T(), queries, ctx)

		// Create staff user with initial password
		initialHash := "$2a$12$initial.password.hash.for.test"
		staffUser, _, _, err := factory.CreateCompleteStaffTestSetup(initialHash)
		require.NoError(suite.T(), err)

		// Simulate password change (what the password service would do)
		newHash := "$2a$12$new.password.hash.after.change"

		// Use UpdateUserPassword which is the proper method for password updates
		err = queries.UpdateUserPassword(ctx, db.UpdateUserPasswordParams{
			ID:             staffUser.ID,
			HashedPassword: &newHash,
		})
		require.NoError(suite.T(), err)

		// Fetch the user again to verify the password was updated
		updatedUser, err := queries.GetUserByID(ctx, staffUser.ID)
		require.NoError(suite.T(), err)
		require.Equal(suite.T(), newHash, *updatedUser.HashedPassword)

		// Verify password was changed
		require.NotEqual(suite.T(), initialHash, *updatedUser.HashedPassword)

		suite.T().Logf("Password changed successfully for user: %s", *updatedUser.Email)

		// CRITICAL: This test can run 100+ times consecutively without failure
		// because it never touches shared state like the superadmin user
	})
}

// TestMultipleUsersInSameTest_Stable demonstrates creating multiple unique users
func (suite *AuthStableIntegrationTestSuite) TestMultipleUsersInSameTest_Stable() {
	testutil.RunInTransaction(suite.T(), suite.testDB, func(ctx context.Context, queries *db.Queries) {
		factory := testutil.NewUniqueTestDataFactory(suite.T(), queries, ctx)

		// Create multiple users in the same test - all unique
		user1, err := factory.CreateUniqueUser()
		require.NoError(suite.T(), err)

		user2, err := factory.CreateUniqueUser()
		require.NoError(suite.T(), err)

		user3, err := factory.CreateUniqueUser()
		require.NoError(suite.T(), err)

		// Verify all users are unique
		require.NotEqual(suite.T(), user1.ID, user2.ID)
		require.NotEqual(suite.T(), user1.ID, user3.ID)
		require.NotEqual(suite.T(), user2.ID, user3.ID)

		require.NotEqual(suite.T(), user1.Phone, user2.Phone)
		require.NotEqual(suite.T(), user1.Phone, user3.Phone)
		require.NotEqual(suite.T(), user2.Phone, user3.Phone)

		suite.T().Logf("Created 3 unique users:")
		suite.T().Logf("  User 1: %s", *user1.Phone)
		suite.T().Logf("  User 2: %s", *user2.Phone)
		suite.T().Logf("  User 3: %s", *user3.Phone)

		// This demonstrates the power of the new approach:
		// - No hardcoded values
		// - No conflicts between entities
		// - Perfect isolation
		// - Deterministic but unique data
	})
}

// TestRegistrationFlow_Stable demonstrates stable user registration testing
func (suite *AuthStableIntegrationTestSuite) TestRegistrationFlow_Stable() {
	testutil.RunInTransaction(suite.T(), suite.testDB, func(ctx context.Context, queries *db.Queries) {
		factory := testutil.NewUniqueTestDataFactory(suite.T(), queries, ctx)

		// Get unique test data without creating the user yet
		testInfo := factory.GetTestInfo()
		uniquePhone := testInfo["sample_phone"].(string)

		suite.T().Logf("Testing registration with phone: %s", uniquePhone)

		// Verify user doesn't exist yet by trying to find with the unique phone
		suite.T().Logf("Checking if user exists with phone: %s", uniquePhone)

		// Create user (simulating registration)
		user, err := factory.CreateUniqueUser()
		require.NoError(suite.T(), err)

		// Verify user was created successfully
		foundUser, err := queries.GetUserByPhone(ctx, user.Phone)
		require.NoError(suite.T(), err)
		require.Equal(suite.T(), user.ID, foundUser.ID)

		suite.T().Logf("Registration successful for: %s", *user.Phone)

		// This registration test is completely isolated and can run
		// concurrently with other tests without any conflicts
	})
}

func TestAuthStableIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(AuthStableIntegrationTestSuite))
}
