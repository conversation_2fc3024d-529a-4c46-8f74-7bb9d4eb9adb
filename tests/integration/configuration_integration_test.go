package integration

import (
	"net/http"
	"os"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestConfigurationEnvironmentVariables tests environment variable handling
func TestConfigurationEnvironmentVariables(t *testing.T) {
	// Test various environment variable configurations
	testCases := []struct {
		name     string
		setupEnv func(t *testing.T)
		verify   func(t *testing.T, server *TestServer)
	}{
		{
			name: "basic_test_environment",
			setupEnv: func(t *testing.T) {
				// Default test environment
			},
			verify: func(t *testing.T, server *TestServer) {
				verifier := server.VerifyConfiguration()
				assert.Equal(t, "test", verifier.Environment())
				assert.False(t, verifier.RateLimitingEnabled())
			},
		},
		{
			name: "production_environment_with_rate_limiting",
			setupEnv: func(t *testing.T) {
				t.Setenv("GO_ENV", "production")
				t.Setenv("RATE_LIMIT_ENABLED", "true")
				t.Setenv("RATE_LIMIT_PER_MINUTE", "60")
			},
			verify: func(t *testing.T, server *TestServer) {
				verifier := server.VerifyConfiguration()
				assert.Equal(t, "production", verifier.Environment())
				// In production, rate limiting might be enabled by default
			},
		},
		{
			name: "cache_configuration",
			setupEnv: func(t *testing.T) {
				t.Setenv("CACHE_ENABLED", "true")
				t.Setenv("CACHE_BACKEND", "redis")
				t.Setenv("REDIS_ADDR", "localhost:6379")
			},
			verify: func(t *testing.T, server *TestServer) {
				verifier := server.VerifyConfiguration()
				assert.True(t, verifier.HasRedis())
			},
		},
		{
			name: "cors_configuration",
			setupEnv: func(t *testing.T) {
				t.Setenv("CORS_ALLOWED_ORIGINS", "https://app1.example.com,https://app2.example.com")
			},
			verify: func(t *testing.T, server *TestServer) {
				verifier := server.VerifyConfiguration()
				origins := verifier.CORSAllowedOrigins()
				assert.Contains(t, origins, "https://app1.example.com")
				assert.Contains(t, origins, "https://app2.example.com")
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Save current environment
			originalEnv := os.Environ()
			defer func() {
				// Restore environment
				os.Clearenv()
				for _, env := range originalEnv {
					if kv := strings.SplitN(env, "=", 2); len(kv) == 2 {
						os.Setenv(kv[0], kv[1])
					}
				}
			}()

			// Setup test environment
			tc.setupEnv(t)

			// Create server with environment
			server := NewTestServer(t)

			// Verify configuration
			tc.verify(t, server)

			// Test that server is functional
			rec, err := server.MakeRequest("GET", "/health", nil, nil)
			require.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})
	}
}

// TestConfigurationPriority tests configuration option priority
func TestConfigurationPriority(t *testing.T) {
	// Test that explicit options override environment variables
	testCases := []struct {
		name         string
		envSetup     func(t *testing.T)
		options      []TestServerOption
		verifyConfig func(t *testing.T, verifier ConfigVerifier)
	}{
		{
			name: "option_overrides_env_for_rate_limiting",
			envSetup: func(t *testing.T) {
				t.Setenv("RATE_LIMIT_ENABLED", "false")
				t.Setenv("RATE_LIMIT_PER_MINUTE", "5")
			},
			options: []TestServerOption{
				WithRateLimiting(true, 20),
			},
			verifyConfig: func(t *testing.T, verifier ConfigVerifier) {
				assert.True(t, verifier.RateLimitingEnabled())
				assert.Equal(t, 20, verifier.RateLimitPerMinute())
			},
		},
		{
			name: "option_overrides_env_for_cors",
			envSetup: func(t *testing.T) {
				t.Setenv("CORS_ALLOWED_ORIGINS", "https://env.example.com")
			},
			options: []TestServerOption{
				WithCORS("https://option.example.com"),
			},
			verifyConfig: func(t *testing.T, verifier ConfigVerifier) {
				origins := verifier.CORSAllowedOrigins()
				assert.Equal(t, []string{"https://option.example.com"}, origins)
			},
		},
		{
			name: "multiple_options_compose",
			envSetup: func(t *testing.T) {
				t.Setenv("GO_ENV", "test")
			},
			options: []TestServerOption{
				WithEnvironment("staging"),
				WithRateLimiting(true, 30),
				WithCORS("https://staging.example.com"),
			},
			verifyConfig: func(t *testing.T, verifier ConfigVerifier) {
				assert.Equal(t, "staging", verifier.Environment())
				assert.True(t, verifier.RateLimitingEnabled())
				assert.Equal(t, 30, verifier.RateLimitPerMinute())
				assert.Equal(t, []string{"https://staging.example.com"}, verifier.CORSAllowedOrigins())
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			tc.envSetup(t)

			server := NewTestServer(t, tc.options...)

			verifier := server.VerifyConfiguration()
			tc.verifyConfig(t, verifier)
		})
	}
}

// TestMiddlewareInitializationOrder tests that middleware is initialized in correct order
func TestMiddlewareInitializationOrder(t *testing.T) {
	// Create server with various middleware configurations
	testCases := []struct {
		name    string
		options []TestServerOption
		test    func(t *testing.T, server *TestServer)
	}{
		{
			name: "rate_limiting_middleware_order",
			options: []TestServerOption{
				WithRateLimiting(true, 10),
			},
			test: func(t *testing.T, server *TestServer) {
				// Make requests to verify middleware is working
				for i := 0; i < 15; i++ {
					rec, err := server.MakeRequest("GET", "/health", nil, nil)
					require.NoError(t, err)

					// Eventually should hit rate limit
					if rec.Code == http.StatusTooManyRequests {
						// Rate limiting middleware is working
						return
					}
				}
			},
		},
		{
			name: "cors_middleware_order",
			options: []TestServerOption{
				WithCORS("https://allowed.example.com"),
			},
			test: func(t *testing.T, server *TestServer) {
				// Test CORS preflight
				headers := map[string]string{
					"Origin":                         "https://allowed.example.com",
					"Access-Control-Request-Method":  "POST",
					"Access-Control-Request-Headers": "Content-Type",
				}

				rec, err := server.MakeRequest("OPTIONS", "/health", nil, headers)
				require.NoError(t, err)

				// Check CORS headers in response
				assert.NotEmpty(t, rec.Header().Get("Access-Control-Allow-Origin"))
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			server := NewTestServer(t, tc.options...)

			tc.test(t, server)
		})
	}
}

// TestConfigurationChangePropagation tests that configuration changes are properly applied
func TestConfigurationChangePropagation(t *testing.T) {
	// Create multiple servers with different configurations simultaneously
	var wg sync.WaitGroup
	servers := make([]*TestServer, 3)

	configs := [][]TestServerOption{
		{WithRateLimiting(true, 5)},
		{WithRateLimiting(true, 10), WithCORS("https://app1.example.com")},
		{WithEnvironment("staging"), WithRateLimiting(false, 0)},
	}

	// Create servers concurrently
	for i := range servers {
		wg.Add(1)
		go func(idx int) {
			defer wg.Done()
			servers[idx] = NewTestServer(t, configs[idx]...)
		}(i)
	}

	wg.Wait()

	// Verify each server has its own configuration
	assert.Equal(t, 5, servers[0].VerifyConfiguration().RateLimitPerMinute())
	assert.Equal(t, 10, servers[1].VerifyConfiguration().RateLimitPerMinute())
	assert.False(t, servers[2].VerifyConfiguration().RateLimitingEnabled())

	// Clean up
	for _, server := range servers {
		server.Close()
	}
}

// TestConfigurationWithServiceContainer tests configuration integration with service container
func TestConfigurationWithServiceContainer(t *testing.T) {
	testCases := []struct {
		name    string
		options []TestServerOption
		verify  func(t *testing.T, server *TestServer)
	}{
		{
			name:    "default_services",
			options: nil,
			verify: func(t *testing.T, server *TestServer) {
				container := server.GetServiceContainer()
				assert.NotNil(t, container.DB)
				assert.NotNil(t, container.Store)
				assert.NotNil(t, container.TwilioClient)
			},
		},
		{
			name: "services_with_rate_limiting",
			options: []TestServerOption{
				WithRateLimiting(true, 20),
			},
			verify: func(t *testing.T, server *TestServer) {
				container := server.GetServiceContainer()
				assert.NotNil(t, container)

				// All services should still be available
				assert.NotNil(t, container.DB)
				assert.NotNil(t, container.Store)
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			server := NewTestServer(t, tc.options...)

			tc.verify(t, server)
		})
	}
}

// TestConfigurationErrorScenarios tests configuration error handling
func TestConfigurationErrorScenarios(t *testing.T) {
	testCases := []struct {
		name    string
		options []TestServerOption
		test    func(t *testing.T, server *TestServer)
	}{
		{
			name: "invalid_redis_connection",
			options: []TestServerOption{
				WithRedis("invalid-host:9999"),
				WithRateLimiting(true, 10),
			},
			test: func(t *testing.T, server *TestServer) {
				// Server should still work even with invalid Redis
				rec, err := server.MakeRequest("GET", "/health", nil, nil)
				require.NoError(t, err)
				assert.Equal(t, http.StatusOK, rec.Code)
			},
		},
		{
			name: "conflicting_environment_settings",
			options: []TestServerOption{
				WithEnvironment("test"),
				WithRateLimiting(true, 10), // This might force production mode
			},
			test: func(t *testing.T, server *TestServer) {
				// Verify how conflicts are resolved
				verifier := server.VerifyConfiguration()
				// The actual behavior depends on implementation
				t.Logf("Environment: %s, RateLimiting: %v",
					verifier.Environment(),
					verifier.RateLimitingEnabled())
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			server := NewTestServer(t, tc.options...)

			tc.test(t, server)
		})
	}
}

// TestConfigurationConcurrency tests configuration under concurrent access
func TestConfigurationConcurrency(t *testing.T) {
	server := NewTestServer(t, WithRateLimiting(true, 100))

	const numGoroutines = 20
	const requestsPerGoroutine = 10

	var wg sync.WaitGroup
	results := make(chan int, numGoroutines*requestsPerGoroutine)

	// Launch concurrent requests with configuration verification
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			for j := 0; j < requestsPerGoroutine; j++ {
				// Verify configuration is consistent
				verifier := server.VerifyConfiguration()
				assert.True(t, verifier.RateLimitingEnabled())
				assert.Equal(t, 100, verifier.RateLimitPerMinute())

				// Make request
				rec, err := server.MakeRequest("GET", "/health", nil, nil)
				if err == nil {
					results <- rec.Code
				}

				time.Sleep(5 * time.Millisecond)
			}
		}(i)
	}

	wg.Wait()
	close(results)

	// Count results
	statusCounts := make(map[int]int)
	for code := range results {
		statusCounts[code]++
	}

	t.Logf("Request results: %+v", statusCounts)
	assert.Greater(t, statusCounts[http.StatusOK], 0, "Should have successful requests")
}

// TestConfigurationIntegrationWithAllComponents tests all configuration components together
func TestConfigurationIntegrationWithAllComponents(t *testing.T) {
	// Complex configuration scenario
	server := NewTestServer(t,
		WithEnvironment("integration-test"),
		WithRateLimiting(true, 50),
		WithCORS("https://test1.example.com", "https://test2.example.com"),
		WithTrustedProxies("10.0.0.0/8", "**********/12"),
		WithRedis("localhost:6379"),
	)

	// Verify all configurations are applied
	verifier := server.VerifyConfiguration()
	assert.Equal(t, "integration-test", verifier.Environment())
	assert.True(t, verifier.RateLimitingEnabled())
	assert.Equal(t, 50, verifier.RateLimitPerMinute())
	assert.Len(t, verifier.CORSAllowedOrigins(), 2)
	assert.True(t, verifier.HasRedis())

	// Test with proxy headers
	headers := map[string]string{
		"X-Forwarded-For": "*************, ********",
		"X-Real-IP":       "*************",
		"Origin":          "https://test1.example.com",
	}

	rec, err := server.MakeRequest("GET", "/health", nil, headers)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, rec.Code)

	// Verify CORS headers
	assert.NotEmpty(t, rec.Header().Get("Access-Control-Allow-Origin"))
}
