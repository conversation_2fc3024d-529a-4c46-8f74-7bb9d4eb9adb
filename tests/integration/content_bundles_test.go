package integration

import (
	"compress/gzip"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/services/bundle"
	"Membership-SAAS-System-Backend/internal/testutil"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"go.uber.org/zap"
)

type ContentBundleTestSuite struct {
	testutil.BaseServiceTestSuite
	bundleService     bundle.BundleService
	generationService bundle.BundleGenerationService
	collectorService  bundle.ContentCollectorService
	logger            *zap.Logger
	bundleStoragePath string
	testOrg           db.Organization
	testUser          db.User
	testPosts         []db.Post
	testEvents        []db.Event
	testResources     []db.ResourceFile
}

func TestContentBundleTestSuite(t *testing.T) {
	suite.Run(t, new(ContentBundleTestSuite))
}

func (s *ContentBundleTestSuite) SetupTest() {
	s.BaseServiceTestSuite.SetupTest()

	// Create temporary directory for bundle storage
	tmpDir, err := os.MkdirTemp("", "bundle_test_*")
	require.NoError(s.T(), err)
	s.bundleStoragePath = tmpDir

	// Initialize logger
	s.logger, _ = zap.NewDevelopment()

	// Initialize services
	s.collectorService = bundle.NewContentCollectorService(s.MockStore, s.logger)
	s.generationService = bundle.NewBundleGenerationService(s.collectorService, s.logger, s.bundleStoragePath)
	s.bundleService = bundle.NewBundleService(
		s.MockStore,
		s.generationService,
		s.collectorService,
		s.logger,
		s.bundleStoragePath,
	)

	// Create test data
	s.setupTestData()
}

func (s *ContentBundleTestSuite) TearDownTest() {
	s.BaseServiceTestSuite.TearDownTest()

	// Clean up temporary directory
	if s.bundleStoragePath != "" {
		os.RemoveAll(s.bundleStoragePath)
	}
}

func (s *ContentBundleTestSuite) setupTestData() {
	ctx := context.Background()

	// Create test organization
	s.testOrg = s.createTestOrganization()
	s.testUser = s.createTestUser()

	// Create test posts
	for i := 0; i < 10; i++ {
		content := json.RawMessage(fmt.Sprintf(`{
			"type": "doc",
			"content": [
				{
					"type": "paragraph",
					"content": [
						{
							"type": "text",
							"text": "This is test post %d content with rich formatting"
						}
					]
				}
			]
		}`, i))

		post, err := s.MockStore.CreatePost(ctx, db.CreatePostParams{
			OrganizationID: s.testOrg.ID,
			Title:          fmt.Sprintf("Test Post %d", i),
			Content:        content,
			AuthorID:       s.testUser.ID,
			Status:         "published",
		})
		require.NoError(s.T(), err)
		s.testPosts = append(s.testPosts, post)
	}

	// Create test events
	for i := 0; i < 5; i++ {
		maxAttendees := int32(100)
		location := fmt.Sprintf("Location %d", i)
		event, err := s.MockStore.CreateEvent(ctx, db.CreateEventParams{
			OrganizationID:                  s.testOrg.ID,
			Title:                           fmt.Sprintf("Test Event %d", i),
			DescriptionContent:              []byte(fmt.Sprintf("Description for test event %d", i)),
			StartTime:                       time.Now().Add(time.Duration(i) * 24 * time.Hour),
			EndTime:                         time.Now().Add(time.Duration(i) * 24 * time.Hour).Add(2 * time.Hour),
			LocationType:                    db.EventLocationTypePhysical,
			LocationFullAddress:             &location,
			ParticipantLimit:                &maxAttendees,
			CreatedByUserID:                 s.testUser.ID,
			Status:                          db.EventStatusTypeDraft,
			RequiresApprovalForRegistration: false,
		})
		require.NoError(s.T(), err)
		s.testEvents = append(s.testEvents, event)
	}

	// Create test resources
	for i := 0; i < 3; i++ {
		// First create a resource
		desc := fmt.Sprintf("Test resource %d", i)
		resource, err := s.MockStore.CreateResource(ctx, db.CreateResourceParams{
			OrganizationID: s.testOrg.ID,
			Title:          fmt.Sprintf("Test Resource %d", i),
			Slug:           fmt.Sprintf("test-resource-%d", i),
			Description:    &desc,
			Visibility:     "public",
			Status:         "published",
		})
		require.NoError(s.T(), err)

		// Then create a resource file for it
		fileDesc := fmt.Sprintf("Test resource file %d", i)
		resourceFile, err := s.MockStore.CreateResourceFile(ctx, db.CreateResourceFileParams{
			ResourceID:  resource.ID,
			FileName:    fmt.Sprintf("resource_%d.pdf", i),
			FilePath:    fmt.Sprintf("/resources/%s/resource_%d.pdf", s.testOrg.ID, i),
			FileType:    "application/pdf",
			FileSize:    1024 * int64(i+1), // Variable sizes
			Description: &fileDesc,
		})
		require.NoError(s.T(), err)
		s.testResources = append(s.testResources, resourceFile)
	}
}

// Bundle Creation Tests

func (s *ContentBundleTestSuite) TestCreateFullBundle() {
	t := s.T()
	ctx := context.Background()

	req := bundle.CreateBundleRequest{
		OrganizationID: s.testOrg.ID,
		BundleType:     db.BundleTypeFull,
		BundleFormat:   db.BundleFormatJson,
		Name:           "Test Full Bundle",
		Description:    "Complete bundle with all content",
		Config: bundle.BundleConfig{
			IncludePosts:     true,
			IncludeEvents:    true,
			IncludeResources: true,
			IncludeMedia:     false,
		},
		RequestedBy: s.testUser.ID,
	}

	createdBundle, err := s.bundleService.CreateBundle(ctx, req)
	require.NoError(t, err)
	require.NotNil(t, createdBundle)
	require.Equal(t, req.Name, *createdBundle.Name)
	require.Equal(t, req.Description, *createdBundle.Description)
	require.Equal(t, db.BundleStatusPending, createdBundle.Status)
	require.True(t, *createdBundle.IncludePosts)
	require.True(t, *createdBundle.IncludeEvents)
	require.True(t, *createdBundle.IncludeResources)
	require.False(t, *createdBundle.IncludeMedia)

	// Verify expiration is set
	require.NotNil(t, createdBundle.ExpiresAt)
	require.True(t, createdBundle.ExpiresAt.After(time.Now()))
}

func (s *ContentBundleTestSuite) TestCreateSelectiveBundle() {
	t := s.T()
	ctx := context.Background()

	// Create bundle with selective criteria
	filterCriteria := map[string]interface{}{
		"tags":          []string{"important", "featured"},
		"date_from":     time.Now().Add(-30 * 24 * time.Hour).Format(time.RFC3339),
		"date_to":       time.Now().Format(time.RFC3339),
		"content_types": []string{"post", "event"},
	}

	req := bundle.CreateBundleRequest{
		OrganizationID: s.testOrg.ID,
		BundleType:     db.BundleTypeSelective,
		BundleFormat:   db.BundleFormatJson,
		Name:           "Selective Bundle",
		Description:    "Bundle with filtered content",
		Config: bundle.BundleConfig{
			IncludePosts:  true,
			IncludeEvents: true,
		},
		FilterCriteria: filterCriteria,
		RequestedBy:    s.testUser.ID,
	}

	bundle, err := s.bundleService.CreateBundle(ctx, req)
	require.NoError(t, err)
	require.NotNil(t, bundle)

	// Verify filter criteria is stored
	require.NotNil(t, bundle.FilterCriteria)

	var storedCriteria map[string]interface{}
	err = json.Unmarshal(bundle.FilterCriteria, &storedCriteria)
	require.NoError(t, err)

	config, exists := storedCriteria["config"]
	require.True(t, exists)
	require.NotNil(t, config)
}

func (s *ContentBundleTestSuite) TestCreateDeltaBundle() {
	t := s.T()
	ctx := context.Background()

	// First create a full bundle as base
	fullReq := bundle.CreateBundleRequest{
		OrganizationID: s.testOrg.ID,
		BundleType:     db.BundleTypeFull,
		BundleFormat:   db.BundleFormatJson,
		Name:           "Base Bundle",
		Config: bundle.BundleConfig{
			IncludePosts:  true,
			IncludeEvents: true,
		},
		RequestedBy: s.testUser.ID,
	}

	baseBundle, err := s.bundleService.CreateBundle(ctx, fullReq)
	require.NoError(t, err)

	// Mark base bundle as completed (simulate generation)
	_, err = s.MockStore.UpdateBundleStatus(ctx, db.UpdateBundleStatusParams{
		ID:             baseBundle.ID,
		Status:         db.BundleStatusCompleted,
		OrganizationID: s.testOrg.ID,
	})
	require.NoError(t, err)

	// Create delta bundle
	deltaReq := bundle.CreateBundleRequest{
		OrganizationID:       s.testOrg.ID,
		BundleType:           db.BundleTypeDelta,
		BundleFormat:         db.BundleFormatJson,
		Name:                 "Delta Bundle",
		BaseBundleID:         &baseBundle.ID,
		LastUpdatedContentAt: &baseBundle.CreatedAt,
		Config: bundle.BundleConfig{
			IncludePosts:  true,
			IncludeEvents: true,
		},
		RequestedBy: s.testUser.ID,
	}

	deltaBundle, err := s.bundleService.CreateBundle(ctx, deltaReq)
	require.NoError(t, err)
	require.NotNil(t, deltaBundle)
	require.Equal(t, baseBundle.ID, *deltaBundle.BaseBundleID)
	require.Equal(t, db.BundleTypeDelta, deltaBundle.BundleType)
}

// Bundle Generation Tests

func (s *ContentBundleTestSuite) TestFullBundleGeneration() {
	t := s.T()
	ctx := context.Background()

	// Create bundle
	req := bundle.CreateBundleRequest{
		OrganizationID: s.testOrg.ID,
		BundleType:     db.BundleTypeFull,
		BundleFormat:   db.BundleFormatJson,
		Name:           "Generation Test Bundle",
		Config: bundle.BundleConfig{
			IncludePosts:     true,
			IncludeEvents:    true,
			IncludeResources: true,
		},
		RequestedBy: s.testUser.ID,
	}

	createdBundle, err := s.bundleService.CreateBundle(ctx, req)
	require.NoError(t, err)

	// Generate bundle
	err = s.bundleService.GenerateBundle(ctx, createdBundle.ID)
	require.NoError(t, err)

	// Verify bundle was generated
	bundle, err := s.bundleService.GetBundle(ctx, createdBundle.ID, s.testOrg.ID)
	require.NoError(t, err)
	require.Equal(t, db.BundleStatusCompleted, bundle.Status)
	require.NotNil(t, bundle.FilePath)
	require.NotNil(t, bundle.ChecksumSha256)
	require.NotNil(t, bundle.CompressedSizeBytes)
	require.Greater(t, *bundle.CompressedSizeBytes, int64(0))

	// Verify file exists
	require.FileExists(t, *bundle.FilePath)

	// Verify file is valid gzip
	file, err := os.Open(*bundle.FilePath)
	require.NoError(t, err)
	defer file.Close()

	gzReader, err := gzip.NewReader(file)
	require.NoError(t, err)
	defer gzReader.Close()

	// Read and parse content
	content, err := io.ReadAll(gzReader)
	require.NoError(t, err)
	require.Greater(t, len(content), 0)

	// Verify JSON structure
	var bundleData map[string]interface{}
	err = json.Unmarshal(content, &bundleData)
	require.NoError(t, err)
	require.Contains(t, bundleData, "bundle_info")
	require.Contains(t, bundleData, "content")

	// Verify content sections
	contentData := bundleData["content"].(map[string]interface{})
	require.Contains(t, contentData, "posts")
	require.Contains(t, contentData, "events")
	require.Contains(t, contentData, "resources")

	// Verify actual data is included
	posts := contentData["posts"].([]interface{})
	require.Len(t, posts, len(s.testPosts))

	events := contentData["events"].([]interface{})
	require.Len(t, events, len(s.testEvents))

	resources := contentData["resources"].([]interface{})
	require.Len(t, resources, len(s.testResources))
}

func (s *ContentBundleTestSuite) TestDeltaBundleGeneration() {
	t := s.T()
	ctx := context.Background()

	// Create and generate base bundle
	baseReq := bundle.CreateBundleRequest{
		OrganizationID: s.testOrg.ID,
		BundleType:     db.BundleTypeFull,
		BundleFormat:   db.BundleFormatJson,
		Name:           "Base Bundle for Delta",
		Config: bundle.BundleConfig{
			IncludePosts: true,
		},
		RequestedBy: s.testUser.ID,
	}

	baseBundle, err := s.bundleService.CreateBundle(ctx, baseReq)
	require.NoError(t, err)

	err = s.bundleService.GenerateBundle(ctx, baseBundle.ID)
	require.NoError(t, err)

	// Create new content after base bundle
	time.Sleep(100 * time.Millisecond) // Ensure timestamp difference

	newPost, err := s.MockStore.CreatePost(ctx, db.CreatePostParams{
		OrganizationID: s.testOrg.ID,
		Title:          "New Post After Base Bundle",
		Content:        json.RawMessage(`{"type": "doc", "content": []}`),
		AuthorID:       s.testUser.ID,
		Status:         "published",
	})
	require.NoError(t, err)

	// Create delta bundle
	deltaReq := bundle.CreateBundleRequest{
		OrganizationID:       s.testOrg.ID,
		BundleType:           db.BundleTypeDelta,
		BundleFormat:         db.BundleFormatJson,
		Name:                 "Delta Bundle",
		BaseBundleID:         &baseBundle.ID,
		LastUpdatedContentAt: &baseBundle.CreatedAt,
		Config: bundle.BundleConfig{
			IncludePosts: true,
		},
		RequestedBy: s.testUser.ID,
	}

	deltaBundle, err := s.bundleService.CreateBundle(ctx, deltaReq)
	require.NoError(t, err)

	err = s.bundleService.GenerateBundle(ctx, deltaBundle.ID)
	require.NoError(t, err)

	// Verify delta bundle
	bundle, err := s.bundleService.GetBundle(ctx, deltaBundle.ID, s.testOrg.ID)
	require.NoError(t, err)
	require.Equal(t, db.BundleStatusCompleted, bundle.Status)

	// Verify delta bundle is smaller than full bundle
	baseBundle, err = s.bundleService.GetBundle(ctx, baseBundle.ID, s.testOrg.ID)
	require.NoError(t, err)

	require.Less(t, *bundle.CompressedSizeBytes, *baseBundle.CompressedSizeBytes)

	// Verify delta content contains only new post
	file, err := os.Open(*bundle.FilePath)
	require.NoError(t, err)
	defer file.Close()

	gzReader, err := gzip.NewReader(file)
	require.NoError(t, err)
	defer gzReader.Close()

	content, err := io.ReadAll(gzReader)
	require.NoError(t, err)

	var deltaData map[string]interface{}
	err = json.Unmarshal(content, &deltaData)
	require.NoError(t, err)

	contentData := deltaData["content"].(map[string]interface{})
	posts := contentData["posts"].([]interface{})
	require.Len(t, posts, 1) // Only the new post

	post := posts[0].(map[string]interface{})
	require.Equal(t, newPost.ID.String(), post["id"].(string))
}

func (s *ContentBundleTestSuite) TestSelectiveBundleGeneration() {
	t := s.T()
	ctx := context.Background()

	// Tag some posts for selective inclusion
	testPosts := s.testPosts[:3] // Select first 3 posts
	for _, post := range testPosts {
		// Skip post tagging for now - CreatePostTag not available on MockStore
		// TODO: Update once post tagging is implemented
		_ = post.ID
	}

	// Create selective bundle
	filterCriteria := map[string]interface{}{
		"content_types": []string{"post"},
		"date_from":     time.Now().Add(-1 * time.Hour).Format(time.RFC3339),
		"date_to":       time.Now().Add(1 * time.Hour).Format(time.RFC3339),
	}

	req := bundle.CreateBundleRequest{
		OrganizationID: s.testOrg.ID,
		BundleType:     db.BundleTypeSelective,
		BundleFormat:   db.BundleFormatJson,
		Name:           "Selective Bundle",
		Config: bundle.BundleConfig{
			IncludePosts: true,
		},
		FilterCriteria: filterCriteria,
		RequestedBy:    s.testUser.ID,
	}

	bundle, err := s.bundleService.CreateBundle(ctx, req)
	require.NoError(t, err)

	err = s.bundleService.GenerateBundle(ctx, bundle.ID)
	require.NoError(t, err)

	// Verify selective bundle
	bundle, err = s.bundleService.GetBundle(ctx, bundle.ID, s.testOrg.ID)
	require.NoError(t, err)
	require.Equal(t, db.BundleStatusCompleted, bundle.Status)

	// Verify content is filtered
	file, err := os.Open(*bundle.FilePath)
	require.NoError(t, err)
	defer file.Close()

	gzReader, err := gzip.NewReader(file)
	require.NoError(t, err)
	defer gzReader.Close()

	content, err := io.ReadAll(gzReader)
	require.NoError(t, err)

	var bundleData map[string]interface{}
	err = json.Unmarshal(content, &bundleData)
	require.NoError(t, err)

	contentData := bundleData["content"].(map[string]interface{})
	posts := contentData["posts"].([]interface{})

	// Should contain all posts within date range
	require.Len(t, posts, len(s.testPosts))

	// Events should not be included
	_, hasEvents := contentData["events"]
	require.False(t, hasEvents)
}

// Performance Tests

func (s *ContentBundleTestSuite) TestBundleGenerationPerformance() {
	t := s.T()
	ctx := context.Background()

	if testing.Short() {
		t.Skip("Skipping performance test in short mode")
	}

	// Create bundle with all content types
	req := bundle.CreateBundleRequest{
		OrganizationID: s.testOrg.ID,
		BundleType:     db.BundleTypeFull,
		BundleFormat:   db.BundleFormatJson,
		Name:           "Performance Test Bundle",
		Config: bundle.BundleConfig{
			IncludePosts:     true,
			IncludeEvents:    true,
			IncludeResources: true,
			IncludeMedia:     false,
		},
		RequestedBy: s.testUser.ID,
	}

	bundle, err := s.bundleService.CreateBundle(ctx, req)
	require.NoError(t, err)

	// Measure generation time
	start := time.Now()
	err = s.bundleService.GenerateBundle(ctx, bundle.ID)
	duration := time.Since(start)

	require.NoError(t, err)
	require.Less(t, duration, 30*time.Second, "Bundle generation should complete within 30 seconds")

	t.Logf("Bundle generation completed in %v", duration)

	// Verify compression efficiency
	bundle, err = s.bundleService.GetBundle(ctx, bundle.ID, s.testOrg.ID)
	require.NoError(t, err)

	file, err := os.Open(*bundle.FilePath)
	require.NoError(t, err)
	defer file.Close()

	// Get uncompressed size
	gzReader, err := gzip.NewReader(file)
	require.NoError(t, err)
	defer gzReader.Close()

	uncompressedContent, err := io.ReadAll(gzReader)
	require.NoError(t, err)
	uncompressedSize := int64(len(uncompressedContent))

	// Calculate compression ratio
	compressionRatio := float64(*bundle.CompressedSizeBytes) / float64(uncompressedSize)
	require.Less(t, compressionRatio, 0.8, "Compression should achieve at least 20% reduction")

	t.Logf("Compression: %d bytes -> %d bytes (ratio: %.2f)",
		uncompressedSize, *bundle.CompressedSizeBytes, compressionRatio)
}

func (s *ContentBundleTestSuite) TestConcurrentBundleGeneration() {
	t := s.T()
	ctx := context.Background()

	if testing.Short() {
		t.Skip("Skipping concurrency test in short mode")
	}

	// Create multiple bundles concurrently
	bundleCount := 5
	bundles := make([]*db.ContentBundle, bundleCount)
	errors := make(chan error, bundleCount)

	// Create bundles
	for i := 0; i < bundleCount; i++ {
		req := bundle.CreateBundleRequest{
			OrganizationID: s.testOrg.ID,
			BundleType:     db.BundleTypeFull,
			BundleFormat:   db.BundleFormatJson,
			Name:           fmt.Sprintf("Concurrent Bundle %d", i),
			Config: bundle.BundleConfig{
				IncludePosts: true,
			},
			RequestedBy: s.testUser.ID,
		}

		var err error
		bundles[i], err = s.bundleService.CreateBundle(ctx, req)
		require.NoError(t, err)
	}

	// Generate bundles concurrently
	start := time.Now()
	for i := 0; i < bundleCount; i++ {
		go func(bundleID uuid.UUID) {
			err := s.bundleService.GenerateBundle(ctx, bundleID)
			errors <- err
		}(bundles[i].ID)
	}

	// Wait for all generations to complete
	for i := 0; i < bundleCount; i++ {
		err := <-errors
		require.NoError(t, err)
	}

	duration := time.Since(start)
	t.Logf("Concurrent generation of %d bundles completed in %v", bundleCount, duration)

	// Verify all bundles were generated successfully
	for i, bundle := range bundles {
		updatedBundle, err := s.bundleService.GetBundle(ctx, bundle.ID, s.testOrg.ID)
		require.NoError(t, err)
		require.Equal(t, db.BundleStatusCompleted, updatedBundle.Status,
			"Bundle %d should be completed", i)
		require.FileExists(t, *updatedBundle.FilePath)
	}
}

// Bundle Management Tests

func (s *ContentBundleTestSuite) TestBundleListingAndFiltering() {
	t := s.T()
	ctx := context.Background()

	// Create bundles with different types and statuses
	bundleConfigs := []struct {
		bundleType   db.BundleType
		bundleFormat db.BundleFormat
		name         string
	}{
		{db.BundleTypeFull, db.BundleFormatJson, "Full JSON Bundle"},
		{db.BundleTypeFull, db.BundleFormatMsgpack, "Full Msgpack Bundle"},
		{db.BundleTypeSelective, db.BundleFormatJson, "Selective Bundle"},
		{db.BundleTypeDelta, db.BundleFormatJson, "Delta Bundle"},
	}

	createdBundles := make([]*db.ContentBundle, len(bundleConfigs))
	for i, config := range bundleConfigs {
		req := bundle.CreateBundleRequest{
			OrganizationID: s.testOrg.ID,
			BundleType:     config.bundleType,
			BundleFormat:   config.bundleFormat,
			Name:           config.name,
			Config: bundle.BundleConfig{
				IncludePosts: true,
			},
			RequestedBy: s.testUser.ID,
		}

		var err error
		createdBundles[i], err = s.bundleService.CreateBundle(ctx, req)
		require.NoError(t, err)
	}

	// Test listing all bundles
	listReq := bundle.ListBundlesRequest{
		OrganizationID: s.testOrg.ID,
		Limit:          10,
		Offset:         0,
	}

	response, err := s.bundleService.ListBundles(ctx, listReq)
	require.NoError(t, err)
	require.GreaterOrEqual(t, len(response.Bundles), len(bundleConfigs))
	require.Equal(t, int64(len(response.Bundles)), response.Total)

	// Test filtering by bundle type
	fullType := db.BundleTypeFull
	listReq.BundleType = &fullType

	response, err = s.bundleService.ListBundles(ctx, listReq)
	require.NoError(t, err)
	require.Len(t, response.Bundles, 2) // 2 full bundles

	for _, bundle := range response.Bundles {
		require.Equal(t, db.BundleTypeFull, bundle.BundleType)
	}

	// Test filtering by format
	jsonFormat := db.BundleFormatJson
	listReq.BundleType = nil
	listReq.BundleFormat = &jsonFormat

	response, err = s.bundleService.ListBundles(ctx, listReq)
	require.NoError(t, err)
	require.Len(t, response.Bundles, 3) // 3 JSON bundles

	// Test pagination
	listReq.BundleFormat = nil
	listReq.Limit = 2
	listReq.Offset = 0

	firstPage, err := s.bundleService.ListBundles(ctx, listReq)
	require.NoError(t, err)
	require.Len(t, firstPage.Bundles, 2)
	require.True(t, firstPage.HasMore)

	listReq.Offset = 2
	secondPage, err := s.bundleService.ListBundles(ctx, listReq)
	require.NoError(t, err)
	require.GreaterOrEqual(t, len(secondPage.Bundles), 2)

	// Ensure no overlap
	firstPageIDs := make(map[uuid.UUID]bool)
	for _, bundle := range firstPage.Bundles {
		firstPageIDs[bundle.ID] = true
	}

	for _, bundle := range secondPage.Bundles {
		require.False(t, firstPageIDs[bundle.ID], "Bundle should not appear in both pages")
	}
}

func (s *ContentBundleTestSuite) TestBundleDownloadInfo() {
	t := s.T()
	ctx := context.Background()

	// Create and generate bundle
	req := bundle.CreateBundleRequest{
		OrganizationID: s.testOrg.ID,
		BundleType:     db.BundleTypeFull,
		BundleFormat:   db.BundleFormatJson,
		Name:           "Download Test Bundle",
		Config: bundle.BundleConfig{
			IncludePosts: true,
		},
		RequestedBy: s.testUser.ID,
	}

	bundle, err := s.bundleService.CreateBundle(ctx, req)
	require.NoError(t, err)

	// Test download info for pending bundle (should fail)
	_, err = s.bundleService.GetBundleDownloadInfo(ctx, bundle.ID, s.testOrg.ID)
	require.Error(t, err)
	require.Contains(t, err.Error(), "not ready for download")

	// Generate bundle
	err = s.bundleService.GenerateBundle(ctx, bundle.ID)
	require.NoError(t, err)

	// Test download info for completed bundle
	downloadInfo, err := s.bundleService.GetBundleDownloadInfo(ctx, bundle.ID, s.testOrg.ID)
	require.NoError(t, err)
	require.NotNil(t, downloadInfo)
	require.Equal(t, bundle.ID, downloadInfo.Bundle.ID)
	require.Greater(t, downloadInfo.Size, int64(0))
	require.NotEmpty(t, downloadInfo.Checksum)
	require.NotEmpty(t, downloadInfo.Filename)
	require.Contains(t, downloadInfo.Filename, "themoment-bundle-full")
	require.Contains(t, downloadInfo.Filename, ".json.gz")
}

func (s *ContentBundleTestSuite) TestBundleStreaming() {
	t := s.T()
	ctx := context.Background()

	// Create and generate bundle
	req := bundle.CreateBundleRequest{
		OrganizationID: s.testOrg.ID,
		BundleType:     db.BundleTypeFull,
		BundleFormat:   db.BundleFormatJson,
		Name:           "Stream Test Bundle",
		Config: bundle.BundleConfig{
			IncludePosts: true,
		},
		RequestedBy: s.testUser.ID,
	}

	bundle, err := s.bundleService.CreateBundle(ctx, req)
	require.NoError(t, err)

	err = s.bundleService.GenerateBundle(ctx, bundle.ID)
	require.NoError(t, err)

	// Test streaming
	stream, err := s.bundleService.StreamBundle(ctx, bundle.ID, s.testOrg.ID)
	require.NoError(t, err)
	require.NotNil(t, stream)
	defer stream.Close()

	// Verify we can read from stream
	buffer := make([]byte, 1024)
	n, err := stream.Read(buffer)
	if err != io.EOF {
		require.NoError(t, err)
	}
	require.Greater(t, n, 0)
}

// Bundle Lifecycle Tests

func (s *ContentBundleTestSuite) TestBundleExpiration() {
	t := s.T()
	ctx := context.Background()

	// Create bundle with short expiration
	req := bundle.CreateBundleRequest{
		OrganizationID: s.testOrg.ID,
		BundleType:     db.BundleTypeFull,
		BundleFormat:   db.BundleFormatJson,
		Name:           "Expiration Test Bundle",
		Config: bundle.BundleConfig{
			IncludePosts: true,
		},
		RequestedBy: s.testUser.ID,
	}

	bundle, err := s.bundleService.CreateBundle(ctx, req)
	require.NoError(t, err)

	// Set short expiration
	expiresAt := time.Now().Add(100 * time.Millisecond)
	err = s.bundleService.SetBundleExpiration(ctx, bundle.ID, s.testOrg.ID, expiresAt)
	require.NoError(t, err)

	// Generate bundle
	err = s.bundleService.GenerateBundle(ctx, bundle.ID)
	require.NoError(t, err)

	// Wait for expiration
	time.Sleep(200 * time.Millisecond)

	// Test cleanup
	deletedBundles, err := s.bundleService.CleanupExpiredBundles(ctx)
	require.NoError(t, err)

	// Verify our bundle was deleted
	found := false
	for _, deleted := range deletedBundles {
		if deleted.ID == bundle.ID {
			found = true
			break
		}
	}
	require.True(t, found, "Bundle should be in deleted list")
}

func (s *ContentBundleTestSuite) TestBundleDeletion() {
	t := s.T()
	ctx := context.Background()

	// Create and generate bundle
	req := bundle.CreateBundleRequest{
		OrganizationID: s.testOrg.ID,
		BundleType:     db.BundleTypeFull,
		BundleFormat:   db.BundleFormatJson,
		Name:           "Deletion Test Bundle",
		Config: bundle.BundleConfig{
			IncludePosts: true,
		},
		RequestedBy: s.testUser.ID,
	}

	bundle, err := s.bundleService.CreateBundle(ctx, req)
	require.NoError(t, err)

	err = s.bundleService.GenerateBundle(ctx, bundle.ID)
	require.NoError(t, err)

	// Verify file exists
	bundle, err = s.bundleService.GetBundle(ctx, bundle.ID, s.testOrg.ID)
	require.NoError(t, err)
	require.FileExists(t, *bundle.FilePath)

	// Delete bundle
	err = s.bundleService.DeleteBundle(ctx, bundle.ID, s.testOrg.ID)
	require.NoError(t, err)

	// Verify bundle status is updated
	bundle, err = s.bundleService.GetBundle(ctx, bundle.ID, s.testOrg.ID)
	require.NoError(t, err)
	require.Equal(t, db.BundleStatusExpired, bundle.Status)
}

// Statistics and Monitoring Tests

func (s *ContentBundleTestSuite) TestBundleGenerationStats() {
	t := s.T()
	ctx := context.Background()

	// Create and generate multiple bundles
	bundleCount := 3
	successfulBundles := 0

	for i := 0; i < bundleCount; i++ {
		req := bundle.CreateBundleRequest{
			OrganizationID: s.testOrg.ID,
			BundleType:     db.BundleTypeFull,
			BundleFormat:   db.BundleFormatJson,
			Name:           fmt.Sprintf("Stats Test Bundle %d", i),
			Config: bundle.BundleConfig{
				IncludePosts: true,
			},
			RequestedBy: s.testUser.ID,
		}

		bundle, err := s.bundleService.CreateBundle(ctx, req)
		require.NoError(t, err)

		// Generate some successfully, simulate failure for others
		if i < 2 {
			err = s.bundleService.GenerateBundle(ctx, bundle.ID)
			if err == nil {
				successfulBundles++
			}
		}
	}

	// Get generation stats
	stats, err := s.bundleService.GetBundleGenerationStats(ctx, s.testOrg.ID, time.Now().Add(-1*time.Hour))
	require.NoError(t, err)
	require.NotNil(t, stats)
	require.GreaterOrEqual(t, stats.TotalBundles, int64(bundleCount))
	require.GreaterOrEqual(t, stats.CompletedBundles, int64(successfulBundles))
	require.GreaterOrEqual(t, stats.PendingBundles, int64(1)) // At least 1 pending

	if stats.AvgGenerationTimeMs != nil {
		require.Greater(t, *stats.AvgGenerationTimeMs, int64(0))
	}
	if stats.TotalSizeBytes != nil {
		require.Greater(t, *stats.TotalSizeBytes, int64(0))
	}
}

func (s *ContentBundleTestSuite) TestBundleActivityTracking() {
	t := s.T()
	ctx := context.Background()

	// Create and generate bundle
	req := bundle.CreateBundleRequest{
		OrganizationID: s.testOrg.ID,
		BundleType:     db.BundleTypeFull,
		BundleFormat:   db.BundleFormatJson,
		Name:           "Activity Test Bundle",
		Config: bundle.BundleConfig{
			IncludePosts: true,
		},
		RequestedBy: s.testUser.ID,
	}

	bundle, err := s.bundleService.CreateBundle(ctx, req)
	require.NoError(t, err)

	err = s.bundleService.GenerateBundle(ctx, bundle.ID)
	require.NoError(t, err)

	// Get recent activity
	activities, err := s.bundleService.GetRecentBundleActivity(ctx, s.testOrg.ID, 10)
	require.NoError(t, err)
	require.NotEmpty(t, activities)

	// Find our bundle in activities
	found := false
	for _, activity := range activities {
		if activity.ID == bundle.ID {
			found = true
			require.Equal(t, db.BundleStatusCompleted, activity.Status)
			require.Equal(t, req.Name, activity.Name)
			require.NotNil(t, activity.CompressedSizeBytes)
			require.Greater(t, *activity.CompressedSizeBytes, int64(0))
			break
		}
	}
	require.True(t, found, "Bundle should appear in recent activity")
}

func (s *ContentBundleTestSuite) TestBundleGenerationLogs() {
	t := s.T()
	ctx := context.Background()

	// Create bundle
	req := bundle.CreateBundleRequest{
		OrganizationID: s.testOrg.ID,
		BundleType:     db.BundleTypeFull,
		BundleFormat:   db.BundleFormatJson,
		Name:           "Logging Test Bundle",
		Config: bundle.BundleConfig{
			IncludePosts: true,
		},
		RequestedBy: s.testUser.ID,
	}

	bundle, err := s.bundleService.CreateBundle(ctx, req)
	require.NoError(t, err)

	// Generate bundle (this should create logs)
	err = s.bundleService.GenerateBundle(ctx, bundle.ID)
	require.NoError(t, err)

	// Get generation logs
	logs, err := s.bundleService.GetBundleGenerationLogs(ctx, bundle.ID, "", 10, 0)
	require.NoError(t, err)
	require.NotEmpty(t, logs)

	// Verify log structure
	hasStartLog := false
	hasCompletionLog := false

	for _, log := range logs {
		require.Equal(t, bundle.ID, log.BundleID)
		require.NotEmpty(t, log.Message)
		require.NotNil(t, log.CreatedAt)

		if log.Message == "Bundle generation started" {
			hasStartLog = true
		}
		if log.Message == "Bundle generation completed" {
			hasCompletionLog = true
		}
	}

	require.True(t, hasStartLog, "Should have start log")
	require.True(t, hasCompletionLog, "Should have completion log")

	// Test filtering by log level
	infoLogs, err := s.bundleService.GetBundleGenerationLogs(ctx, bundle.ID, "info", 10, 0)
	require.NoError(t, err)
	require.NotEmpty(t, infoLogs)

	for _, log := range infoLogs {
		require.Equal(t, "info", log.LogLevel)
	}
}

// Helper methods

func (s *ContentBundleTestSuite) createTestOrganization() db.Organization {
	desc := "Organization for bundle testing"
	org, err := s.MockStore.CreateOrganization(context.Background(), db.CreateOrganizationParams{
		Name:        "Bundle Test Org " + uuid.New().String()[:8],
		Description: &desc,
		Status:      "active",
	})
	require.NoError(s.T(), err)
	return org
}

func (s *ContentBundleTestSuite) createTestUser() db.User {
	// Use the base test suite's CreateTestUser method
	return s.BaseServiceTestSuite.CreateTestUser()
}
