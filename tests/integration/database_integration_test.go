package integration

import (
	"context"
	"fmt"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/testutil"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// DatabaseIntegrationTestSuite provides infrastructure for database integration tests
type DatabaseIntegrationTestSuite struct {
	suite.Suite
	testDB  *testutil.TestDatabase
	store   db.Store
	queries *db.Queries // Keep for backward compatibility
	ctx     context.Context
}

// SetupSuite runs once before all tests in the suite
func (suite *DatabaseIntegrationTestSuite) SetupSuite() {
	suite.ctx = context.Background()
	suite.testDB, suite.store = testutil.SetupTestDatabaseWithStore(suite.T())
	suite.queries = db.New(suite.testDB.DB) // Keep queries for backward compatibility
}

// SetupTest runs before each test
func (suite *DatabaseIntegrationTestSuite) SetupTest() {
	// Clean database state before each test
	suite.testDB.TruncateAllTables(suite.T())
}

// TestDatabaseConnection verifies database connectivity and basic operations
func (suite *DatabaseIntegrationTestSuite) TestDatabaseConnection() {
	// Test basic connectivity
	err := suite.testDB.DB.Ping(suite.ctx)
	suite.NoError(err, "Database should be accessible")

	// Test simple query execution
	var result int
	err = suite.testDB.DB.QueryRow(suite.ctx, "SELECT 1").Scan(&result)
	suite.NoError(err, "Should be able to execute simple queries")
	suite.Equal(1, result, "Query should return expected result")
}

// TestSchemaLoaded verifies that the database schema is properly loaded
func (suite *DatabaseIntegrationTestSuite) TestSchemaLoaded() {
	// Verify key tables exist
	expectedTables := []string{
		"users",
		"organizations",
		"events",
		"event_registrations",
		"event_volunteer_applications",
		"event_media_items",
		"posts",
		"post_tags",
		"user_volunteer_applications",
		"user_verification_requests",
		"otp_attempts",
		"refresh_tokens",
		"jobs",
	}

	for _, table := range expectedTables {
		var exists bool
		err := suite.testDB.DB.QueryRow(suite.ctx,
			"SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = $1)",
			table).Scan(&exists)

		suite.NoError(err, "Should be able to check table existence")
		suite.True(exists, "Table %s should exist", table)
	}
}

// TestTransactionSupport verifies transaction functionality
func (suite *DatabaseIntegrationTestSuite) TestTransactionSupport() {
	// Start transaction
	tx, err := suite.testDB.DB.Begin(suite.ctx)
	suite.NoError(err, "Should be able to start transaction")

	// Create user first for organization owner
	userParams := db.CreateUserWithPhoneParams{
		DisplayName:                 "Transaction Test User",
		Phone:                       testutil.ToPtr("+1234567890"),
		PhoneVerifiedAt:             testutil.ToPtr(time.Now()),
		InterfaceLanguage:           "en",
		CommunicationLanguage:       "en",
		EnableAppNotifications:      true,
		EnableWhatsappNotifications: false,
		EnableSmsNotifications:      true,
		EnableEmailNotifications:    true,
		PhoneOtpChannel:             "sms",
	}

	qtx := suite.queries.WithTx(tx)
	user, err := qtx.CreateUserWithPhone(suite.ctx, userParams)
	suite.NoError(err, "Should create user in transaction")

	// Create organization within transaction using valid user ID
	orgParams := db.CreateOrganizationParams{
		Name:         "Test Org",
		Description:  testutil.ToPtr("Test organization"),
		OwnerUserID:  user.ID, // Use valid user ID
		IsDefaultOrg: false,
		Status:       "active"}

	org, err := qtx.CreateOrganization(suite.ctx, orgParams)
	suite.NoError(err, "Should create organization in transaction")

	// Rollback transaction
	err = tx.Rollback(suite.ctx)
	suite.NoError(err, "Should be able to rollback transaction")

	// Verify organization was not committed
	_, err = suite.queries.GetOrganizationByID(suite.ctx, org.ID)
	suite.Error(err, "Organization should not exist after rollback")
}

// TestConcurrentAccess verifies database handles concurrent operations
func (suite *DatabaseIntegrationTestSuite) TestConcurrentAccess() {
	numGoroutines := 10
	resultChan := make(chan error, numGoroutines)

	// Create user first for organization owner
	owner := testutil.CreateDBUser(suite.T(), suite.queries, func(params *db.CreateUserWithPhoneParams) {
		params.DisplayName = "Concurrent Test Owner"
		params.Phone = testutil.ToPtr("+1555000001")
	})

	// Create organization for testing
	org := testutil.CreateDBOrganization(suite.T(), suite.queries, func(params *db.CreateOrganizationParams) {
		params.Name = "Concurrent Test Org"
		params.OwnerUserID = owner.ID // Set valid owner ID
	})

	// Launch concurrent operations
	for i := 0; i < numGoroutines; i++ {
		go func(index int) {
			// Each goroutine creates a user in the organization with unique phone number
			user := testutil.CreateDBUser(suite.T(), suite.queries, func(params *db.CreateUserWithPhoneParams) {
				params.DisplayName = "Concurrent User " + string(rune('A'+index))
				// Generate unique phone number for each goroutine
				params.Phone = testutil.ToPtr(fmt.Sprintf("+1555555%04d", index))
			})

			// Add user to organization
			_, err := suite.queries.AddUserToOrganization(suite.ctx, db.AddUserToOrganizationParams{
				UserID:         user.ID,
				OrganizationID: org.ID,
				Role:           "member",
			})
			resultChan <- err
		}(i)
	}

	// Collect results
	for i := 0; i < numGoroutines; i++ {
		err := <-resultChan
		suite.NoError(err, "Concurrent operation %d should succeed", i)
	}

	// Verify all users were added
	memberships, err := testutil.ListOrganizationMemberships(suite.queries, suite.ctx, org.ID)
	suite.NoError(err, "Should be able to list memberships")
	suite.Len(memberships, numGoroutines, "Should have created all memberships")
}

// TestDatabaseConstraints verifies database constraints are enforced
func (suite *DatabaseIntegrationTestSuite) TestDatabaseConstraints() {
	// Test foreign key constraint
	invalidEventParams := db.CreateEventParams{
		OrganizationID:                  uuid.New(), // Non-existent organization
		CreatedByUserID:                 uuid.New(), // Non-existent user
		Title:                           "Invalid Event",
		DescriptionContent:              []byte(`{"type": "doc"}`),
		LocationType:                    db.EventLocationTypePhysical,
		StartTime:                       time.Now().Add(24 * time.Hour),
		EndTime:                         time.Now().Add(26 * time.Hour),
		Status:                          db.EventStatusTypeDraft,
		RequiresApprovalForRegistration: false,
	}

	_, err := suite.queries.CreateEvent(suite.ctx, invalidEventParams)
	suite.Error(err, "Should fail to create event with invalid foreign keys")

	// Test unique constraint (if any exists in your schema)
	// Example: duplicate organization names if that constraint exists
	// Create user first for organization owner
	owner := testutil.CreateDBUser(suite.T(), suite.queries, func(params *db.CreateUserWithPhoneParams) {
		params.DisplayName = "Constraint Test Owner"
		params.Phone = testutil.ToPtr("+1555000002")
	})

	orgParams := db.CreateOrganizationParams{
		Name:         "Unique Test Org",
		Description:  testutil.ToPtr("Test organization"),
		OwnerUserID:  owner.ID, // Use valid user ID
		IsDefaultOrg: false,
		Status:       "active"}

	// Create first organization
	_, err = suite.queries.CreateOrganization(suite.ctx, orgParams)
	suite.NoError(err, "Should create first organization")

	// Try to create duplicate (this test depends on your schema having unique constraints)
	_, err = suite.queries.CreateOrganization(suite.ctx, orgParams)
	// Note: This assertion depends on whether your schema has unique constraints
	// Adjust based on your actual schema requirements
}

// TestIndexPerformance verifies database indexes are working effectively
func (suite *DatabaseIntegrationTestSuite) TestIndexPerformance() {
	// Create user first for organization owner
	user := testutil.CreateDBUser(suite.T(), suite.queries, func(params *db.CreateUserWithPhoneParams) {
		params.DisplayName = "Performance Test User"
		params.Phone = testutil.ToPtr("+1555000003")
	})

	// Create organization with valid owner
	org := testutil.CreateDBOrganization(suite.T(), suite.queries, func(params *db.CreateOrganizationParams) {
		params.Name = "Performance Test Org"
		params.OwnerUserID = user.ID
	})

	// Create multiple events for performance testing
	numEvents := 100
	for i := 0; i < numEvents; i++ {
		testutil.CreateDBEvent(suite.T(), suite.queries, func(params *db.CreateEventParams) {
			params.OrganizationID = org.ID
			params.CreatedByUserID = user.ID
			params.Title = "Performance Test Event " + string(rune('A'+i))
		})
	}

	// Test query performance (should use indexes)
	start := time.Now()
	events, err := suite.queries.ListEventsByOrganization(suite.ctx, db.ListEventsByOrganizationParams{
		OrganizationID: org.ID,
		Limit:          50,
		Offset:         0,
	})
	duration := time.Since(start)

	suite.NoError(err, "Should be able to list events")
	suite.Len(events, 50, "Should return requested number of events")
	suite.Less(duration, 100*time.Millisecond, "Query should be fast with proper indexing")
}

// TestDataIntegrity verifies data integrity across related tables
func (suite *DatabaseIntegrationTestSuite) TestDataIntegrity() {
	// Create user first for organization owner
	user := testutil.CreateDBUser(suite.T(), suite.queries, func(params *db.CreateUserWithPhoneParams) {
		params.DisplayName = "Data Integrity Test User"
		params.Phone = testutil.ToPtr("+1555000004")
	})

	// Create organization with valid owner
	org := testutil.CreateDBOrganization(suite.T(), suite.queries, func(params *db.CreateOrganizationParams) {
		params.Name = "Data Integrity Test Org"
		params.OwnerUserID = user.ID
	})

	// Add user to organization
	_, err := suite.queries.AddUserToOrganization(suite.ctx, db.AddUserToOrganizationParams{
		UserID:         user.ID,
		OrganizationID: org.ID,
		Role:           "admin",
	})
	suite.NoError(err, "Should create membership")

	// Create event
	event := testutil.CreateDBEvent(suite.T(), suite.queries, func(params *db.CreateEventParams) {
		params.OrganizationID = org.ID
		params.CreatedByUserID = user.ID
	})

	// Create event registration
	registration, err := suite.queries.CreateEventRegistration(suite.ctx, db.CreateEventRegistrationParams{
		EventID:          event.ID,
		UserID:           user.ID,
		Status:           "registered",
		PaymentStatus:    db.PaymentStatusTypeNotRequired,
		RegistrationRole: db.EventRegistrationRoleTypeParticipant,
	})
	suite.NoError(err, "Should create event registration")

	// Verify data integrity through joins
	registrations, err := testutil.ListEventRegistrations(suite.queries, suite.ctx, testutil.ListEventRegistrationsParams{
		EventID: event.ID,
		Limit:   10,
		Offset:  0,
	})
	suite.NoError(err, "Should be able to list event registrations")
	suite.Len(registrations, 1, "Should have one registration")
	suite.Equal(registration.ID, registrations[0].ID, "Registration IDs should match")

	// Test cascading constraints (if implemented)
	// Note: This depends on your schema design for cascade rules
}

// TestDatabaseCleanup verifies cleanup operations work correctly
func (suite *DatabaseIntegrationTestSuite) TestDatabaseCleanup() {
	// Create user first for organization owner
	user := testutil.CreateDBUser(suite.T(), suite.queries, func(params *db.CreateUserWithPhoneParams) {
		params.DisplayName = "Cleanup Test User"
		params.Phone = testutil.ToPtr("+1555000005")
	})

	// Create organization with valid owner
	org := testutil.CreateDBOrganization(suite.T(), suite.queries, func(params *db.CreateOrganizationParams) {
		params.Name = "Cleanup Test Org"
		params.OwnerUserID = user.ID
	})
	event := testutil.CreateDBEvent(suite.T(), suite.queries, func(params *db.CreateEventParams) {
		params.OrganizationID = org.ID
		params.CreatedByUserID = user.ID
	})

	// Verify data exists
	retrievedEvent, err := suite.queries.GetEventByID(suite.ctx, event.ID)
	suite.NoError(err, "Event should exist")
	suite.Equal(event.ID, retrievedEvent.ID, "Event IDs should match")

	// Clean database
	suite.testDB.TruncateAllTables(suite.T())

	// Verify data is removed
	_, err = suite.queries.GetEventByID(suite.ctx, event.ID)
	suite.Error(err, "Event should not exist after cleanup")
}

// Run the test suite
func TestDatabaseIntegrationTestSuite(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration tests in short mode")
	}

	suite.Run(t, new(DatabaseIntegrationTestSuite))
}

// TestBasicDatabaseOperations tests fundamental database operations without test suite
func TestBasicDatabaseOperations(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration tests in short mode")
	}

	// Setup
	ctx := context.Background()
	testDB, _ := testutil.SetupTestDatabaseWithStore(t)
	queries := db.New(testDB.DB) // Keep queries for test utilities
	defer testDB.TruncateAllTables(t)

	t.Run("CreateAndRetrieveOrganization", func(t *testing.T) {
		// Create user first for organization owner
		userParams := db.CreateUserWithPhoneParams{
			DisplayName:                 "Basic Test User",
			Phone:                       testutil.ToPtr("+1555000006"),
			PhoneVerifiedAt:             testutil.ToPtr(time.Now()),
			InterfaceLanguage:           "en",
			CommunicationLanguage:       "en",
			EnableAppNotifications:      true,
			EnableWhatsappNotifications: false,
			EnableSmsNotifications:      true,
			EnableEmailNotifications:    true,
			PhoneOtpChannel:             "sms",
		}

		user, err := queries.CreateUserWithPhone(ctx, userParams)
		require.NoError(t, err, "Should create user")

		// Create organization with valid owner
		orgParams := db.CreateOrganizationParams{
			Name:         "Integration Test Org",
			Description:  testutil.ToPtr("Created in integration test"),
			OwnerUserID:  user.ID, // Use valid user ID
			IsDefaultOrg: false,
			Status:       "active"}

		org, err := queries.CreateOrganization(ctx, orgParams)
		require.NoError(t, err, "Should create organization")
		assert.Equal(t, orgParams.Name, org.Name, "Organization name should match")

		// Retrieve organization
		retrievedOrg, err := queries.GetOrganizationByID(ctx, org.ID)
		require.NoError(t, err, "Should retrieve organization")
		assert.Equal(t, org.ID, retrievedOrg.ID, "Organization IDs should match")
		assert.Equal(t, org.Name, retrievedOrg.Name, "Organization names should match")
	})

	t.Run("CreateAndRetrieveUser", func(t *testing.T) {
		// Create user
		userParams := db.CreateUserWithPhoneParams{
			DisplayName:                 "Integration Test User",
			Phone:                       testutil.ToPtr("+1234567890"),
			PhoneVerifiedAt:             testutil.ToPtr(time.Now()),
			InterfaceLanguage:           "en",
			CommunicationLanguage:       "en",
			EnableAppNotifications:      true,
			EnableWhatsappNotifications: false,
			EnableSmsNotifications:      true,
			EnableEmailNotifications:    true,
			PhoneOtpChannel:             "sms",
		}

		user, err := queries.CreateUserWithPhone(ctx, userParams)
		require.NoError(t, err, "Should create user")
		assert.Equal(t, userParams.DisplayName, user.DisplayName, "User display name should match")

		// Retrieve user
		retrievedUser, err := queries.GetUserByID(ctx, user.ID)
		require.NoError(t, err, "Should retrieve user")
		assert.Equal(t, user.ID, retrievedUser.ID, "User IDs should match")
		assert.Equal(t, user.DisplayName, retrievedUser.DisplayName, "User names should match")
	})

	t.Run("CreateAndRetrieveEvent", func(t *testing.T) {
		// First create user for organization owner
		user := testutil.CreateDBUser(t, queries, func(params *db.CreateUserWithPhoneParams) {
			params.DisplayName = "Event Creator"
			params.Phone = testutil.ToPtr("+1555000007")
		})

		// Then create organization with valid owner
		org := testutil.CreateDBOrganization(t, queries, func(params *db.CreateOrganizationParams) {
			params.Name = "Event Test Org"
			params.OwnerUserID = user.ID
		})

		// Create event
		now := time.Now()
		eventParams := db.CreateEventParams{
			OrganizationID:                  org.ID,
			CreatedByUserID:                 user.ID,
			Title:                           "Integration Test Event",
			DescriptionContent:              []byte(`{"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "Test event description"}]}]}`),
			LocationType:                    db.EventLocationTypePhysical,
			LocationFullAddress:             testutil.ToPtr("123 Test Street"),
			StartTime:                       now.Add(24 * time.Hour),
			EndTime:                         now.Add(26 * time.Hour),
			ParticipantLimit:                testutil.ToPtr(int32(100)),
			Status:                          db.EventStatusTypeDraft,
			RequiresApprovalForRegistration: false,
		}

		event, err := queries.CreateEvent(ctx, eventParams)
		require.NoError(t, err, "Should create event")
		assert.Equal(t, eventParams.Title, event.Title, "Event title should match")

		// Retrieve event
		retrievedEvent, err := queries.GetEventByID(ctx, event.ID)
		require.NoError(t, err, "Should retrieve event")
		assert.Equal(t, event.ID, retrievedEvent.ID, "Event IDs should match")
		assert.Equal(t, event.Title, retrievedEvent.Title, "Event titles should match")
	})

	t.Run("EventRegistrationWorkflow", func(t *testing.T) {
		// Setup entities - user first for organization owner
		user := testutil.CreateDBUser(t, queries, func(params *db.CreateUserWithPhoneParams) {
			params.DisplayName = "Registration Test User"
			params.Phone = testutil.ToPtr("+1555000008")
		})

		// Create organization with valid owner
		org := testutil.CreateDBOrganization(t, queries, func(params *db.CreateOrganizationParams) {
			params.Name = "Registration Test Org"
			params.OwnerUserID = user.ID
		})
		event := testutil.CreateDBEvent(t, queries, func(params *db.CreateEventParams) {
			params.OrganizationID = org.ID
			params.CreatedByUserID = user.ID
			params.Status = db.EventStatusTypePublished
		})

		// Create event registration
		registrationParams := db.CreateEventRegistrationParams{
			EventID:          event.ID,
			UserID:           user.ID,
			Status:           "registered",
			PaymentStatus:    db.PaymentStatusTypeNotRequired,
			RegistrationRole: db.EventRegistrationRoleTypeParticipant,
		}

		registration, err := queries.CreateEventRegistration(ctx, registrationParams)
		require.NoError(t, err, "Should create event registration")
		assert.Equal(t, event.ID, registration.EventID, "Registration event ID should match")
		assert.Equal(t, user.ID, registration.UserID, "Registration user ID should match")

		// List registrations for event
		registrations, err := testutil.ListEventRegistrations(queries, ctx, testutil.ListEventRegistrationsParams{
			EventID: event.ID,
			Limit:   10,
			Offset:  0,
		})
		require.NoError(t, err, "Should list event registrations")
		assert.Len(t, registrations, 1, "Should have one registration")
		assert.Equal(t, registration.ID, registrations[0].ID, "Registration IDs should match")
	})
}
