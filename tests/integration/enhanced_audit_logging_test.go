package integration

import (
	"context"
	"path/filepath"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"Membership-SAAS-System-Backend/config"
	"Membership-SAAS-System-Backend/pkg/logger"
)

func TestEnhancedAuditLogging(t *testing.T) {
	// Create temporary directory for test logs
	tempDir := t.TempDir()
	logDir := filepath.Join(tempDir, "audit-logs")

	// Configure audit logging for testing
	auditConfig := &config.AuditConfig{
		Logger: logger.AuditLoggerConfig{
			LogDir:             logDir,
			MaxSize:            10, // Small size for testing
			MaxBackups:         2,
			MaxAge:             1,
			Compress:           true,
			EnableJSON:         true,
			EnableConsole:      false, // Disable for test
			EnableMetrics:      true,
			EnableTracing:      false,
			EnableAlerts:       false,
			EnablePII:          false, // GDPR compliance
			AnonymizeAfterDays: 1,
		},
		Retention: logger.RetentionPolicy{
			AuditLogRetention:     30 * 24 * time.Hour,
			SecurityLogRetention:  90 * 24 * time.Hour,
			AccessLogRetention:    7 * 24 * time.Hour,
			PersonalDataRetention: 30 * 24 * time.Hour,
			AnonymizationEnabled:  true,
			DeletionEnabled:       true,
			ArchiveEnabled:        false, // Disable for test
			CleanupSchedule:       "0 2 * * *",
		},
		GDPR: logger.DefaultGDPRCompliance(),
	}

	// Validate configuration
	require.NoError(t, config.ValidateAuditConfig(auditConfig))

	// Create audit logger
	auditLogger, err := logger.NewSecurityAuditLogger(auditConfig.Logger, nil)
	require.NoError(t, err)
	defer auditLogger.Close()

	t.Run("TestSecurityEventLogging", func(t *testing.T) {
		testSecurityEventLogging(t, auditLogger)
	})

	t.Run("TestAuthenticationLogging", func(t *testing.T) {
		testAuthenticationLogging(t, auditLogger)
	})

	t.Run("TestAuthorizationLogging", func(t *testing.T) {
		testAuthorizationLogging(t, auditLogger)
	})

	t.Run("TestDataAccessLogging", func(t *testing.T) {
		testDataAccessLogging(t, auditLogger)
	})

	t.Run("TestConfigurationChangeLogging", func(t *testing.T) {
		testConfigurationChangeLogging(t, auditLogger)
	})

	t.Run("TestSuspiciousActivityLogging", func(t *testing.T) {
		testSuspiciousActivityLogging(t, auditLogger)
	})

	t.Run("TestGDPRCompliance", func(t *testing.T) {
		testGDPRCompliance(t, auditConfig.GDPR)
	})

	t.Run("TestLogRetention", func(t *testing.T) {
		testLogRetention(t, auditConfig.Retention, logDir, *auditLogger)
	})
}

func testSecurityEventLogging(t *testing.T, auditLogger *logger.SecurityAuditLogger) {
	ctx := context.Background()

	userID := uuid.New()
	orgID := uuid.New()
	resourceID := uuid.New()
	correlationID := uuid.New().String()

	event := logger.SecurityEvent{
		Category:       "authentication",
		Action:         "login_success",
		Severity:       "low",
		UserID:         userID,
		OrganizationID: &orgID,
		ResourceType:   "user_session",
		ResourceID:     resourceID,
		IPAddress:      "*************",
		UserAgent:      "Mozilla/5.0 Test Browser",
		Success:        true,
		Result:         "authenticated",
		Message:        "User successfully logged in",
		Details: map[string]interface{}{
			"login_method": "password",
			"mfa_enabled":  true,
		},
		CorrelationID: correlationID,
		TraceID:       "trace-123",
	}

	// Log the event
	auditLogger.LogSecurityEvent(ctx, event)

	// Verify metrics were updated
	metrics := auditLogger.GetMetrics()
	assert.Greater(t, metrics.TotalLogs, int64(0))
	assert.Greater(t, metrics.AuthenticationLogs, int64(0))
}

func testAuthenticationLogging(t *testing.T, auditLogger *logger.SecurityAuditLogger) {
	ctx := context.Background()

	userID := uuid.New()
	orgID := uuid.New()

	// Test successful authentication
	auditLogger.LogAuthentication(ctx, logger.AuthenticationLogParams{
		Action:         "login",
		UserID:         userID,
		OrganizationID: &orgID,
		Method:         "password",
		Provider:       "local",
		Success:        true,
		Result:         "success",
		MFARequired:    true,
		MFACompleted:   true,
		IPAddress:      "*************",
		UserAgent:      "Test Browser",
		CorrelationID:  uuid.New().String(),
		TraceID:        "trace-auth-123",
	})

	// Test failed authentication
	auditLogger.LogAuthentication(ctx, logger.AuthenticationLogParams{
		Action:         "login",
		UserID:         userID,
		OrganizationID: &orgID,
		Method:         "password",
		Provider:       "local",
		Success:        false,
		Result:         "invalid_credentials",
		IPAddress:      "*************",
		UserAgent:      "Test Browser",
		CorrelationID:  uuid.New().String(),
		TraceID:        "trace-auth-456",
	})

	// Verify metrics
	metrics := auditLogger.GetMetrics()
	assert.GreaterOrEqual(t, metrics.AuthenticationLogs, int64(2))
}

func testAuthorizationLogging(t *testing.T, auditLogger *logger.SecurityAuditLogger) {
	ctx := context.Background()

	userID := uuid.New()
	orgID := uuid.New()
	resourceID := uuid.New()

	// Test authorization success
	auditLogger.LogAuthorization(ctx, logger.AuthorizationLogParams{
		Action:             "read",
		UserID:             userID,
		OrganizationID:     &orgID,
		ResourceType:       "user_data",
		ResourceID:         resourceID,
		RequiredPermission: "user.read",
		UserPermissions:    []string{"user.read", "user.write"},
		Allowed:            true,
		Reason:             "",
		IPAddress:          "*************",
		UserAgent:          "Test Browser",
		CorrelationID:      uuid.New().String(),
		TraceID:            "trace-authz-123",
	})

	// Test authorization failure
	auditLogger.LogAuthorization(ctx, logger.AuthorizationLogParams{
		Action:             "delete",
		UserID:             userID,
		OrganizationID:     &orgID,
		ResourceType:       "user_data",
		ResourceID:         resourceID,
		RequiredPermission: "user.delete",
		UserPermissions:    []string{"user.read"},
		Allowed:            false,
		Reason:             "insufficient_permissions",
		IPAddress:          "*************",
		UserAgent:          "Test Browser",
		CorrelationID:      uuid.New().String(),
		TraceID:            "trace-authz-456",
	})

	// Verify metrics
	metrics := auditLogger.GetMetrics()
	assert.GreaterOrEqual(t, metrics.AuthorizationLogs, int64(2))
}

func testDataAccessLogging(t *testing.T, auditLogger *logger.SecurityAuditLogger) {
	ctx := context.Background()

	userID := uuid.New()
	orgID := uuid.New()
	dataID := uuid.New()

	// Test data export
	auditLogger.LogDataAccess(ctx, logger.DataAccessLogParams{
		Operation:      "export",
		UserID:         userID,
		OrganizationID: &orgID,
		DataType:       "user_data",
		DataID:         dataID,
		FieldsAccessed: []string{"name", "email", "created_at"},
		RecordCount:    150, // High count should trigger elevated severity
		ExportFormat:   "csv",
		Purpose:        "compliance_audit",
		Success:        true,
		Result:         "export_completed",
		IPAddress:      "*************",
		UserAgent:      "Test Browser",
		CorrelationID:  uuid.New().String(),
		TraceID:        "trace-data-123",
	})

	// Verify metrics
	metrics := auditLogger.GetMetrics()
	assert.GreaterOrEqual(t, metrics.DataAccessLogs, int64(1))
}

func testConfigurationChangeLogging(t *testing.T, auditLogger *logger.SecurityAuditLogger) {
	ctx := context.Background()

	userID := uuid.New()
	orgID := uuid.New()

	// Test configuration change
	auditLogger.LogConfigurationChange(ctx, logger.ConfigChangeLogParams{
		Action:         "update",
		UserID:         userID,
		OrganizationID: &orgID,
		ConfigType:     "security",
		ConfigID:       "password_policy",
		ConfigName:     "min_password_length",
		OldValue:       8,
		NewValue:       12,
		ChangeReason:   "Enhanced security requirements",
		Success:        true,
		Result:         "configuration_updated",
		IPAddress:      "*************",
		UserAgent:      "Test Browser",
		CorrelationID:  uuid.New().String(),
		TraceID:        "trace-config-123",
	})

	// Verify metrics
	metrics := auditLogger.GetMetrics()
	assert.GreaterOrEqual(t, metrics.ConfigChangeLogs, int64(1))
}

func testSuspiciousActivityLogging(t *testing.T, auditLogger *logger.SecurityAuditLogger) {
	ctx := context.Background()

	userID := uuid.New()
	orgID := uuid.New()

	// Test suspicious activity
	auditLogger.LogSuspiciousActivity(ctx, logger.SuspiciousActivityParams{
		ActivityType:   "brute_force_attack",
		Severity:       "high",
		UserID:         userID,
		OrganizationID: &orgID,
		Description:    "Multiple failed login attempts detected",
		Indicators:     []string{"repeated_failures", "short_intervals", "multiple_ips"},
		RiskScore:      85,
		ActionTaken:    "account_locked",
		PatternMatched: "brute_force_pattern_v1",
		IPAddress:      "*************",
		UserAgent:      "Test Browser",
		CorrelationID:  uuid.New().String(),
		TraceID:        "trace-suspicious-123",
	})

	// Verify metrics
	metrics := auditLogger.GetMetrics()
	assert.GreaterOrEqual(t, metrics.SuspiciousActivities, int64(1))
}

func testGDPRCompliance(t *testing.T, gdpr *logger.GDPRCompliance) {
	// Test PII redaction
	sensitiveText := "User email: <EMAIL>, Phone: ******-123-4567, IP: *************"
	redactedText := gdpr.RedactPII(sensitiveText)

	assert.NotEqual(t, sensitiveText, redactedText)
	assert.Contains(t, redactedText, "[REDACTED]")

	// Test sensitive field detection
	assert.True(t, gdpr.IsSensitiveField("password"))
	assert.True(t, gdpr.IsSensitiveField("credit_card_number"))
	assert.False(t, gdpr.IsSensitiveField("username"))

	// Test data anonymization
	userID := uuid.New()
	userData := map[string]interface{}{
		"user_id":    userID,
		"email":      "<EMAIL>",
		"ip_address": "*************",
		"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
		"action":     "login",
	}

	anonymizedData := gdpr.AnonymizeUserData(userID, userData)

	// Verify anonymization
	assert.NotEqual(t, userData["user_id"], anonymizedData["user_id"])
	assert.NotEqual(t, userData["email"], anonymizedData["email"])
	assert.NotEqual(t, userData["ip_address"], anonymizedData["ip_address"])
	assert.Equal(t, userData["action"], anonymizedData["action"]) // Non-sensitive data unchanged

	// Test consent management
	ctx := context.Background()

	consentRecord := logger.ConsentRecord{
		UserID:      userID,
		ConsentID:   uuid.New(),
		Purpose:     "audit_logging",
		DataTypes:   []string{"ip_address", "user_agent", "activity_logs"},
		GrantedAt:   time.Now(),
		ExpiresAt:   time.Now().Add(365 * 24 * time.Hour),
		IPAddress:   "*************",
		UserAgent:   "Test Browser",
		ConsentText: "I agree to the processing of my data for audit logging purposes",
		Version:     "1.0",
	}

	err := gdpr.RecordConsent(ctx, consentRecord)
	assert.NoError(t, err)

	// Test consent checking
	hasConsent, err := gdpr.CheckConsent(ctx, userID, "audit_logging")
	assert.NoError(t, err)
	assert.True(t, hasConsent)
}

func testLogRetention(t *testing.T, policy logger.RetentionPolicy, logDir string, auditLogger logger.SecurityAuditLogger) {
	// Create retention service with nil store for testing (or skip this test)
	// The actual implementation would require a db.Store instance
	t.Skip("Log retention service test requires db.Store implementation - skipping for now")

	// When implementing, uncomment and provide a proper store:
	// retentionService, err := logger.NewLogRetentionService(policy, logDir, &auditLogger, store)
	// require.NoError(t, err)
	// defer retentionService.Stop()

	// TODO: Implement the following tests when db.Store is available:
	// - Test retention statistics
	// - Verify log files were created
	// - Test export functionality

	/*
		stats, err := retentionService.GetRetentionStatistics()
		require.NoError(t, err)

		assert.Contains(t, stats, "active_logs")
		assert.Contains(t, stats, "retention_days")
		assert.Contains(t, stats, "anonymization_enabled")

		// Verify log files were created
		logFiles, err := filepath.Glob(filepath.Join(logDir, "*.log"))
		require.NoError(t, err)
		assert.Greater(t, len(logFiles), 0, "Expected audit log files to be created")

		// Test export functionality
		ctx := context.Background()
		exportParams := logger.AuditLogExportParams{
			Query: logger.AuditLogQuery{
				StartTime: time.Now().Add(-24 * time.Hour).Format(time.RFC3339),
				EndTime:   time.Now().Format(time.RFC3339),
				Limit:     100,
			},
			Format:     "json",
			IncludePII: false,
			Reason:     "compliance_audit",
			ApprovedBy: uuid.New(),
			ExportID:   uuid.New(),
		}

		exportFile, err := retentionService.ExportLogsForCompliance(ctx, exportParams)
		require.NoError(t, err)
		assert.NotEmpty(t, exportFile)
	*/
}

func TestAuditMiddleware(t *testing.T) {
	// This would test the Echo middleware integration
	// Implementation would create a test Echo server and verify audit logging
	t.Skip("Middleware test requires Echo server setup - implement separately")
}

func TestNATSAuditLogger(t *testing.T) {
	// This would test the NATS integration
	// Implementation would require NATS server setup
	t.Skip("NATS test requires NATS server setup - implement separately")
}

func TestPrometheusMetrics(t *testing.T) {
	// This would test Prometheus metrics integration
	// Implementation would verify metrics are properly exposed
	t.Skip("Prometheus test requires metrics server setup - implement separately")
}
