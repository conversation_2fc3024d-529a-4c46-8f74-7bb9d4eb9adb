package integration

import (
	"context"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/services/event"
	"Membership-SAAS-System-Backend/internal/testutil"
	"Membership-SAAS-System-Backend/internal/utils/validation"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// EventComprehensiveIntegrationTestSuite provides comprehensive integration testing focused on coverage improvement
type EventComprehensiveIntegrationTestSuite struct {
	suite.Suite
	testDB    *testutil.TestDatabase
	store     db.Store
	queries   *db.Queries // Keep for backward compatibility
	validator *validation.RequestValidator
	ctx       context.Context

	// Event domain services - focusing on direct service testing rather than management facade
	eventService      event.EventService
	tagService        event.TagService
	mediaService      event.MediaService
	volunteerService  event.VolunteerService
	statisticsService event.StatisticsService

	// Test entities
	testOrg   db.Organization
	testUser  db.User
	testAdmin db.User
}

// SetupSuite runs once before all tests in the suite
func (suite *EventComprehensiveIntegrationTestSuite) SetupSuite() {
	suite.ctx = context.Background()
	suite.testDB, suite.store = testutil.SetupTestDatabaseWithStore(suite.T())
	suite.queries = db.New(suite.testDB.DB) // Keep queries for backward compatibility
	suite.validator = validation.NewRequestValidator()

	// Initialize individual event services directly (not through management facade)
	// This provides better coverage of the actual business logic services
	suite.eventService = event.NewEventService(suite.store, nil)
	suite.tagService = event.NewTagService(suite.store)
	suite.mediaService = event.NewMediaService(suite.store)
	suite.volunteerService = event.NewVolunteerService(suite.store)
	suite.statisticsService = event.NewStatisticsService(suite.store)
}

// SetupTest runs before each test
func (suite *EventComprehensiveIntegrationTestSuite) SetupTest() {
	// Clean database state before each test
	suite.testDB.TruncateAllTables(suite.T())

	// Create test users first (needed for organization owner)
	suite.testUser = testutil.CreateDBUser(suite.T(), suite.queries, func(params *db.CreateUserWithPhoneParams) {
		params.DisplayName = "Test User"
		params.Phone = testutil.ToPtr("+1234567890")
	})

	suite.testAdmin = testutil.CreateDBUser(suite.T(), suite.queries, func(params *db.CreateUserWithPhoneParams) {
		params.DisplayName = "Test Admin"
		params.Phone = testutil.ToPtr("+1987654321")
	})

	// Create test organization with admin as owner
	suite.testOrg = testutil.CreateDBOrganization(suite.T(), suite.queries, func(params *db.CreateOrganizationParams) {
		params.Name = "Integration Test Organization"
		params.Status = "active"
		params.OwnerUserID = suite.testAdmin.ID
	})

	// Add admin to organization with admin role
	_, err := suite.queries.AddUserToOrganization(suite.ctx, db.AddUserToOrganizationParams{
		UserID:         suite.testAdmin.ID,
		OrganizationID: suite.testOrg.ID,
		Role:           "admin",
	})
	require.NoError(suite.T(), err)

	// Add regular user to organization as member
	_, err = suite.queries.AddUserToOrganization(suite.ctx, db.AddUserToOrganizationParams{
		UserID:         suite.testUser.ID,
		OrganizationID: suite.testOrg.ID,
		Role:           "member",
	})
	require.NoError(suite.T(), err)
}

// TestCrudServiceComprehensiveWorkflow tests all CRUD operations thoroughly
func (suite *EventComprehensiveIntegrationTestSuite) TestCrudServiceComprehensiveWorkflow() {
	// Test 1: Create Event
	createReq := payloads.CreateEventRequest{
		Title:                           "CRUD Test Event",
		JsonContent:                     []byte(`{"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "Test event description"}]}]}`),
		LocationType:                    "physical",
		LocationFullAddress:             testutil.ToPtr("123 Test Street"),
		StartTime:                       time.Now().Add(24 * time.Hour),
		EndTime:                         time.Now().Add(26 * time.Hour),
		ParticipantLimit:                testutil.ToPtr(int32(100)),
		RequiresApprovalForRegistration: false,
		Status:                          testutil.ToPtr("draft"),
	}

	createdEvent, err := suite.eventService.CreateEvent(suite.ctx, suite.testOrg.ID, suite.testAdmin.ID, createReq)
	suite.NoError(err, "Should create event successfully")
	suite.Equal(createReq.Title, createdEvent.Title)
	suite.Equal("draft", createdEvent.Status)

	// Test 2: Get Event by ID (admin view)
	retrievedEvent, err := suite.eventService.GetEventByID(suite.ctx, createdEvent.ID, suite.testAdmin.ID, true)
	suite.NoError(err, "Admin should retrieve event successfully")
	suite.Equal(createdEvent.ID, retrievedEvent.ID)
	suite.Equal(createdEvent.Title, retrievedEvent.Title)

	// Test 3: Get Event by ID (user view)
	userViewEvent, err := suite.eventService.GetEventByID(suite.ctx, createdEvent.ID, suite.testUser.ID, false)
	suite.NoError(err, "User should retrieve event successfully")
	suite.Equal(createdEvent.ID, userViewEvent.ID)

	// Test 4: Update Event Details
	updateReq := payloads.UpdateEventRequest{
		Title:             testutil.ToPtr("Updated CRUD Test Event"),
		LocationOnlineURL: testutil.ToPtr("https://zoom.us/meeting/123"),
	}

	updatedEvent, err := suite.eventService.UpdateEventDetails(suite.ctx, createdEvent.ID, suite.testOrg.ID, suite.testAdmin.ID, updateReq)
	suite.NoError(err, "Should update event successfully")
	suite.Equal(*updateReq.Title, updatedEvent.Title)

	// Test 5: Get Public Event by ID
	// First publish the event via direct SQL (since UpdateEventDetails doesn't change status)
	_, err = suite.queries.UpdateEventStatus(suite.ctx, db.UpdateEventStatusParams{
		ID:     createdEvent.ID,
		Status: db.EventStatusTypePublished,
	})
	suite.NoError(err)

	publicEvent, err := suite.eventService.GetPublicEventByID(suite.ctx, createdEvent.ID, suite.testUser.ID)
	suite.NoError(err, "Should get public event successfully")
	suite.Equal(createdEvent.ID, publicEvent.ID)

	// Test 6: Delete Event
	err = suite.eventService.DeleteEvent(suite.ctx, createdEvent.ID, suite.testOrg.ID, suite.testAdmin.ID)
	suite.NoError(err, "Should delete event successfully")

	// Verify event is deleted
	_, err = suite.eventService.GetEventByID(suite.ctx, createdEvent.ID, suite.testAdmin.ID, true)
	suite.Error(err, "Should not retrieve deleted event")
}

// TestQueryServiceComprehensiveWorkflow tests all query operations
func (suite *EventComprehensiveIntegrationTestSuite) TestQueryServiceComprehensiveWorkflow() {
	// Create multiple events for testing
	events := make([]db.Event, 3)
	for i := 0; i < 3; i++ {
		event := testutil.CreateDBEvent(suite.T(), suite.queries, func(params *db.CreateEventParams) {
			params.OrganizationID = suite.testOrg.ID
			params.CreatedByUserID = suite.testAdmin.ID
			params.Title = "Query Test Event " + string(rune('A'+i))
			params.Status = db.EventStatusTypePublished
		})
		events[i] = event
	}

	// Test 1: List Events by Organization
	listReq := payloads.ListOrganizationEventsRequest{
		PageRequest: payloads.PageRequest{Limit: 10, Offset: 0},
		Status:      testutil.ToPtr("published"),
	}

	orgEvents, total, err := suite.eventService.ListEventsByOrganization(suite.ctx, suite.testOrg.ID, suite.testAdmin.ID, listReq)
	suite.NoError(err, "Should list organization events successfully")
	suite.Equal(int64(3), total, "Should have 3 events")
	suite.Len(orgEvents, 3, "Should return 3 events")

	// Test 2: List Public Events
	publicListReq := payloads.ListPublicEventsRequest{
		PageRequest: payloads.PageRequest{Limit: 5, Offset: 0},
	}

	publicEvents, publicTotal, err := suite.eventService.ListPublicEvents(suite.ctx, suite.testUser.ID, publicListReq)
	suite.NoError(err, "Should list public events successfully")
	suite.Equal(int64(3), publicTotal, "Should have 3 public events")
	suite.Len(publicEvents, 3, "Should return 3 public events")

	// Test 3: List Popular Events
	_, err = suite.eventService.ListPopularEvents(suite.ctx, 5)
	suite.NoError(err, "Should list popular events successfully")
	// Note: May return empty if no registrations exist, which is fine

	// Test 4: List Popular Events by Organization
	_, err = suite.eventService.ListPopularEventsByOrganization(suite.ctx, suite.testOrg.ID, 5)
	suite.NoError(err, "Should list organization popular events successfully")

	// Test 5: Search Events
	searchResults, err := suite.eventService.SearchEvents(suite.ctx, "Query Test", &suite.testOrg.ID, 10)
	suite.NoError(err, "Should search events successfully")
	suite.GreaterOrEqual(len(searchResults), 0, "Should return search results")
}

// TestTagServiceComprehensiveWorkflow tests all tag operations
func (suite *EventComprehensiveIntegrationTestSuite) TestTagServiceComprehensiveWorkflow() {
	// Create test event
	event := testutil.CreateDBEvent(suite.T(), suite.queries, func(params *db.CreateEventParams) {
		params.OrganizationID = suite.testOrg.ID
		params.CreatedByUserID = suite.testAdmin.ID
		params.Title = "Tag Test Event"
		params.Status = db.EventStatusTypePublished
	})

	// Create test event tags (not post tags)
	tag1, err := suite.queries.CreateTag(suite.ctx, db.CreateTagParams{
		NameEnUs:        "integration-test-1",
		NameZhHk:        "integration-test-1",
		NameZhCn:        "integration-test-1",
		DescriptionEnUs: testutil.ToPtr("First Integration Test Tag"),
		OrganizationID:  &suite.testOrg.ID,
		ApprovalStatus:  db.TagApprovalStatusApproved,
		CreatedByUserID: suite.testAdmin.ID,
	})
	suite.NoError(err)

	tag2, err := suite.queries.CreateTag(suite.ctx, db.CreateTagParams{
		NameEnUs:        "integration-test-2",
		NameZhHk:        "integration-test-2",
		NameZhCn:        "integration-test-2",
		DescriptionEnUs: testutil.ToPtr("Second Integration Test Tag"),
		OrganizationID:  &suite.testOrg.ID,
		ApprovalStatus:  db.TagApprovalStatusApproved,
		CreatedByUserID: suite.testAdmin.ID,
	})
	suite.NoError(err)

	// Test 1: Get Available Tags for Organization
	availableTags, err := suite.tagService.GetAvailableTagsForOrganization(suite.ctx, suite.testOrg.ID)
	suite.NoError(err, "Should get available tags successfully")
	// Note: Available tags may be filtered by organization, so we check that the method works
	suite.GreaterOrEqual(len(availableTags), 0, "Should get available tags (may be empty)")

	// Test 2: Add Tag to Event (use created tags regardless of available tags filter)
	err = suite.tagService.AddTagToEvent(suite.ctx, event.ID, tag1.ID, suite.testOrg.ID, suite.testAdmin.ID)
	suite.NoError(err, "Should add tag to event successfully")

	// Test 3: List Tags for Event
	eventTags, err := suite.tagService.ListTagsForEvent(suite.ctx, event.ID)
	suite.NoError(err, "Should list event tags successfully")
	suite.Len(eventTags, 1, "Should have one tag")
	suite.Equal(tag1.ID, eventTags[0].ID, "Tag ID should match")

	// Test 4: Bulk Update Event Tags
	tagIDs := []uuid.UUID{tag1.ID, tag2.ID}
	err = suite.tagService.BulkUpdateTags(suite.ctx, event.ID, tagIDs, suite.testOrg.ID, suite.testAdmin.ID)
	suite.NoError(err, "Should bulk update tags successfully")

	// Verify bulk update worked
	updatedTags, err := suite.tagService.ListTagsForEvent(suite.ctx, event.ID)
	suite.NoError(err, "Should list updated tags successfully")
	suite.Len(updatedTags, 2, "Should have two tags after bulk update")

	// Test 5: Remove Tag from Event
	err = suite.tagService.RemoveTagFromEvent(suite.ctx, event.ID, tag1.ID, suite.testOrg.ID, suite.testAdmin.ID)
	suite.NoError(err, "Should remove tag from event successfully")

	// Verify tag removal
	finalTags, err := suite.tagService.ListTagsForEvent(suite.ctx, event.ID)
	suite.NoError(err, "Should list final tags successfully")
	suite.Len(finalTags, 1, "Should have one tag after removal")
	suite.Equal(tag2.ID, finalTags[0].ID, "Remaining tag should be tag2")
}

// TestVolunteerServiceComprehensiveWorkflow tests volunteer management operations
func (suite *EventComprehensiveIntegrationTestSuite) TestVolunteerServiceComprehensiveWorkflow() {
	// Create test event
	event := testutil.CreateDBEvent(suite.T(), suite.queries, func(params *db.CreateEventParams) {
		params.OrganizationID = suite.testOrg.ID
		params.CreatedByUserID = suite.testAdmin.ID
		params.Title = "Volunteer Test Event"
		params.Status = db.EventStatusTypePublished
	})

	// Create volunteer application
	application, err := suite.queries.CreateEventVolunteerApplication(suite.ctx, db.CreateEventVolunteerApplicationParams{
		EventID:                event.ID,
		UserID:                 suite.testUser.ID,
		OrganizationID:         suite.testOrg.ID,
		ApplicationNotesByUser: testutil.ToPtr("I have experience volunteering"),
		Status:                 db.ApplicationStatusEnumPending,
	})
	suite.NoError(err)

	// Test 1: List Pending Review Applications for Event (page numbers start from 1)
	pendingApplications, total, err := suite.volunteerService.ListPendingReviewVolunteerApplicationsForEvent(suite.ctx, event.ID, 1, 10)
	suite.NoError(err, "Should list pending applications for event")
	suite.Equal(int64(1), total, "Should have 1 pending application")
	suite.Len(pendingApplications, 1, "Should return 1 pending application")

	// Test 2: List Pending Review Applications for Organization (page numbers start from 1)
	orgPendingApplications, orgTotal, err := suite.volunteerService.ListPendingReviewVolunteerApplicationsForOrganization(suite.ctx, suite.testOrg.ID, 1, 10)
	suite.NoError(err, "Should list pending applications for organization")
	suite.Equal(int64(1), orgTotal, "Should have 1 pending application for org")
	suite.Len(orgPendingApplications, 1, "Should return 1 pending application for org")

	// Test 3: Get Event Volunteer Application Details
	appDetails, err := suite.volunteerService.GetEventVolunteerApplicationDetails(suite.ctx, suite.testAdmin.ID, suite.testOrg.ID, event.ID, application.ID)
	suite.NoError(err, "Should get application details")
	suite.Equal(application.ID, appDetails.ID, "Application ID should match")

	// Test 4: Approve Volunteer Application
	err = suite.volunteerService.ApproveVolunteerApplication(suite.ctx, application.ID, suite.testAdmin.ID, suite.testOrg.ID)
	suite.NoError(err, "Should approve volunteer application")

	// Test 5: Mark Volunteer Attendance
	err = suite.volunteerService.MarkVolunteerAttendance(suite.ctx, application.ID, suite.testAdmin.ID, suite.testOrg.ID)
	suite.NoError(err, "Should mark volunteer attendance")

	// Test 6: List Organization Event Volunteer Applications
	orgApps, orgAppsTotal, err := suite.volunteerService.ListOrgEventVolunteerApplications(suite.ctx, suite.testOrg.ID, "approved", payloads.PageRequest{
		Limit:  10,
		Offset: 0,
	})
	suite.NoError(err, "Should list organization volunteer applications")
	suite.Equal(int64(1), orgAppsTotal, "Should have 1 approved application")
	suite.Len(orgApps, 1, "Should return 1 approved application")

	// Test 7: Count Volunteer Applications for Event
	count, err := suite.volunteerService.CountVolunteerApplicationsForEvent(suite.ctx, event.ID)
	suite.NoError(err, "Should count volunteer applications")
	suite.Equal(int64(1), count, "Should have 1 application for event")
}

// TestStatisticsServiceComprehensiveWorkflow tests statistics operations
func (suite *EventComprehensiveIntegrationTestSuite) TestStatisticsServiceComprehensiveWorkflow() {
	// Create test event with registrations and volunteers
	event := testutil.CreateDBEvent(suite.T(), suite.queries, func(params *db.CreateEventParams) {
		params.OrganizationID = suite.testOrg.ID
		params.CreatedByUserID = suite.testAdmin.ID
		params.Title = "Statistics Test Event"
		params.Status = db.EventStatusTypePublished
		params.ParticipantLimit = testutil.ToPtr(int32(10))
	})

	// Create registrations
	_, err := suite.queries.CreateEventRegistration(suite.ctx, db.CreateEventRegistrationParams{
		EventID:          event.ID,
		UserID:           suite.testUser.ID,
		Status:           db.EventRegistrationStatusTypeRegistered,
		PaymentStatus:    db.PaymentStatusTypeNotRequired,
		RegistrationRole: db.EventRegistrationRoleTypeParticipant,
	})
	suite.NoError(err)

	// Create volunteer application
	_, err = suite.queries.CreateEventVolunteerApplication(suite.ctx, db.CreateEventVolunteerApplicationParams{
		EventID:        event.ID,
		UserID:         suite.testUser.ID,
		OrganizationID: suite.testOrg.ID,
		Status:         db.ApplicationStatusEnumApproved,
	})
	suite.NoError(err)

	// Test 1: Get Event Registration Counts
	regCounts, err := suite.statisticsService.GetEventRegistrationCounts(suite.ctx, event.ID)
	suite.NoError(err, "Should get registration counts")
	suite.Equal(int64(1), regCounts.TotalRegistrations, "Should have 1 registration")

	// Test 2: Get Organization Event Statistics
	orgStats, err := suite.statisticsService.GetOrganizationEventStatistics(suite.ctx, suite.testOrg.ID, payloads.PageRequest{
		Limit:  10,
		Offset: 0,
	})
	suite.NoError(err, "Should get organization statistics")
	suite.NotNil(orgStats, "Organization stats should not be nil")

	// Test 3: Get User Event Statistics
	userStats, err := suite.statisticsService.GetUserEventStatistics(suite.ctx, suite.testUser.ID, payloads.UserEventStatisticsRequest{
		Limit:  10,
		Offset: 0,
	})
	suite.NoError(err, "Should get user statistics")
	suite.NotNil(userStats, "User stats should not be nil")

	// Test 4: Get Popular Events
	_, err = suite.statisticsService.GetPopularEvents(suite.ctx, 5, &suite.testOrg.ID)
	suite.NoError(err, "Should get popular events")
	// Note: Results may be empty, which is acceptable

	// Test 5: Get Top Events by Registrations
	_, err = suite.statisticsService.GetTopEventsByRegistrations(suite.ctx, suite.testOrg.ID, 5)
	suite.NoError(err, "Should get top events by registrations")

	// Test 6: Get Volunteer Participation Stats
	volunteerStats, err := suite.statisticsService.GetVolunteerParticipationStats(suite.ctx, suite.testOrg.ID)
	suite.NoError(err, "Should get volunteer participation stats")
	suite.NotNil(volunteerStats, "Volunteer stats should not be nil")
}

// TearDownSuite runs once after all tests
func (suite *EventComprehensiveIntegrationTestSuite) TearDownSuite() {
	if suite.testDB != nil {
		suite.testDB.Cleanup()
	}
}

// TestEventComprehensiveIntegrationTestSuite runs the comprehensive event service integration test suite
func TestEventComprehensiveIntegrationTestSuite(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration tests in short mode")
	}

	suite.Run(t, new(EventComprehensiveIntegrationTestSuite))
}
