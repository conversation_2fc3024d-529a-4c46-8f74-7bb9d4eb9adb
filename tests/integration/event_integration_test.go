package integration

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// EventIntegrationTestSuite tests event management endpoints with real API calls
type EventIntegrationTestSuite struct {
	suite.Suite
	apiSuite *APITestSuite
}

func (suite *EventIntegrationTestSuite) SetupSuite() {
	suite.apiSuite = SetupAPITest(suite.T())
}

func (suite *EventIntegrationTestSuite) TearDownSuite() {
	suite.apiSuite.TeardownAPITest(suite.T())
}

func (suite *EventIntegrationTestSuite) SetupTest() {
	suite.apiSuite.CleanupTestData(suite.T())
}

// TestEventCreation tests complete event creation workflow
func (suite *EventIntegrationTestSuite) TestEventCreation() {
	createReq := map[string]interface{}{
		"title":       "Test Integration Event",
		"description": "This is a test event for integration testing",
		"location":    "Test Location",
		"start_time":  time.Now().Add(24 * time.Hour).Format(time.RFC3339),
		"end_time":    time.Now().Add(26 * time.Hour).Format(time.RFC3339),
		"capacity":    100,
		"status":      "draft",
	}

	rec, err := suite.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/events", createReq)
	require.NoError(suite.T(), err)

	var response EventResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusCreated, &response)

	// Verify event was created with correct data
	require.Equal(suite.T(), "Test Integration Event", response.Title)
	require.Equal(suite.T(), "Test Location", response.Location)
	require.Equal(suite.T(), int32(100), response.Capacity)
	require.Equal(suite.T(), "draft", response.Status)
}

// TestEventCreation_ValidationErrors tests event creation with invalid data
func (suite *EventIntegrationTestSuite) TestEventCreation_ValidationErrors() {
	// Test missing required fields
	createReq := map[string]interface{}{
		"description": "Missing title",
	}

	rec, err := suite.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/events", createReq)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertErrorResponse(suite.T(), rec, http.StatusBadRequest, "title")

	// Test invalid date range
	createReq = map[string]interface{}{
		"title":      "Invalid Date Event",
		"start_time": time.Now().Add(24 * time.Hour).Format(time.RFC3339),
		"end_time":   time.Now().Add(12 * time.Hour).Format(time.RFC3339), // End before start
	}

	rec, err = suite.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/events", createReq)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertErrorResponse(suite.T(), rec, http.StatusBadRequest, "end time must be after start time")
}

// TestEventUpdate tests event update functionality
func (suite *EventIntegrationTestSuite) TestEventUpdate() {
	// First create an event
	eventID := suite.createTestEvent()

	// Update the event
	updateReq := map[string]interface{}{
		"title":       "Updated Event Title",
		"description": "Updated description",
		"capacity":    150,
	}

	rec, err := suite.apiSuite.MakeAuthenticatedRequest("PUT", fmt.Sprintf("/api/v1/events/%s", eventID), updateReq)
	require.NoError(suite.T(), err)

	var response EventResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	// Verify updates were applied
	require.Equal(suite.T(), "Updated Event Title", response.Title)
	require.Equal(suite.T(), "Updated description", response.Description)
	require.Equal(suite.T(), int32(150), response.Capacity)
}

// TestEventUpdate_NotFound tests updating non-existent event
func (suite *EventIntegrationTestSuite) TestEventUpdate_NotFound() {
	updateReq := map[string]interface{}{
		"title": "Update Non-existent Event",
	}

	rec, err := suite.apiSuite.MakeAuthenticatedRequest("PUT", "/api/v1/events/00000000-0000-0000-0000-000000000999", updateReq)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertErrorResponse(suite.T(), rec, http.StatusNotFound, "event not found")
}

// TestEventDeletion tests event deletion functionality
func (suite *EventIntegrationTestSuite) TestEventDeletion() {
	// First create an event
	eventID := suite.createTestEvent()

	// Delete the event
	rec, err := suite.apiSuite.MakeAuthenticatedRequest("DELETE", fmt.Sprintf("/api/v1/events/%s", eventID), nil)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusNoContent, nil)

	// Verify event is deleted
	rec, err = suite.apiSuite.MakeAuthenticatedRequest("GET", fmt.Sprintf("/api/v1/events/%s", eventID), nil)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertErrorResponse(suite.T(), rec, http.StatusNotFound, "event not found")
}

// TestEventListing tests event listing with filters and pagination
func (suite *EventIntegrationTestSuite) TestEventListing() {
	// Create multiple test events
	suite.createTestEvent()
	suite.createTestEvent()
	suite.createTestEvent()

	// Test basic listing
	rec, err := suite.apiSuite.MakeAuthenticatedRequest("GET", "/api/v1/events", nil)
	require.NoError(suite.T(), err)

	var response PaginatedEventsResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	require.GreaterOrEqual(suite.T(), len(response.Events), 3)
	require.NotNil(suite.T(), response.Pagination)

	// Test listing with pagination
	rec, err = suite.apiSuite.MakeAuthenticatedRequest("GET", "/api/v1/events?page=1&limit=2", nil)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	require.LessOrEqual(suite.T(), len(response.Events), 2)

	// Test listing with status filter
	rec, err = suite.apiSuite.MakeAuthenticatedRequest("GET", "/api/v1/events?status=draft", nil)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	for _, event := range response.Events {
		require.Equal(suite.T(), "draft", event.Status)
	}
}

// TestEventPublishing tests event publishing workflow
func (suite *EventIntegrationTestSuite) TestEventPublishing() {
	// Create a draft event
	eventID := suite.createTestEvent()

	// Publish the event
	publishReq := map[string]interface{}{
		"status": "published",
	}

	rec, err := suite.apiSuite.MakeAuthenticatedRequest("PATCH", fmt.Sprintf("/api/v1/events/%s/status", eventID), publishReq)
	require.NoError(suite.T(), err)

	var response EventResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	require.Equal(suite.T(), "published", response.Status)
	require.NotNil(suite.T(), response.PublishedAt)
}

// TestEventRegistration tests event registration functionality
func (suite *EventIntegrationTestSuite) TestEventRegistration() {
	// Create and publish an event
	eventID := suite.createTestEvent()
	suite.publishEvent(eventID)

	// Register for the event
	registerReq := map[string]interface{}{
		"participant_type": "individual",
		"notes":            "Test registration",
	}

	rec, err := suite.apiSuite.MakeUserRequest("POST", fmt.Sprintf("/api/v1/events/%s/register", eventID), registerReq)
	require.NoError(suite.T(), err)

	var response EventRegistrationResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusCreated, &response)

	require.Equal(suite.T(), eventID, response.EventID.String())
	require.Equal(suite.T(), "registered", response.Status)
}

// TestEventRegistration_AlreadyRegistered tests duplicate registration
func (suite *EventIntegrationTestSuite) TestEventRegistration_AlreadyRegistered() {
	// Create and publish an event
	eventID := suite.createTestEvent()
	suite.publishEvent(eventID)

	// Register for the event first time
	registerReq := map[string]interface{}{
		"participant_type": "individual",
	}

	rec, err := suite.apiSuite.MakeUserRequest("POST", fmt.Sprintf("/api/v1/events/%s/register", eventID), registerReq)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusCreated, nil)

	// Try to register again
	rec, err = suite.apiSuite.MakeUserRequest("POST", fmt.Sprintf("/api/v1/events/%s/register", eventID), registerReq)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertErrorResponse(suite.T(), rec, http.StatusConflict, "already registered")
}

// TestEventVolunteerApplication tests volunteer application workflow
func (suite *EventIntegrationTestSuite) TestEventVolunteerApplication() {
	// Create and publish an event
	eventID := suite.createTestEvent()
	suite.publishEvent(eventID)

	// Apply as volunteer
	applyReq := map[string]interface{}{
		"motivation": "I want to help with this event",
		"experience": "Previous volunteer experience",
	}

	rec, err := suite.apiSuite.MakeUserRequest("POST", fmt.Sprintf("/api/v1/events/%s/volunteer/apply", eventID), applyReq)
	require.NoError(suite.T(), err)

	var response VolunteerApplicationResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusCreated, &response)

	require.Equal(suite.T(), eventID, response.EventID.String())
	require.Equal(suite.T(), "pending", response.Status)
}

// TestEventStatistics tests event statistics endpoints
func (suite *EventIntegrationTestSuite) TestEventStatistics() {
	// Create events and registrations for statistics
	eventID := suite.createTestEvent()
	suite.publishEvent(eventID)

	// Get event statistics
	rec, err := suite.apiSuite.MakeAuthenticatedRequest("GET", fmt.Sprintf("/api/v1/events/%s/statistics", eventID), nil)
	require.NoError(suite.T(), err)

	var response EventStatisticsResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	require.NotNil(suite.T(), response.RegistrationStats)
	require.NotNil(suite.T(), response.VolunteerStats)
	require.GreaterOrEqual(suite.T(), response.RegistrationStats.Total, int32(0))
}

// TestEventMediaUpload tests event media upload functionality
func (suite *EventIntegrationTestSuite) TestEventMediaUpload() {
	// Create an event
	eventID := suite.createTestEvent()

	// Test media upload using the file upload tester
	uploadTest := suite.apiSuite.NewFileUploadTest(suite.T())
	uploadTest.TestEventMediaUpload(eventID, []byte("test file content"), "test-image.jpg")
}

// TestEventTags tests event tag management
func (suite *EventIntegrationTestSuite) TestEventTags() {
	// Create an event
	eventID := suite.createTestEvent()

	// Add tags to event
	tagsReq := map[string]interface{}{
		"tag_ids": []string{"tag1", "tag2"},
	}

	rec, err := suite.apiSuite.MakeAuthenticatedRequest("PUT", fmt.Sprintf("/api/v1/events/%s/tags", eventID), tagsReq)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, nil)

	// Get event with tags
	rec, err = suite.apiSuite.MakeAuthenticatedRequest("GET", fmt.Sprintf("/api/v1/events/%s", eventID), nil)
	require.NoError(suite.T(), err)

	var response EventResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	require.NotNil(suite.T(), response.Tags)
}

// TestUnauthorizedAccess tests unauthorized access to protected endpoints
func (suite *EventIntegrationTestSuite) TestUnauthorizedAccess() {
	// Test creating event without authentication
	createReq := map[string]interface{}{
		"title": "Unauthorized Event",
	}

	rec, err := suite.apiSuite.MakeUnauthenticatedRequest("POST", "/api/v1/events", createReq)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertErrorResponse(suite.T(), rec, http.StatusUnauthorized, "unauthorized")
}

// TestCrossOrganizationAccess tests that users cannot access events from other organizations
func (suite *EventIntegrationTestSuite) TestCrossOrganizationAccess() {
	// Create event in organization A
	eventID := suite.createTestEvent()

	// Try to access with user from organization B (would need different org token)
	// This test assumes we have multi-org test setup
	rec, err := suite.apiSuite.MakeUserRequest("GET", fmt.Sprintf("/api/v1/events/%s", eventID), nil)
	require.NoError(suite.T(), err)

	// Should either return the event (if accessible) or forbidden (if not accessible)
	// The exact behavior depends on your organization access control implementation
	require.True(suite.T(), rec.Code == http.StatusOK || rec.Code == http.StatusForbidden)
}

// Helper methods for test setup

func (suite *EventIntegrationTestSuite) createTestEvent() string {
	createReq := map[string]interface{}{
		"title":       "Test Event",
		"description": "Test event description",
		"location":    "Test Location",
		"start_time":  time.Now().Add(24 * time.Hour).Format(time.RFC3339),
		"end_time":    time.Now().Add(26 * time.Hour).Format(time.RFC3339),
		"capacity":    100,
		"status":      "draft",
	}

	rec, err := suite.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/events", createReq)
	require.NoError(suite.T(), err)

	var response EventResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusCreated, &response)

	return response.ID.String()
}

func (suite *EventIntegrationTestSuite) publishEvent(eventID string) {
	publishReq := map[string]interface{}{
		"status": "published",
	}

	rec, err := suite.apiSuite.MakeAuthenticatedRequest("PATCH", fmt.Sprintf("/api/v1/events/%s/status", eventID), publishReq)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, nil)
}

func TestEventIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(EventIntegrationTestSuite))
}
