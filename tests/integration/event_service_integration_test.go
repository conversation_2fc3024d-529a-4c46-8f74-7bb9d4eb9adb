package integration

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/services/event"
	"Membership-SAAS-System-Backend/internal/testutil"
	"Membership-SAAS-System-Backend/internal/utils/validation"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// eventManagementAdapter adapts the new EventService to the legacy ManagementService interface
type eventManagementAdapter struct {
	eventService     event.EventService
	tagService       event.TagService
	lifecycleService event.LifecycleService
}

// CRUD Operations delegation to EventService
func (a *eventManagementAdapter) CreateEvent(ctx context.Context, orgID uuid.UUID, userID uuid.UUID, params payloads.CreateEventRequest) (payloads.EventResponse, error) {
	return a.eventService.CreateEvent(ctx, orgID, userID, params)
}

func (a *eventManagementAdapter) GetEventByID(ctx context.Context, eventID uuid.UUID, currentUserID uuid.UUID, isAdminView bool) (payloads.EventResponse, error) {
	return a.eventService.GetEventByID(ctx, eventID, currentUserID, isAdminView)
}

func (a *eventManagementAdapter) GetPublicEventByID(ctx context.Context, eventID uuid.UUID, currentUserID uuid.UUID) (payloads.PublicEventResponse, error) {
	return a.eventService.GetPublicEventByID(ctx, eventID, currentUserID)
}

func (a *eventManagementAdapter) UpdateEventDetails(ctx context.Context, eventID uuid.UUID, orgID uuid.UUID, userID uuid.UUID, req payloads.UpdateEventRequest) (payloads.EventResponse, error) {
	return a.eventService.UpdateEventDetails(ctx, eventID, orgID, userID, req)
}

func (a *eventManagementAdapter) DeleteEvent(ctx context.Context, eventID uuid.UUID, orgID uuid.UUID, userID uuid.UUID) error {
	return a.eventService.DeleteEvent(ctx, eventID, orgID, userID)
}

// Query Operations delegation to EventService
func (a *eventManagementAdapter) ListEventsByOrganization(ctx context.Context, orgID uuid.UUID, userID uuid.UUID, filterParams payloads.ListOrganizationEventsRequest) ([]payloads.EventResponse, int64, error) {
	return a.eventService.ListEventsByOrganization(ctx, orgID, userID, filterParams)
}

func (a *eventManagementAdapter) ListPublicEvents(ctx context.Context, userID uuid.UUID, filterParams payloads.ListPublicEventsRequest) ([]payloads.PublicEventResponse, int64, error) {
	return a.eventService.ListPublicEvents(ctx, userID, filterParams)
}

func (a *eventManagementAdapter) ListPopularEvents(ctx context.Context, limit int) ([]payloads.PopularEvent, error) {
	return a.eventService.ListPopularEvents(ctx, limit)
}

func (a *eventManagementAdapter) ListPopularEventsByOrganization(ctx context.Context, orgID uuid.UUID, limit int) ([]payloads.PopularEvent, error) {
	return a.eventService.ListPopularEventsByOrganization(ctx, orgID, limit)
}

// Tag Operations delegation to TagService
func (a *eventManagementAdapter) AddTagToEvent(ctx context.Context, eventID uuid.UUID, tagID uuid.UUID, orgID uuid.UUID, userID uuid.UUID) error {
	return a.tagService.AddTagToEvent(ctx, eventID, tagID, orgID, userID)
}

func (a *eventManagementAdapter) RemoveTagFromEvent(ctx context.Context, eventID uuid.UUID, tagID uuid.UUID, orgID uuid.UUID, userID uuid.UUID) error {
	return a.tagService.RemoveTagFromEvent(ctx, eventID, tagID, orgID, userID)
}

func (a *eventManagementAdapter) ListTagsForEvent(ctx context.Context, eventID uuid.UUID) ([]payloads.TagResponse, error) {
	return a.tagService.ListTagsForEvent(ctx, eventID)
}

// Status Management Operations delegation to LifecycleService
func (a *eventManagementAdapter) UpdateEventStatus(ctx context.Context, eventID uuid.UUID, orgID uuid.UUID, userID uuid.UUID, status string) error {
	return a.lifecycleService.UpdateEventStatus(ctx, eventID, status, orgID, userID)
}

// EventServiceIntegrationTestSuite provides comprehensive integration testing for event services
type EventServiceIntegrationTestSuite struct {
	suite.Suite
	testDB    *testutil.TestDatabase
	store     db.Store
	queries   *db.Queries // Keep for backward compatibility
	validator *validation.RequestValidator
	ctx       context.Context

	// Event domain services
	managementService event.ManagementService
	mediaService      event.MediaService
	volunteerService  event.VolunteerService
	statisticsService event.StatisticsService
	tagService        event.TagService

	// Test entities
	testOrg   db.Organization
	testUser  db.User
	testAdmin db.User
	testEvent db.Event
}

// SetupSuite runs once before all tests in the suite
func (suite *EventServiceIntegrationTestSuite) SetupSuite() {
	suite.ctx = context.Background()
	suite.testDB, suite.store = testutil.SetupTestDatabaseWithStore(suite.T())
	suite.queries = db.New(suite.testDB.DB) // Keep queries for backward compatibility
	suite.validator = validation.NewRequestValidator()

	// Initialize event domain services
	// For integration tests, we'll create the sub-services with nil job service (or create a mock)
	eventSvc := event.NewEventService(suite.store, nil)
	tagSvc := event.NewTagService(suite.store)
	lifecycleSvc := event.NewLifecycleService(suite.store, nil)

	// Create adapter for ManagementService interface (temporary until handlers are updated)
	suite.managementService = &eventManagementAdapter{
		eventService:     eventSvc,
		tagService:       tagSvc,
		lifecycleService: lifecycleSvc,
	}
	suite.mediaService = event.NewMediaService(suite.store)
	suite.volunteerService = event.NewVolunteerService(suite.store)
	suite.statisticsService = event.NewStatisticsService(suite.store)
	suite.tagService = tagSvc
}

// SetupTest runs before each test
func (suite *EventServiceIntegrationTestSuite) SetupTest() {
	// Clean database state before each test
	suite.testDB.TruncateAllTables(suite.T())

	// Create test data helper
	helper := NewSuiteTestDataHelper(suite.T(), suite.queries, suite.ctx)
	testData := helper.SetupSuiteData()

	// Assign to suite fields
	suite.testUser = testData.RegularUser
	suite.testAdmin = testData.AdminUser
	suite.testOrg = testData.Organization
	suite.testEvent = testData.Event
}

// TestCompleteEventLifecycle tests the complete event management workflow
func (suite *EventServiceIntegrationTestSuite) TestCompleteEventLifecycle() {
	// Step 1: Create event
	createReq := payloads.CreateEventRequest{
		Title:                           "Integration Test Event",
		JsonContent:                     []byte(`{"type": "doc", "content": []}`),
		LocationType:                    "physical",
		LocationFullAddress:             testutil.ToPtr("123 Test Street"),
		StartTime:                       time.Now().Add(24 * time.Hour),
		EndTime:                         time.Now().Add(26 * time.Hour),
		ParticipantLimit:                testutil.ToPtr(int32(100)),
		RequiresApprovalForRegistration: false,
		Status:                          testutil.ToPtr("draft"),
	}

	createdEvent, err := suite.managementService.CreateEvent(suite.ctx, suite.testOrg.ID, suite.testAdmin.ID, createReq)
	suite.NoError(err, "Should create event successfully")
	suite.Equal(createReq.Title, createdEvent.Title, "Event title should match")
	suite.Equal("draft", createdEvent.Status, "Event should be in draft status")

	// Step 2: Update event details
	updateReq := payloads.UpdateEventRequest{
		Title:             testutil.ToPtr("Updated Integration Test Event"),
		LocationOnlineURL: testutil.ToPtr("https://zoom.us/meeting/123"),
		Status:            testutil.ToPtr("published"),
		PublishedAt:       testutil.ToPtr(time.Now()),
	}

	updatedEvent, err := suite.managementService.UpdateEventDetails(suite.ctx, createdEvent.ID, suite.testOrg.ID, suite.testAdmin.ID, updateReq)
	suite.NoError(err, "Should update event successfully")
	suite.Equal(*updateReq.Title, updatedEvent.Title, "Event title should be updated")
	suite.Equal("published", updatedEvent.Status, "Event status should be published")

	// Step 3: Add tags to event
	// Create a tag for testing
	tag, err := suite.queries.CreateTag(suite.ctx, db.CreateTagParams{
		NameEnUs:        "integration-test",
		NameZhHk:        "integration-test",
		NameZhCn:        "integration-test",
		DescriptionEnUs: testutil.ToPtr("Integration Test Tag"),
		OrganizationID:  &suite.testOrg.ID,
		ApprovalStatus:  db.TagApprovalStatusApproved,
		CreatedByUserID: suite.testAdmin.ID,
	})
	suite.NoError(err)

	err = suite.tagService.AddTagToEvent(suite.ctx, createdEvent.ID, tag.ID, suite.testOrg.ID, suite.testAdmin.ID)
	suite.NoError(err, "Should add tag to event successfully")

	// Verify tag was added
	tags, err := suite.tagService.ListTagsForEvent(suite.ctx, createdEvent.ID)
	suite.NoError(err, "Should list event tags successfully")
	suite.Len(tags, 1, "Should have one tag")
	suite.Equal(tag.ID, tags[0].ID, "Tag ID should match")

	// Step 4: Test event retrieval
	retrievedEvent, err := suite.managementService.GetEventByID(suite.ctx, createdEvent.ID, suite.testUser.ID, true)
	suite.NoError(err, "Should retrieve event successfully")
	suite.Equal(updatedEvent.ID, retrievedEvent.ID, "Retrieved event ID should match")
	suite.Equal(updatedEvent.Title, retrievedEvent.Title, "Retrieved event title should match")

	// Step 5: List organization events
	listReq := payloads.ListOrganizationEventsRequest{
		PageRequest: payloads.PageRequest{Limit: 10, Offset: 0},
		Status:      testutil.ToPtr("published"),
	}

	events, total, err := suite.managementService.ListEventsByOrganization(suite.ctx, suite.testOrg.ID, suite.testAdmin.ID, listReq)
	suite.NoError(err, "Should list organization events successfully")
	suite.Equal(int64(1), total, "Should have one event")
	suite.Len(events, 1, "Should return one event")
	suite.Equal(createdEvent.ID, events[0].ID, "Event ID should match")

	// Step 6: Delete event
	err = suite.managementService.DeleteEvent(suite.ctx, createdEvent.ID, suite.testOrg.ID, suite.testAdmin.ID)
	suite.NoError(err, "Should delete event successfully")

	// Verify event is deleted
	_, err = suite.managementService.GetEventByID(suite.ctx, createdEvent.ID, suite.testUser.ID, true)
	suite.Error(err, "Should not be able to retrieve deleted event")
}

// TestEventRegistrationWorkflow tests the complete event registration process
func (suite *EventServiceIntegrationTestSuite) TestEventRegistrationWorkflow() {
	// Create test event
	event := testutil.CreateDBEvent(suite.T(), suite.queries, func(params *db.CreateEventParams) {
		params.OrganizationID = suite.testOrg.ID
		params.CreatedByUserID = suite.testAdmin.ID
		params.Title = "Registration Test Event"
		params.Status = db.EventStatusTypePublished
		params.ParticipantLimit = testutil.ToPtr(int32(2))
	})

	// Step 1: Register user for event
	registration, err := suite.queries.CreateEventRegistration(suite.ctx, db.CreateEventRegistrationParams{
		EventID:          event.ID,
		UserID:           suite.testUser.ID,
		Status:           db.EventRegistrationStatusTypeRegistered,
		PaymentStatus:    db.PaymentStatusTypeNotRequired,
		RegistrationRole: db.EventRegistrationRoleTypeParticipant,
	})
	suite.NoError(err, "Should create event registration successfully")
	suite.Equal(event.ID, registration.EventID, "Registration event ID should match")
	suite.Equal(suite.testUser.ID, registration.UserID, "Registration user ID should match")

	// Step 2: Create another user and register them
	secondUser := testutil.CreateDBUser(suite.T(), suite.queries, func(params *db.CreateUserWithPhoneParams) {
		params.DisplayName = "Second Test User"
		params.Phone = testutil.ToPtr("+1555555555")
	})

	_, err = suite.queries.CreateEventRegistration(suite.ctx, db.CreateEventRegistrationParams{
		EventID:          event.ID,
		UserID:           secondUser.ID,
		Status:           db.EventRegistrationStatusTypeRegistered,
		PaymentStatus:    db.PaymentStatusTypeNotRequired,
		RegistrationRole: db.EventRegistrationRoleTypeParticipant,
	})
	suite.NoError(err, "Should create second registration successfully")

	// Step 3: Third user should be waitlisted (event is full)
	thirdUser := testutil.CreateDBUser(suite.T(), suite.queries, func(params *db.CreateUserWithPhoneParams) {
		params.DisplayName = "Third Test User"
		params.Phone = testutil.ToPtr("+1666666666")
	})

	// This would typically be handled by registration service logic
	thirdRegistration, err := suite.queries.CreateEventRegistration(suite.ctx, db.CreateEventRegistrationParams{
		EventID:          event.ID,
		UserID:           thirdUser.ID,
		Status:           db.EventRegistrationStatusTypeWaitlisted,
		PaymentStatus:    db.PaymentStatusTypeNotRequired,
		RegistrationRole: db.EventRegistrationRoleTypeParticipant,
	})
	suite.NoError(err, "Should create waitlisted registration successfully")
	suite.Equal(db.EventRegistrationStatusTypeWaitlisted, thirdRegistration.Status, "Third user should be waitlisted")

	// Step 4: Check event statistics
	// TODO: Fix statistics service interface - GetEventStatistics doesn't accept event ID
	// stats, err := suite.statisticsService.GetEventStatistics(suite.ctx, event.ID)
	// suite.NoError(err, "Should get event statistics successfully")
	// suite.Equal(int32(2), stats.TotalRegistrations, "Should have 2 registrations")
	// suite.Equal(int32(1), stats.TotalWaitlisted, "Should have 1 waitlisted")

	// Step 5: First user cancels registration
	_, err = suite.queries.UpdateEventRegistrationStatus(suite.ctx, db.UpdateEventRegistrationStatusParams{
		ID:     registration.ID,
		Status: db.EventRegistrationStatusTypeCancelledByUser,
	})
	suite.NoError(err, "Should cancel registration successfully")

	// In real scenario, the registration service would promote waitlisted user
	// For this test, we'll simulate it manually
	_, err = suite.queries.UpdateEventRegistrationStatus(suite.ctx, db.UpdateEventRegistrationStatusParams{
		ID:     thirdRegistration.ID,
		Status: db.EventRegistrationStatusTypeRegistered,
	})
	suite.NoError(err, "Should promote waitlisted user successfully")
}

// TestEventVolunteerManagement tests the volunteer application workflow
func (suite *EventServiceIntegrationTestSuite) TestEventVolunteerManagement() {
	// Create test event
	event := testutil.CreateDBEvent(suite.T(), suite.queries, func(params *db.CreateEventParams) {
		params.OrganizationID = suite.testOrg.ID
		params.CreatedByUserID = suite.testAdmin.ID
		params.Title = "Volunteer Test Event"
		params.Status = db.EventStatusTypePublished
	})

	// Step 1: User applies to volunteer
	application, err := suite.queries.CreateEventVolunteerApplication(suite.ctx, db.CreateEventVolunteerApplicationParams{
		EventID:                event.ID,
		UserID:                 suite.testUser.ID,
		OrganizationID:         suite.testOrg.ID,
		ApplicationNotesByUser: testutil.ToPtr("I have experience volunteering at similar events"),
		Status:                 db.ApplicationStatusEnumPending,
	})
	suite.NoError(err, "Should create volunteer application successfully")
	suite.Equal(event.ID, application.EventID, "Application event ID should match")
	suite.Equal(suite.testUser.ID, application.UserID, "Application user ID should match")
	suite.Equal(db.ApplicationStatusEnumPending, application.Status, "Application should be pending")

	// Step 2: Admin reviews and approves application
	err = suite.volunteerService.ApproveVolunteerApplication(suite.ctx, application.ID, suite.testAdmin.ID, suite.testOrg.ID)
	suite.NoError(err, "Should approve volunteer application successfully")

	// Verify application was approved
	updatedApplication, err := suite.queries.GetEventVolunteerApplicationByID(suite.ctx, application.ID)
	suite.NoError(err, "Should retrieve updated application")
	suite.Equal(db.ApplicationStatusEnumApproved, updatedApplication.Status, "Application should be approved")

	// Step 3: Mark volunteer attendance
	err = suite.volunteerService.MarkVolunteerAttendance(suite.ctx, application.ID, suite.testAdmin.ID, suite.testOrg.ID)
	suite.NoError(err, "Should mark volunteer attendance successfully")

	// Verify attendance was marked
	finalApplication, err := suite.queries.GetEventVolunteerApplicationByID(suite.ctx, application.ID)
	suite.NoError(err, "Should retrieve final application")
	suite.NotNil(finalApplication.AttendedAt, "Attendance time should be set")

	// Step 4: List volunteer applications
	applications, total, err := suite.volunteerService.ListOrgEventVolunteerApplications(suite.ctx, suite.testOrg.ID, "approved", payloads.PageRequest{
		Limit:  10,
		Offset: 0,
	})
	suite.NoError(err, "Should list volunteer applications successfully")
	suite.Equal(int64(1), total, "Should have one application")
	suite.Len(applications, 1, "Should return one application")
	suite.Equal(application.ID, applications[0].ID, "Application ID should match")
}

// TestEventMediaManagement tests the media management workflow
func (suite *EventServiceIntegrationTestSuite) TestEventMediaManagement() {
	// Skip this test for now as it requires file operations
	suite.T().Skip("Skipping media management test - requires file system operations")
}

// TestEventStatistics tests event statistics calculations
func (suite *EventServiceIntegrationTestSuite) TestEventStatistics() {
	// Create test event
	event := testutil.CreateDBEvent(suite.T(), suite.queries, func(params *db.CreateEventParams) {
		params.OrganizationID = suite.testOrg.ID
		params.CreatedByUserID = suite.testAdmin.ID
		params.Title = "Statistics Test Event"
		params.Status = db.EventStatusTypePublished
		params.ParticipantLimit = testutil.ToPtr(int32(10))
	})

	// Create multiple registrations
	for i := 0; i < 5; i++ {
		user := testutil.CreateDBUser(suite.T(), suite.queries, func(params *db.CreateUserWithPhoneParams) {
			params.DisplayName = fmt.Sprintf("User %d", i+1)
			params.Phone = testutil.ToPtr(fmt.Sprintf("+155555%04d", i))
		})

		status := db.EventRegistrationStatusTypeRegistered
		if i >= 3 {
			status = db.EventRegistrationStatusTypeWaitlisted
		}

		_, err := suite.queries.CreateEventRegistration(suite.ctx, db.CreateEventRegistrationParams{
			EventID:          event.ID,
			UserID:           user.ID,
			Status:           status,
			PaymentStatus:    db.PaymentStatusTypeNotRequired,
			RegistrationRole: db.EventRegistrationRoleTypeParticipant,
		})
		require.NoError(suite.T(), err)
	}

	// Create volunteer applications
	for i := 0; i < 3; i++ {
		user := testutil.CreateDBUser(suite.T(), suite.queries, func(params *db.CreateUserWithPhoneParams) {
			params.DisplayName = fmt.Sprintf("Volunteer %d", i+1)
			params.Phone = testutil.ToPtr(fmt.Sprintf("+166666%04d", i))
		})

		status := db.ApplicationStatusEnumPending
		if i == 0 {
			status = db.ApplicationStatusEnumApproved
		}

		_, err := suite.queries.CreateEventVolunteerApplication(suite.ctx, db.CreateEventVolunteerApplicationParams{
			EventID:        event.ID,
			UserID:         user.ID,
			OrganizationID: suite.testOrg.ID,
			Status:         status,
		})
		require.NoError(suite.T(), err)
	}

	// Get event statistics
	// TODO: Fix statistics service interface - GetEventStatistics doesn't accept event ID
	// stats, err := suite.statisticsService.GetEventStatistics(suite.ctx, event.ID)
	// suite.NoError(err, "Should get event statistics successfully")

	// Verify statistics
	// suite.Equal(int32(3), stats.TotalRegistrations, "Should have 3 registrations")
	// suite.Equal(int32(2), stats.TotalWaitlisted, "Should have 2 waitlisted")
	// suite.Equal(int32(0), stats.TotalAttended, "Should have 0 attended")
	// suite.Equal(int32(3), stats.TotalVolunteerApplications, "Should have 3 volunteer applications")
	// suite.Equal(int32(1), stats.ApprovedVolunteers, "Should have 1 approved volunteer")
	// suite.Equal(int32(7), stats.AvailableSpots, "Should have 7 available spots")
}

// TestUpdateEventStatus tests the complete event status update workflow
func (suite *EventServiceIntegrationTestSuite) TestUpdateEventStatus() {
	// Step 1: Create a draft event
	testContent, _ := json.Marshal(map[string]interface{}{"blocks": []interface{}{}})
	createReq := payloads.CreateEventRequest{
		Title:               "Event Status Test",
		JsonContent:         testContent,
		StartTime:           time.Now().Add(24 * time.Hour),
		EndTime:             time.Now().Add(25 * time.Hour),
		LocationType:        "physical",
		LocationFullAddress: testutil.ToPtr("Test Location"),
		ParticipantLimit:    testutil.ToPtr(int32(50)),
		ContactEmail:        testutil.ToPtr[string]("<EMAIL>"),
	}

	createdEvent, err := suite.managementService.CreateEvent(suite.ctx, suite.testOrg.ID, suite.testAdmin.ID, createReq)
	suite.NoError(err, "Should create draft event successfully")
	suite.Equal("draft", createdEvent.Status, "Event should start as draft")

	// Step 2: Publish the event
	err = suite.managementService.UpdateEventStatus(suite.ctx, createdEvent.ID, suite.testOrg.ID, suite.testAdmin.ID, "published")
	suite.NoError(err, "Should update event status to published")

	// Verify the event is published
	updatedEvent, err := suite.managementService.GetEventByID(suite.ctx, createdEvent.ID, suite.testAdmin.ID, true)
	suite.NoError(err, "Should get updated event successfully")
	suite.Equal("published", updatedEvent.Status, "Event status should be published")
	suite.NotNil(updatedEvent.PublishedAt, "PublishedAt should be set when published")

	// Step 3: Archive the event
	err = suite.managementService.UpdateEventStatus(suite.ctx, createdEvent.ID, suite.testOrg.ID, suite.testAdmin.ID, "archived")
	suite.NoError(err, "Should update event status to archived")

	// Verify the event is archived
	archivedEvent, err := suite.managementService.GetEventByID(suite.ctx, createdEvent.ID, suite.testAdmin.ID, true)
	suite.NoError(err, "Should get archived event successfully")
	suite.Equal("archived", archivedEvent.Status, "Event status should be archived")

	// Step 4: Test permission restrictions - regular user should not be able to update status
	err = suite.managementService.UpdateEventStatus(suite.ctx, createdEvent.ID, suite.testOrg.ID, suite.testUser.ID, "published")
	suite.Error(err, "Regular user should not be able to update event status")
	suite.Contains(err.Error(), "insufficient permissions", "Error should mention insufficient permissions")

	// Step 5: Test invalid status
	err = suite.managementService.UpdateEventStatus(suite.ctx, createdEvent.ID, suite.testOrg.ID, suite.testAdmin.ID, "invalid_status")
	suite.Error(err, "Should reject invalid status")
	suite.Contains(err.Error(), "invalid status", "Error should mention invalid status")

	// Step 6: Test updating non-existent event
	nonExistentEventID := uuid.New()
	err = suite.managementService.UpdateEventStatus(suite.ctx, nonExistentEventID, suite.testOrg.ID, suite.testAdmin.ID, "published")
	suite.Error(err, "Should reject updating non-existent event")
	suite.Contains(err.Error(), "event not found", "Error should mention event not found")

	// Step 7: Test updating event with wrong organization
	otherOrg := testutil.CreateDBOrganization(suite.T(), suite.queries, func(params *db.CreateOrganizationParams) {
		params.Name = "Other Organization"
		params.ThemeColor = testutil.ToPtr("red")
	})

	err = suite.managementService.UpdateEventStatus(suite.ctx, createdEvent.ID, otherOrg.ID, suite.testAdmin.ID, "published")
	suite.Error(err, "Should reject updating event from wrong organization")
	suite.Contains(err.Error(), "event does not belong to the specified organization", "Error should mention organization mismatch")
}

// TearDownSuite runs once after all tests
func (suite *EventServiceIntegrationTestSuite) TearDownSuite() {
	if suite.testDB != nil {
		suite.testDB.Cleanup()
	}
}

// TestEventServiceIntegrationTestSuite runs the event service integration test suite
func TestEventServiceIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(EventServiceIntegrationTestSuite))
}
