//go:build skip
// +build skip

// TODO: Enable this test when FieldFilteringMiddleware is implemented

package integration

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/middleware"
	"Membership-SAAS-System-Backend/internal/testutil"
	"Membership-SAAS-System-Backend/internal/token"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

type FieldFilteringMiddlewareTestSuite struct {
	testutil.BaseServiceTestSuite
	echo      *echo.Echo
	testOrg   db.Organization
	testUser  db.User
	testPosts []db.Post
}

func TestFieldFilteringMiddlewareTestSuite(t *testing.T) {
	suite.Run(t, new(FieldFilteringMiddlewareTestSuite))
}

func (s *FieldFilteringMiddlewareTestSuite) SetupTest() {
	s.BaseServiceTestSuite.SetupTest()

	// Setup Echo with field filtering middleware
	s.echo = echo.New()
	// TODO: Enable when FieldFilteringMiddleware is implemented
	// s.echo.Use(middleware.FieldFilteringMiddleware())

	// Create test data
	s.setupTestData()
}

func (s *FieldFilteringMiddlewareTestSuite) setupTestData() {
	ctx := context.Background()

	// Create test organization and user
	s.testOrg = s.createTestOrganization()
	s.testUser = s.createTestUser()

	// Create test posts with different data structures
	postData := []struct {
		title   string
		content map[string]interface{}
	}{
		{
			"Public Post",
			map[string]interface{}{
				"type": "doc",
				"content": []map[string]interface{}{
					{
						"type": "paragraph",
						"content": []map[string]interface{}{
							{
								"type": "text",
								"text": "This is public content",
							},
						},
					},
				},
				"metadata": map[string]interface{}{
					"visibility": "public",
					"tags":       []string{"public", "general"},
					"stats": map[string]interface{}{
						"views":    100,
						"likes":    25,
						"comments": 5,
					},
				},
			},
		},
		{
			"Private Post",
			map[string]interface{}{
				"type": "doc",
				"content": []map[string]interface{}{
					{
						"type": "paragraph",
						"content": []map[string]interface{}{
							{
								"type": "text",
								"text": "This is private content with sensitive data",
							},
						},
					},
				},
				"metadata": map[string]interface{}{
					"visibility":  "private",
					"internal_id": "INTERNAL-123",
					"audit_trail": []string{"created", "reviewed", "approved"},
					"sensitive_data": map[string]interface{}{
						"user_data":    "sensitive info",
						"internal_ref": "REF-456",
					},
				},
			},
		},
		{
			"Rich Content Post",
			map[string]interface{}{
				"type": "doc",
				"content": []map[string]interface{}{
					{
						"type": "heading",
						"attrs": map[string]interface{}{
							"level": 1,
						},
						"content": []map[string]interface{}{
							{
								"type": "text",
								"text": "Rich Content Example",
							},
						},
					},
					{
						"type": "paragraph",
						"content": []map[string]interface{}{
							{
								"type": "text",
								"text": "This post has complex nested data structures",
							},
						},
					},
				},
				"metadata": map[string]interface{}{
					"author_details": map[string]interface{}{
						"name":     s.testUser.DisplayName,
						"email":    "<EMAIL>",
						"role":     "admin",
						"internal": "should_be_filtered",
					},
					"performance": map[string]interface{}{
						"load_time":     150,
						"cache_hits":    45,
						"db_queries":    8,
						"memory_usage":  "256MB",
						"internal_perf": "debug_data",
					},
				},
			},
		},
	}

	for _, data := range postData {
		contentBytes, err := json.Marshal(data.content)
		require.NoError(s.T(), err)

		post, err := s.Store.CreatePost(ctx, db.CreatePostParams{
			OrganizationID: s.testOrg.ID,
			Title:          data.title,
			Content:        json.RawMessage(contentBytes),
			AuthorID:       s.testUser.ID,
			Status:         "published",
		})
		require.NoError(s.T(), err)
		s.testPosts = append(s.testPosts, post)
	}
}

// Basic Field Filtering Tests

func (s *FieldFilteringMiddlewareTestSuite) TestBasicFieldSelection() {
	t := s.T()

	// Mock handler that returns structured data
	handler := func(c echo.Context) error {
		data := map[string]interface{}{
			"id":          s.testPosts[0].ID,
			"title":       s.testPosts[0].Title,
			"content":     json.RawMessage(s.testPosts[0].Content),
			"author_id":   s.testPosts[0].AuthorID,
			"created_at":  s.testPosts[0].CreatedAt,
			"updated_at":  s.testPosts[0].UpdatedAt,
			"internal_id": "INTERNAL-123",
			"debug_info":  "should be filtered",
		}
		return c.JSON(http.StatusOK, data)
	}

	// Test selecting specific fields
	req := httptest.NewRequest(http.MethodGet, "/test?fields=id,title,created_at", nil)
	rec := httptest.NewRecorder()
	c := s.echo.NewContext(req, rec)

	// Execute handler with middleware
	middlewareFunc := middleware.FieldFilteringMiddleware()
	err := middlewareFunc(handler)(c)
	require.NoError(t, err)

	// Parse response
	var response map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	require.NoError(t, err)

	// Verify only requested fields are present
	require.Contains(t, response, "id")
	require.Contains(t, response, "title")
	require.Contains(t, response, "created_at")
	require.NotContains(t, response, "content")
	require.NotContains(t, response, "author_id")
	require.NotContains(t, response, "updated_at")
	require.NotContains(t, response, "internal_id")
	require.NotContains(t, response, "debug_info")

	require.Len(t, response, 3) // Only 3 fields should be present
}

func (s *FieldFilteringMiddlewareTestSuite) TestFieldExclusion() {
	t := s.T()

	// Mock handler that returns structured data
	handler := func(c echo.Context) error {
		data := map[string]interface{}{
			"id":         s.testPosts[0].ID,
			"title":      s.testPosts[0].Title,
			"content":    json.RawMessage(s.testPosts[0].Content),
			"author_id":  s.testPosts[0].AuthorID,
			"created_at": s.testPosts[0].CreatedAt,
			"sensitive":  "should be excluded",
			"internal":   "should be excluded",
		}
		return c.JSON(http.StatusOK, data)
	}

	// Test excluding specific fields
	req := httptest.NewRequest(http.MethodGet, "/test?exclude=sensitive,internal", nil)
	rec := httptest.NewRecorder()
	c := s.echo.NewContext(req, rec)

	middlewareFunc := middleware.FieldFilteringMiddleware()
	err := middlewareFunc(handler)(c)
	require.NoError(t, err)

	var response map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	require.NoError(t, err)

	// Verify excluded fields are not present
	require.Contains(t, response, "id")
	require.Contains(t, response, "title")
	require.Contains(t, response, "content")
	require.Contains(t, response, "author_id")
	require.Contains(t, response, "created_at")
	require.NotContains(t, response, "sensitive")
	require.NotContains(t, response, "internal")
}

func (s *FieldFilteringMiddlewareTestSuite) TestNoFilteringWithoutParams() {
	t := s.T()

	// Mock handler
	handler := func(c echo.Context) error {
		data := map[string]interface{}{
			"id":       s.testPosts[0].ID,
			"title":    s.testPosts[0].Title,
			"content":  json.RawMessage(s.testPosts[0].Content),
			"internal": "should remain",
		}
		return c.JSON(http.StatusOK, data)
	}

	// Test without any filtering parameters
	req := httptest.NewRequest(http.MethodGet, "/test", nil)
	rec := httptest.NewRecorder()
	c := s.echo.NewContext(req, rec)

	middlewareFunc := middleware.FieldFilteringMiddleware()
	err := middlewareFunc(handler)(c)
	require.NoError(t, err)

	var response map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	require.NoError(t, err)

	// Verify all fields are present (no filtering)
	require.Contains(t, response, "id")
	require.Contains(t, response, "title")
	require.Contains(t, response, "content")
	require.Contains(t, response, "internal")
	require.Len(t, response, 4) // All fields should be present
}

// Nested Field Filtering Tests

func (s *FieldFilteringMiddlewareTestSuite) TestNestedFieldSelection() {
	t := s.T()

	// Mock handler with nested data structure
	handler := func(c echo.Context) error {
		data := map[string]interface{}{
			"id":    s.testPosts[2].ID,
			"title": s.testPosts[2].Title,
			"metadata": map[string]interface{}{
				"author": map[string]interface{}{
					"id":       s.testUser.ID,
					"name":     s.testUser.DisplayName,
					"email":    "<EMAIL>",
					"internal": "should be filtered",
				},
				"stats": map[string]interface{}{
					"views":    100,
					"likes":    25,
					"comments": 5,
					"debug":    "internal_data",
				},
				"visibility": "public",
			},
		}
		return c.JSON(http.StatusOK, data)
	}

	// Test selecting nested fields
	req := httptest.NewRequest(http.MethodGet, "/test?fields=id,title,metadata.author.name,metadata.stats.views", nil)
	rec := httptest.NewRecorder()
	c := s.echo.NewContext(req, rec)

	middlewareFunc := middleware.FieldFilteringMiddleware()
	err := middlewareFunc(handler)(c)
	require.NoError(t, err)

	var response map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	require.NoError(t, err)

	// Verify structure
	require.Contains(t, response, "id")
	require.Contains(t, response, "title")
	require.Contains(t, response, "metadata")

	metadata := response["metadata"].(map[string]interface{})
	require.Contains(t, metadata, "author")
	require.Contains(t, metadata, "stats")

	author := metadata["author"].(map[string]interface{})
	require.Contains(t, author, "name")
	require.NotContains(t, author, "id")
	require.NotContains(t, author, "email")
	require.NotContains(t, author, "internal")

	stats := metadata["stats"].(map[string]interface{})
	require.Contains(t, stats, "views")
	require.NotContains(t, stats, "likes")
	require.NotContains(t, stats, "comments")
	require.NotContains(t, stats, "debug")
}

func (s *FieldFilteringMiddlewareTestSuite) TestNestedFieldExclusion() {
	t := s.T()

	handler := func(c echo.Context) error {
		data := map[string]interface{}{
			"id":    s.testPosts[1].ID,
			"title": s.testPosts[1].Title,
			"metadata": map[string]interface{}{
				"visibility":  "private",
				"internal_id": "INTERNAL-123",
				"audit_trail": []string{"created", "reviewed"},
				"sensitive_data": map[string]interface{}{
					"user_data":    "sensitive",
					"internal_ref": "REF-456",
				},
				"public_stats": map[string]interface{}{
					"views": 50,
					"score": 8.5,
				},
			},
		}
		return c.JSON(http.StatusOK, data)
	}

	// Test excluding nested sensitive fields
	req := httptest.NewRequest(http.MethodGet, "/test?exclude=metadata.internal_id,metadata.sensitive_data", nil)
	rec := httptest.NewRecorder()
	c := s.echo.NewContext(req, rec)

	middlewareFunc := middleware.FieldFilteringMiddleware()
	err := middlewareFunc(handler)(c)
	require.NoError(t, err)

	var response map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	require.NoError(t, err)

	// Verify structure
	require.Contains(t, response, "metadata")

	metadata := response["metadata"].(map[string]interface{})
	require.Contains(t, metadata, "visibility")
	require.Contains(t, metadata, "audit_trail")
	require.Contains(t, metadata, "public_stats")
	require.NotContains(t, metadata, "internal_id")
	require.NotContains(t, metadata, "sensitive_data")
}

// Array Filtering Tests

func (s *FieldFilteringMiddlewareTestSuite) TestArrayFieldFiltering() {
	t := s.T()

	handler := func(c echo.Context) error {
		data := map[string]interface{}{
			"posts": []map[string]interface{}{
				{
					"id":       s.testPosts[0].ID,
					"title":    s.testPosts[0].Title,
					"internal": "should be filtered",
					"content":  "public content",
				},
				{
					"id":       s.testPosts[1].ID,
					"title":    s.testPosts[1].Title,
					"internal": "should be filtered",
					"content":  "private content",
				},
			},
			"total": 2,
		}
		return c.JSON(http.StatusOK, data)
	}

	// Test filtering fields in array items
	req := httptest.NewRequest(http.MethodGet, "/test?fields=posts.id,posts.title,total", nil)
	rec := httptest.NewRecorder()
	c := s.echo.NewContext(req, rec)

	middlewareFunc := middleware.FieldFilteringMiddleware()
	err := middlewareFunc(handler)(c)
	require.NoError(t, err)

	var response map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	require.NoError(t, err)

	require.Contains(t, response, "posts")
	require.Contains(t, response, "total")

	posts := response["posts"].([]interface{})
	require.Len(t, posts, 2)

	for _, postInterface := range posts {
		post := postInterface.(map[string]interface{})
		require.Contains(t, post, "id")
		require.Contains(t, post, "title")
		require.NotContains(t, post, "internal")
		require.NotContains(t, post, "content")
	}
}

// Performance Tests

func (s *FieldFilteringMiddlewareTestSuite) TestFieldFilteringPerformance() {
	t := s.T()

	if testing.Short() {
		t.Skip("Skipping performance test in short mode")
	}

	// Create large data structure for performance testing
	largeData := map[string]interface{}{
		"metadata": map[string]interface{}{
			"performance": make(map[string]interface{}),
		},
		"items": make([]map[string]interface{}, 1000),
	}

	// Add many nested fields
	for i := 0; i < 100; i++ {
		largeData["metadata"].(map[string]interface{})["performance"].(map[string]interface{})[fmt.Sprintf("metric_%d", i)] = i * 10
	}

	// Add many array items
	for i := 0; i < 1000; i++ {
		item := map[string]interface{}{
			"id":          uuid.New(),
			"title":       fmt.Sprintf("Item %d", i),
			"description": fmt.Sprintf("Description for item %d", i),
			"metadata": map[string]interface{}{
				"index":    i,
				"category": fmt.Sprintf("category_%d", i%10),
				"tags":     []string{fmt.Sprintf("tag_%d", i), fmt.Sprintf("tag_%d", i+1)},
			},
			"internal_data": map[string]interface{}{
				"debug_info":  fmt.Sprintf("debug_%d", i),
				"trace_id":    uuid.New(),
				"performance": map[string]interface{}{"latency": i * 2},
			},
		}
		largeData["items"].([]interface{})[i] = item
	}

	handler := func(c echo.Context) error {
		return c.JSON(http.StatusOK, largeData)
	}

	// Test performance with field selection
	req := httptest.NewRequest(http.MethodGet, "/test?fields=items.id,items.title,items.metadata.category", nil)
	rec := httptest.NewRecorder()
	c := s.echo.NewContext(req, rec)

	start := time.Now()
	middlewareFunc := middleware.FieldFilteringMiddleware()
	err := middlewareFunc(handler)(c)
	duration := time.Since(start)

	require.NoError(t, err)
	require.Less(t, duration, 500*time.Millisecond, "Field filtering should complete within 500ms for large data")

	t.Logf("Field filtering of large dataset completed in %v", duration)

	// Verify response size is reduced
	var response map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	require.NoError(t, err)

	items := response["items"].([]interface{})
	require.Len(t, items, 1000)

	// Verify filtering worked
	item := items[0].(map[string]interface{})
	require.Contains(t, item, "id")
	require.Contains(t, item, "title")
	require.Contains(t, item, "metadata")
	require.NotContains(t, item, "description")
	require.NotContains(t, item, "internal_data")

	metadata := item["metadata"].(map[string]interface{})
	require.Contains(t, metadata, "category")
	require.NotContains(t, metadata, "index")
	require.NotContains(t, metadata, "tags")
}

func (s *FieldFilteringMiddlewareTestSuite) TestConcurrentFieldFiltering() {
	t := s.T()

	if testing.Short() {
		t.Skip("Skipping concurrency test in short mode")
	}

	handler := func(c echo.Context) error {
		data := map[string]interface{}{
			"id":      uuid.New(),
			"title":   "Concurrent Test",
			"content": "Test content",
			"metadata": map[string]interface{}{
				"views":    100,
				"internal": "should be filtered",
			},
		}
		return c.JSON(http.StatusOK, data)
	}

	// Test concurrent requests with different filtering parameters
	concurrentRequests := 50
	errors := make(chan error, concurrentRequests)
	durations := make(chan time.Duration, concurrentRequests)

	filterParams := []string{
		"fields=id,title",
		"fields=id,metadata.views",
		"exclude=metadata.internal",
		"fields=title,content",
		"", // No filtering
	}

	start := time.Now()

	for i := 0; i < concurrentRequests; i++ {
		go func(requestID int) {
			params := filterParams[requestID%len(filterParams)]
			url := "/test"
			if params != "" {
				url += "?" + params
			}

			reqStart := time.Now()
			req := httptest.NewRequest(http.MethodGet, url, nil)
			rec := httptest.NewRecorder()
			c := s.echo.NewContext(req, rec)

			middlewareFunc := middleware.FieldFilteringMiddleware()
			err := middlewareFunc(handler)(c)
			duration := time.Since(reqStart)

			errors <- err
			durations <- duration
		}(i)
	}

	// Wait for all requests to complete
	for i := 0; i < concurrentRequests; i++ {
		err := <-errors
		duration := <-durations
		require.NoError(t, err)
		require.Less(t, duration, 100*time.Millisecond, "Individual request should complete quickly")
	}

	totalDuration := time.Since(start)
	t.Logf("Concurrent field filtering: %d requests completed in %v", concurrentRequests, totalDuration)
}

// Security Tests

func (s *FieldFilteringMiddlewareTestSuite) TestSecurityFieldFiltering() {
	t := s.T()

	handler := func(c echo.Context) error {
		// Simulate endpoint returning sensitive data
		data := map[string]interface{}{
			"user": map[string]interface{}{
				"id":           s.testUser.ID,
				"display_name": s.testUser.DisplayName,
				"phone_number": s.testUser.PhoneNumber,
				"email":        "<EMAIL>",
				"password":     "should_never_be_returned",
				"api_key":      "secret_key_123",
				"internal_id":  "INTERNAL-456",
				"role":         "admin",
			},
			"organization": map[string]interface{}{
				"id":               s.testOrg.ID,
				"name":             s.testOrg.Name,
				"secret_config":    "sensitive_configuration",
				"billing_info":     "credit_card_data",
				"internal_metrics": "performance_data",
			},
		}
		return c.JSON(http.StatusOK, data)
	}

	// Test excluding sensitive fields
	req := httptest.NewRequest(http.MethodGet,
		"/test?exclude=user.password,user.api_key,user.internal_id,organization.secret_config,organization.billing_info",
		nil)
	rec := httptest.NewRecorder()
	c := s.echo.NewContext(req, rec)

	// Set user context for authorization - simulate JWT middleware
	enhancedClaims := &token.EnhancedClaims{
		UserID: s.testUser.ID,
		ActiveOrg: &token.OrgContext{
			ID:   s.testOrg.ID.String(),
			Role: "member",
		},
		PlatformRole: "user",
	}
	jwtToken := jwt.NewWithClaims(jwt.SigningMethodHS256, enhancedClaims)
	c.Set("user", jwtToken)

	middlewareFunc := middleware.FieldFilteringMiddleware()
	err := middlewareFunc(handler)(c)
	require.NoError(t, err)

	var response map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	require.NoError(t, err)

	// Verify sensitive fields are filtered
	user := response["user"].(map[string]interface{})
	require.Contains(t, user, "id")
	require.Contains(t, user, "display_name")
	require.Contains(t, user, "role")
	require.NotContains(t, user, "password")
	require.NotContains(t, user, "api_key")
	require.NotContains(t, user, "internal_id")

	org := response["organization"].(map[string]interface{})
	require.Contains(t, org, "id")
	require.Contains(t, org, "name")
	require.NotContains(t, org, "secret_config")
	require.NotContains(t, org, "billing_info")
}

func (s *FieldFilteringMiddlewareTestSuite) TestInputValidation() {
	t := s.T()

	handler := func(c echo.Context) error {
		return c.JSON(http.StatusOK, map[string]interface{}{
			"test": "data",
		})
	}

	testCases := []struct {
		name        string
		query       string
		expectError bool
	}{
		{
			name:        "Valid field selection",
			query:       "fields=id,title,created_at",
			expectError: false,
		},
		{
			name:        "Valid nested field selection",
			query:       "fields=user.id,user.name,metadata.stats",
			expectError: false,
		},
		{
			name:        "Valid exclusion",
			query:       "exclude=password,internal",
			expectError: false,
		},
		{
			name:        "Empty fields parameter",
			query:       "fields=",
			expectError: false, // Should be handled gracefully
		},
		{
			name:        "Malformed field name",
			query:       "fields=id,title.,metadata..stats",
			expectError: false, // Should filter invalid field names
		},
		{
			name:        "SQL injection attempt",
			query:       "fields=id;DROP TABLE users;--",
			expectError: false, // Should be sanitized
		},
		{
			name:        "Script injection attempt",
			query:       "fields=<script>alert('xss')</script>",
			expectError: false, // Should be sanitized
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			req := httptest.NewRequest(http.MethodGet, "/test?"+tc.query, nil)
			rec := httptest.NewRecorder()
			c := s.echo.NewContext(req, rec)

			middlewareFunc := middleware.FieldFilteringMiddleware()
			err := middlewareFunc(handler)(c)

			if tc.expectError {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, http.StatusOK, rec.Code)
			}
		})
	}
}

// Edge Cases and Error Handling

func (s *FieldFilteringMiddlewareTestSuite) TestNonJSONResponse() {
	t := s.T()

	// Test with text response
	textHandler := func(c echo.Context) error {
		return c.String(http.StatusOK, "Plain text response")
	}

	req := httptest.NewRequest(http.MethodGet, "/test?fields=id,title", nil)
	rec := httptest.NewRecorder()
	c := s.echo.NewContext(req, rec)

	middlewareFunc := middleware.FieldFilteringMiddleware()
	err := middlewareFunc(textHandler)(c)
	require.NoError(t, err)

	// Should pass through unchanged
	require.Equal(t, "Plain text response", rec.Body.String())

	// Test with HTML response
	htmlHandler := func(c echo.Context) error {
		return c.HTML(http.StatusOK, "<html><body>Test</body></html>")
	}

	req = httptest.NewRequest(http.MethodGet, "/test?fields=id,title", nil)
	rec = httptest.NewRecorder()
	c = s.echo.NewContext(req, rec)

	err = middlewareFunc(htmlHandler)(c)
	require.NoError(t, err)

	// Should pass through unchanged
	require.Equal(t, "<html><body>Test</body></html>", rec.Body.String())
}

func (s *FieldFilteringMiddlewareTestSuite) TestInvalidJSONResponse() {
	t := s.T()

	// Handler that returns invalid JSON
	handler := func(c echo.Context) error {
		c.Response().Header().Set("Content-Type", "application/json")
		c.Response().WriteHeader(http.StatusOK)
		_, err := c.Response().Write([]byte(`{"invalid": json}`))
		return err
	}

	req := httptest.NewRequest(http.MethodGet, "/test?fields=id,title", nil)
	rec := httptest.NewRecorder()
	c := s.echo.NewContext(req, rec)

	middlewareFunc := middleware.FieldFilteringMiddleware()
	err := middlewareFunc(handler)(c)
	require.NoError(t, err)

	// Should pass through invalid JSON unchanged
	require.Equal(t, `{"invalid": json}`, rec.Body.String())
}

func (s *FieldFilteringMiddlewareTestSuite) TestFieldFilteringWithPagination() {
	t := s.T()

	// Simulate paginated response
	handler := func(c echo.Context) error {
		data := map[string]interface{}{
			"data": []map[string]interface{}{
				{
					"id":       s.testPosts[0].ID,
					"title":    s.testPosts[0].Title,
					"content":  "content 1",
					"internal": "debug1",
				},
				{
					"id":       s.testPosts[1].ID,
					"title":    s.testPosts[1].Title,
					"content":  "content 2",
					"internal": "debug2",
				},
			},
			"pagination": map[string]interface{}{
				"total":       100,
				"page":        1,
				"per_page":    2,
				"total_pages": 50,
				"has_next":    true,
				"has_prev":    false,
			},
			"metadata": map[string]interface{}{
				"request_id":    uuid.New(),
				"response_time": "150ms",
				"cache_hit":     true,
				"debug_info":    "should be filtered",
			},
		}
		return c.JSON(http.StatusOK, data)
	}

	// Test filtering with pagination structure
	req := httptest.NewRequest(http.MethodGet,
		"/test?fields=data.id,data.title,pagination.total,pagination.page,metadata.response_time",
		nil)
	rec := httptest.NewRecorder()
	c := s.echo.NewContext(req, rec)

	middlewareFunc := middleware.FieldFilteringMiddleware()
	err := middlewareFunc(handler)(c)
	require.NoError(t, err)

	var response map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	require.NoError(t, err)

	// Verify pagination structure
	require.Contains(t, response, "data")
	require.Contains(t, response, "pagination")
	require.Contains(t, response, "metadata")

	// Check data array filtering
	data := response["data"].([]interface{})
	require.Len(t, data, 2)

	item := data[0].(map[string]interface{})
	require.Contains(t, item, "id")
	require.Contains(t, item, "title")
	require.NotContains(t, item, "content")
	require.NotContains(t, item, "internal")

	// Check pagination filtering
	pagination := response["pagination"].(map[string]interface{})
	require.Contains(t, pagination, "total")
	require.Contains(t, pagination, "page")
	require.NotContains(t, pagination, "per_page")
	require.NotContains(t, pagination, "has_next")

	// Check metadata filtering
	metadata := response["metadata"].(map[string]interface{})
	require.Contains(t, metadata, "response_time")
	require.NotContains(t, metadata, "request_id")
	require.NotContains(t, metadata, "cache_hit")
	require.NotContains(t, metadata, "debug_info")
}

// Helper methods

func (s *FieldFilteringMiddlewareTestSuite) createTestOrganization() db.Organization {
	org, err := s.Store.CreateOrganization(context.Background(), db.CreateOrganizationParams{
		Name:        "Field Filtering Test Org " + uuid.New().String()[:8],
		Type:        "test",
		Description: "Organization for field filtering testing",
		Status:      "active",
	})
	require.NoError(s.T(), err)
	return org
}

func (s *FieldFilteringMiddlewareTestSuite) createTestUser() db.User {
	user, err := s.Store.CreateUser(context.Background(), db.CreateUserParams{
		PhoneNumber: "+1234567" + uuid.New().String()[:3],
		DisplayName: "Field Filtering Test User " + uuid.New().String()[:8],
		Language:    "en",
	})
	require.NoError(s.T(), err)
	return user
}
