package integration

import (
	"bytes"
	"encoding/json"
	"fmt"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"net/textproto"
	"strings"
	"sync"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// FileSecurityIntegrationTestSuite tests comprehensive file security scenarios
type FileSecurityIntegrationTestSuite struct {
	suite.Suite
	apiSuite *APITestSuite
}

func (suite *FileSecurityIntegrationTestSuite) SetupSuite() {
	suite.apiSuite = SetupAPITest(suite.T())
}

func (suite *FileSecurityIntegrationTestSuite) TearDownSuite() {
	suite.apiSuite.TeardownAPITest(suite.T())
}

func (suite *FileSecurityIntegrationTestSuite) SetupTest() {
	suite.apiSuite.CleanupTestData(suite.T())
}

// Test file upload authorization
func (suite *FileSecurityIntegrationTestSuite) TestFileUploadAuthorization() {
	tests := []struct {
		name           string
		setupAuth      func() string // Returns token
		orgID          string
		expectedStatus int
		expectedError  string
	}{
		{
			name: "Authenticated user can upload to their org",
			setupAuth: func() string {
				return suite.apiSuite.userToken
			},
			orgID:          suite.apiSuite.orgID,
			expectedStatus: http.StatusCreated,
		},
		{
			name: "Unauthenticated request rejected",
			setupAuth: func() string {
				return ""
			},
			orgID:          suite.apiSuite.orgID,
			expectedStatus: http.StatusUnauthorized,
			expectedError:  "unauthorized",
		},
		{
			name: "Invalid token rejected",
			setupAuth: func() string {
				return "invalid-token"
			},
			orgID:          suite.apiSuite.orgID,
			expectedStatus: http.StatusUnauthorized,
			expectedError:  "invalid token",
		},
		{
			name: "Cannot upload to different org",
			setupAuth: func() string {
				// Create user in different org
				otherOrgID := suite.createNewOrganization()
				otherUserToken := suite.createUserInOrg(otherOrgID)
				return otherUserToken
			},
			orgID:          suite.apiSuite.orgID,
			expectedStatus: http.StatusForbidden,
			expectedError:  "access denied",
		},
	}

	for _, tt := range tests {
		suite.T().Run(tt.name, func(t *testing.T) {
			token := tt.setupAuth()

			// Create upload request
			body := &bytes.Buffer{}
			writer := multipart.NewWriter(body)
			part, _ := writer.CreateFormFile("file", "test.jpg")
			part.Write([]byte("test image content"))
			writer.Close()

			req := httptest.NewRequest("POST", "/api/v1/files/upload?organization_id="+tt.orgID, body)
			req.Header.Set("Content-Type", writer.FormDataContentType())
			if token != "" {
				req.Header.Set("Authorization", "Bearer "+token)
			}

			rec := httptest.NewRecorder()
			suite.apiSuite.e.ServeHTTP(rec, req)

			assert.Equal(t, tt.expectedStatus, rec.Code)
			if tt.expectedError != "" {
				var response map[string]interface{}
				json.Unmarshal(rec.Body.Bytes(), &response)
				assert.Contains(t, strings.ToLower(fmt.Sprint(response["error"])), tt.expectedError)
			}
		})
	}
}

// Test file download permissions
func (suite *FileSecurityIntegrationTestSuite) TestFileDownloadPermissions() {
	// First upload a file
	fileID := suite.uploadTestFile()

	tests := []struct {
		name           string
		setupAuth      func() string
		fileID         string
		expectedStatus int
	}{
		{
			name: "Owner can download their file",
			setupAuth: func() string {
				return suite.apiSuite.userToken
			},
			fileID:         fileID,
			expectedStatus: http.StatusOK,
		},
		{
			name: "Other user in same org can download",
			setupAuth: func() string {
				// Create another user in same org
				return suite.createUserInOrg(suite.apiSuite.orgID)
			},
			fileID:         fileID,
			expectedStatus: http.StatusOK,
		},
		{
			name: "User from different org cannot download",
			setupAuth: func() string {
				otherOrgID := suite.createNewOrganization()
				return suite.createUserInOrg(otherOrgID)
			},
			fileID:         fileID,
			expectedStatus: http.StatusForbidden,
		},
		{
			name: "Unauthenticated cannot download",
			setupAuth: func() string {
				return ""
			},
			fileID:         fileID,
			expectedStatus: http.StatusUnauthorized,
		},
	}

	for _, tt := range tests {
		suite.T().Run(tt.name, func(t *testing.T) {
			token := tt.setupAuth()

			req := httptest.NewRequest("GET", "/api/v1/files/"+tt.fileID+"/download", nil)
			if token != "" {
				req.Header.Set("Authorization", "Bearer "+token)
			}

			rec := httptest.NewRecorder()
			suite.apiSuite.e.ServeHTTP(rec, req)

			assert.Equal(t, tt.expectedStatus, rec.Code)
		})
	}
}

// Test malicious file upload attempts
func (suite *FileSecurityIntegrationTestSuite) TestMaliciousFileUploads() {
	tests := []struct {
		name           string
		filename       string
		content        []byte
		contentType    string
		expectedStatus int
		expectedError  string
	}{
		{
			name:           "Path traversal in filename",
			filename:       "../../../etc/passwd",
			content:        []byte("fake content"),
			contentType:    "text/plain",
			expectedStatus: http.StatusBadRequest,
			expectedError:  "invalid filename", // Go's multipart parser sanitizes path, but our validator still catches "../"
		},
		{
			name:           "Null byte in filename",
			filename:       "file\x00.txt",
			content:        []byte("content"),
			contentType:    "text/plain",
			expectedStatus: http.StatusBadRequest,
			expectedError:  "invalid filename",
		},
		{
			name:           "Script tag in filename",
			filename:       "<script>alert('xss')</script>.jpg",
			content:        []byte("content"),
			contentType:    "image/jpeg",
			expectedStatus: http.StatusBadRequest,
			expectedError:  "invalid filename",
		},
		{
			name:           "Executable file",
			filename:       "malware.exe",
			content:        []byte("MZ\x90\x00"), // PE header
			contentType:    "application/x-msdownload",
			expectedStatus: http.StatusBadRequest,
			expectedError:  "mime type", // ValidateMIMEType returns "MIME type X is not allowed for file type Y"
		},
		{
			name:           "PHP script disguised as image",
			filename:       "shell.jpg",
			content:        []byte("\xFF\xD8\xFF<?php system($_GET['cmd']); ?>"),
			contentType:    "image/jpeg",
			expectedStatus: http.StatusBadRequest,
			expectedError:  "malicious content",
		},
		{
			name:           "SVG with embedded JavaScript",
			filename:       "icon.svg",
			content:        []byte(`<svg xmlns="http://www.w3.org/2000/svg"><script>alert('xss')</script></svg>`),
			contentType:    "image/svg+xml",
			expectedStatus: http.StatusBadRequest,
			expectedError:  "script content", // Validator returns "SVG file contains script content"
		},
		{
			name:           "HTML file upload",
			filename:       "page.html",
			content:        []byte(`<html><body onload="malicious()"></body></html>`),
			contentType:    "text/html",
			expectedStatus: http.StatusBadRequest,
			expectedError:  "mime type", // ValidateMIMEType returns "MIME type X is not allowed for file type Y"
		},
		{
			name:           "Polyglot file (JPEG+ZIP)",
			filename:       "image.jpg",
			content:        append([]byte("\xFF\xD8\xFF"), []byte("PK\x03\x04")...),
			contentType:    "image/jpeg",
			expectedStatus: http.StatusBadRequest,
			expectedError:  "polyglot",
		},
	}

	for _, tt := range tests {
		suite.T().Run(tt.name, func(t *testing.T) {
			body := &bytes.Buffer{}
			writer := multipart.NewWriter(body)
			part, _ := writer.CreateFormFile("file", tt.filename)
			part.Write(tt.content)
			writer.Close()

			req := httptest.NewRequest("POST",
				"/api/v1/files/upload?organization_id="+suite.apiSuite.orgID,
				body)
			req.Header.Set("Content-Type", writer.FormDataContentType())
			req.Header.Set("Authorization", "Bearer "+suite.apiSuite.userToken)

			rec := httptest.NewRecorder()
			suite.apiSuite.e.ServeHTTP(rec, req)

			assert.Equal(t, tt.expectedStatus, rec.Code)
			var response map[string]interface{}
			json.Unmarshal(rec.Body.Bytes(), &response)
			assert.Contains(t, strings.ToLower(fmt.Sprint(response["error"])), tt.expectedError)
		})
	}
}

// Test file size limits
func (suite *FileSecurityIntegrationTestSuite) TestFileSizeLimits() {
	tests := []struct {
		name           string
		fileType       string
		size           int64
		expectedStatus int
	}{
		{
			name:           "Image within limit",
			fileType:       "image/jpeg",
			size:           5 * 1024 * 1024, // 5MB
			expectedStatus: http.StatusCreated,
		},
		{
			name:           "Image exceeds limit",
			fileType:       "image/jpeg",
			size:           11 * 1024 * 1024, // 11MB
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "Document within limit",
			fileType:       "application/pdf",
			size:           40 * 1024 * 1024, // 40MB
			expectedStatus: http.StatusCreated,
		},
		{
			name:           "Zero size file",
			fileType:       "image/jpeg",
			size:           0,
			expectedStatus: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		suite.T().Run(tt.name, func(t *testing.T) {
			// For large files, we simulate by setting Content-Length
			body := &bytes.Buffer{}
			writer := multipart.NewWriter(body)

			// Create form file with size hint
			h := make(textproto.MIMEHeader)
			h.Set("Content-Disposition",
				fmt.Sprintf(`form-data; name="file"; filename="test%s"`,
					suite.getExtensionForMime(tt.fileType)))
			h.Set("Content-Type", tt.fileType)

			part, _ := writer.CreatePart(h)

			// Write minimal content but set Content-Length header
			if tt.size > 0 {
				part.Write([]byte("x"))
			}
			writer.Close()

			req := httptest.NewRequest("POST",
				"/api/v1/files/upload?organization_id="+suite.apiSuite.orgID,
				body)
			req.Header.Set("Content-Type", writer.FormDataContentType())
			req.Header.Set("Authorization", "Bearer "+suite.apiSuite.userToken)
			req.ContentLength = tt.size

			rec := httptest.NewRecorder()
			suite.apiSuite.e.ServeHTTP(rec, req)

			assert.Equal(t, tt.expectedStatus, rec.Code)
		})
	}
}

// Test organization isolation
func (suite *FileSecurityIntegrationTestSuite) TestOrganizationIsolation() {
	// Create two organizations
	org1ID := suite.apiSuite.orgID
	org2ID := suite.createNewOrganization()

	// Create users in each org
	user1Token := suite.apiSuite.userToken
	user2Token := suite.createUserInOrg(org2ID)

	// Upload file to org1
	file1ID := suite.uploadFileToOrg(org1ID, user1Token, "org1-file.pdf")

	// Upload file to org2
	file2ID := suite.uploadFileToOrg(org2ID, user2Token, "org2-file.pdf")

	// Test cross-org access attempts
	tests := []struct {
		name           string
		userToken      string
		fileID         string
		operation      string
		expectedStatus int
	}{
		{
			name:           "User1 cannot access org2 file",
			userToken:      user1Token,
			fileID:         file2ID,
			operation:      "download",
			expectedStatus: http.StatusForbidden,
		},
		{
			name:           "User2 cannot access org1 file",
			userToken:      user2Token,
			fileID:         file1ID,
			operation:      "download",
			expectedStatus: http.StatusForbidden,
		},
		{
			name:           "User1 cannot delete org2 file",
			userToken:      user1Token,
			fileID:         file2ID,
			operation:      "delete",
			expectedStatus: http.StatusForbidden,
		},
		{
			name:           "User2 cannot delete org1 file",
			userToken:      user2Token,
			fileID:         file1ID,
			operation:      "delete",
			expectedStatus: http.StatusForbidden,
		},
	}

	for _, tt := range tests {
		suite.T().Run(tt.name, func(t *testing.T) {
			var req *http.Request

			switch tt.operation {
			case "download":
				req = httptest.NewRequest("GET", "/api/v1/files/"+tt.fileID+"/download", nil)
			case "delete":
				req = httptest.NewRequest("DELETE", "/api/v1/files/"+tt.fileID, nil)
			}

			req.Header.Set("Authorization", "Bearer "+tt.userToken)

			rec := httptest.NewRecorder()
			suite.apiSuite.e.ServeHTTP(rec, req)

			assert.Equal(t, tt.expectedStatus, rec.Code)
		})
	}
}

// Test S3 presigned URL security
func (suite *FileSecurityIntegrationTestSuite) TestPresignedURLSecurity() {
	// Upload a file
	fileID := suite.uploadTestFile()

	// Get file info to check for presigned URL
	req := httptest.NewRequest("GET", "/api/v1/files/"+fileID, nil)
	req.Header.Set("Authorization", "Bearer "+suite.apiSuite.userToken)

	rec := httptest.NewRecorder()
	suite.apiSuite.e.ServeHTTP(rec, req)

	require.Equal(suite.T(), http.StatusOK, rec.Code)

	var fileInfo map[string]interface{}
	json.Unmarshal(rec.Body.Bytes(), &fileInfo)

	// If using S3, verify presigned URL properties
	if downloadURL, ok := fileInfo["download_url"].(string); ok && strings.Contains(downloadURL, "X-Amz-") {
		suite.T().Run("Presigned URL has expiration", func(t *testing.T) {
			assert.Contains(t, downloadURL, "X-Amz-Expires")
		})

		suite.T().Run("Presigned URL is scoped to organization", func(t *testing.T) {
			assert.Contains(t, downloadURL, suite.apiSuite.orgID)
		})

		suite.T().Run("Presigned URL has signature", func(t *testing.T) {
			assert.Contains(t, downloadURL, "X-Amz-Signature")
		})
	}
}

// Test rate limiting
func (suite *FileSecurityIntegrationTestSuite) TestFileOperationRateLimiting() {
	// Test rapid uploads
	suite.T().Run("Upload rate limiting", func(t *testing.T) {
		uploadCount := 0
		maxUploadsPerMinute := 10

		for i := 0; i < maxUploadsPerMinute+5; i++ {
			body := &bytes.Buffer{}
			writer := multipart.NewWriter(body)
			part, _ := writer.CreateFormFile("file", fmt.Sprintf("file%d.jpg", i))
			part.Write([]byte("content"))
			writer.Close()

			req := httptest.NewRequest("POST",
				"/api/v1/files/upload?organization_id="+suite.apiSuite.orgID,
				body)
			req.Header.Set("Content-Type", writer.FormDataContentType())
			req.Header.Set("Authorization", "Bearer "+suite.apiSuite.userToken)

			rec := httptest.NewRecorder()
			suite.apiSuite.e.ServeHTTP(rec, req)

			if rec.Code == http.StatusCreated {
				uploadCount++
			} else if rec.Code == http.StatusTooManyRequests {
				// Rate limit hit
				break
			}
		}

		// Should hit rate limit before max attempts
		assert.Less(t, uploadCount, maxUploadsPerMinute+5)
	})
}

// Test concurrent access
func (suite *FileSecurityIntegrationTestSuite) TestConcurrentFileAccess() {
	// Upload a file
	fileID := suite.uploadTestFile()

	// Test concurrent downloads don't interfere
	suite.T().Run("Concurrent downloads", func(t *testing.T) {
		concurrency := 10
		errors := make(chan error, concurrency)

		var wg sync.WaitGroup
		wg.Add(concurrency)

		for i := 0; i < concurrency; i++ {
			go func() {
				defer wg.Done()

				req := httptest.NewRequest("GET", "/api/v1/files/"+fileID+"/download", nil)
				req.Header.Set("Authorization", "Bearer "+suite.apiSuite.userToken)

				rec := httptest.NewRecorder()
				suite.apiSuite.e.ServeHTTP(rec, req)

				if rec.Code != http.StatusOK {
					errors <- fmt.Errorf("download failed: %d", rec.Code)
				}
			}()
		}

		wg.Wait()
		close(errors)

		// Check for errors
		errorCount := 0
		for err := range errors {
			t.Logf("Concurrent download error: %v", err)
			errorCount++
		}

		assert.Equal(t, 0, errorCount, "All concurrent downloads should succeed")
	})
}

// Test security headers on downloads
func (suite *FileSecurityIntegrationTestSuite) TestDownloadSecurityHeaders() {
	// Upload files of different types
	files := []struct {
		name        string
		content     []byte
		contentType string
	}{
		{
			name:        "image.jpg",
			content:     []byte("\xFF\xD8\xFF"),
			contentType: "image/jpeg",
		},
		{
			name:        "document.pdf",
			content:     []byte("%PDF-1.4"),
			contentType: "application/pdf",
		},
	}

	for _, file := range files {
		suite.T().Run(file.name, func(t *testing.T) {
			// Upload file
			fileID := suite.uploadFileWithContent(file.name, file.content, file.contentType)

			// Download file
			req := httptest.NewRequest("GET", "/api/v1/files/"+fileID+"/download", nil)
			req.Header.Set("Authorization", "Bearer "+suite.apiSuite.userToken)

			rec := httptest.NewRecorder()
			suite.apiSuite.e.ServeHTTP(rec, req)

			require.Equal(t, http.StatusOK, rec.Code)

			// Check security headers
			assert.NotEmpty(t, rec.Header().Get("X-Content-Type-Options"))
			assert.NotEmpty(t, rec.Header().Get("X-Frame-Options"))

			// PDFs should force download
			if file.contentType == "application/pdf" {
				assert.Contains(t, rec.Header().Get("Content-Disposition"), "attachment")
			}
		})
	}
}

// Helper methods

func (suite *FileSecurityIntegrationTestSuite) uploadTestFile() string {
	return suite.uploadFileWithContent("test.jpg", []byte("test image content"), "image/jpeg")
}

func (suite *FileSecurityIntegrationTestSuite) uploadFileWithContent(filename string, content []byte, contentType string) string {
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	part, _ := writer.CreateFormFile("file", filename)
	part.Write(content)
	writer.Close()

	req := httptest.NewRequest("POST",
		"/api/v1/files/upload?organization_id="+suite.apiSuite.orgID,
		body)
	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("Authorization", "Bearer "+suite.apiSuite.userToken)

	rec := httptest.NewRecorder()
	suite.apiSuite.e.ServeHTTP(rec, req)

	require.Equal(suite.T(), http.StatusCreated, rec.Code)

	var response map[string]interface{}
	json.Unmarshal(rec.Body.Bytes(), &response)

	file := response["file"].(map[string]interface{})
	return file["id"].(string)
}

func (suite *FileSecurityIntegrationTestSuite) uploadFileToOrg(orgID string, token string, filename string) string {
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	part, _ := writer.CreateFormFile("file", filename)
	part.Write([]byte("test content"))
	writer.Close()

	req := httptest.NewRequest("POST",
		"/api/v1/files/upload?organization_id="+orgID,
		body)
	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("Authorization", "Bearer "+token)

	rec := httptest.NewRecorder()
	suite.apiSuite.e.ServeHTTP(rec, req)

	require.Equal(suite.T(), http.StatusCreated, rec.Code)

	var response map[string]interface{}
	json.Unmarshal(rec.Body.Bytes(), &response)

	file := response["file"].(map[string]interface{})
	return file["id"].(string)
}

func (suite *FileSecurityIntegrationTestSuite) createNewOrganization() string {
	// Implementation depends on your API
	// This is a placeholder
	return uuid.New().String()
}

func (suite *FileSecurityIntegrationTestSuite) createUserInOrg(orgID string) string {
	// Implementation depends on your API
	// This is a placeholder that returns a valid token
	return "test-token-" + orgID
}

func (suite *FileSecurityIntegrationTestSuite) getExtensionForMime(mimeType string) string {
	switch mimeType {
	case "image/jpeg":
		return ".jpg"
	case "image/png":
		return ".png"
	case "application/pdf":
		return ".pdf"
	default:
		return ".bin"
	}
}

func TestFileSecurityIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(FileSecurityIntegrationTestSuite))
}
