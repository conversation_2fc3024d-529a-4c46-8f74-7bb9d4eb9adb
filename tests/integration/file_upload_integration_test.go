package integration

import (
	"bytes"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// FileUploadIntegrationTestSuite tests file upload endpoints with real API calls
type FileUploadIntegrationTestSuite struct {
	suite.Suite
	apiSuite *APITestSuite
}

func (suite *FileUploadIntegrationTestSuite) SetupSuite() {
	suite.apiSuite = SetupAPITest(suite.T())
}

func (suite *FileUploadIntegrationTestSuite) TearDownSuite() {
	suite.apiSuite.TeardownAPITest(suite.T())
}

func (suite *FileUploadIntegrationTestSuite) SetupTest() {
	suite.apiSuite.CleanupTestData(suite.T())
}

// TestEventMediaUpload tests uploading media files to events
func (suite *FileUploadIntegrationTestSuite) TestEventMediaUpload() {
	// First create an event
	eventID := suite.createTestEvent()

	// Create test image file
	fileContent := []byte("fake image content for testing")
	fileName := "test-image.jpg"
	fieldName := "media_file"

	// Upload media file
	rec := suite.uploadFile("/api/v1/events/"+eventID+"/media", fieldName, fileName, fileContent, map[string]string{
		"description": "Test event media",
		"is_banner":   "false",
	})

	var response EventMediaResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusCreated, &response)

	require.Equal(suite.T(), eventID, response.EventID.String())
	require.Equal(suite.T(), "Test event media", response.Description)
	require.False(suite.T(), response.IsBanner)
	require.NotEmpty(suite.T(), response.FileURL)
	require.Equal(suite.T(), fileName, response.FileName)
}

// TestEventMediaUpload_InvalidFileType tests uploading invalid file types
func (suite *FileUploadIntegrationTestSuite) TestEventMediaUpload_InvalidFileType() {
	eventID := suite.createTestEvent()

	// Try to upload executable file
	fileContent := []byte("fake executable content")
	fileName := "malicious.exe"
	fieldName := "media_file"

	rec := suite.uploadFile("/api/v1/events/"+eventID+"/media", fieldName, fileName, fileContent, nil)
	suite.apiSuite.AssertErrorResponse(suite.T(), rec, http.StatusBadRequest, "mime type") // application/x-msdownload not allowed
}

// TestEventMediaUpload_FileTooLarge tests uploading files that exceed size limits
func (suite *FileUploadIntegrationTestSuite) TestEventMediaUpload_FileTooLarge() {
	eventID := suite.createTestEvent()

	// Create large file (assuming 10MB limit)
	largeFileContent := make([]byte, 11*1024*1024) // 11MB
	fileName := "large-image.jpg"
	fieldName := "media_file"

	rec := suite.uploadFile("/api/v1/events/"+eventID+"/media", fieldName, fileName, largeFileContent, nil)
	suite.apiSuite.AssertErrorResponse(suite.T(), rec, http.StatusBadRequest, "file too large")
}

// TestEventMediaUpload_MissingFile tests uploading without file
func (suite *FileUploadIntegrationTestSuite) TestEventMediaUpload_MissingFile() {
	eventID := suite.createTestEvent()

	// Make request without file
	req := httptest.NewRequest("POST", "/api/v1/events/"+eventID+"/media", nil)
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", suite.apiSuite.adminToken))

	rec := httptest.NewRecorder()
	suite.apiSuite.e.ServeHTTP(rec, req)

	suite.apiSuite.AssertErrorResponse(suite.T(), rec, http.StatusBadRequest, "no file uploaded")
}

// TestEventBannerUpload tests uploading banner image for events
func (suite *FileUploadIntegrationTestSuite) TestEventBannerUpload() {
	eventID := suite.createTestEvent()

	fileContent := []byte("fake banner image content")
	fileName := "banner.jpg"
	fieldName := "media_file"

	rec := suite.uploadFile("/api/v1/events/"+eventID+"/media", fieldName, fileName, fileContent, map[string]string{
		"description": "Event banner image",
		"is_banner":   "true",
	})

	var response EventMediaResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusCreated, &response)

	require.True(suite.T(), response.IsBanner)
	require.Equal(suite.T(), "Event banner image", response.Description)
}

// TestUserProfilePictureUpload tests uploading user profile pictures
func (suite *FileUploadIntegrationTestSuite) TestUserProfilePictureUpload() {
	fileContent := []byte("fake profile picture content")
	fileName := "profile.jpg"
	fieldName := "profile_picture"

	rec := suite.uploadFileAsUser("/api/v1/users/profile/picture", fieldName, fileName, fileContent, nil)

	var response UserProfilePictureResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	require.NotEmpty(suite.T(), response.ImageURL)
	require.Equal(suite.T(), fileName, response.FileName)
}

// TestUserProfilePictureUpload_InvalidImageFormat tests invalid image formats
func (suite *FileUploadIntegrationTestSuite) TestUserProfilePictureUpload_InvalidImageFormat() {
	fileContent := []byte("not an image")
	fileName := "document.pdf"
	fieldName := "profile_picture"

	rec := suite.uploadFileAsUser("/api/v1/users/profile/picture", fieldName, fileName, fileContent, nil)
	suite.apiSuite.AssertErrorResponse(suite.T(), rec, http.StatusBadRequest, "invalid image format")
}

// TestOrganizationLogoUpload tests uploading organization logos
func (suite *FileUploadIntegrationTestSuite) TestOrganizationLogoUpload() {
	fileContent := []byte("fake logo content")
	fileName := "logo.png"
	fieldName := "logo"

	rec := suite.uploadFile("/api/v1/organizations/logo", fieldName, fileName, fileContent, nil)

	var response OrganizationLogoResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	require.NotEmpty(suite.T(), response.ImageURL)
	require.Equal(suite.T(), fileName, response.FileName)
}

// TestOrganizationResourceUpload tests uploading organization resources
func (suite *FileUploadIntegrationTestSuite) TestOrganizationResourceUpload() {
	// First create a resource
	resourceReq := map[string]interface{}{
		"title":        "Test Document",
		"description":  "Test resource document",
		"type":         "document",
		"category":     "policies",
		"access_level": "members",
	}

	rec, err := suite.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/organizations/resources", resourceReq)
	require.NoError(suite.T(), err)

	var resourceResponse ResourceResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusCreated, &resourceResponse)

	resourceID := resourceResponse.ID.String()

	// Upload file to the resource
	fileContent := []byte("fake document content")
	fileName := "policy-document.pdf"
	fieldName := "resource_file"

	rec = suite.uploadFile("/api/v1/organizations/resources/"+resourceID+"/files", fieldName, fileName, fileContent, map[string]string{
		"description": "Policy document attachment",
	})

	var fileResponse ResourceFileResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusCreated, &fileResponse)

	require.Equal(suite.T(), resourceID, fileResponse.ResourceID.String())
	require.Equal(suite.T(), "Policy document attachment", fileResponse.Description)
	require.Equal(suite.T(), fileName, fileResponse.FileName)
	require.NotEmpty(suite.T(), fileResponse.FileURL)
}

// TestPostMediaUpload tests uploading media files to posts
func (suite *FileUploadIntegrationTestSuite) TestPostMediaUpload() {
	// First create a post
	postReq := map[string]interface{}{
		"title":   "Test Post",
		"content": "Test post content",
		"status":  "draft",
	}

	rec, err := suite.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/posts", postReq)
	require.NoError(suite.T(), err)

	var postResponse PostResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusCreated, &postResponse)

	postID := postResponse.ID.String()

	// Upload media to the post
	fileContent := []byte("fake post image content")
	fileName := "post-image.jpg"
	fieldName := "media_file"

	rec = suite.uploadFile("/api/v1/posts/"+postID+"/media", fieldName, fileName, fileContent, map[string]string{
		"description": "Post media attachment",
		"is_banner":   "false",
	})

	var mediaResponse PostMediaResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusCreated, &mediaResponse)

	require.Equal(suite.T(), postID, mediaResponse.PostID.String())
	require.Equal(suite.T(), "Post media attachment", mediaResponse.Description)
	require.False(suite.T(), mediaResponse.IsBanner)
	require.Equal(suite.T(), fileName, mediaResponse.FileName)
}

// TestMultipleFileUpload tests uploading multiple files at once
func (suite *FileUploadIntegrationTestSuite) TestMultipleFileUpload() {
	eventID := suite.createTestEvent()

	// Create multiple files
	files := []struct {
		content  []byte
		filename string
	}{
		{[]byte("image 1 content"), "image1.jpg"},
		{[]byte("image 2 content"), "image2.png"},
		{[]byte("image 3 content"), "image3.gif"},
	}

	rec := suite.uploadMultipleFiles("/api/v1/events/"+eventID+"/media/batch", "media_files", files, nil)

	var response BatchUploadResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusCreated, &response)

	require.Equal(suite.T(), 3, len(response.UploadedFiles))
	require.Equal(suite.T(), 0, len(response.FailedFiles))

	for i, file := range response.UploadedFiles {
		require.Equal(suite.T(), files[i].filename, file.FileName)
		require.NotEmpty(suite.T(), file.FileURL)
	}
}

// TestFileUploadSecurity tests security measures for file uploads
func (suite *FileUploadIntegrationTestSuite) TestFileUploadSecurity() {
	eventID := suite.createTestEvent()

	// Test uploading file with malicious filename
	fileContent := []byte("image content")
	maliciousFileName := "../../../etc/passwd"
	fieldName := "media_file"

	rec := suite.uploadFile("/api/v1/events/"+eventID+"/media", fieldName, maliciousFileName, fileContent, nil)
	suite.apiSuite.AssertErrorResponse(suite.T(), rec, http.StatusBadRequest, "invalid filename")

	// Test uploading file with script content
	scriptContent := []byte("<script>alert('xss')</script>")
	fileName := "script.html"

	rec = suite.uploadFile("/api/v1/events/"+eventID+"/media", fieldName, fileName, scriptContent, nil)
	suite.apiSuite.AssertErrorResponse(suite.T(), rec, http.StatusBadRequest, "invalid file type")
}

// TestFileUploadPermissions tests file upload permissions
func (suite *FileUploadIntegrationTestSuite) TestFileUploadPermissions() {
	eventID := suite.createTestEvent()

	fileContent := []byte("image content")
	fileName := "unauthorized.jpg"
	fieldName := "media_file"

	// Test uploading without authentication
	rec := suite.uploadFileUnauthenticated("/api/v1/events/"+eventID+"/media", fieldName, fileName, fileContent, nil)
	suite.apiSuite.AssertErrorResponse(suite.T(), rec, http.StatusUnauthorized, "unauthorized")

	// Test uploading to event from different organization (if applicable)
	// This would depend on your multi-tenancy implementation
}

// TestFileMetadataRetrieval tests retrieving file metadata
func (suite *FileUploadIntegrationTestSuite) TestFileMetadataRetrieval() {
	eventID := suite.createTestEvent()

	// Upload a file first
	fileContent := []byte("test image content")
	fileName := "metadata-test.jpg"
	fieldName := "media_file"

	uploadRec := suite.uploadFile("/api/v1/events/"+eventID+"/media", fieldName, fileName, fileContent, map[string]string{
		"description": "Test metadata",
	})

	var uploadResponse EventMediaResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), uploadRec, http.StatusCreated, &uploadResponse)

	mediaID := uploadResponse.ID.String()

	// Get file metadata
	rec, err := suite.apiSuite.MakeAuthenticatedRequest("GET", "/api/v1/events/"+eventID+"/media/"+mediaID, nil)
	require.NoError(suite.T(), err)

	var response EventMediaResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	require.Equal(suite.T(), mediaID, response.ID.String())
	require.Equal(suite.T(), fileName, response.FileName)
	require.Equal(suite.T(), "Test metadata", response.Description)
	require.NotEmpty(suite.T(), response.FileURL)
	require.NotEmpty(suite.T(), response.CreatedAt)
}

// TestFileDownload tests file download functionality
func (suite *FileUploadIntegrationTestSuite) TestFileDownload() {
	eventID := suite.createTestEvent()

	// Upload a file first
	fileContent := []byte("downloadable content")
	fileName := "download-test.jpg"
	fieldName := "media_file"

	uploadRec := suite.uploadFile("/api/v1/events/"+eventID+"/media", fieldName, fileName, fileContent, nil)

	var uploadResponse EventMediaResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), uploadRec, http.StatusCreated, &uploadResponse)

	mediaID := uploadResponse.ID.String()

	// Download the file
	rec, err := suite.apiSuite.MakeAuthenticatedRequest("GET", "/api/v1/events/"+eventID+"/media/"+mediaID+"/download", nil)
	require.NoError(suite.T(), err)

	require.Equal(suite.T(), http.StatusOK, rec.Code)
	require.Equal(suite.T(), "application/octet-stream", rec.Header().Get("Content-Type"))
	require.Contains(suite.T(), rec.Header().Get("Content-Disposition"), fileName)
}

// TestFileDeletion tests file deletion functionality
func (suite *FileUploadIntegrationTestSuite) TestFileDeletion() {
	eventID := suite.createTestEvent()

	// Upload a file first
	fileContent := []byte("deletable content")
	fileName := "delete-test.jpg"
	fieldName := "media_file"

	uploadRec := suite.uploadFile("/api/v1/events/"+eventID+"/media", fieldName, fileName, fileContent, nil)

	var uploadResponse EventMediaResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), uploadRec, http.StatusCreated, &uploadResponse)

	mediaID := uploadResponse.ID.String()

	// Delete the file
	rec, err := suite.apiSuite.MakeAuthenticatedRequest("DELETE", "/api/v1/events/"+eventID+"/media/"+mediaID, nil)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusNoContent, nil)

	// Verify file is deleted
	rec, err = suite.apiSuite.MakeAuthenticatedRequest("GET", "/api/v1/events/"+eventID+"/media/"+mediaID, nil)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertErrorResponse(suite.T(), rec, http.StatusNotFound, "media not found")
}

// Helper methods for file upload testing

func (suite *FileUploadIntegrationTestSuite) createTestEvent() string {
	createReq := map[string]interface{}{
		"title":       "Test Event for Upload",
		"description": "Test event for file upload testing",
		"location":    "Test Location",
		"start_time":  "2024-12-01T10:00:00Z",
		"end_time":    "2024-12-01T12:00:00Z",
		"capacity":    100,
		"status":      "draft",
	}

	rec, err := suite.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/events", createReq)
	require.NoError(suite.T(), err)

	var response EventResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusCreated, &response)

	return response.ID.String()
}

func (suite *FileUploadIntegrationTestSuite) uploadFile(endpoint, fieldName, fileName string, fileContent []byte, additionalFields map[string]string) *httptest.ResponseRecorder {
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	// Add file
	part, err := writer.CreateFormFile(fieldName, fileName)
	require.NoError(suite.T(), err)
	_, err = io.Copy(part, bytes.NewReader(fileContent))
	require.NoError(suite.T(), err)

	// Add additional fields
	for key, value := range additionalFields {
		err = writer.WriteField(key, value)
		require.NoError(suite.T(), err)
	}

	err = writer.Close()
	require.NoError(suite.T(), err)

	req := httptest.NewRequest("POST", endpoint, body)
	req.Header.Set(echo.HeaderContentType, writer.FormDataContentType())
	req.Header.Set(echo.HeaderAuthorization, fmt.Sprintf("Bearer %s", suite.apiSuite.adminToken))

	rec := httptest.NewRecorder()
	suite.apiSuite.e.ServeHTTP(rec, req)

	return rec
}

func (suite *FileUploadIntegrationTestSuite) uploadFileAsUser(endpoint, fieldName, fileName string, fileContent []byte, additionalFields map[string]string) *httptest.ResponseRecorder {
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	// Add file
	part, err := writer.CreateFormFile(fieldName, fileName)
	require.NoError(suite.T(), err)
	_, err = io.Copy(part, bytes.NewReader(fileContent))
	require.NoError(suite.T(), err)

	// Add additional fields
	for key, value := range additionalFields {
		err = writer.WriteField(key, value)
		require.NoError(suite.T(), err)
	}

	err = writer.Close()
	require.NoError(suite.T(), err)

	req := httptest.NewRequest("POST", endpoint, body)
	req.Header.Set(echo.HeaderContentType, writer.FormDataContentType())
	req.Header.Set(echo.HeaderAuthorization, fmt.Sprintf("Bearer %s", suite.apiSuite.userToken))

	rec := httptest.NewRecorder()
	suite.apiSuite.e.ServeHTTP(rec, req)

	return rec
}

func (suite *FileUploadIntegrationTestSuite) uploadFileUnauthenticated(endpoint, fieldName, fileName string, fileContent []byte, additionalFields map[string]string) *httptest.ResponseRecorder {
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	// Add file
	part, err := writer.CreateFormFile(fieldName, fileName)
	require.NoError(suite.T(), err)
	_, err = io.Copy(part, bytes.NewReader(fileContent))
	require.NoError(suite.T(), err)

	// Add additional fields
	for key, value := range additionalFields {
		err = writer.WriteField(key, value)
		require.NoError(suite.T(), err)
	}

	err = writer.Close()
	require.NoError(suite.T(), err)

	req := httptest.NewRequest("POST", endpoint, body)
	req.Header.Set(echo.HeaderContentType, writer.FormDataContentType())

	rec := httptest.NewRecorder()
	suite.apiSuite.e.ServeHTTP(rec, req)

	return rec
}

func (suite *FileUploadIntegrationTestSuite) uploadMultipleFiles(endpoint, fieldName string, files []struct {
	content  []byte
	filename string
}, additionalFields map[string]string,
) *httptest.ResponseRecorder {
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	// Add multiple files
	for _, file := range files {
		part, err := writer.CreateFormFile(fieldName, file.filename)
		require.NoError(suite.T(), err)
		_, err = io.Copy(part, bytes.NewReader(file.content))
		require.NoError(suite.T(), err)
	}

	// Add additional fields
	for key, value := range additionalFields {
		err := writer.WriteField(key, value)
		require.NoError(suite.T(), err)
	}

	err := writer.Close()
	require.NoError(suite.T(), err)

	req := httptest.NewRequest("POST", endpoint, body)
	req.Header.Set(echo.HeaderContentType, writer.FormDataContentType())
	req.Header.Set(echo.HeaderAuthorization, fmt.Sprintf("Bearer %s", suite.apiSuite.adminToken))

	rec := httptest.NewRecorder()
	suite.apiSuite.e.ServeHTTP(rec, req)

	return rec
}

func TestFileUploadIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(FileUploadIntegrationTestSuite))
}
