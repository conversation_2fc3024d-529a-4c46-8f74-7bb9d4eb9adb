package integration

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/testutil"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestInfrastructurePerformance demonstrates the performance improvements of the new test infrastructure
func TestInfrastructurePerformance(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration tests in short mode")
	}

	t.Run("SharedContainerVsIndividualContainer", func(t *testing.T) {
		// Test 1: Individual container per test (original pattern)
		var individualTimes []time.Duration
		for i := 0; i < 3; i++ {
			start := time.Now()
			_, store := testutil.SetupTestDatabaseWithStore(t)
			testutil.RunInTransactionWithStore(t, store, func(ctx context.Context, txStore db.Store) {
				user, err := txStore.CreateUserWithPhone(ctx, db.CreateUserWithPhoneParams{
					DisplayName:                 fmt.Sprintf("Individual User %d", i),
					Phone:                       testutil.ToPtr(fmt.Sprintf("+1000000%03d", i)),
					PhoneVerifiedAt:             testutil.ToPtr(time.Now()),
					InterfaceLanguage:           "en",
					CommunicationLanguage:       "en",
					PhoneOtpChannel:             "sms",
					EnableAppNotifications:      true,
					EnableWhatsappNotifications: false,
					EnableSmsNotifications:      true,
					EnableEmailNotifications:    false,
				})
				require.NoError(t, err)
				assert.NotEqual(t, uuid.Nil, user.ID)
			})
			// testDB cleanup is handled automatically by defer
			individualTimes = append(individualTimes, time.Since(start))
		}

		// Test 2: Shared container (new pattern)
		start := time.Now()
		store, cleanup := testutil.SetupSharedTestDB(t)
		sharedSetupTime := time.Since(start)
		defer cleanup()

		var sharedTimes []time.Duration
		for i := 0; i < 3; i++ {
			start := time.Now()
			testutil.RunInTransactionWithStore(t, store, func(ctx context.Context, txStore db.Store) {
				user, err := txStore.CreateUserWithPhone(ctx, db.CreateUserWithPhoneParams{
					DisplayName:                 fmt.Sprintf("Shared User %d", i),
					Phone:                       testutil.ToPtr(fmt.Sprintf("+2000000%03d", i)),
					PhoneVerifiedAt:             testutil.ToPtr(time.Now()),
					InterfaceLanguage:           "en",
					CommunicationLanguage:       "en",
					PhoneOtpChannel:             "sms",
					EnableAppNotifications:      true,
					EnableWhatsappNotifications: false,
					EnableSmsNotifications:      true,
					EnableEmailNotifications:    false,
				})
				require.NoError(t, err)
				assert.NotEqual(t, uuid.Nil, user.ID)
			})
			sharedTimes = append(sharedTimes, time.Since(start))
		}

		// Calculate averages
		avgIndividual := calculateAverage(individualTimes)
		avgShared := calculateAverage(sharedTimes)

		t.Logf("Average individual container time: %v", avgIndividual)
		t.Logf("Shared container setup time: %v", sharedSetupTime)
		t.Logf("Average shared container test time: %v", avgShared)
		t.Logf("Performance improvement: %.2fx faster", float64(avgIndividual)/float64(avgShared))

		// The shared container approach should be significantly faster for individual tests
		// after the initial setup cost is amortized
		speedupFactor := float64(avgIndividual) / float64(avgShared)
		assert.Greater(t, speedupFactor, 100.0, "Shared container should provide substantial speedup (>100x)")
	})
}

// TestTransactionIsolationReliability tests the reliability of transaction isolation
func TestTransactionIsolationReliability(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration tests in short mode")
	}

	store, cleanup := testutil.SetupSharedTestDB(t)
	defer cleanup()

	const numTests = 20
	const concurrency = 5

	// Run multiple concurrent tests to ensure isolation is reliable
	var wg sync.WaitGroup
	errors := make(chan error, numTests)

	for i := 0; i < numTests; i++ {
		wg.Add(1)
		go func(testID int) {
			defer wg.Done()

			testutil.RunInTransactionWithStore(t, store, func(ctx context.Context, txStore db.Store) {
				// Create a user with a unique phone number
				phoneNumber := fmt.Sprintf("+3%09d", testID)
				user, err := txStore.CreateUserWithPhone(ctx, db.CreateUserWithPhoneParams{
					DisplayName:                 fmt.Sprintf("Isolation Test User %d", testID),
					Phone:                       testutil.ToPtr(phoneNumber),
					PhoneVerifiedAt:             testutil.ToPtr(time.Now()),
					InterfaceLanguage:           "en",
					CommunicationLanguage:       "en",
					PhoneOtpChannel:             "sms",
					EnableAppNotifications:      true,
					EnableWhatsappNotifications: false,
					EnableSmsNotifications:      true,
					EnableEmailNotifications:    false,
				})
				if err != nil {
					errors <- fmt.Errorf("test %d: failed to create user: %w", testID, err)
					return
				}

				// Verify the user exists within the transaction
				foundUser, err := txStore.GetUserByID(ctx, user.ID)
				if err != nil {
					errors <- fmt.Errorf("test %d: failed to find user: %w", testID, err)
					return
				}

				if foundUser.ID != user.ID {
					errors <- fmt.Errorf("test %d: user ID mismatch", testID)
					return
				}

				// Create an organization owned by this user
				org, err := txStore.CreateOrganization(ctx, db.CreateOrganizationParams{
					Name:         fmt.Sprintf("Isolation Test Org %d", testID),
					OwnerUserID:  user.ID,
					IsDefaultOrg: false,
					Status:       "active"})
				if err != nil {
					errors <- fmt.Errorf("test %d: failed to create organization: %w", testID, err)
					return
				}

				// Verify the organization exists within the transaction
				foundOrg, err := txStore.GetOrganizationByID(ctx, org.ID)
				if err != nil {
					errors <- fmt.Errorf("test %d: failed to find organization: %w", testID, err)
					return
				}

				if foundOrg.ID != org.ID {
					errors <- fmt.Errorf("test %d: organization ID mismatch", testID)
					return
				}

				// Simulate some work
				time.Sleep(time.Duration(testID%10) * time.Millisecond)
			})
		}(i)

		// Control concurrency
		if (i+1)%concurrency == 0 {
			wg.Wait()
		}
	}

	wg.Wait()
	close(errors)

	// Check for any errors
	var errorCount int
	for err := range errors {
		t.Errorf("Transaction isolation error: %v", err)
		errorCount++
	}

	assert.Zero(t, errorCount, "All transaction isolation tests should pass")

	// Verify no data persisted outside transactions
	ctx := context.Background()
	users, err := store.ListUsers(ctx, db.ListUsersParams{
		Limit:  1000,
		Offset: 0,
	})
	require.NoError(t, err)

	for _, user := range users {
		if user.Phone != nil && len(*user.Phone) >= 10 {
			phoneStr := *user.Phone
			if phoneStr[0:2] == "+3" {
				t.Errorf("Isolation test user should not persist: %s", phoneStr)
			}
		}
	}

	t.Logf("Successfully ran %d concurrent transaction isolation tests", numTests)
}

// TestContainerReuseEfficiency tests the efficiency of container reuse
func TestContainerReuseEfficiency(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration tests in short mode")
	}

	// Measure container reuse efficiency
	const numOperations = 10

	start := time.Now()
	store, cleanup := testutil.SetupSharedTestDB(t)
	setupTime := time.Since(start)
	defer cleanup()

	var operationTimes []time.Duration

	for i := 0; i < numOperations; i++ {
		operationStart := time.Now()
		testutil.RunInTransactionWithStore(t, store, func(ctx context.Context, txStore db.Store) {
			// Perform a complex database operation
			user, err := txStore.CreateUserWithPhone(ctx, db.CreateUserWithPhoneParams{
				DisplayName:                 fmt.Sprintf("Efficiency User %d", i),
				Phone:                       testutil.ToPtr(fmt.Sprintf("+4%09d", i)),
				PhoneVerifiedAt:             testutil.ToPtr(time.Now()),
				InterfaceLanguage:           "en",
				CommunicationLanguage:       "en",
				PhoneOtpChannel:             "sms",
				EnableAppNotifications:      true,
				EnableWhatsappNotifications: false,
				EnableSmsNotifications:      true,
				EnableEmailNotifications:    false,
			})
			require.NoError(t, err)

			org, err := txStore.CreateOrganization(ctx, db.CreateOrganizationParams{
				Name:         fmt.Sprintf("Efficiency Org %d", i),
				OwnerUserID:  user.ID,
				IsDefaultOrg: false,
				Status:       "active"})
			require.NoError(t, err)

			_, err = txStore.AddUserToOrganization(ctx, db.AddUserToOrganizationParams{
				UserID:         user.ID,
				OrganizationID: org.ID,
				Role:           "admin",
			})
			require.NoError(t, err)

			// Query to verify data
			membership, err := txStore.GetUserOrganizationMembership(ctx, db.GetUserOrganizationMembershipParams{
				UserID:         user.ID,
				OrganizationID: org.ID,
			})
			require.NoError(t, err)
			assert.Equal(t, "admin", membership.Role)
		})
		operationTimes = append(operationTimes, time.Since(operationStart))
	}

	avgOperationTime := calculateAverage(operationTimes)
	totalTime := time.Since(start)

	t.Logf("Container setup time: %v", setupTime)
	t.Logf("Average operation time: %v", avgOperationTime)
	t.Logf("Total test time: %v", totalTime)
	t.Logf("Setup overhead: %.1f%%", float64(setupTime)/float64(totalTime)*100)

	// The setup overhead should be reasonable
	// Note: For small numbers of operations, setup will dominate, but operations should still be fast
	setupOverhead := float64(setupTime) / float64(totalTime)
	t.Logf("Setup overhead: %.1f%% (expected to be high for small operation counts)", setupOverhead*100)

	// The key metric is that once container is set up, operations are fast
	// Setup overhead dominance is acceptable for small operation counts

	// Operations should be fast (less than 100ms on average)
	assert.Less(t, avgOperationTime, 100*time.Millisecond, "Database operations should be fast")
}

// TestStorePatternConsistency verifies Store pattern consistency across tests
func TestStorePatternConsistency(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration tests in short mode")
	}

	store, cleanup := testutil.SetupSharedTestDB(t)
	defer cleanup()

	t.Run("ExecTxNotAvailableInTransactionStore", func(t *testing.T) {
		testutil.RunInTransactionWithStore(t, store, func(ctx context.Context, txStore db.Store) {
			// ExecTx should not be available within transaction-isolated tests
			err := txStore.ExecTx(ctx, func(q db.Querier) error {
				return nil
			})
			assert.Error(t, err, "ExecTx should not be available in transaction-isolated tests")
			assert.Contains(t, err.Error(), "not supported within test transaction isolation")
		})
	})

	t.Run("QuerierMethodsAvailable", func(t *testing.T) {
		testutil.RunInTransactionWithStore(t, store, func(ctx context.Context, txStore db.Store) {
			// All Querier methods should be available
			user, err := txStore.CreateUserWithPhone(ctx, db.CreateUserWithPhoneParams{
				DisplayName:                 "Store Pattern Test User",
				Phone:                       testutil.ToPtr("+15555551234"),
				PhoneVerifiedAt:             testutil.ToPtr(time.Now()),
				InterfaceLanguage:           "en",
				CommunicationLanguage:       "en",
				PhoneOtpChannel:             "sms",
				EnableAppNotifications:      true,
				EnableWhatsappNotifications: false,
				EnableSmsNotifications:      true,
				EnableEmailNotifications:    false,
			})
			require.NoError(t, err)

			// Test query methods
			foundUser, err := txStore.GetUserByID(ctx, user.ID)
			require.NoError(t, err)
			assert.Equal(t, user.ID, foundUser.ID)

			foundByPhone, err := txStore.GetUserByPhone(ctx, user.Phone)
			require.NoError(t, err)
			assert.Equal(t, user.ID, foundByPhone.ID)

			users, err := txStore.ListUsers(ctx, db.ListUsersParams{
				Limit:  10,
				Offset: 0,
			})
			require.NoError(t, err)
			assert.GreaterOrEqual(t, len(users), 1)
		})
	})
}

// Helper function to calculate average duration
func calculateAverage(durations []time.Duration) time.Duration {
	if len(durations) == 0 {
		return 0
	}

	var total time.Duration
	for _, d := range durations {
		total += d
	}
	return total / time.Duration(len(durations))
}
