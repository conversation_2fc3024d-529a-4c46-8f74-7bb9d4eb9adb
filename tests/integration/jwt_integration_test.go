package integration

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/internal/authorization"
	"Membership-SAAS-System-Backend/internal/token"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestJWTIntegration tests the new JWT format integration
func TestJWTIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration tests in short mode")
	}

	ctx := context.Background()

	// Set up API test suite
	suite := SetupAPITest(t)
	defer suite.TeardownAPITest(t)

	t.Run("TokenGeneration", func(t *testing.T) {
		testJWTTokenGeneration(t, ctx, suite)
	})

	t.Run("TokenValidation", func(t *testing.T) {
		testJWTTokenValidation(t, ctx, suite)
	})

	t.Run("OrganizationSwitching", func(t *testing.T) {
		testOrganizationSwitching(t, ctx, suite)
	})

	t.Run("AuthenticationFlows", func(t *testing.T) {
		testAuthenticationFlows(t, ctx, suite)
	})
}

// testJWTTokenGeneration tests that JWT tokens are generated with new format
func testJWTTokenGeneration(t *testing.T, ctx context.Context, suite *APITestSuite) {
	// Test that we can generate a token for the default superadmin user
	superAdminID := uuid.MustParse("00000000-0000-0000-0000-000000000001")

	// Generate token using the enhanced token generation
	tokenString, err := token.GenerateEnhancedAccessToken(ctx, suite.Server.ServiceContainer().Store, superAdminID)
	require.NoError(t, err)
	require.NotEmpty(t, tokenString)

	// Parse and validate token structure
	parsedToken, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// Validate signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte("test-secret-key-for-testing"), nil
	})
	require.NoError(t, err)
	require.True(t, parsedToken.Valid)

	// Validate claims structure
	claims, ok := parsedToken.Claims.(jwt.MapClaims)
	require.True(t, ok)

	// Check required fields exist
	assert.Contains(t, claims, "user_id")
	assert.Contains(t, claims, "platform_role")
	assert.Contains(t, claims, "active_org")
	assert.Contains(t, claims, "exp")

	// Validate user_id format
	userIDStr, ok := claims["user_id"].(string)
	require.True(t, ok)
	_, err = uuid.Parse(userIDStr)
	require.NoError(t, err)

	// Validate platform_role is one of valid values
	platformRole, ok := claims["platform_role"].(string)
	require.True(t, ok)
	validRoles := []string{authorization.PlatformRoleSuperAdmin, authorization.PlatformRoleStaff, authorization.PlatformRoleUser}
	assert.Contains(t, validRoles, platformRole)

	// Validate active_org structure
	activeOrg, ok := claims["active_org"].(map[string]interface{})
	require.True(t, ok)
	assert.Contains(t, activeOrg, "id")
	assert.Contains(t, activeOrg, "role")

	// Validate organization role
	orgRole, ok := activeOrg["role"].(string)
	require.True(t, ok)
	validOrgRoles := []string{authorization.OrgRoleOwner, authorization.OrgRoleAdmin, authorization.OrgRoleStaff, authorization.OrgRoleMember}
	assert.Contains(t, validOrgRoles, orgRole)

	t.Logf("Generated JWT claims: %+v", claims)
}

// testJWTTokenValidation tests that JWT tokens are properly validated
func testJWTTokenValidation(t *testing.T, ctx context.Context, suite *APITestSuite) {
	// Test with valid token
	validToken := suite.adminToken

	req := httptest.NewRequest(http.MethodGet, "/api/v1/users/me", nil)
	req.Header.Set(echo.HeaderAuthorization, "Bearer "+validToken)
	rec := httptest.NewRecorder()

	suite.e.ServeHTTP(rec, req)

	// Should succeed with valid token
	assert.Equal(t, http.StatusOK, rec.Code, "Valid token should be accepted")

	// Test with invalid token
	invalidToken := "invalid.jwt.token"

	req = httptest.NewRequest(http.MethodGet, "/api/v1/users/me", nil)
	req.Header.Set(echo.HeaderAuthorization, "Bearer "+invalidToken)
	rec = httptest.NewRecorder()

	suite.e.ServeHTTP(rec, req)

	// Should fail with invalid token
	assert.Equal(t, http.StatusUnauthorized, rec.Code, "Invalid token should be rejected")

	// Test with expired token
	expiredClaims := jwt.MapClaims{
		"user_id":       "00000000-0000-0000-0000-000000000001",
		"platform_role": authorization.PlatformRoleSuperAdmin,
		"active_org": map[string]interface{}{
			"id":   "00000000-0000-0000-0000-000000000001",
			"role": authorization.OrgRoleOwner,
		},
		"exp": time.Now().Add(-time.Hour).Unix(), // Expired 1 hour ago
		"iat": time.Now().Add(-2 * time.Hour).Unix(),
	}

	expiredToken := jwt.NewWithClaims(jwt.SigningMethodHS256, expiredClaims)
	expiredTokenString, err := expiredToken.SignedString([]byte("test-secret-key-for-testing"))
	require.NoError(t, err)

	req = httptest.NewRequest(http.MethodGet, "/api/v1/users/me", nil)
	req.Header.Set(echo.HeaderAuthorization, "Bearer "+expiredTokenString)
	rec = httptest.NewRecorder()

	suite.e.ServeHTTP(rec, req)

	// Should fail with expired token
	assert.Equal(t, http.StatusUnauthorized, rec.Code, "Expired token should be rejected")
}

// testOrganizationSwitching tests the organization switching functionality
func testOrganizationSwitching(t *testing.T, ctx context.Context, suite *APITestSuite) {
	// For this test, we'll use the default organization ID from the suite
	defaultOrgID := suite.orgID

	// Test switching to the same organization (should succeed)
	req := httptest.NewRequest(http.MethodPost, fmt.Sprintf("/api/v1/auth/switch-org/%s", defaultOrgID), nil)
	req.Header.Set(echo.HeaderAuthorization, "Bearer "+suite.adminToken)
	rec := httptest.NewRecorder()

	suite.e.ServeHTTP(rec, req)

	if rec.Code == http.StatusOK {
		// Parse the response to get the new token
		var response struct {
			AccessToken string `json:"access_token"`
		}
		err := json.Unmarshal(rec.Body.Bytes(), &response)
		require.NoError(t, err)
		require.NotEmpty(t, response.AccessToken)

		// Validate the new token has the correct organization
		parsedToken, err := jwt.Parse(response.AccessToken, func(token *jwt.Token) (interface{}, error) {
			return []byte("test-secret-key-for-testing"), nil
		})
		require.NoError(t, err)

		claims, ok := parsedToken.Claims.(jwt.MapClaims)
		require.True(t, ok)

		activeOrg, ok := claims["active_org"].(map[string]interface{})
		require.True(t, ok)

		orgIDFromToken, ok := activeOrg["id"].(string)
		require.True(t, ok)
		assert.Equal(t, defaultOrgID, orgIDFromToken)

		t.Logf("Organization switching successful: %s", orgIDFromToken)
	} else {
		t.Logf("Organization switching returned status %d: %s", rec.Code, rec.Body.String())
		// This might be expected if the endpoint isn't implemented yet
		assert.Contains(t, []int{http.StatusNotFound, http.StatusNotImplemented, http.StatusInternalServerError}, rec.Code)
	}

	// Test switching to invalid organization (should fail)
	invalidOrgID := uuid.New().String()

	req = httptest.NewRequest(http.MethodPost, fmt.Sprintf("/api/v1/auth/switch-org/%s", invalidOrgID), nil)
	req.Header.Set(echo.HeaderAuthorization, "Bearer "+suite.adminToken)
	rec = httptest.NewRecorder()

	suite.e.ServeHTTP(rec, req)

	// Should fail - either 404, 403, or 500 depending on implementation
	assert.Contains(t, []int{http.StatusNotFound, http.StatusForbidden, http.StatusInternalServerError}, rec.Code)
}

// testAuthenticationFlows tests the PKCE authentication flows with new JWT format
func testAuthenticationFlows(t *testing.T, ctx context.Context, suite *APITestSuite) {
	// Test phone OTP flow
	t.Run("PhoneOTPFlow", func(t *testing.T) {
		flow := suite.NewTestAuthFlow(t)

		// This will test the complete phone OTP flow
		// The implementation should use the new JWT format for tokens
		flow.TestPhoneOTPFlow("+1234567890")
	})

	// Test staff login flow
	t.Run("StaffLoginFlow", func(t *testing.T) {
		flow := suite.NewTestAuthFlow(t)

		// This will test the staff login flow
		// Should generate JWT with platform_role = 'staff' or 'super_admin'
		flow.TestStaffLoginFlow("<EMAIL>", "123456")
	})
}

// TestMultiTenantDataIsolation tests that users can only access data from their active organization
func TestMultiTenantDataIsolation(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration tests in short mode")
	}

	// Set up API test suite
	suite := SetupAPITest(t)
	defer suite.TeardownAPITest(t)

	// Test accessing organization-specific endpoints
	t.Run("OrganizationResourceAccess", func(t *testing.T) {
		// Test accessing organization events
		req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1/organizations/%s/events", suite.orgID), nil)
		req.Header.Set(echo.HeaderAuthorization, "Bearer "+suite.adminToken)
		req.Header.Set("X-Organization-ID", suite.orgID)
		rec := httptest.NewRecorder()

		suite.e.ServeHTTP(rec, req)

		// Should succeed for valid organization
		if rec.Code != http.StatusOK {
			t.Logf("Organization events access returned status %d: %s", rec.Code, rec.Body.String())
			// Might not be implemented yet
			assert.Contains(t, []int{http.StatusNotFound, http.StatusInternalServerError}, rec.Code)
		} else {
			assert.Equal(t, http.StatusOK, rec.Code)
		}

		// Test accessing invalid organization
		invalidOrgID := uuid.New().String()
		req = httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1/organizations/%s/events", invalidOrgID), nil)
		req.Header.Set(echo.HeaderAuthorization, "Bearer "+suite.adminToken)
		req.Header.Set("X-Organization-ID", invalidOrgID)
		rec = httptest.NewRecorder()

		suite.e.ServeHTTP(rec, req)

		// Should fail for invalid organization
		assert.Contains(t, []int{http.StatusNotFound, http.StatusForbidden, http.StatusUnauthorized}, rec.Code)
	})
}

// TestAuthorizationWithNewJWT tests that the authorization system works with new JWT format
func TestAuthorizationWithNewJWT(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration tests in short mode")
	}

	// Set up API test suite
	suite := SetupAPITest(t)
	defer suite.TeardownAPITest(t)

	// Test platform-level permissions
	t.Run("PlatformPermissions", func(t *testing.T) {
		// Admin token should have elevated permissions
		req := httptest.NewRequest(http.MethodGet, "/api/v1/users/me", nil)
		req.Header.Set(echo.HeaderAuthorization, "Bearer "+suite.adminToken)
		rec := httptest.NewRecorder()

		suite.e.ServeHTTP(rec, req)

		assert.Equal(t, http.StatusOK, rec.Code, "Admin should access user profile")

		// User token should have limited permissions
		req = httptest.NewRequest(http.MethodGet, "/api/v1/users/me", nil)
		req.Header.Set(echo.HeaderAuthorization, "Bearer "+suite.userToken)
		rec = httptest.NewRecorder()

		suite.e.ServeHTTP(rec, req)

		// Both should work for accessing their own profile
		assert.Equal(t, http.StatusOK, rec.Code, "User should access own profile")
	})

	// Test organization-level permissions
	t.Run("OrganizationPermissions", func(t *testing.T) {
		// Test accessing organization settings (should require admin role)
		req := httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1/organizations/%s", suite.orgID), nil)
		req.Header.Set(echo.HeaderAuthorization, "Bearer "+suite.adminToken)
		req.Header.Set("X-Organization-ID", suite.orgID)
		rec := httptest.NewRecorder()

		suite.e.ServeHTTP(rec, req)

		if rec.Code != http.StatusOK {
			t.Logf("Organization access returned status %d: %s", rec.Code, rec.Body.String())
			// Might not be implemented yet
			assert.Contains(t, []int{http.StatusNotFound, http.StatusInternalServerError}, rec.Code)
		}
	})
}

// TestJWTBackwardCompatibility tests that the system handles different JWT formats gracefully
func TestJWTBackwardCompatibility(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration tests in short mode")
	}

	// Set up API test suite
	suite := SetupAPITest(t)
	defer suite.TeardownAPITest(t)

	// Test with new format token (should work)
	t.Run("NewFormatToken", func(t *testing.T) {
		newFormatClaims := jwt.MapClaims{
			"user_id":       "00000000-0000-0000-0000-000000000001",
			"platform_role": authorization.PlatformRoleSuperAdmin,
			"active_org": map[string]interface{}{
				"id":   suite.orgID,
				"role": authorization.OrgRoleOwner,
			},
			"exp": time.Now().Add(time.Hour).Unix(),
			"iat": time.Now().Unix(),
		}

		newFormatToken := jwt.NewWithClaims(jwt.SigningMethodHS256, newFormatClaims)
		tokenString, err := newFormatToken.SignedString([]byte("test-secret-key-for-testing"))
		require.NoError(t, err)

		req := httptest.NewRequest(http.MethodGet, "/api/v1/users/me", nil)
		req.Header.Set(echo.HeaderAuthorization, "Bearer "+tokenString)
		rec := httptest.NewRecorder()

		suite.e.ServeHTTP(rec, req)

		assert.Equal(t, http.StatusOK, rec.Code, "New format token should work")
	})

	// Test with malformed token (should fail)
	t.Run("MalformedToken", func(t *testing.T) {
		malformedClaims := jwt.MapClaims{
			"user_id": "00000000-0000-0000-0000-000000000001",
			// Missing required fields
			"exp": time.Now().Add(time.Hour).Unix(),
		}

		malformedToken := jwt.NewWithClaims(jwt.SigningMethodHS256, malformedClaims)
		tokenString, err := malformedToken.SignedString([]byte("test-secret-key-for-testing"))
		require.NoError(t, err)

		req := httptest.NewRequest(http.MethodGet, "/api/v1/users/me", nil)
		req.Header.Set(echo.HeaderAuthorization, "Bearer "+tokenString)
		rec := httptest.NewRecorder()

		suite.e.ServeHTTP(rec, req)

		// Should fail due to missing required fields
		assert.Contains(t, []int{http.StatusUnauthorized, http.StatusBadRequest}, rec.Code)
	})
}
