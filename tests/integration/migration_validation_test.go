package integration

import (
	"database/sql"
	"fmt"
	"testing"

	"github.com/golang-migrate/migrate/v4"
	"github.com/golang-migrate/migrate/v4/database/postgres"
	_ "github.com/golang-migrate/migrate/v4/source/file"
	_ "github.com/lib/pq"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestMigrationConsolidation validates that consolidated migrations produce
// the same schema as the original 70 migrations
func TestMigrationConsolidation(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping migration validation test in short mode")
	}

	// Database connection strings
	originalDBURL := "postgres://postgres@localhost:5432/the_moment_test_original?sslmode=disable"
	consolidatedDBURL := "postgres://postgres@localhost:5432/the_moment_test_consolidated?sslmode=disable"

	// Create test databases
	t.Log("Creating test databases...")
	require.NoError(t, createTestDB("the_moment_test_original"))
	require.NoError(t, createTestDB("the_moment_test_consolidated"))

	// Cleanup databases after test
	t.Cleanup(func() {
		dropTestDB("the_moment_test_original")
		dropTestDB("the_moment_test_consolidated")
	})

	// Apply original migrations
	t.Log("Applying original migrations...")
	err := applyMigrations(originalDBURL, "file://../../db/migrations_archive")
	require.NoError(t, err, "Failed to apply original migrations")

	// Apply consolidated migrations
	t.Log("Applying consolidated migrations...")
	err = applyMigrations(consolidatedDBURL, "file://../../db/migrations")
	require.NoError(t, err, "Failed to apply consolidated migrations")

	// Compare schemas
	t.Log("Comparing schemas...")
	originalSchema := getSchemaInfo(t, originalDBURL)
	consolidatedSchema := getSchemaInfo(t, consolidatedDBURL)

	// Validate tables
	t.Run("ValidateTables", func(t *testing.T) {
		assert.Equal(t, len(originalSchema.Tables), len(consolidatedSchema.Tables),
			"Number of tables should match")

		for tableName, originalTable := range originalSchema.Tables {
			consolidatedTable, exists := consolidatedSchema.Tables[tableName]
			assert.True(t, exists, "Table %s should exist in consolidated schema", tableName)

			if exists {
				// Compare columns
				assert.Equal(t, len(originalTable.Columns), len(consolidatedTable.Columns),
					"Number of columns in table %s should match", tableName)

				for colName, originalCol := range originalTable.Columns {
					consolidatedCol, exists := consolidatedTable.Columns[colName]
					assert.True(t, exists, "Column %s.%s should exist", tableName, colName)

					if exists {
						assert.Equal(t, originalCol.DataType, consolidatedCol.DataType,
							"Data type for %s.%s should match", tableName, colName)
						assert.Equal(t, originalCol.IsNullable, consolidatedCol.IsNullable,
							"Nullable property for %s.%s should match", tableName, colName)
					}
				}
			}
		}
	})

	// Validate indexes
	t.Run("ValidateIndexes", func(t *testing.T) {
		assert.Equal(t, len(originalSchema.Indexes), len(consolidatedSchema.Indexes),
			"Number of indexes should match")

		for indexName := range originalSchema.Indexes {
			_, exists := consolidatedSchema.Indexes[indexName]
			assert.True(t, exists, "Index %s should exist in consolidated schema", indexName)
		}
	})

	// Validate constraints
	t.Run("ValidateConstraints", func(t *testing.T) {
		assert.Equal(t, len(originalSchema.Constraints), len(consolidatedSchema.Constraints),
			"Number of constraints should match")

		for constraintName := range originalSchema.Constraints {
			_, exists := consolidatedSchema.Constraints[constraintName]
			assert.True(t, exists, "Constraint %s should exist in consolidated schema", constraintName)
		}
	})

	// Validate enums
	t.Run("ValidateEnums", func(t *testing.T) {
		assert.Equal(t, len(originalSchema.Enums), len(consolidatedSchema.Enums),
			"Number of enums should match")

		for enumName, originalValues := range originalSchema.Enums {
			consolidatedValues, exists := consolidatedSchema.Enums[enumName]
			assert.True(t, exists, "Enum %s should exist in consolidated schema", enumName)

			if exists {
				assert.ElementsMatch(t, originalValues, consolidatedValues,
					"Enum values for %s should match", enumName)
			}
		}
	})

	// Validate seed data
	t.Run("ValidateSeedData", func(t *testing.T) {
		// Check for default admin user
		validateSeedDataExists(t, consolidatedDBURL,
			"SELECT COUNT(*) FROM users WHERE platform_role = 'super_admin'",
			"Default admin user should exist")

		// Check for default organization
		validateSeedDataExists(t, consolidatedDBURL,
			"SELECT COUNT(*) FROM organizations WHERE is_default_org = true",
			"Default organization should exist")

		// Check for tags
		validateSeedDataCount(t, originalDBURL, consolidatedDBURL,
			"SELECT COUNT(*) FROM tags",
			"Tag count should match")

		// Check for tag translations
		validateSeedDataCount(t, originalDBURL, consolidatedDBURL,
			"SELECT COUNT(*) FROM tag_translations",
			"Tag translation count should match")
	})

	// Validate functions and triggers
	t.Run("ValidateFunctionsAndTriggers", func(t *testing.T) {
		assert.Equal(t, len(originalSchema.Functions), len(consolidatedSchema.Functions),
			"Number of functions should match")

		assert.Equal(t, len(originalSchema.Triggers), len(consolidatedSchema.Triggers),
			"Number of triggers should match")
	})

	// Validate RLS policies
	t.Run("ValidateRLSPolicies", func(t *testing.T) {
		assert.Equal(t, len(originalSchema.RLSPolicies), len(consolidatedSchema.RLSPolicies),
			"Number of RLS policies should match")

		for policyName := range originalSchema.RLSPolicies {
			_, exists := consolidatedSchema.RLSPolicies[policyName]
			assert.True(t, exists, "RLS policy %s should exist in consolidated schema", policyName)
		}
	})
}

// Helper types for schema comparison
type SchemaInfo struct {
	Tables      map[string]TableInfo
	Indexes     map[string]IndexInfo
	Constraints map[string]ConstraintInfo
	Enums       map[string][]string
	Functions   map[string]FunctionInfo
	Triggers    map[string]TriggerInfo
	RLSPolicies map[string]PolicyInfo
}

type TableInfo struct {
	Name    string
	Columns map[string]ColumnInfo
}

type ColumnInfo struct {
	Name       string
	DataType   string
	IsNullable bool
	Default    sql.NullString
}

type IndexInfo struct {
	Name       string
	TableName  string
	Definition string
}

type ConstraintInfo struct {
	Name          string
	Type          string
	TableName     string
	ColumnName    string
	ForeignTable  sql.NullString
	ForeignColumn sql.NullString
}

type FunctionInfo struct {
	Name      string
	Arguments string
}

type TriggerInfo struct {
	Name      string
	TableName string
	Event     string
}

type PolicyInfo struct {
	Name      string
	TableName string
	Command   string
}

// Helper functions

func createTestDB(dbName string) error {
	db, err := sql.Open("postgres", "postgres://postgres@localhost:5432/postgres?sslmode=disable")
	if err != nil {
		return err
	}
	defer db.Close()

	// Drop if exists
	_, _ = db.Exec(fmt.Sprintf("DROP DATABASE IF EXISTS %s", dbName))

	// Create new database
	_, err = db.Exec(fmt.Sprintf("CREATE DATABASE %s", dbName))
	return err
}

func dropTestDB(dbName string) {
	db, err := sql.Open("postgres", "postgres://postgres@localhost:5432/postgres?sslmode=disable")
	if err != nil {
		return
	}
	defer db.Close()

	_, _ = db.Exec(fmt.Sprintf("DROP DATABASE IF EXISTS %s", dbName))
}

func applyMigrations(dbURL string, migrationsPath string) error {
	db, err := sql.Open("postgres", dbURL)
	if err != nil {
		return err
	}
	defer db.Close()

	driver, err := postgres.WithInstance(db, &postgres.Config{})
	if err != nil {
		return err
	}

	m, err := migrate.NewWithDatabaseInstance(migrationsPath, "postgres", driver)
	if err != nil {
		return err
	}

	return m.Up()
}

func getSchemaInfo(t *testing.T, dbURL string) SchemaInfo {
	db, err := sql.Open("postgres", dbURL)
	require.NoError(t, err)
	defer db.Close()

	schema := SchemaInfo{
		Tables:      make(map[string]TableInfo),
		Indexes:     make(map[string]IndexInfo),
		Constraints: make(map[string]ConstraintInfo),
		Enums:       make(map[string][]string),
		Functions:   make(map[string]FunctionInfo),
		Triggers:    make(map[string]TriggerInfo),
		RLSPolicies: make(map[string]PolicyInfo),
	}

	// Get tables and columns
	rows, err := db.Query(`
		SELECT table_name, column_name, data_type, is_nullable, column_default
		FROM information_schema.columns
		WHERE table_schema = 'public'
		ORDER BY table_name, ordinal_position
	`)
	require.NoError(t, err)
	defer rows.Close()

	for rows.Next() {
		var tableName, columnName, dataType, isNullable string
		var columnDefault sql.NullString

		err := rows.Scan(&tableName, &columnName, &dataType, &isNullable, &columnDefault)
		require.NoError(t, err)

		if _, exists := schema.Tables[tableName]; !exists {
			schema.Tables[tableName] = TableInfo{
				Name:    tableName,
				Columns: make(map[string]ColumnInfo),
			}
		}

		schema.Tables[tableName].Columns[columnName] = ColumnInfo{
			Name:       columnName,
			DataType:   dataType,
			IsNullable: isNullable == "YES",
			Default:    columnDefault,
		}
	}

	// Get indexes
	rows, err = db.Query(`
		SELECT indexname, tablename, indexdef
		FROM pg_indexes
		WHERE schemaname = 'public'
	`)
	require.NoError(t, err)
	defer rows.Close()

	for rows.Next() {
		var index IndexInfo
		err := rows.Scan(&index.Name, &index.TableName, &index.Definition)
		require.NoError(t, err)
		schema.Indexes[index.Name] = index
	}

	// Get constraints
	rows, err = db.Query(`
		SELECT 
			tc.constraint_name,
			tc.constraint_type,
			tc.table_name,
			kcu.column_name,
			ccu.table_name AS foreign_table_name,
			ccu.column_name AS foreign_column_name
		FROM information_schema.table_constraints tc
		JOIN information_schema.key_column_usage kcu 
			ON tc.constraint_name = kcu.constraint_name
			AND tc.table_schema = kcu.table_schema
		LEFT JOIN information_schema.constraint_column_usage ccu
			ON ccu.constraint_name = tc.constraint_name
			AND ccu.table_schema = tc.table_schema
		WHERE tc.table_schema = 'public'
	`)
	require.NoError(t, err)
	defer rows.Close()

	for rows.Next() {
		var constraint ConstraintInfo
		err := rows.Scan(
			&constraint.Name,
			&constraint.Type,
			&constraint.TableName,
			&constraint.ColumnName,
			&constraint.ForeignTable,
			&constraint.ForeignColumn,
		)
		require.NoError(t, err)
		schema.Constraints[constraint.Name] = constraint
	}

	// Get enums
	rows, err = db.Query(`
		SELECT 
			t.typname AS enum_name,
			array_agg(e.enumlabel ORDER BY e.enumsortorder) AS enum_values
		FROM pg_type t
		JOIN pg_enum e ON t.oid = e.enumtypid
		WHERE t.typnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
		GROUP BY t.typname
	`)
	require.NoError(t, err)
	defer rows.Close()

	for rows.Next() {
		var enumName string
		var values []string
		err := rows.Scan(&enumName, &values)
		require.NoError(t, err)
		schema.Enums[enumName] = values
	}

	// Get functions
	rows, err = db.Query(`
		SELECT 
			proname AS function_name,
			pg_get_function_identity_arguments(oid) AS arguments
		FROM pg_proc
		WHERE pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
	`)
	require.NoError(t, err)
	defer rows.Close()

	for rows.Next() {
		var function FunctionInfo
		err := rows.Scan(&function.Name, &function.Arguments)
		require.NoError(t, err)
		schema.Functions[function.Name] = function
	}

	// Get triggers
	rows, err = db.Query(`
		SELECT 
			trigger_name,
			event_object_table,
			event_manipulation
		FROM information_schema.triggers
		WHERE trigger_schema = 'public'
	`)
	require.NoError(t, err)
	defer rows.Close()

	for rows.Next() {
		var trigger TriggerInfo
		err := rows.Scan(&trigger.Name, &trigger.TableName, &trigger.Event)
		require.NoError(t, err)
		schema.Triggers[trigger.Name] = trigger
	}

	// Get RLS policies
	rows, err = db.Query(`
		SELECT 
			policyname,
			tablename,
			cmd
		FROM pg_policies
		WHERE schemaname = 'public'
	`)
	if err == nil {
		defer rows.Close()

		for rows.Next() {
			var policy PolicyInfo
			err := rows.Scan(&policy.Name, &policy.TableName, &policy.Command)
			require.NoError(t, err)
			schema.RLSPolicies[policy.Name] = policy
		}
	}

	return schema
}

func validateSeedDataExists(t *testing.T, dbURL string, query string, message string) {
	db, err := sql.Open("postgres", dbURL)
	require.NoError(t, err)
	defer db.Close()

	var count int
	err = db.QueryRow(query).Scan(&count)
	require.NoError(t, err)
	assert.Greater(t, count, 0, message)
}

func validateSeedDataCount(t *testing.T, originalDB string, consolidatedDB string, query string, message string) {
	db1, err := sql.Open("postgres", originalDB)
	require.NoError(t, err)
	defer db1.Close()

	db2, err := sql.Open("postgres", consolidatedDB)
	require.NoError(t, err)
	defer db2.Close()

	var count1, count2 int
	err = db1.QueryRow(query).Scan(&count1)
	require.NoError(t, err)

	err = db2.QueryRow(query).Scan(&count2)
	require.NoError(t, err)

	assert.Equal(t, count1, count2, message)
}
