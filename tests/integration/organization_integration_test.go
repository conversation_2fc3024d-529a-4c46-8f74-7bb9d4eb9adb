package integration

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// OrganizationIntegrationTestSuite tests organization management endpoints with real API calls
type OrganizationIntegrationTestSuite struct {
	suite.Suite
	apiSuite *APITestSuite
}

func (suite *OrganizationIntegrationTestSuite) SetupSuite() {
	suite.apiSuite = SetupAPITest(suite.T())
}

func (suite *OrganizationIntegrationTestSuite) TearDownSuite() {
	suite.apiSuite.TeardownAPITest(suite.T())
}

func (suite *OrganizationIntegrationTestSuite) SetupTest() {
	suite.apiSuite.CleanupTestData(suite.T())
}

// TestOrganizationProfile tests organization profile management
func (suite *OrganizationIntegrationTestSuite) TestOrganizationProfile() {
	// Get organization profile
	rec, err := suite.apiSuite.MakeAuthenticatedRequest("GET", "/api/v1/organizations/profile", nil)
	require.NoError(suite.T(), err)

	var response OrganizationResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	require.NotEmpty(suite.T(), response.ID)
	require.NotEmpty(suite.T(), response.Name)
	require.NotEmpty(suite.T(), response.Status)
}

// TestOrganizationProfileUpdate tests organization profile update functionality
func (suite *OrganizationIntegrationTestSuite) TestOrganizationProfileUpdate() {
	updateReq := map[string]interface{}{
		"name":        "Updated Organization Name",
		"description": "Updated organization description",
		"address":     "123 Updated Street, City",
		"phone":       "+1555000111",
		"email":       "<EMAIL>",
		"website":     "https://updated-org.com",
		"theme_color": "#FF5722",
	}

	rec, err := suite.apiSuite.MakeAuthenticatedRequest("PUT", "/api/v1/organizations/profile", updateReq)
	require.NoError(suite.T(), err)

	var response OrganizationResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	require.Equal(suite.T(), "Updated Organization Name", response.Name)
	require.Equal(suite.T(), "Updated organization description", response.Description)
	require.Equal(suite.T(), "123 Updated Street, City", response.Address)
	require.Equal(suite.T(), "+1555000111", response.Phone)
	require.Equal(suite.T(), "<EMAIL>", response.Email)
	require.Equal(suite.T(), "https://updated-org.com", response.Website)
	require.Equal(suite.T(), "#FF5722", response.ThemeColor)
}

// TestOrganizationProfileUpdate_ValidationErrors tests profile update with invalid data
func (suite *OrganizationIntegrationTestSuite) TestOrganizationProfileUpdate_ValidationErrors() {
	// Test invalid email format
	updateReq := map[string]interface{}{
		"email": "invalid-email-format",
	}

	rec, err := suite.apiSuite.MakeAuthenticatedRequest("PUT", "/api/v1/organizations/profile", updateReq)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertErrorResponse(suite.T(), rec, http.StatusBadRequest, "invalid email format")

	// Test invalid website URL
	updateReq = map[string]interface{}{
		"website": "not-a-valid-url",
	}

	rec, err = suite.apiSuite.MakeAuthenticatedRequest("PUT", "/api/v1/organizations/profile", updateReq)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertErrorResponse(suite.T(), rec, http.StatusBadRequest, "invalid website URL")

	// Test invalid theme color
	updateReq = map[string]interface{}{
		"theme_color": "invalid-color",
	}

	rec, err = suite.apiSuite.MakeAuthenticatedRequest("PUT", "/api/v1/organizations/profile", updateReq)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertErrorResponse(suite.T(), rec, http.StatusBadRequest, "invalid theme color")
}

// TestOrganizationMembers tests organization member management
func (suite *OrganizationIntegrationTestSuite) TestOrganizationMembers() {
	// List organization members
	rec, err := suite.apiSuite.MakeAuthenticatedRequest("GET", "/api/v1/organizations/members", nil)
	require.NoError(suite.T(), err)

	var response PaginatedOrganizationMembersResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	require.NotNil(suite.T(), response.Members)
	require.NotNil(suite.T(), response.Pagination)
	require.GreaterOrEqual(suite.T(), len(response.Members), 1) // At least the current user
}

// TestOrganizationMembers_WithFilters tests member listing with filters
func (suite *OrganizationIntegrationTestSuite) TestOrganizationMembers_WithFilters() {
	// Test with role filter
	rec, err := suite.apiSuite.MakeAuthenticatedRequest("GET", "/api/v1/organizations/members?role=admin", nil)
	require.NoError(suite.T(), err)

	var response PaginatedOrganizationMembersResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	// All members should have 'admin' role
	for _, member := range response.Members {
		require.Equal(suite.T(), "admin", member.Role)
	}

	// Test with status filter
	rec, err = suite.apiSuite.MakeAuthenticatedRequest("GET", "/api/v1/organizations/members?status=active", nil)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	// All members should be active
	for _, member := range response.Members {
		require.Equal(suite.T(), "active", member.Status)
	}
}

// TestInviteMember tests member invitation workflow
func (suite *OrganizationIntegrationTestSuite) TestInviteMember() {
	inviteReq := map[string]interface{}{
		"email":   "<EMAIL>",
		"role":    "member",
		"message": "Welcome to our organization!",
	}

	rec, err := suite.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/organizations/members/invite", inviteReq)
	require.NoError(suite.T(), err)

	var response MemberInvitationResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusCreated, &response)

	require.Equal(suite.T(), "<EMAIL>", response.Email)
	require.Equal(suite.T(), "member", response.Role)
	require.Equal(suite.T(), "pending", response.Status)
	require.NotEmpty(suite.T(), response.InvitationToken)
}

// TestInviteMember_DuplicateEmail tests inviting existing member
func (suite *OrganizationIntegrationTestSuite) TestInviteMember_DuplicateEmail() {
	// First invitation
	inviteReq := map[string]interface{}{
		"email": "<EMAIL>",
		"role":  "member",
	}

	rec, err := suite.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/organizations/members/invite", inviteReq)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusCreated, nil)

	// Duplicate invitation
	rec, err = suite.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/organizations/members/invite", inviteReq)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertErrorResponse(suite.T(), rec, http.StatusConflict, "member already invited")
}

// TestInviteMember_InvalidRole tests invitation with invalid role
func (suite *OrganizationIntegrationTestSuite) TestInviteMember_InvalidRole() {
	inviteReq := map[string]interface{}{
		"email": "<EMAIL>",
		"role":  "invalid_role",
	}

	rec, err := suite.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/organizations/members/invite", inviteReq)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertErrorResponse(suite.T(), rec, http.StatusBadRequest, "invalid role")
}

// TestUpdateMemberRole tests updating member role
func (suite *OrganizationIntegrationTestSuite) TestUpdateMemberRole() {
	// First, we need a member to update (this would require setup data)
	memberID := "member-id-from-setup" // In real test, this would come from test data

	updateReq := map[string]interface{}{
		"role": "manager",
	}

	rec, err := suite.apiSuite.MakeAuthenticatedRequest("PUT", fmt.Sprintf("/api/v1/organizations/members/%s/role", memberID), updateReq)
	require.NoError(suite.T(), err)

	var response OrganizationMemberResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	require.Equal(suite.T(), "manager", response.Role)
}

// TestRemoveMember tests removing member from organization
func (suite *OrganizationIntegrationTestSuite) TestRemoveMember() {
	memberID := "member-id-from-setup" // In real test, this would come from test data

	rec, err := suite.apiSuite.MakeAuthenticatedRequest("DELETE", fmt.Sprintf("/api/v1/organizations/members/%s", memberID), nil)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusNoContent, nil)

	// Verify member is removed
	rec, err = suite.apiSuite.MakeAuthenticatedRequest("GET", fmt.Sprintf("/api/v1/organizations/members/%s", memberID), nil)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertErrorResponse(suite.T(), rec, http.StatusNotFound, "member not found")
}

// TestRemoveMember_CannotRemoveSelf tests that user cannot remove themselves
func (suite *OrganizationIntegrationTestSuite) TestRemoveMember_CannotRemoveSelf() {
	// Try to remove self (current user)
	currentUserID := "current-user-id" // This would be derived from the authenticated user

	rec, err := suite.apiSuite.MakeAuthenticatedRequest("DELETE", fmt.Sprintf("/api/v1/organizations/members/%s", currentUserID), nil)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertErrorResponse(suite.T(), rec, http.StatusBadRequest, "cannot remove yourself")
}

// TestOrganizationResources tests organization resource management
func (suite *OrganizationIntegrationTestSuite) TestOrganizationResources() {
	// List organization resources
	rec, err := suite.apiSuite.MakeAuthenticatedRequest("GET", "/api/v1/organizations/resources", nil)
	require.NoError(suite.T(), err)

	var response PaginatedResourceResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	require.NotNil(suite.T(), response.Resources)
	require.NotNil(suite.T(), response.Pagination)
}

// TestCreateOrganizationResource tests creating organization resource
func (suite *OrganizationIntegrationTestSuite) TestCreateOrganizationResource() {
	createReq := map[string]interface{}{
		"title":        "Organization Handbook",
		"description":  "Complete guide to organization policies",
		"type":         "document",
		"category":     "policies",
		"access_level": "members",
	}

	rec, err := suite.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/organizations/resources", createReq)
	require.NoError(suite.T(), err)

	var response ResourceResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusCreated, &response)

	require.Equal(suite.T(), "Organization Handbook", response.Title)
	require.Equal(suite.T(), "Complete guide to organization policies", response.Description)
	require.Equal(suite.T(), "document", response.Type)
	require.Equal(suite.T(), "policies", response.Category)
	require.Equal(suite.T(), "members", response.AccessLevel)
}

// TestOrganizationStatistics tests organization statistics endpoints
func (suite *OrganizationIntegrationTestSuite) TestOrganizationStatistics() {
	// Get organization statistics
	rec, err := suite.apiSuite.MakeAuthenticatedRequest("GET", "/api/v1/organizations/statistics", nil)
	require.NoError(suite.T(), err)

	var response OrganizationStatisticsResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	require.NotNil(suite.T(), response.MemberStats)
	require.NotNil(suite.T(), response.EventStats)
	require.NotNil(suite.T(), response.VolunteerStats)
	require.GreaterOrEqual(suite.T(), response.MemberStats.Total, int32(0))
	require.GreaterOrEqual(suite.T(), response.EventStats.Total, int32(0))
}

// TestOrganizationStatistics_WithDateRange tests statistics with date filters
func (suite *OrganizationIntegrationTestSuite) TestOrganizationStatistics_WithDateRange() {
	// Get statistics for last 30 days
	rec, err := suite.apiSuite.MakeAuthenticatedRequest("GET", "/api/v1/organizations/statistics?period=30d", nil)
	require.NoError(suite.T(), err)

	var response OrganizationStatisticsResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	require.NotNil(suite.T(), response.MemberStats)
	require.NotNil(suite.T(), response.EventStats)
}

// TestOrganizationSettings tests organization settings management
func (suite *OrganizationIntegrationTestSuite) TestOrganizationSettings() {
	// Get organization settings
	rec, err := suite.apiSuite.MakeAuthenticatedRequest("GET", "/api/v1/organizations/settings", nil)
	require.NoError(suite.T(), err)

	var response OrganizationSettingsResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	require.NotNil(suite.T(), response.Features)
	require.NotNil(suite.T(), response.Preferences)
}

// TestUpdateOrganizationSettings tests updating organization settings
func (suite *OrganizationIntegrationTestSuite) TestUpdateOrganizationSettings() {
	updateReq := map[string]interface{}{
		"features": map[string]interface{}{
			"event_registration":   true,
			"volunteer_management": true,
			"resource_sharing":     true,
			"member_verification":  false,
		},
		"preferences": map[string]interface{}{
			"default_language":     "en",
			"timezone":             "Asia/Hong_Kong",
			"public_profile":       true,
			"auto_approve_members": false,
		},
	}

	rec, err := suite.apiSuite.MakeAuthenticatedRequest("PUT", "/api/v1/organizations/settings", updateReq)
	require.NoError(suite.T(), err)

	var response OrganizationSettingsResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	require.True(suite.T(), response.Features.EventRegistration)
	require.True(suite.T(), response.Features.VolunteerManagement)
	require.False(suite.T(), response.Features.MemberVerification)
	require.Equal(suite.T(), "en", response.Preferences.DefaultLanguage)
	require.Equal(suite.T(), "Asia/Hong_Kong", response.Preferences.Timezone)
}

// TestOrganizationLogo tests organization logo upload and management
func (suite *OrganizationIntegrationTestSuite) TestOrganizationLogo() {
	// This is a simplified test - in real implementation, you'd handle multipart form data
	req := map[string]interface{}{
		"action": "upload_logo",
	}

	rec, err := suite.apiSuite.MakeAuthenticatedRequest("POST", "/api/v1/organizations/logo", req)
	require.NoError(suite.T(), err)

	// The response depends on your implementation
	require.True(suite.T(), rec.Code == http.StatusOK || rec.Code == http.StatusCreated)
}

// TestOrganizationAuditLog tests organization audit log access
func (suite *OrganizationIntegrationTestSuite) TestOrganizationAuditLog() {
	// Get organization audit log
	rec, err := suite.apiSuite.MakeAuthenticatedRequest("GET", "/api/v1/organizations/audit-log", nil)
	require.NoError(suite.T(), err)

	var response PaginatedAuditLogResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	require.NotNil(suite.T(), response.Entries)
	require.NotNil(suite.T(), response.Pagination)
}

// TestOrganizationAuditLog_WithFilters tests audit log with filters
func (suite *OrganizationIntegrationTestSuite) TestOrganizationAuditLog_WithFilters() {
	// Test with action filter
	rec, err := suite.apiSuite.MakeAuthenticatedRequest("GET", "/api/v1/organizations/audit-log?action=member_invited", nil)
	require.NoError(suite.T(), err)

	var response PaginatedAuditLogResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	// All entries should have 'member_invited' action
	for _, entry := range response.Entries {
		require.Equal(suite.T(), "member_invited", entry.Action)
	}

	// Test with user filter
	rec, err = suite.apiSuite.MakeAuthenticatedRequest("GET", "/api/v1/organizations/audit-log?user_id=specific-user-id", nil)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)
}

// TestUnauthorizedOrganizationAccess tests unauthorized access to organization endpoints
func (suite *OrganizationIntegrationTestSuite) TestUnauthorizedOrganizationAccess() {
	// Test accessing organization profile without authentication
	rec, err := suite.apiSuite.MakeUnauthenticatedRequest("GET", "/api/v1/organizations/profile", nil)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertErrorResponse(suite.T(), rec, http.StatusUnauthorized, "unauthorized")

	// Test updating organization profile without authentication
	updateReq := map[string]interface{}{
		"name": "Unauthorized Update",
	}

	rec, err = suite.apiSuite.MakeUnauthenticatedRequest("PUT", "/api/v1/organizations/profile", updateReq)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertErrorResponse(suite.T(), rec, http.StatusUnauthorized, "unauthorized")
}

// TestInsufficientPermissions tests access with insufficient permissions
func (suite *OrganizationIntegrationTestSuite) TestInsufficientPermissions() {
	// Test member trying to invite other members (assuming only admins can invite)
	inviteReq := map[string]interface{}{
		"email": "<EMAIL>",
		"role":  "member",
	}

	rec, err := suite.apiSuite.MakeUserRequest("POST", "/api/v1/organizations/members/invite", inviteReq)
	require.NoError(suite.T(), err)

	// Should either succeed (if members can invite) or fail with forbidden
	require.True(suite.T(), rec.Code == http.StatusCreated || rec.Code == http.StatusForbidden)
}

func TestOrganizationIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(OrganizationIntegrationTestSuite))
}
