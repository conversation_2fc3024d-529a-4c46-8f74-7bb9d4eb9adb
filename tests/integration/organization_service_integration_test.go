package integration

import (
	"context"
	"testing"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/services/organization"
	"Membership-SAAS-System-Backend/internal/testutil"
	"Membership-SAAS-System-Backend/internal/utils/validation"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// OrganizationServiceIntegrationTestSuite provides comprehensive integration testing for organization services
type OrganizationServiceIntegrationTestSuite struct {
	suite.Suite
	testDB    *testutil.TestDatabase
	store     db.Store
	queries   *db.Queries // Keep for backward compatibility
	validator *validation.RequestValidator
	ctx       context.Context

	// Organization domain services
	managementService organization.ManagementService
	membershipService organization.MembershipService
	mediaService      organization.MediaService

	// Test entities
	testOrg          db.Organization
	testOtherOrg     db.Organization
	testOwner        db.User
	testAdmin        db.User
	testManager      db.User
	testMember       db.User
	testExternalUser db.User
}

// SetupSuite runs once before all tests in the suite
func (suite *OrganizationServiceIntegrationTestSuite) SetupSuite() {
	suite.ctx = context.Background()
	suite.testDB, suite.store = testutil.SetupTestDatabaseWithStore(suite.T())
	suite.queries = db.New(suite.testDB.DB) // Keep queries for backward compatibility
	suite.validator = validation.NewRequestValidator()

	// Initialize organization domain services
	suite.managementService = organization.NewManagementService(suite.store)
	suite.membershipService = organization.NewMembershipService(suite.store, suite.managementService)
	suite.mediaService = organization.NewMediaService(suite.store, "test-uploads")
}

// SetupTest runs before each test
func (suite *OrganizationServiceIntegrationTestSuite) SetupTest() {
	// Clean database state before each test
	suite.testDB.TruncateAllTables(suite.T())

	// Create test data helper
	helper := NewSuiteTestDataHelper(suite.T(), suite.queries, suite.ctx)

	// Create base test data
	testData := helper.SetupSuiteData()

	// Create additional users for different roles
	suite.testOwner = testData.AdminUser // Reuse as owner
	suite.testAdmin = helper.CreateAdditionalUser("Test Admin", 2222)
	suite.testManager = helper.CreateAdditionalUser("Test Manager", 3333)
	suite.testMember = testData.RegularUser // Reuse as member
	suite.testExternalUser = helper.CreateAdditionalUser("External User", 5555)

	// Use the organizations from test data
	suite.testOrg = testData.Organization
	suite.testOtherOrg = testData.SecondOrganization

	// Add users to organization with different roles
	// Owner is already added in SetupSuiteData
	err := helper.AddUserToOrganization(suite.testAdmin.ID, suite.testOrg.ID, "admin")
	require.NoError(suite.T(), err)

	err = helper.AddUserToOrganization(suite.testManager.ID, suite.testOrg.ID, "manager")
	require.NoError(suite.T(), err)
	// Member is already added in SetupSuiteData
}

// TestCompleteOrganizationManagement tests the complete organization management workflow
func (suite *OrganizationServiceIntegrationTestSuite) TestCompleteOrganizationManagement() {
	// Step 1: Create new organization
	createReq := payloads.CreateOrganizationRequest{
		Name:        "New Integration Test Org",
		Description: "Created during integration test",
		ThemeColor:  "#00FF00",
	}

	newOrg, err := suite.managementService.CreateOrganization(suite.ctx, suite.testOwner.ID, createReq)
	suite.NoError(err, "Should create organization successfully")
	suite.Equal(createReq.Name, newOrg.Name, "Organization name should match")
	if newOrg.Description != nil {
		suite.Equal(createReq.Description, *newOrg.Description, "Organization description should match")
	}
	if newOrg.ThemeColor != nil {
		suite.Equal(createReq.ThemeColor, *newOrg.ThemeColor, "Theme color should match")
	}
	suite.Equal("active", newOrg.Status, "Organization should be active")

	// Step 2: Get organization details
	orgDetails, err := suite.managementService.GetOrganizationByID(suite.ctx, newOrg.ID)
	suite.NoError(err, "Should get organization details successfully")
	suite.Equal(newOrg.ID, orgDetails.ID, "Organization ID should match")
	suite.Equal(newOrg.Name, orgDetails.Name, "Organization name should match")

	// Step 3: Update organization
	updateReq := payloads.UpdateOrganizationRequest{
		Name:        testutil.ToPtr[string]("Updated Integration Test Org"),
		Description: testutil.ToPtr[string]("Updated description"),
		ThemeColor:  testutil.ToPtr[string]("#0000FF"),
		Status:      testutil.ToPtr[string]("active"),
	}

	updatedOrg, err := suite.managementService.UpdateOrganization(suite.ctx, newOrg.ID, updateReq)
	suite.NoError(err, "Should update organization successfully")
	suite.Equal(*updateReq.Name, updatedOrg.Name, "Organization name should be updated")
	suite.Equal(*updateReq.Description, *updatedOrg.Description, "Description should be updated")
	suite.Equal(*updateReq.ThemeColor, *updatedOrg.ThemeColor, "Theme color should be updated")

	// Step 4: List all organizations to verify our org exists
	allOrgs, totalCount, err := suite.managementService.ListOrganizations(suite.ctx, 10, 0)
	suite.NoError(err, "Should list organizations successfully")
	suite.GreaterOrEqual(int(totalCount), 1, "Should have at least 1 organization")

	// Find the newly created organization in the list
	var foundNewOrg bool
	for _, org := range allOrgs {
		if org.ID == newOrg.ID {
			foundNewOrg = true
			suite.Equal(*updateReq.Name, org.Name, "Listed organization should have updated name")
			break
		}
	}
	suite.True(foundNewOrg, "New organization should be in organization list")

	// Step 5: Verify final organization state
	finalOrg, err := suite.managementService.GetOrganizationByID(suite.ctx, newOrg.ID)
	suite.NoError(err, "Should get organization details")
	suite.Equal(*updateReq.Name, finalOrg.Name, "Final organization name should match update")
}

// TestOrganizationMembershipManagement tests the basic membership functionality
// Note: Some methods are not implemented yet, so this is a simplified test
func (suite *OrganizationServiceIntegrationTestSuite) TestOrganizationMembershipManagement() {
	// Test basic membership operations
	input := payloads.AddUserToOrganizationInput{
		UserID:               suite.testExternalUser.ID,
		OrganizationID:       suite.testOrg.ID,
		Role:                 "member",
		NotificationsEnabled: true,
	}

	// Add user to organization
	membership, err := suite.membershipService.AddUserToOrganization(suite.ctx, input)
	suite.NoError(err, "Should add user to organization successfully")
	suite.Equal(suite.testExternalUser.ID, membership.UserID, "User ID should match")
	suite.Equal(suite.testOrg.ID, membership.OrganizationID, "Organization ID should match")
	suite.Equal("member", membership.Role, "Role should match")

	// Verify user is now a member
	isMember, err := suite.membershipService.IsUserMemberOfOrganization(suite.ctx, suite.testExternalUser.ID, suite.testOrg.ID)
	suite.NoError(err, "Should check membership successfully")
	suite.True(isMember, "User should be a member of the organization")
}

func TestOrganizationServiceIntegrationTestSuite(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration tests in short mode")
	}

	suite.Run(t, new(OrganizationServiceIntegrationTestSuite))
}
