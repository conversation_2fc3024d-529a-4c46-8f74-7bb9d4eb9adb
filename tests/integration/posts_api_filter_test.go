package integration_test

import (
	"context"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/services/post"
	"Membership-SAAS-System-Backend/internal/testutil"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/stretchr/testify/suite"
)

// PostsAPIFilterTestSuite tests that the posts API returns posts from all user roles
type PostsAPIFilterTestSuite struct {
	suite.Suite
	ctx          context.Context
	testDB       *testutil.TestDatabase
	pool         *pgxpool.Pool
	store        db.Store
	queryService post.QueryService

	// Test data
	org         db.Organization
	superUser   db.User
	staffUser   db.User
	ownerUser   db.User
	adminUser   db.User
	memberUser  db.User
	managerUser db.User

	// Posts created by different users
	superPost   db.Post
	staffPost   db.Post
	ownerPost   db.Post
	adminPost   db.Post
	memberPost  db.Post
	managerPost db.Post
}

func (s *PostsAPIFilterTestSuite) SetupSuite() {
	s.ctx = context.Background()

	// Setup test database
	s.testDB = testutil.SetupTestDatabase(s.T())
	s.pool = s.testDB.DB
	s.store = db.NewStore(s.pool)

	// Create query service using store
	s.queryService = post.NewQueryService(s.store)
}

func (s *PostsAPIFilterTestSuite) TearDownSuite() {
	if s.testDB != nil {
		s.testDB.Cleanup()
	}
}

func (s *PostsAPIFilterTestSuite) SetupTest() {
	// Clean up any existing data
	s.cleanupTestData()

	// Create test organization
	s.createTestOrganization()

	// Create users with different roles
	s.createTestUsers()

	// Create posts from each user
	s.createTestPosts()
}

func (s *PostsAPIFilterTestSuite) TearDownTest() {
	s.cleanupTestData()
}

func (s *PostsAPIFilterTestSuite) createTestOrganization() {
	// Create a user to be the owner first
	params := db.CreateUserWithPhoneParams{
		DisplayName:           "Org Owner",
		Phone:                 testutil.ToPtr("+999000001"),
		PhoneVerifiedAt:       testutil.ToPtr(time.Now()),
		InterfaceLanguage:     "en",
		CommunicationLanguage: "en",
	}
	owner, err := s.store.CreateUserWithPhone(s.ctx, params)
	s.Require().NoError(err)

	// Create organization
	orgParams := db.CreateOrganizationParams{
		Name:        "Test Posts Organization",
		Description: testutil.ToPtr("Organization for testing posts API"),
		OwnerUserID: owner.ID,
		Status:      "active",
	}
	org, err := s.store.CreateOrganization(s.ctx, orgParams)
	s.Require().NoError(err)
	s.org = org

	// Keep the owner user
	s.ownerUser = owner
}

func (s *PostsAPIFilterTestSuite) createTestUsers() {
	// Create superadmin user
	superParams := db.CreateUserWithPhoneParams{
		DisplayName:           "Super Admin",
		Phone:                 testutil.ToPtr("+999000002"),
		PhoneVerifiedAt:       testutil.ToPtr(time.Now()),
		InterfaceLanguage:     "en",
		CommunicationLanguage: "en",
	}
	superUser, err := s.store.CreateUserWithPhone(s.ctx, superParams)
	s.Require().NoError(err)
	s.superUser = superUser
	// Update platform role
	err = s.store.UpdateUserPlatformRole(s.ctx, db.UpdateUserPlatformRoleParams{
		ID:           superUser.ID,
		PlatformRole: "super_admin",
	})
	s.Require().NoError(err)

	// Create staff user
	staffParams := db.CreateUserWithPhoneParams{
		DisplayName:           "Staff User",
		Phone:                 testutil.ToPtr("+999000003"),
		PhoneVerifiedAt:       testutil.ToPtr(time.Now()),
		InterfaceLanguage:     "en",
		CommunicationLanguage: "en",
	}
	staffUser, err := s.store.CreateUserWithPhone(s.ctx, staffParams)
	s.Require().NoError(err)
	s.staffUser = staffUser
	// Update platform role
	err = s.store.UpdateUserPlatformRole(s.ctx, db.UpdateUserPlatformRoleParams{
		ID:           staffUser.ID,
		PlatformRole: "staff",
	})
	s.Require().NoError(err)

	// Create admin user and add to organization
	adminParams := db.CreateUserWithPhoneParams{
		DisplayName:           "Admin User",
		Phone:                 testutil.ToPtr("+999000004"),
		PhoneVerifiedAt:       testutil.ToPtr(time.Now()),
		InterfaceLanguage:     "en",
		CommunicationLanguage: "en",
	}
	adminUser, err := s.store.CreateUserWithPhone(s.ctx, adminParams)
	s.Require().NoError(err)
	s.adminUser = adminUser

	// Add admin to organization
	_, err = s.store.AddUserToOrganization(s.ctx, db.AddUserToOrganizationParams{
		UserID:         adminUser.ID,
		OrganizationID: s.org.ID,
		Role:           "admin",
	})
	s.Require().NoError(err)

	// Create manager user and add to organization
	managerParams := db.CreateUserWithPhoneParams{
		DisplayName:           "Manager User",
		Phone:                 testutil.ToPtr("+999000005"),
		PhoneVerifiedAt:       testutil.ToPtr(time.Now()),
		InterfaceLanguage:     "en",
		CommunicationLanguage: "en",
	}
	managerUser, err := s.store.CreateUserWithPhone(s.ctx, managerParams)
	s.Require().NoError(err)
	s.managerUser = managerUser

	// Add manager to organization
	_, err = s.store.AddUserToOrganization(s.ctx, db.AddUserToOrganizationParams{
		UserID:         managerUser.ID,
		OrganizationID: s.org.ID,
		Role:           "manager",
	})
	s.Require().NoError(err)

	// Create member user and add to organization
	memberParams := db.CreateUserWithPhoneParams{
		DisplayName:           "Member User",
		Phone:                 testutil.ToPtr("+999000006"),
		PhoneVerifiedAt:       testutil.ToPtr(time.Now()),
		InterfaceLanguage:     "en",
		CommunicationLanguage: "en",
	}
	memberUser, err := s.store.CreateUserWithPhone(s.ctx, memberParams)
	s.Require().NoError(err)
	s.memberUser = memberUser

	// Add member to organization
	_, err = s.store.AddUserToOrganization(s.ctx, db.AddUserToOrganizationParams{
		UserID:         memberUser.ID,
		OrganizationID: s.org.ID,
		Role:           "member",
	})
	s.Require().NoError(err)
}

func (s *PostsAPIFilterTestSuite) createTestPosts() {
	publishedAt := time.Now()

	// Create post by superadmin
	superPostParams := db.CreatePostParams{
		OrganizationID: s.org.ID,
		Title:          "Post by Super Admin",
		Slug:           "post-by-super-admin",
		Content:        []byte(`{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"This post was created by a super admin."}]}]}`),
		Status:         "published",
		AuthorID:       s.superUser.ID,
		PublishedAt:    &publishedAt,
	}
	superPost, err := s.store.CreatePost(s.ctx, superPostParams)
	s.Require().NoError(err)
	s.superPost = superPost

	// Create post by staff
	staffPostParams := db.CreatePostParams{
		OrganizationID: s.org.ID,
		Title:          "Post by Staff",
		Slug:           "post-by-staff",
		Content:        []byte(`{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"This post was created by a staff user."}]}]}`),
		Status:         "published",
		AuthorID:       s.staffUser.ID,
		PublishedAt:    &publishedAt,
	}
	staffPost, err := s.store.CreatePost(s.ctx, staffPostParams)
	s.Require().NoError(err)
	s.staffPost = staffPost

	// Create post by owner
	ownerPostParams := db.CreatePostParams{
		OrganizationID: s.org.ID,
		Title:          "Post by Owner",
		Slug:           "post-by-owner",
		Content:        []byte(`{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"This post was created by an organization owner."}]}]}`),
		Status:         "published",
		AuthorID:       s.ownerUser.ID,
		PublishedAt:    &publishedAt,
	}
	ownerPost, err := s.store.CreatePost(s.ctx, ownerPostParams)
	s.Require().NoError(err)
	s.ownerPost = ownerPost

	// Create post by admin
	adminPostParams := db.CreatePostParams{
		OrganizationID: s.org.ID,
		Title:          "Post by Admin",
		Slug:           "post-by-admin",
		Content:        []byte(`{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"This post was created by an organization admin."}]}]}`),
		Status:         "published",
		AuthorID:       s.adminUser.ID,
		PublishedAt:    &publishedAt,
	}
	adminPost, err := s.store.CreatePost(s.ctx, adminPostParams)
	s.Require().NoError(err)
	s.adminPost = adminPost

	// Create post by manager
	managerPostParams := db.CreatePostParams{
		OrganizationID: s.org.ID,
		Title:          "Post by Manager",
		Slug:           "post-by-manager",
		Content:        []byte(`{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"This post was created by an organization manager."}]}]}`),
		Status:         "published",
		AuthorID:       s.managerUser.ID,
		PublishedAt:    &publishedAt,
	}
	managerPost, err := s.store.CreatePost(s.ctx, managerPostParams)
	s.Require().NoError(err)
	s.managerPost = managerPost

	// Create post by member
	memberPostParams := db.CreatePostParams{
		OrganizationID: s.org.ID,
		Title:          "Post by Member",
		Slug:           "post-by-member",
		Content:        []byte(`{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"This post was created by an organization member."}]}]}`),
		Status:         "published",
		AuthorID:       s.memberUser.ID,
		PublishedAt:    &publishedAt,
	}
	memberPost, err := s.store.CreatePost(s.ctx, memberPostParams)
	s.Require().NoError(err)
	s.memberPost = memberPost
}

func (s *PostsAPIFilterTestSuite) cleanupTestData() {
	// Clean up in reverse order of creation
	if s.pool != nil && s.store != nil && s.org.ID != uuid.Nil {
		// Delete all posts first
		_, err := s.pool.Exec(s.ctx, "DELETE FROM posts WHERE organization_id = $1", s.org.ID)
		if err != nil {
			s.T().Logf("Failed to delete posts: %v", err)
		}

		// Delete organization memberships
		_, err = s.pool.Exec(s.ctx, "DELETE FROM user_organization_memberships WHERE organization_id = $1", s.org.ID)
		if err != nil {
			s.T().Logf("Failed to delete memberships: %v", err)
		}

		// Delete organization (this must come before deleting the owner user)
		_, err = s.pool.Exec(s.ctx, "DELETE FROM organizations WHERE id = $1", s.org.ID)
		if err != nil {
			s.T().Logf("Failed to delete organization: %v", err)
		}

		// Delete users - using LIKE pattern to be more flexible
		_, err = s.pool.Exec(s.ctx, "DELETE FROM users WHERE phone LIKE '+99900%'")
		if err != nil {
			s.T().Logf("Failed to delete users: %v", err)
		}
	}
}

// TestListPublicPosts_ReturnsPostsFromAllRoles verifies that the public posts API
// returns posts from all user roles without filtering
func (s *PostsAPIFilterTestSuite) TestListPublicPosts_ReturnsPostsFromAllRoles() {
	// Set up query parameters
	params := payloads.ListPostsParams{
		Limit:  100,
		Offset: 0,
	}

	// Call the service directly
	posts, totalCount, err := s.queryService.ListPublishedPosts(s.ctx, params)
	s.Require().NoError(err)

	// Verify we got all 6 posts
	s.Require().Len(posts, 6, "Expected 6 posts from different user roles")
	s.Equal(int64(6), totalCount, "Total count should be 6")

	// Create a map of post titles for easy verification
	postTitles := make(map[string]bool)
	for _, post := range posts {
		postTitles[post.Title] = true
	}

	// Verify posts from all roles are present
	s.True(postTitles["Post by Super Admin"], "Post by super admin should be included")
	s.True(postTitles["Post by Staff"], "Post by staff should be included")
	s.True(postTitles["Post by Owner"], "Post by owner should be included")
	s.True(postTitles["Post by Admin"], "Post by admin should be included")
	s.True(postTitles["Post by Manager"], "Post by manager should be included")
	s.True(postTitles["Post by Member"], "Post by member should be included")
}

// TestListPublicPosts_WithOrganizationFilter verifies filtering by organization works
func (s *PostsAPIFilterTestSuite) TestListPublicPosts_WithOrganizationFilter() {
	// Create another organization with a post
	otherOrgParams := db.CreateOrganizationParams{
		Name:        "Other Organization",
		Description: testutil.ToPtr("Another org"),
		OwnerUserID: s.superUser.ID,
		Status:      "active",
	}
	otherOrg, err := s.store.CreateOrganization(s.ctx, otherOrgParams)
	s.Require().NoError(err)

	// Create a post in the other organization
	publishedAt := time.Now()
	otherPostParams := db.CreatePostParams{
		OrganizationID: otherOrg.ID,
		Title:          "Post in Other Org",
		Slug:           "post-in-other-org",
		Content:        []byte(`{"type":"doc","content":[]}`),
		Status:         "published",
		AuthorID:       s.superUser.ID,
		PublishedAt:    &publishedAt,
	}
	_, err = s.store.CreatePost(s.ctx, otherPostParams)
	s.Require().NoError(err)

	// Query posts filtered by our test organization
	params := payloads.ListPostsParams{
		Limit:          100,
		Offset:         0,
		OrganizationID: &s.org.ID,
	}

	posts, totalCount, err := s.queryService.ListPublishedPosts(s.ctx, params)
	s.Require().NoError(err)

	// Should only get posts from our test org (6 posts)
	s.Require().Len(posts, 6)
	s.Equal(int64(6), totalCount)

	// Verify all posts are from our test org
	for _, post := range posts {
		s.Equal(s.org.ID, post.OrganizationID)
	}

	// Clean up
	s.pool.Exec(s.ctx, "DELETE FROM posts WHERE organization_id = $1", otherOrg.ID)
	s.pool.Exec(s.ctx, "DELETE FROM organizations WHERE id = $1", otherOrg.ID)
}

// TestListPublicPosts_DraftPostsExcluded verifies draft posts are not returned
func (s *PostsAPIFilterTestSuite) TestListPublicPosts_DraftPostsExcluded() {
	// Create a draft post
	draftPostParams := db.CreatePostParams{
		OrganizationID: s.org.ID,
		Title:          "Draft Post",
		Slug:           "draft-post",
		Content:        []byte(`{"type":"doc","content":[]}`),
		Status:         "draft",
		AuthorID:       s.superUser.ID,
	}
	_, err := s.store.CreatePost(s.ctx, draftPostParams)
	s.Require().NoError(err)

	// Query all posts
	params := payloads.ListPostsParams{
		Limit:  100,
		Offset: 0,
	}

	posts, totalCount, err := s.queryService.ListPublishedPosts(s.ctx, params)
	s.Require().NoError(err)

	// Should still only get 6 published posts
	s.Require().Len(posts, 6)
	s.Equal(int64(6), totalCount)

	// Verify draft post is not included
	for _, post := range posts {
		s.NotEqual("Draft Post", post.Title)
		s.Equal("published", post.Status)
	}

	// Clean up
	s.pool.Exec(s.ctx, "DELETE FROM posts WHERE title = $1", "Draft Post")
}

// TestListPublicPosts_Pagination verifies pagination works correctly
func (s *PostsAPIFilterTestSuite) TestListPublicPosts_Pagination() {
	// Test first page
	params := payloads.ListPostsParams{
		Limit:  3,
		Offset: 0,
	}

	firstPage, totalCount, err := s.queryService.ListPublishedPosts(s.ctx, params)
	s.Require().NoError(err)
	s.Require().Len(firstPage, 3)
	s.Equal(int64(6), totalCount)

	// Test second page
	params.Offset = 3

	secondPage, totalCount2, err := s.queryService.ListPublishedPosts(s.ctx, params)
	s.Require().NoError(err)
	s.Require().Len(secondPage, 3)
	s.Equal(int64(6), totalCount2)

	// Verify no overlap between pages
	firstPageIDs := make(map[uuid.UUID]bool)
	for _, post := range firstPage {
		firstPageIDs[post.ID] = true
	}

	for _, post := range secondPage {
		s.False(firstPageIDs[post.ID], "Post should not appear in both pages")
	}
}

// TestListOrganizationPosts_ShowsAllPublishedPosts verifies that ListPostsByOrganization
// returns all published posts from the organization regardless of author role
func (s *PostsAPIFilterTestSuite) TestListOrganizationPosts_ShowsAllPublishedPosts() {
	// Query posts by organization with status filter
	params := payloads.ListPostsParams{
		Limit:  100,
		Offset: 0,
		Status: testutil.ToPtr("published"),
	}

	posts, totalCount, err := s.queryService.ListPostsByOrganization(s.ctx, s.org.ID, params)
	s.Require().NoError(err)

	// Should see all 6 published posts
	s.Require().Len(posts, 6)
	s.Equal(int64(6), totalCount)

	// Verify posts from all roles are visible
	postTitles := make(map[string]bool)
	for _, post := range posts {
		postTitles[post.Title] = true
	}

	s.True(postTitles["Post by Super Admin"])
	s.True(postTitles["Post by Staff"])
	s.True(postTitles["Post by Owner"])
	s.True(postTitles["Post by Admin"])
	s.True(postTitles["Post by Manager"])
	s.True(postTitles["Post by Member"])
}

func TestPostsAPIFilterTestSuite(t *testing.T) {
	suite.Run(t, new(PostsAPIFilterTestSuite))
}
