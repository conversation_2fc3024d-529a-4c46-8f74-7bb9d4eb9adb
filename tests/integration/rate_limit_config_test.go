package integration

import (
	"encoding/json"
	"net/http"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestRateLimitingConfiguration tests that rate limiting configuration is properly applied
func TestRateLimitingConfiguration(t *testing.T) {
	// Create server with rate limiting enabled
	server := NewTestServer(t,
		WithRateLimiting(true, 5),   // 5 requests per minute
		WithRedis("localhost:6379"), // Enable Redis if available
	)

	// Verify configuration was applied
	verifier := server.VerifyConfiguration()
	assert.True(t, verifier.RateLimitingEnabled(), "Rate limiting should be enabled")
	assert.Equal(t, 5, verifier.RateLimitPerMinute(), "Rate limit should be 5 per minute")

	// Test rate limiting behavior
	endpoint := "/api/v1/health"
	successCount := 0
	rateLimitedCount := 0

	// Make 10 rapid requests
	for i := 0; i < 10; i++ {
		rec, err := server.MakeRequest("GET", endpoint, nil, nil)
		require.NoError(t, err)

		if rec.Code == http.StatusOK {
			successCount++
		} else if rec.Code == http.StatusTooManyRequests {
			rateLimitedCount++

			// Verify rate limit error response
			var response map[string]interface{}
			err := json.Unmarshal(rec.Body.Bytes(), &response)
			require.NoError(t, err)
			assert.Contains(t, response["error"], "Too many requests")
		}
	}

	// Should have some successful requests and some rate limited
	assert.Greater(t, successCount, 0, "Should have at least one successful request")
	assert.Greater(t, rateLimitedCount, 0, "Should have rate limited some requests")
	assert.Equal(t, 10, successCount+rateLimitedCount, "All requests should be accounted for")
}

// TestRateLimitingDisabledByDefault tests default behavior
func TestRateLimitingDisabledByDefault(t *testing.T) {
	// Create server with default configuration
	server := NewTestServer(t)

	// Verify rate limiting is disabled
	verifier := server.VerifyConfiguration()
	assert.False(t, verifier.RateLimitingEnabled(), "Rate limiting should be disabled by default")

	// Make many requests - none should be rate limited
	endpoint := "/api/v1/health"
	for i := 0; i < 100; i++ {
		rec, err := server.MakeRequest("GET", endpoint, nil, nil)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, rec.Code, "All requests should succeed when rate limiting is disabled")
	}
}

// TestRateLimitingWithTrustedProxies tests rate limiting with proxy configuration
func TestRateLimitingWithTrustedProxies(t *testing.T) {
	server := NewTestServer(t,
		WithRateLimiting(true, 10),
		WithTrustedProxies("192.168.1.0/24"),
	)

	// Make request with X-Forwarded-For header
	headers := map[string]string{
		"X-Forwarded-For": "203.0.113.1, 192.168.1.1",
	}

	rec, err := server.MakeRequest("GET", "/api/v1/health", nil, headers)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, rec.Code)

	// Check for rate limit headers
	assert.NotEmpty(t, rec.Header().Get("X-RateLimit-Limit"))
	assert.NotEmpty(t, rec.Header().Get("X-RateLimit-Remaining"))
}

// TestConcurrentRateLimiting tests rate limiting under concurrent load
func TestConcurrentRateLimiting(t *testing.T) {
	server := NewTestServer(t, WithRateLimiting(true, 20)) // 20 per minute

	var wg sync.WaitGroup
	var mu sync.Mutex
	results := make(map[int]int) // status code -> count

	// Launch 50 concurrent requests
	for i := 0; i < 50; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()

			rec, err := server.MakeRequest("GET", "/api/v1/health", nil, nil)
			if err != nil {
				return
			}

			mu.Lock()
			results[rec.Code]++
			mu.Unlock()
		}()
	}

	wg.Wait()

	// Should have mix of successful and rate limited
	assert.Greater(t, results[http.StatusOK], 0, "Should have successful requests")
	assert.Greater(t, results[http.StatusTooManyRequests], 0, "Should have rate limited requests")

	// Total should be 50
	total := 0
	for _, count := range results {
		total += count
	}
	assert.Equal(t, 50, total, "All requests should complete")
}

// TestRateLimitingRecovery tests that rate limits reset properly
func TestRateLimitingRecovery(t *testing.T) {
	// Removed short mode check - tests run with full infrastructure

	server := NewTestServer(t, WithRateLimiting(true, 60)) // 60 per minute = 1 per second

	// Exhaust rate limit
	for i := 0; i < 2; i++ {
		rec, _ := server.MakeRequest("GET", "/api/v1/health", nil, nil)
		if rec.Code == http.StatusTooManyRequests {
			break
		}
	}

	// Wait for rate limit to reset
	time.Sleep(2 * time.Second)

	// Should be able to make request again
	rec, err := server.MakeRequest("GET", "/api/v1/health", nil, nil)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, rec.Code, "Should be able to make request after rate limit reset")
}

// Example: Testing specific endpoints with custom rate limits
func TestAuthEndpointRateLimiting(t *testing.T) {
	server := NewTestServer(t, WithRateLimiting(true, 100))

	// Auth endpoints typically have stricter limits
	// This would test the endpoint-specific limits configured in setupRateLimiting

	endpoint := "/api/v1/authn/phone/otp/initiate"

	// Make several requests
	for i := 0; i < 10; i++ {
		body := map[string]interface{}{
			"phone": "+**********",
		}
		rec, err := server.MakeRequest("POST", endpoint, body, nil)
		require.NoError(t, err)

		// Auth endpoints might have stricter limits
		if i >= 3 && rec.Code == http.StatusTooManyRequests {
			// Expected - auth endpoints are rate limited more strictly
			return
		}
	}
}
