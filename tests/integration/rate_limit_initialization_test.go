package integration

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/internal/config"
	"Membership-SAAS-System-Backend/internal/infrastructure/cache"
	"Membership-SAAS-System-Backend/internal/middleware"
	"Membership-SAAS-System-Backend/internal/server"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestRateLimitMiddlewareWithNilKeyExtractor tests that the middleware handles nil KeyExtractor gracefully
func TestRateLimitMiddlewareWithNilKeyExtractor(t *testing.T) {
	// Create a minimal Redis config
	rateLimitConfig := middleware.RedisRateLimitConfig{
		DefaultRate:      100,
		DefaultBurst:     100,
		FallbackRate:     100,
		FallbackBurst:    100,
		EndpointLimits:   make(map[string]middleware.EndpointLimit),
		TrustedProxies:   []string{},
		KeyExtractor:     nil, // Explicitly set to nil
		KeyTypeExtractor: nil, // Also test nil KeyTypeExtractor
		Skipper:          func(c echo.Context) bool { return false },
		ErrorHandler:     nil,
	}

	// Create Echo instance
	e := echo.New()

	// Mock Redis client (nil for this test, should trigger fallback)
	rateLimitConfig.Redis = nil

	// This should not panic even with nil KeyExtractor
	assert.NotPanics(t, func() {
		// Create middleware - should handle nil gracefully
		mw := middleware.RedisRateLimitMiddleware(rateLimitConfig)
		e.Use(mw)
	})

	// Add test endpoint
	e.GET("/test", func(c echo.Context) error {
		return c.String(http.StatusOK, "OK")
	})

	// Make a request - should work even with nil KeyExtractor
	req := httptest.NewRequest(http.MethodGet, "/test", nil)
	rec := httptest.NewRecorder()

	e.ServeHTTP(rec, req)

	// Should get OK response (rate limiting with fallback)
	assert.Equal(t, http.StatusOK, rec.Code)
}

// setMinimalTestEnv sets minimal environment variables for testing
func setMinimalTestEnv() {
	os.Setenv("DATABASE_URL", "postgres://test:test@localhost/test")
	os.Setenv("ACCESS_TOKEN_SECRET", "test-secret")
	os.Setenv("REFRESH_TOKEN_SECRET", "test-refresh-secret")
}

// TestServerSetupRateLimitingInitialization tests that setupRateLimiting properly initializes KeyExtractor
func TestServerSetupRateLimitingInitialization(t *testing.T) {
	// Set minimal test environment
	setMinimalTestEnv()

	// Save current env
	oldGoEnv := os.Getenv("GO_ENV")
	oldEnabled := os.Getenv("RATE_LIMIT_ENABLED")
	oldSkipInTests := os.Getenv("RATE_LIMIT_SKIP_IN_TESTS")
	oldRedisAddr := os.Getenv("REDIS_ADDR")

	// Force rate limiting to be enabled with Redis
	os.Setenv("GO_ENV", "production")
	os.Setenv("RATE_LIMIT_ENABLED", "true")
	os.Setenv("RATE_LIMIT_SKIP_IN_TESTS", "false")
	os.Setenv("REDIS_ADDR", "localhost:6379") // Use a dummy Redis address

	defer func() {
		// Restore env
		if oldGoEnv != "" {
			os.Setenv("GO_ENV", oldGoEnv)
		} else {
			os.Unsetenv("GO_ENV")
		}
		if oldEnabled != "" {
			os.Setenv("RATE_LIMIT_ENABLED", oldEnabled)
		} else {
			os.Unsetenv("RATE_LIMIT_ENABLED")
		}
		if oldSkipInTests != "" {
			os.Setenv("RATE_LIMIT_SKIP_IN_TESTS", oldSkipInTests)
		} else {
			os.Unsetenv("RATE_LIMIT_SKIP_IN_TESTS")
		}
		if oldRedisAddr != "" {
			os.Setenv("REDIS_ADDR", oldRedisAddr)
		} else {
			os.Unsetenv("REDIS_ADDR")
		}
	}()

	// Create test configuration
	cfg := config.Load()
	// TODO: Fix these fields - they don't exist in SecurityConfig
	// cfg.Security.RateLimitEnabled = true
	// cfg.Security.RateLimitSkipInTests = false

	// Create a mock Redis cache
	redisConfig := &cache.RedisConfig{
		Addr:         "localhost:6379",
		Password:     "",
		DB:           0,
		MaxRetries:   3,
		DialTimeout:  cfg.Cache.DialTimeout,
		ReadTimeout:  cfg.Cache.ReadTimeout,
		WriteTimeout: cfg.Cache.WriteTimeout,
		PoolSize:     cfg.Cache.PoolSize,
		MinIdleConns: cfg.Cache.MinIdleConns,
		MaxIdleTime:  cfg.Cache.MaxIdleTime,
		KeyPrefix:    "test:",
	}

	// Try to create Redis cache (might fail if Redis not running)
	metrics := cache.NewCacheMetrics("test", "cache")
	_, err := cache.NewRedisCache(redisConfig, metrics)
	if err != nil {
		t.Skip("Redis not available, skipping test")
	}

	// Override cache backend to use Redis
	cfg.Cache.Backend = "redis"

	// Create server - this should properly initialize rate limiting
	srv, err := server.New(cfg)
	if err != nil {
		// If server creation fails due to Redis, skip the test
		t.Skip("Server creation failed, likely due to Redis not being available")
	}
	require.NotNil(t, srv)

	// Make a test request to ensure server is functional
	req := httptest.NewRequest(http.MethodGet, "/health", nil)
	rec := httptest.NewRecorder()

	srv.Echo().ServeHTTP(rec, req)

	// Should get OK response
	assert.Equal(t, http.StatusOK, rec.Code)
}

// TestRateLimitMiddlewareDefaultsWithRedis tests that defaults work properly when Redis is provided
func TestRateLimitMiddlewareDefaultsWithRedis(t *testing.T) {
	// Skip if Redis is not available
	redisAddr := os.Getenv("REDIS_ADDR")
	if redisAddr == "" {
		redisAddr = "localhost:6379"
	}

	// Create Redis config
	redisConfig := &cache.RedisConfig{
		Addr:         redisAddr,
		Password:     "",
		DB:           0,
		MaxRetries:   3,
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
		PoolSize:     10,
		MinIdleConns: 1,
		MaxIdleTime:  5 * time.Minute,
		KeyPrefix:    "test:",
	}

	// Try to create Redis cache
	metrics := cache.NewCacheMetrics("test", "cache")
	redisCache, err := cache.NewRedisCache(redisConfig, metrics)
	if err != nil {
		t.Skip("Redis not available, skipping test")
	}

	// Create rate limit config using defaults
	rateLimitConfig := middleware.DefaultRedisRateLimitConfig()
	rateLimitConfig.Redis = redisCache.GetRedisClient()
	rateLimitConfig.Metrics = middleware.NewRateLimitMetrics("test", "rate_limit")

	// Verify defaults are set
	assert.NotNil(t, rateLimitConfig.KeyExtractor, "Default KeyExtractor should be set")
	assert.NotNil(t, rateLimitConfig.KeyTypeExtractor, "Default KeyTypeExtractor should be set")
	assert.NotNil(t, rateLimitConfig.Skipper, "Default Skipper should be set")
	assert.NotNil(t, rateLimitConfig.ErrorHandler, "Default ErrorHandler should be set")

	// Create Echo instance
	e := echo.New()

	// Apply middleware - should not panic
	assert.NotPanics(t, func() {
		mw := middleware.RedisRateLimitMiddleware(rateLimitConfig)
		e.Use(mw)
	})

	// Add test endpoint
	e.GET("/test", func(c echo.Context) error {
		return c.String(http.StatusOK, "OK")
	})

	// Make a request
	req := httptest.NewRequest(http.MethodGet, "/test", nil)
	rec := httptest.NewRecorder()

	e.ServeHTTP(rec, req)

	// Should get OK response
	assert.Equal(t, http.StatusOK, rec.Code)

	// Verify rate limit headers are set
	assert.NotEmpty(t, rec.Header().Get("X-RateLimit-Limit"))
	assert.NotEmpty(t, rec.Header().Get("X-RateLimit-Remaining"))
}

// TestKeyExtractorWithTrustedProxies tests that KeyExtractor is properly overridden when TrustedProxies are set
func TestKeyExtractorWithTrustedProxies(t *testing.T) {
	// Skip if Redis is not available
	redisAddr := os.Getenv("REDIS_ADDR")
	if redisAddr == "" {
		redisAddr = "localhost:6379"
	}

	// Create Redis config
	redisConfig := &cache.RedisConfig{
		Addr:         redisAddr,
		Password:     "",
		DB:           0,
		MaxRetries:   3,
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
		PoolSize:     10,
		MinIdleConns: 1,
		MaxIdleTime:  5 * time.Minute,
		KeyPrefix:    "test:",
	}

	// Try to create Redis cache
	metrics := cache.NewCacheMetrics("test", "cache")
	redisCache, err := cache.NewRedisCache(redisConfig, metrics)
	if err != nil {
		t.Skip("Redis not available, skipping test")
	}

	// Create rate limit config with trusted proxies
	rateLimitConfig := middleware.DefaultRedisRateLimitConfig()
	rateLimitConfig.Redis = redisCache.GetRedisClient()
	rateLimitConfig.Metrics = middleware.NewRateLimitMetrics("test", "rate_limit")
	rateLimitConfig.TrustedProxies = []string{"***********/24", "10.0.0.0/8"}

	// Create Echo instance
	e := echo.New()

	// Apply middleware
	mw := middleware.RedisRateLimitMiddleware(rateLimitConfig)
	e.Use(mw)

	// Add test endpoint
	e.GET("/test", func(c echo.Context) error {
		return c.String(http.StatusOK, "OK")
	})

	// Make a request with X-Forwarded-For header
	req := httptest.NewRequest(http.MethodGet, "/test", nil)
	req.Header.Set("X-Forwarded-For", "***********, ***********")
	req.RemoteAddr = "***********:12345"
	rec := httptest.NewRecorder()

	e.ServeHTTP(rec, req)

	// Should get OK response
	assert.Equal(t, http.StatusOK, rec.Code)

	// The rate limiting should use the extracted IP from X-Forwarded-For
	// since *********** is in the trusted proxy range
}

// TestErrorHandlerDefaults tests that error handler defaults work properly with Redis
func TestErrorHandlerDefaults(t *testing.T) {
	// Skip if Redis is not available
	redisAddr := os.Getenv("REDIS_ADDR")
	if redisAddr == "" {
		redisAddr = "localhost:6379"
	}

	// Create Redis config
	redisConfig := &cache.RedisConfig{
		Addr:         redisAddr,
		Password:     "",
		DB:           0,
		MaxRetries:   3,
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,
		PoolSize:     10,
		MinIdleConns: 1,
		MaxIdleTime:  5 * time.Minute,
		KeyPrefix:    "test:",
	}

	// Try to create Redis cache
	metrics := cache.NewCacheMetrics("test", "cache")
	redisCache, err := cache.NewRedisCache(redisConfig, metrics)
	if err != nil {
		t.Skip("Redis not available, skipping test")
	}

	// Create rate limit config with very low limits to trigger rate limiting
	rateLimitConfig := middleware.DefaultRedisRateLimitConfig()
	rateLimitConfig.DefaultRate = 1
	rateLimitConfig.DefaultBurst = 1
	rateLimitConfig.Redis = redisCache.GetRedisClient()
	rateLimitConfig.Metrics = middleware.NewRateLimitMetrics("test", "rate_limit")

	// Create Echo instance
	e := echo.New()

	// Apply middleware
	mw := middleware.RedisRateLimitMiddleware(rateLimitConfig)
	e.Use(mw)

	// Add test endpoint
	e.GET("/test", func(c echo.Context) error {
		return c.String(http.StatusOK, "OK")
	})

	// Clear any previous rate limit for this test
	testKey := "rate:/test:*************:12345"
	err = redisCache.GetRedisClient().Del(context.Background(), testKey).Err()
	if err != nil {
		t.Logf("Warning: Could not clear previous rate limit: %v", err)
	}

	// Make first request - should succeed
	req1 := httptest.NewRequest(http.MethodGet, "/test", nil)
	req1.RemoteAddr = "*************:12345"
	rec1 := httptest.NewRecorder()
	e.ServeHTTP(rec1, req1)
	assert.Equal(t, http.StatusOK, rec1.Code)

	// Make second request from same IP - should be rate limited
	req2 := httptest.NewRequest(http.MethodGet, "/test", nil)
	req2.RemoteAddr = "*************:12345"
	rec2 := httptest.NewRecorder()
	e.ServeHTTP(rec2, req2)

	// Should get rate limited response
	assert.Equal(t, http.StatusTooManyRequests, rec2.Code)

	// Check response body
	var response map[string]interface{}
	err = json.Unmarshal(rec2.Body.Bytes(), &response)
	require.NoError(t, err)

	assert.Equal(t, "Too many requests", response["error"])
	assert.Contains(t, response["message"], "Rate limit exceeded")
	assert.NotNil(t, response["retry_after"])
}
