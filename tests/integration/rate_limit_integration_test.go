package integration

import (
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestRateLimitingDisabledInTestEnvironment verifies rate limiting is disabled by default in test environment
func TestRateLimitingDisabledInTestEnvironment(t *testing.T) {
	// Create server with default test configuration
	server := NewTestServer(t)

	// Verify configuration shows rate limiting is disabled
	verifier := server.VerifyConfiguration()
	assert.False(t, verifier.RateLimitingEnabled(), "Rate limiting should be disabled in test environment by default")
	assert.Equal(t, "test", verifier.Environment(), "Should be in test environment")

	// Make many rapid requests - none should be rate limited
	const numRequests = 100
	for i := 0; i < numRequests; i++ {
		rec, err := server.MakeRequest("GET", "/health", nil, nil)
		require.NoError(t, err)
		assert.Equal(t, http.StatusOK, rec.Code, "Request %d should succeed when rate limiting is disabled", i+1)
	}
}

// TestRateLimitingExplicitlyEnabled verifies rate limiting can be explicitly enabled
func TestRateLimitingExplicitlyEnabled(t *testing.T) {
	// Create server with rate limiting explicitly enabled
	server := NewTestServer(t, WithRateLimiting(true, 10)) // 10 requests per minute

	// Verify configuration
	verifier := server.VerifyConfiguration()
	assert.True(t, verifier.RateLimitingEnabled(), "Rate limiting should be enabled when explicitly configured")
	assert.Equal(t, 10, verifier.RateLimitPerMinute(), "Rate limit should be configured value")

	// Track request results
	var successCount, rateLimitedCount int
	var mu sync.Mutex

	// Make requests until we hit rate limit
	for i := 0; i < 20; i++ {
		rec, err := server.MakeRequest("GET", "/health", nil, nil)
		require.NoError(t, err)

		mu.Lock()
		switch rec.Code {
		case http.StatusOK:
			successCount++
		case http.StatusTooManyRequests:
			rateLimitedCount++
			// Verify rate limit error response
			var response map[string]interface{}
			err := json.Unmarshal(rec.Body.Bytes(), &response)
			assert.NoError(t, err)
			assert.Contains(t, response, "error", "Rate limit response should contain error")
		default:
			t.Errorf("Unexpected status code: %d", rec.Code)
		}
		mu.Unlock()
	}

	// Should have some successful requests and some rate limited
	assert.Greater(t, successCount, 0, "Should have at least one successful request")
	assert.Greater(t, rateLimitedCount, 0, "Should have rate limited some requests")
}

// TestRateLimitingKeyExtractorInitialization verifies KeyExtractor is properly initialized
func TestRateLimitingKeyExtractorInitialization(t *testing.T) {
	testCases := []struct {
		name          string
		setupServer   func(t *testing.T) *TestServer
		expectEnabled bool
		testBehavior  bool
	}{
		{
			name: "disabled_in_test_mode",
			setupServer: func(t *testing.T) *TestServer {
				return NewTestServer(t)
			},
			expectEnabled: false,
			testBehavior:  true,
		},
		{
			name: "enabled_with_config",
			setupServer: func(t *testing.T) *TestServer {
				return NewTestServer(t, WithRateLimiting(true, 5))
			},
			expectEnabled: true,
			testBehavior:  true,
		},
		{
			name: "explicitly_disabled",
			setupServer: func(t *testing.T) *TestServer {
				return NewTestServer(t, WithoutRateLimiting())
			},
			expectEnabled: false,
			testBehavior:  false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			server := tc.setupServer(t)

			// Verify configuration
			verifier := server.VerifyConfiguration()
			assert.Equal(t, tc.expectEnabled, verifier.RateLimitingEnabled(),
				"Rate limiting enabled state should match expected")

			if tc.testBehavior {
				// Test with different client identifiers
				headers1 := map[string]string{"X-Forwarded-For": "192.168.1.1"}
				headers2 := map[string]string{"X-Forwarded-For": "192.168.1.2"}

				// Make requests from different "clients"
				rec1, err := server.MakeRequest("GET", "/health", nil, headers1)
				require.NoError(t, err)
				rec2, err := server.MakeRequest("GET", "/health", nil, headers2)
				require.NoError(t, err)

				if tc.expectEnabled {
					// Both should succeed initially (different clients)
					assert.Equal(t, http.StatusOK, rec1.Code)
					assert.Equal(t, http.StatusOK, rec2.Code)
				} else {
					// Both should succeed (no rate limiting)
					assert.Equal(t, http.StatusOK, rec1.Code)
					assert.Equal(t, http.StatusOK, rec2.Code)
				}
			}
		})
	}
}

// TestRateLimitingConfigurationPropagation verifies configuration changes propagate correctly
func TestRateLimitingConfigurationPropagation(t *testing.T) {
	// Test multiple configuration combinations
	testCases := []struct {
		name    string
		options []TestServerOption
		verify  func(t *testing.T, verifier ConfigVerifier)
	}{
		{
			name: "rate_limiting_with_redis",
			options: []TestServerOption{
				WithRateLimiting(true, 20),
				WithRedis("localhost:6379"),
			},
			verify: func(t *testing.T, verifier ConfigVerifier) {
				assert.True(t, verifier.RateLimitingEnabled())
				assert.Equal(t, 20, verifier.RateLimitPerMinute())
				assert.True(t, verifier.HasRedis())
			},
		},
		{
			name: "rate_limiting_with_trusted_proxies",
			options: []TestServerOption{
				WithRateLimiting(true, 15),
				WithTrustedProxies("10.0.0.0/8", "**********/12"),
			},
			verify: func(t *testing.T, verifier ConfigVerifier) {
				assert.True(t, verifier.RateLimitingEnabled())
				assert.Equal(t, 15, verifier.RateLimitPerMinute())
			},
		},
		{
			name: "rate_limiting_with_custom_environment",
			options: []TestServerOption{
				WithEnvironment("staging"),
				WithRateLimiting(true, 30),
			},
			verify: func(t *testing.T, verifier ConfigVerifier) {
				assert.True(t, verifier.RateLimitingEnabled())
				assert.Equal(t, 30, verifier.RateLimitPerMinute())
				assert.Equal(t, "staging", verifier.Environment())
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			server := NewTestServer(t, tc.options...)

			verifier := server.VerifyConfiguration()
			tc.verify(t, verifier)

			// Make a test request to ensure server is functional
			rec, err := server.MakeRequest("GET", "/health", nil, nil)
			require.NoError(t, err)
			assert.Equal(t, http.StatusOK, rec.Code)
		})
	}
}

// TestRateLimitingWithMissingRedis tests behavior when Redis is configured but not available
func TestRateLimitingWithMissingRedis(t *testing.T) {
	// Create server with rate limiting and Redis configured to non-existent instance
	server := NewTestServer(t,
		WithRateLimiting(true, 10),
		WithRedis("localhost:9999"), // Non-existent Redis
	)

	// Server should still function (fallback to in-memory rate limiting)
	rec, err := server.MakeRequest("GET", "/health", nil, nil)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, rec.Code, "Server should function even with missing Redis")
}

// TestRateLimitingConcurrentRequests tests rate limiting under concurrent load
func TestRateLimitingConcurrentRequests(t *testing.T) {
	server := NewTestServer(t, WithRateLimiting(true, 20)) // 20 per minute

	const numGoroutines = 10
	const requestsPerGoroutine = 5

	var wg sync.WaitGroup
	var mu sync.Mutex
	results := make(map[int]int) // status code -> count

	// Launch concurrent requests
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(clientID int) {
			defer wg.Done()

			// Use different client IPs to test rate limiting per client
			headers := map[string]string{
				"X-Forwarded-For": fmt.Sprintf("192.168.1.%d", clientID),
			}

			for j := 0; j < requestsPerGoroutine; j++ {
				rec, err := server.MakeRequest("GET", "/health", nil, headers)
				if err != nil {
					continue
				}

				mu.Lock()
				results[rec.Code]++
				mu.Unlock()

				// Small delay to spread requests
				time.Sleep(10 * time.Millisecond)
			}
		}(i)
	}

	wg.Wait()

	// Verify results
	totalRequests := 0
	for code, count := range results {
		totalRequests += count
		t.Logf("Status %d: %d requests", code, count)
	}

	assert.Equal(t, numGoroutines*requestsPerGoroutine, totalRequests, "All requests should complete")
	assert.Greater(t, results[http.StatusOK], 0, "Should have some successful requests")

	// With per-client rate limiting, we might not see 429s if clients are properly separated
	// This depends on the rate limiting implementation
}

// TestRateLimitingHeaders verifies rate limit headers are properly set
func TestRateLimitingHeaders(t *testing.T) {
	server := NewTestServer(t, WithRateLimiting(true, 60)) // 60 per minute = 1 per second

	// Make initial request
	rec, err := server.MakeRequest("GET", "/health", nil, nil)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, rec.Code)

	// Check for rate limit headers
	headers := rec.Header()

	// These headers might be present depending on the rate limiting implementation
	possibleHeaders := []string{
		"X-RateLimit-Limit",
		"X-RateLimit-Remaining",
		"X-RateLimit-Reset",
		"RateLimit-Limit",
		"RateLimit-Remaining",
		"RateLimit-Reset",
	}

	hasRateLimitHeaders := false
	for _, header := range possibleHeaders {
		if headers.Get(header) != "" {
			hasRateLimitHeaders = true
			t.Logf("Found rate limit header: %s = %s", header, headers.Get(header))
		}
	}

	// Log all headers for debugging
	if !hasRateLimitHeaders {
		t.Log("No standard rate limit headers found. All headers:")
		for name, values := range headers {
			for _, value := range values {
				t.Logf("  %s: %s", name, value)
			}
		}
	}
}

// TestRateLimitingEndpointSpecific tests endpoint-specific rate limits
func TestRateLimitingEndpointSpecific(t *testing.T) {
	server := NewTestServer(t, WithRateLimiting(true, 100))

	// Test auth endpoints which typically have stricter limits
	testCases := []struct {
		endpoint     string
		method       string
		body         interface{}
		expectStrict bool
	}{
		{
			endpoint: "/api/v1/authn/phone/otp/initiate",
			method:   "POST",
			body: map[string]interface{}{
				"phone": "+**********",
			},
			expectStrict: true,
		},
		{
			endpoint:     "/health",
			method:       "GET",
			body:         nil,
			expectStrict: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.endpoint, func(t *testing.T) {
			// Make several requests to the endpoint
			var rateLimited bool
			for i := 0; i < 10; i++ {
				rec, err := server.MakeRequest(tc.method, tc.endpoint, tc.body, nil)
				require.NoError(t, err)

				if rec.Code == http.StatusTooManyRequests {
					rateLimited = true
					break
				}
			}

			if tc.expectStrict {
				// Auth endpoints might have stricter limits
				t.Logf("Endpoint %s rate limited: %v", tc.endpoint, rateLimited)
			}
		})
	}
}

// TestRateLimitingServerIsolation verifies rate limiting is isolated between test servers
func TestRateLimitingServerIsolation(t *testing.T) {
	// Create two servers with different rate limit configurations
	server1 := NewTestServer(t, WithRateLimiting(true, 5))
	defer server1.Close()

	server2 := NewTestServer(t, WithRateLimiting(true, 10))
	defer server2.Close()

	// Verify configurations are independent
	verifier1 := server1.VerifyConfiguration()
	verifier2 := server2.VerifyConfiguration()

	assert.Equal(t, 5, verifier1.RateLimitPerMinute())
	assert.Equal(t, 10, verifier2.RateLimitPerMinute())

	// Make requests to both servers
	rec1, err := server1.MakeRequest("GET", "/health", nil, nil)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, rec1.Code)

	rec2, err := server2.MakeRequest("GET", "/health", nil, nil)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, rec2.Code)

	// Rate limiting on one server shouldn't affect the other
	// Exhaust rate limit on server1
	for i := 0; i < 10; i++ {
		server1.MakeRequest("GET", "/health", nil, nil)
	}

	// Server2 should still accept requests
	rec2Again, err := server2.MakeRequest("GET", "/health", nil, nil)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, rec2Again.Code, "Server2 should not be affected by server1's rate limit")
}
