package integration_test

import (
	"context"
	"encoding/json"
	"net/http"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/authorization"
	"Membership-SAAS-System-Backend/internal/handlers"
	"Membership-SAAS-System-Backend/internal/testutil"
	"Membership-SAAS-System-Backend/internal/token"
	"Membership-SAAS-System-Backend/tests/integration"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// RefreshTokenOrgContextTestSuite tests that organization context is preserved during token refresh
type RefreshTokenOrgContextTestSuite struct {
	suite.Suite
	apiSuite *integration.APITestSuite
}

func TestRefreshTokenOrgContextSuite(t *testing.T) {
	suite.Run(t, new(RefreshTokenOrgContextTestSuite))
}

func (suite *RefreshTokenOrgContextTestSuite) SetupSuite() {
	suite.apiSuite = integration.SetupAPITest(suite.T())
}

func (suite *RefreshTokenOrgContextTestSuite) TearDownSuite() {
	suite.apiSuite.TeardownAPITest(suite.T())
}

// TestRefreshTokenPreservesOrgContext tests that organization context is preserved when refreshing tokens
func (suite *RefreshTokenOrgContextTestSuite) TestRefreshTokenPreservesOrgContext() {
	ctx := context.Background()
	store := suite.apiSuite.Server.ServiceContainer().Store
	queries := store.(*db.SQLStore).Queries

	// Create a test user
	user := testutil.CreateDBUser(suite.T(), queries, func(params *db.CreateUserWithPhoneParams) {
		params.DisplayName = "Test User"
		params.Phone = testutil.ToPtr("+1234567890")
		params.PhoneVerifiedAt = testutil.ToPtr(time.Now())
	})
	userID := user.ID

	// Create a test organization
	org := testutil.CreateDBOrganization(suite.T(), queries, func(params *db.CreateOrganizationParams) {
		params.Name = "Test Organization"
		params.Status = "active"
		params.OwnerUserID = userID
	})
	orgID := org.ID

	// Add user to organization
	_, err := store.AddUserToOrganization(ctx, db.AddUserToOrganizationParams{
		UserID:         userID,
		OrganizationID: orgID,
		Role:           authorization.OrgRoleAdmin,
	})
	require.NoError(suite.T(), err)

	// Generate initial tokens without organization context
	initialAccessToken, err := token.GenerateEnhancedAccessToken(ctx, store, userID)
	require.NoError(suite.T(), err)

	initialRefreshToken, err := token.GenerateRefreshToken(ctx, store, userID)
	require.NoError(suite.T(), err)

	// Switch to organization context
	switchReq := handlers.SwitchOrganizationRequest{
		OrganizationID: orgID,
	}

	resp, err := suite.apiSuite.MakeRequest(
		"POST",
		"/api/v1/organizations/switch",
		switchReq,
		initialAccessToken,
	)
	require.NoError(suite.T(), err)
	require.Equal(suite.T(), http.StatusOK, resp.Code)

	var switchResp handlers.SwitchOrganizationResponse
	err = json.Unmarshal(resp.Body.Bytes(), &switchResp)
	require.NoError(suite.T(), err)

	// Verify the new access token has organization context
	config, err := token.GetJWTConfig()
	require.NoError(suite.T(), err)

	claims, err := token.ParseEnhancedToken(switchResp.AccessToken, config.AccessTokenSecret)
	require.NoError(suite.T(), err)
	assert.NotNil(suite.T(), claims.ActiveOrg)
	assert.Equal(suite.T(), orgID.String(), claims.ActiveOrg.ID)
	assert.Equal(suite.T(), authorization.OrgRoleAdmin, claims.ActiveOrg.Role)

	// Now refresh the token
	refreshReq := map[string]string{
		"refresh_token": initialRefreshToken,
	}

	refreshResp, err := suite.apiSuite.MakeRequest(
		"POST",
		"/api/v1/authn/token/refresh",
		refreshReq,
		"", // No auth token needed for refresh endpoint
	)
	require.NoError(suite.T(), err)
	require.Equal(suite.T(), http.StatusOK, refreshResp.Code)

	var tokenResp struct {
		AccessToken  string `json:"access_token"`
		RefreshToken string `json:"refresh_token"`
		TokenType    string `json:"token_type"`
	}
	err = json.Unmarshal(refreshResp.Body.Bytes(), &tokenResp)
	require.NoError(suite.T(), err)

	// Verify the refreshed access token still has organization context
	// Note: Since the original refresh token didn't have org context,
	// the refreshed token won't have it either. This is expected behavior
	// for backward compatibility.
	refreshedClaims, err := token.ParseEnhancedToken(tokenResp.AccessToken, config.AccessTokenSecret)
	require.NoError(suite.T(), err)
	assert.Nil(suite.T(), refreshedClaims.ActiveOrg, "Legacy refresh token should not preserve org context")
}

// TestEnhancedRefreshTokenPreservesOrgContext tests that enhanced refresh tokens preserve organization context
func (suite *RefreshTokenOrgContextTestSuite) TestEnhancedRefreshTokenPreservesOrgContext() {
	ctx := context.Background()
	store := suite.apiSuite.Server.ServiceContainer().Store
	queries := store.(*db.SQLStore).Queries

	// Create a test user
	user := testutil.CreateDBUser(suite.T(), queries, func(params *db.CreateUserWithPhoneParams) {
		params.DisplayName = "Test User with Org"
		params.Phone = testutil.ToPtr("+1234567891")
		params.PhoneVerifiedAt = testutil.ToPtr(time.Now())
	})
	userID := user.ID

	// Create a test organization
	org := testutil.CreateDBOrganization(suite.T(), queries, func(params *db.CreateOrganizationParams) {
		params.Name = "Test Organization 2"
		params.Status = "active"
		params.OwnerUserID = userID
	})
	orgID := org.ID

	// Add user to organization
	_, err := store.AddUserToOrganization(ctx, db.AddUserToOrganizationParams{
		UserID:         userID,
		OrganizationID: orgID,
		Role:           authorization.OrgRoleOwner,
	})
	require.NoError(suite.T(), err)

	// Generate enhanced refresh token with organization context
	orgContext := &token.OrgContext{
		ID:   orgID.String(),
		Role: authorization.OrgRoleOwner,
	}
	refreshToken, err := token.GenerateEnhancedRefreshToken(ctx, store, userID, orgContext)
	require.NoError(suite.T(), err)

	// Now refresh the token
	refreshReq := map[string]string{
		"refresh_token": refreshToken,
	}

	refreshResp, err := suite.apiSuite.MakeRequest(
		"POST",
		"/api/v1/authn/token/refresh",
		refreshReq,
		"", // No auth token needed for refresh endpoint
	)
	require.NoError(suite.T(), err)
	require.Equal(suite.T(), http.StatusOK, refreshResp.Code)

	var tokenResp struct {
		AccessToken  string `json:"access_token"`
		RefreshToken string `json:"refresh_token"`
		TokenType    string `json:"token_type"`
	}
	err = json.Unmarshal(refreshResp.Body.Bytes(), &tokenResp)
	require.NoError(suite.T(), err)

	// Verify the refreshed access token still has organization context
	config, err := token.GetJWTConfig()
	require.NoError(suite.T(), err)

	refreshedClaims, err := token.ParseEnhancedToken(tokenResp.AccessToken, config.AccessTokenSecret)
	require.NoError(suite.T(), err)
	assert.NotNil(suite.T(), refreshedClaims.ActiveOrg, "Enhanced refresh token should preserve org context")
	assert.Equal(suite.T(), orgID.String(), refreshedClaims.ActiveOrg.ID)
	assert.Equal(suite.T(), authorization.OrgRoleOwner, refreshedClaims.ActiveOrg.Role)

	// Verify the new refresh token also has organization context
	refreshClaims, err := token.ParseRefreshTokenWithBackwardCompatibility(tokenResp.RefreshToken, config.RefreshTokenSecret)
	require.NoError(suite.T(), err)
	assert.NotNil(suite.T(), refreshClaims.ActiveOrg, "New refresh token should also have org context")
	assert.Equal(suite.T(), orgID.String(), refreshClaims.ActiveOrg.ID)
	assert.Equal(suite.T(), authorization.OrgRoleOwner, refreshClaims.ActiveOrg.Role)
}

// TestRefreshTokenOrgContextRevoked tests that organization context is removed if user is no longer a member
func (suite *RefreshTokenOrgContextTestSuite) TestRefreshTokenOrgContextRevoked() {
	ctx := context.Background()
	store := suite.apiSuite.Server.ServiceContainer().Store
	queries := store.(*db.SQLStore).Queries

	// Create a test user
	user := testutil.CreateDBUser(suite.T(), queries, func(params *db.CreateUserWithPhoneParams) {
		params.DisplayName = "Test User Revoked"
		params.Phone = testutil.ToPtr("+1234567892")
		params.PhoneVerifiedAt = testutil.ToPtr(time.Now())
	})
	userID := user.ID

	// Create a test organization
	org := testutil.CreateDBOrganization(suite.T(), queries, func(params *db.CreateOrganizationParams) {
		params.Name = "Test Organization 3"
		params.Status = "active"
		params.OwnerUserID = userID
	})
	orgID := org.ID

	// Add user to organization
	_, err := store.AddUserToOrganization(ctx, db.AddUserToOrganizationParams{
		UserID:         userID,
		OrganizationID: orgID,
		Role:           authorization.OrgRoleMember,
	})
	require.NoError(suite.T(), err)

	// Generate enhanced refresh token with organization context
	orgContext := &token.OrgContext{
		ID:   orgID.String(),
		Role: authorization.OrgRoleMember,
	}
	refreshToken, err := token.GenerateEnhancedRefreshToken(ctx, store, userID, orgContext)
	require.NoError(suite.T(), err)

	// Remove user from organization
	err = store.RemoveUserFromOrganization(ctx, db.RemoveUserFromOrganizationParams{
		UserID:         userID,
		OrganizationID: orgID,
	})
	require.NoError(suite.T(), err)

	// Now refresh the token
	refreshReq := map[string]string{
		"refresh_token": refreshToken,
	}

	refreshResp, err := suite.apiSuite.MakeRequest(
		"POST",
		"/api/v1/authn/token/refresh",
		refreshReq,
		"", // No auth token needed for refresh endpoint
	)
	require.NoError(suite.T(), err)
	require.Equal(suite.T(), http.StatusOK, refreshResp.Code)

	var tokenResp struct {
		AccessToken  string `json:"access_token"`
		RefreshToken string `json:"refresh_token"`
		TokenType    string `json:"token_type"`
	}
	err = json.Unmarshal(refreshResp.Body.Bytes(), &tokenResp)
	require.NoError(suite.T(), err)

	// Verify the refreshed access token does not have organization context
	config, err := token.GetJWTConfig()
	require.NoError(suite.T(), err)

	refreshedClaims, err := token.ParseEnhancedToken(tokenResp.AccessToken, config.AccessTokenSecret)
	require.NoError(suite.T(), err)
	assert.Nil(suite.T(), refreshedClaims.ActiveOrg, "Access token should not have org context after user removed from org")

	// Verify the new refresh token also doesn't have organization context
	refreshClaims, err := token.ParseRefreshTokenWithBackwardCompatibility(tokenResp.RefreshToken, config.RefreshTokenSecret)
	require.NoError(suite.T(), err)
	assert.Nil(suite.T(), refreshClaims.ActiveOrg, "New refresh token should not have org context after user removed from org")
}
