package integration

import (
	"context"
	"sync"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/payloads"
	"Membership-SAAS-System-Backend/internal/services/registration"
	"Membership-SAAS-System-Backend/internal/testutil"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// RegistrationServiceIntegrationTestSuite provides comprehensive integration testing for registration service
type RegistrationServiceIntegrationTestSuite struct {
	suite.Suite
	testDB  *testutil.TestDatabase
	store   db.Store
	ctx     context.Context
	service registration.RegistrationService

	// Test entities
	testOrg   db.Organization
	testUser  db.User
	testAdmin db.User
	testEvent db.Event
}

// SetupSuite runs once before all tests in the suite
func (suite *RegistrationServiceIntegrationTestSuite) SetupSuite() {
	suite.ctx = context.Background()
	suite.testDB, suite.store = testutil.SetupTestDatabaseWithStore(suite.T())

	// Initialize registration service with real dependencies
	// For integration tests, we'll use nil for external services
	suite.service = registration.NewRegistrationService(
		suite.store,
		nil, // JobCreator
		nil, // NotificationService
		nil, // OrganizationService
	)
}

// SetupTest runs before each test
func (suite *RegistrationServiceIntegrationTestSuite) SetupTest() {
	// Clean database state before each test
	suite.testDB.TruncateAllTables(suite.T())

	// Create test data using test data factory
	factory := testutil.NewUniqueTestDataFactory(suite.T(), db.New(suite.testDB.DB), suite.ctx)

	// Create test data
	err := suite.store.ExecTx(suite.ctx, func(q db.Querier) error {
		// Create regular user first
		user, err := factory.CreateUniqueUser()
		if err != nil {
			return err
		}
		suite.testUser = user

		// Create admin user
		admin, err := factory.CreateUniqueUser()
		if err != nil {
			return err
		}
		suite.testAdmin = admin

		// Create organization with the admin as owner
		org, err := factory.CreateUniqueOrganization(suite.testAdmin.ID)
		if err != nil {
			return err
		}
		suite.testOrg = org

		// Create test event (in the future so registration is open)
		event, err := factory.CreateUniqueEvent(suite.testOrg.ID, suite.testAdmin.ID)
		if err != nil {
			return err
		}

		// Update event to be published
		_, err = q.UpdateEventStatus(suite.ctx, db.UpdateEventStatusParams{
			ID:     event.ID,
			Status: db.EventStatusTypePublished,
		})
		if err != nil {
			return err
		}

		// Query the full event to get all fields
		fullEvent, err := q.GetEventByID(suite.ctx, event.ID)
		if err != nil {
			return err
		}
		suite.testEvent = fullEvent

		return nil
	})
	require.NoError(suite.T(), err)
}

// TestRegisterForMultipleEvents tests that a user can register for multiple different events
func (suite *RegistrationServiceIntegrationTestSuite) TestRegisterForMultipleEvents() {
	// Create additional events
	factory := testutil.NewUniqueTestDataFactory(suite.T(), db.New(suite.testDB.DB), suite.ctx)
	var event2, event3 db.Event
	err := suite.store.ExecTx(suite.ctx, func(q db.Querier) error {
		// Create event 2
		e2, err := factory.CreateUniqueEvent(suite.testOrg.ID, suite.testAdmin.ID)
		if err != nil {
			return err
		}
		// Update to published
		_, err = q.UpdateEventStatus(suite.ctx, db.UpdateEventStatusParams{
			ID:     e2.ID,
			Status: db.EventStatusTypePublished,
		})
		if err != nil {
			return err
		}
		event2, err = q.GetEventByID(suite.ctx, e2.ID)
		if err != nil {
			return err
		}

		// Create event 3
		e3, err := factory.CreateUniqueEvent(suite.testOrg.ID, suite.testAdmin.ID)
		if err != nil {
			return err
		}
		// Update to published
		_, err = q.UpdateEventStatus(suite.ctx, db.UpdateEventStatusParams{
			ID:     e3.ID,
			Status: db.EventStatusTypePublished,
		})
		if err != nil {
			return err
		}
		event3, err = q.GetEventByID(suite.ctx, e3.ID)
		if err != nil {
			return err
		}

		return nil
	})
	require.NoError(suite.T(), err)

	// Register for first event
	resp1, err := suite.service.RegisterForEvent(suite.ctx, suite.testUser.ID, suite.testEvent.ID)
	require.NoError(suite.T(), err)
	require.NotNil(suite.T(), resp1)
	require.Equal(suite.T(), db.EventRegistrationStatusTypeRegistered, resp1.Status)
	require.Equal(suite.T(), suite.testEvent.ID, resp1.EventID)
	require.Equal(suite.T(), suite.testUser.ID, resp1.UserID)

	// Register for second event - this should succeed
	resp2, err := suite.service.RegisterForEvent(suite.ctx, suite.testUser.ID, event2.ID)
	require.NoError(suite.T(), err)
	require.NotNil(suite.T(), resp2)
	require.Equal(suite.T(), db.EventRegistrationStatusTypeRegistered, resp2.Status)
	require.Equal(suite.T(), event2.ID, resp2.EventID)
	require.Equal(suite.T(), suite.testUser.ID, resp2.UserID)

	// Register for third event - this should also succeed
	resp3, err := suite.service.RegisterForEvent(suite.ctx, suite.testUser.ID, event3.ID)
	require.NoError(suite.T(), err)
	require.NotNil(suite.T(), resp3)
	require.Equal(suite.T(), db.EventRegistrationStatusTypeRegistered, resp3.Status)
	require.Equal(suite.T(), event3.ID, resp3.EventID)
	require.Equal(suite.T(), suite.testUser.ID, resp3.UserID)

	// Verify all registrations exist
	reg1, err := suite.service.GetRegistration(suite.ctx, suite.testUser.ID, suite.testEvent.ID)
	require.NoError(suite.T(), err)
	require.NotNil(suite.T(), reg1)

	reg2, err := suite.service.GetRegistration(suite.ctx, suite.testUser.ID, event2.ID)
	require.NoError(suite.T(), err)
	require.NotNil(suite.T(), reg2)

	reg3, err := suite.service.GetRegistration(suite.ctx, suite.testUser.ID, event3.ID)
	require.NoError(suite.T(), err)
	require.NotNil(suite.T(), reg3)
}

// TestPreventDuplicateRegistration tests that duplicate registration for the same event is prevented
func (suite *RegistrationServiceIntegrationTestSuite) TestPreventDuplicateRegistration() {
	// First registration should succeed
	resp1, err := suite.service.RegisterForEvent(suite.ctx, suite.testUser.ID, suite.testEvent.ID)
	require.NoError(suite.T(), err)
	require.NotNil(suite.T(), resp1)
	require.Equal(suite.T(), db.EventRegistrationStatusTypeRegistered, resp1.Status)

	// Second registration for the same event should fail
	resp2, err := suite.service.RegisterForEvent(suite.ctx, suite.testUser.ID, suite.testEvent.ID)
	require.Error(suite.T(), err)
	require.Nil(suite.T(), resp2)
	require.Equal(suite.T(), registration.ErrAlreadyRegistered, err)
}

// TestConcurrentRegistrationAttempts tests handling of concurrent registration attempts
func (suite *RegistrationServiceIntegrationTestSuite) TestConcurrentRegistrationAttempts() {
	// Create multiple users for concurrent testing
	factory := testutil.NewUniqueTestDataFactory(suite.T(), db.New(suite.testDB.DB), suite.ctx)
	users := make([]db.User, 5)
	for i := 0; i < 5; i++ {
		user, err := factory.CreateUniqueUser()
		require.NoError(suite.T(), err)
		users[i] = user
	}

	// Run concurrent registrations
	var wg sync.WaitGroup
	errors := make([]error, len(users))
	responses := make([]*payloads.EventRegistrationResponse, len(users))

	for i, user := range users {
		wg.Add(1)
		go func(idx int, u db.User) {
			defer wg.Done()
			resp, err := suite.service.RegisterForEvent(suite.ctx, u.ID, suite.testEvent.ID)
			errors[idx] = err
			responses[idx] = resp
		}(i, user)
	}

	wg.Wait()

	// All registrations should succeed (different users)
	for i, err := range errors {
		require.NoError(suite.T(), err, "User %d registration failed", i)
		require.NotNil(suite.T(), responses[i], "User %d response is nil", i)
		require.Equal(suite.T(), db.EventRegistrationStatusTypeRegistered, responses[i].Status)
	}

	// Now test concurrent registration attempts by the same user for different events
	// Create multiple events
	events := make([]db.Event, 3)
	err := suite.store.ExecTx(suite.ctx, func(q db.Querier) error {
		for i := 0; i < 3; i++ {
			event, err := factory.CreateUniqueEvent(suite.testOrg.ID, suite.testAdmin.ID)
			if err != nil {
				return err
			}
			// Update to published
			_, err = q.UpdateEventStatus(suite.ctx, db.UpdateEventStatusParams{
				ID:     event.ID,
				Status: db.EventStatusTypePublished,
			})
			if err != nil {
				return err
			}
			fullEvent, err := q.GetEventByID(suite.ctx, event.ID)
			if err != nil {
				return err
			}
			events[i] = fullEvent
		}
		return nil
	})
	require.NoError(suite.T(), err)

	// Single user registering for multiple events concurrently
	wg = sync.WaitGroup{}
	concurrentErrors := make([]error, len(events))
	concurrentResponses := make([]*payloads.EventRegistrationResponse, len(events))

	for i, event := range events {
		wg.Add(1)
		go func(idx int, e db.Event) {
			defer wg.Done()
			resp, err := suite.service.RegisterForEvent(suite.ctx, suite.testUser.ID, e.ID)
			concurrentErrors[idx] = err
			concurrentResponses[idx] = resp
		}(i, event)
	}

	wg.Wait()

	// All should succeed (different events)
	for i, err := range concurrentErrors {
		require.NoError(suite.T(), err, "Event %d registration failed", i)
		require.NotNil(suite.T(), concurrentResponses[i], "Event %d response is nil", i)
		require.Equal(suite.T(), db.EventRegistrationStatusTypeRegistered, concurrentResponses[i].Status)
	}

	// Test concurrent duplicate registration attempts (same user, same event)
	duplicateEvent := events[0]
	wg = sync.WaitGroup{}
	duplicateAttempts := 5
	duplicateErrors := make([]error, duplicateAttempts)

	for i := 0; i < duplicateAttempts; i++ {
		wg.Add(1)
		go func(idx int) {
			defer wg.Done()
			_, err := suite.service.RegisterForEvent(suite.ctx, suite.testUser.ID, duplicateEvent.ID)
			duplicateErrors[idx] = err
		}(i)
	}

	wg.Wait()

	// All attempts should fail with ErrAlreadyRegistered
	for i, err := range duplicateErrors {
		require.Error(suite.T(), err, "Duplicate attempt %d should have failed", i)
		require.Equal(suite.T(), registration.ErrAlreadyRegistered, err, "Duplicate attempt %d has wrong error", i)
	}
}

// TestRegistrationWithCapacityLimits tests registration behavior with capacity limits
func (suite *RegistrationServiceIntegrationTestSuite) TestRegistrationWithCapacityLimits() {
	// Create an event with very limited capacity
	factory := testutil.NewUniqueTestDataFactory(suite.T(), db.New(suite.testDB.DB), suite.ctx)
	var limitedEvent db.Event
	err := suite.store.ExecTx(suite.ctx, func(q db.Querier) error {
		startTime := time.Now().Add(24 * time.Hour)
		endTime := startTime.Add(2 * time.Hour)
		participantLimit := int32(2) // Only 2 spots
		waitlistLimit := int32(2)    // 2 waitlist spots

		event, err := q.CreateEvent(suite.ctx, db.CreateEventParams{
			OrganizationID:                  suite.testOrg.ID,
			Title:                           "Limited Capacity Event",
			DescriptionContent:              []byte(`{"type": "doc", "content": []}`),
			LocationType:                    db.EventLocationTypePhysical,
			LocationFullAddress:             &[]string{"789 Limited St"}[0],
			StartTime:                       startTime,
			EndTime:                         endTime,
			ParticipantLimit:                &participantLimit,
			WaitlistLimit:                   &waitlistLimit,
			Status:                          db.EventStatusTypePublished,
			RequiresApprovalForRegistration: false,
			CreatedByUserID:                 suite.testAdmin.ID,
		})
		if err != nil {
			return err
		}
		limitedEvent, err = q.GetEventByID(suite.ctx, event.ID)
		return err
	})
	require.NoError(suite.T(), err)

	// Create users
	users := make([]db.User, 4)
	for i := 0; i < 4; i++ {
		user, err := factory.CreateUniqueUser()
		require.NoError(suite.T(), err)
		users[i] = user
	}

	// First 2 users should be registered
	for i := 0; i < 2; i++ {
		resp, err := suite.service.RegisterForEvent(suite.ctx, users[i].ID, limitedEvent.ID)
		require.NoError(suite.T(), err)
		require.NotNil(suite.T(), resp)
		require.Equal(suite.T(), db.EventRegistrationStatusTypeRegistered, resp.Status)
	}

	// Third user should be waitlisted
	resp3, err := suite.service.RegisterForEvent(suite.ctx, users[2].ID, limitedEvent.ID)
	require.NoError(suite.T(), err)
	require.NotNil(suite.T(), resp3)
	require.Equal(suite.T(), db.EventRegistrationStatusTypeWaitlisted, resp3.Status)

	// Fourth user should also succeed and be waitlisted (we don't enforce waitlist limit in current implementation)
	resp4, err := suite.service.RegisterForEvent(suite.ctx, users[3].ID, limitedEvent.ID)
	require.NoError(suite.T(), err)
	require.NotNil(suite.T(), resp4)
	require.Equal(suite.T(), db.EventRegistrationStatusTypeWaitlisted, resp4.Status)
}

// TestReRegistrationAfterCancellation tests that users can re-register after cancelling
func (suite *RegistrationServiceIntegrationTestSuite) TestReRegistrationAfterCancellation() {
	// Create an event that's more than 24 hours away
	var futureEvent db.Event
	err := suite.store.ExecTx(suite.ctx, func(q db.Querier) error {
		startTime := time.Now().Add(48 * time.Hour) // 48 hours away
		endTime := startTime.Add(2 * time.Hour)
		participantLimit := int32(100)

		event, err := q.CreateEvent(suite.ctx, db.CreateEventParams{
			OrganizationID:                  suite.testOrg.ID,
			Title:                           "Future Event for Re-registration Test",
			DescriptionContent:              []byte(`{"type": "doc", "content": []}`),
			LocationType:                    db.EventLocationTypePhysical,
			LocationFullAddress:             &[]string{"123 Future St"}[0],
			StartTime:                       startTime,
			EndTime:                         endTime,
			ParticipantLimit:                &participantLimit,
			Status:                          db.EventStatusTypePublished,
			RequiresApprovalForRegistration: false,
			CreatedByUserID:                 suite.testAdmin.ID,
		})
		if err != nil {
			return err
		}
		futureEvent, err = q.GetEventByID(suite.ctx, event.ID)
		return err
	})
	require.NoError(suite.T(), err)

	// Register for event
	resp1, err := suite.service.RegisterForEvent(suite.ctx, suite.testUser.ID, futureEvent.ID)
	require.NoError(suite.T(), err)
	require.NotNil(suite.T(), resp1)
	require.Equal(suite.T(), db.EventRegistrationStatusTypeRegistered, resp1.Status)

	// Cancel registration
	cancelResp, err := suite.service.CancelRegistration(suite.ctx, suite.testUser.ID, resp1.ID)
	require.NoError(suite.T(), err)
	require.NotNil(suite.T(), cancelResp)
	require.Equal(suite.T(), db.EventRegistrationStatusTypeCancelledByUser, cancelResp.Status)

	// Re-register for the same event - should succeed
	resp2, err := suite.service.RegisterForEvent(suite.ctx, suite.testUser.ID, futureEvent.ID)
	require.NoError(suite.T(), err)
	require.NotNil(suite.T(), resp2)
	require.Equal(suite.T(), db.EventRegistrationStatusTypeRegistered, resp2.Status)

	// The registration ID should be the same (we update the existing record)
	require.Equal(suite.T(), resp1.ID, resp2.ID)
}

// TestRegistrationForPastEvent tests that registration is prevented for past events
func (suite *RegistrationServiceIntegrationTestSuite) TestRegistrationForPastEvent() {
	// Create a past event
	var pastEvent db.Event
	err := suite.store.ExecTx(suite.ctx, func(q db.Querier) error {
		startTime := time.Now().Add(-24 * time.Hour) // Yesterday
		endTime := startTime.Add(2 * time.Hour)
		participantLimit := int32(100)

		event, err := q.CreateEvent(suite.ctx, db.CreateEventParams{
			OrganizationID:                  suite.testOrg.ID,
			Title:                           "Past Event",
			DescriptionContent:              []byte(`{"type": "doc", "content": []}`),
			LocationType:                    db.EventLocationTypePhysical,
			LocationFullAddress:             &[]string{"123 Past St"}[0],
			StartTime:                       startTime,
			EndTime:                         endTime,
			ParticipantLimit:                &participantLimit,
			Status:                          db.EventStatusTypePublished,
			RequiresApprovalForRegistration: false,
			CreatedByUserID:                 suite.testAdmin.ID,
		})
		if err != nil {
			return err
		}
		pastEvent, err = q.GetEventByID(suite.ctx, event.ID)
		return err
	})
	require.NoError(suite.T(), err)

	// Attempt to register for past event should fail
	resp, err := suite.service.RegisterForEvent(suite.ctx, suite.testUser.ID, pastEvent.ID)
	require.Error(suite.T(), err)
	require.Nil(suite.T(), resp)
	require.Equal(suite.T(), registration.ErrRegistrationClosed, err)
}

// TestRegistrationForUnpublishedEvent tests that registration is prevented for unpublished events
func (suite *RegistrationServiceIntegrationTestSuite) TestRegistrationForUnpublishedEvent() {
	// Create a draft event
	var draftEvent db.Event
	err := suite.store.ExecTx(suite.ctx, func(q db.Querier) error {
		startTime := time.Now().Add(24 * time.Hour)
		endTime := startTime.Add(2 * time.Hour)
		participantLimit := int32(100)

		event, err := q.CreateEvent(suite.ctx, db.CreateEventParams{
			OrganizationID:                  suite.testOrg.ID,
			Title:                           "Draft Event",
			DescriptionContent:              []byte(`{"type": "doc", "content": []}`),
			LocationType:                    db.EventLocationTypePhysical,
			LocationFullAddress:             &[]string{"123 Draft St"}[0],
			StartTime:                       startTime,
			EndTime:                         endTime,
			ParticipantLimit:                &participantLimit,
			Status:                          db.EventStatusTypeDraft,
			RequiresApprovalForRegistration: false,
			CreatedByUserID:                 suite.testAdmin.ID,
		})
		if err != nil {
			return err
		}
		draftEvent, err = q.GetEventByID(suite.ctx, event.ID)
		return err
	})
	require.NoError(suite.T(), err)

	// Attempt to register for draft event should fail
	resp, err := suite.service.RegisterForEvent(suite.ctx, suite.testUser.ID, draftEvent.ID)
	require.Error(suite.T(), err)
	require.Nil(suite.T(), resp)
	require.Equal(suite.T(), registration.ErrRegistrationClosed, err)
}

// TestGetRegistrationByID tests retrieving a registration by its ID
func (suite *RegistrationServiceIntegrationTestSuite) TestGetRegistrationByID() {
	// Register for event
	resp, err := suite.service.RegisterForEvent(suite.ctx, suite.testUser.ID, suite.testEvent.ID)
	require.NoError(suite.T(), err)
	require.NotNil(suite.T(), resp)

	// Get registration by ID
	fetchedReg, err := suite.service.GetRegistrationByID(suite.ctx, suite.testUser.ID, resp.ID)
	require.NoError(suite.T(), err)
	require.NotNil(suite.T(), fetchedReg)
	require.Equal(suite.T(), resp.ID, fetchedReg.ID)
	require.Equal(suite.T(), resp.EventID, fetchedReg.EventID)
	require.Equal(suite.T(), resp.UserID, fetchedReg.UserID)
	require.Equal(suite.T(), resp.Status, fetchedReg.Status)

	// Try to get someone else's registration - should fail
	factory := testutil.NewUniqueTestDataFactory(suite.T(), db.New(suite.testDB.DB), suite.ctx)
	otherUser, err := factory.CreateUniqueUser()
	require.NoError(suite.T(), err)

	fetchedReg2, err := suite.service.GetRegistrationByID(suite.ctx, otherUser.ID, resp.ID)
	require.Error(suite.T(), err)
	require.Nil(suite.T(), fetchedReg2)
	require.Equal(suite.T(), registration.ErrRegistrationNotFound, err)
}

// TestCancelRegistrationBeforeDeadline tests cancelling registration before the deadline
func (suite *RegistrationServiceIntegrationTestSuite) TestCancelRegistrationBeforeDeadline() {
	// Create an event that's more than 24 hours away (48 hours)
	var futureEvent db.Event
	err := suite.store.ExecTx(suite.ctx, func(q db.Querier) error {
		startTime := time.Now().Add(48 * time.Hour) // 48 hours away
		endTime := startTime.Add(2 * time.Hour)
		participantLimit := int32(100)

		event, err := q.CreateEvent(suite.ctx, db.CreateEventParams{
			OrganizationID:                  suite.testOrg.ID,
			Title:                           "Future Event for Cancellation",
			DescriptionContent:              []byte(`{"type": "doc", "content": []}`),
			LocationType:                    db.EventLocationTypePhysical,
			LocationFullAddress:             &[]string{"123 Future St"}[0],
			StartTime:                       startTime,
			EndTime:                         endTime,
			ParticipantLimit:                &participantLimit,
			Status:                          db.EventStatusTypePublished,
			RequiresApprovalForRegistration: false,
			CreatedByUserID:                 suite.testAdmin.ID,
		})
		if err != nil {
			return err
		}
		futureEvent, err = q.GetEventByID(suite.ctx, event.ID)
		return err
	})
	require.NoError(suite.T(), err)

	// Register for event
	resp, err := suite.service.RegisterForEvent(suite.ctx, suite.testUser.ID, futureEvent.ID)
	require.NoError(suite.T(), err)
	require.NotNil(suite.T(), resp)
	require.Equal(suite.T(), db.EventRegistrationStatusTypeRegistered, resp.Status)

	// Cancel registration (event is 48 hours away, so cancellation should be allowed)
	cancelResp, err := suite.service.CancelRegistration(suite.ctx, suite.testUser.ID, resp.ID)
	require.NoError(suite.T(), err)
	require.NotNil(suite.T(), cancelResp)
	require.Equal(suite.T(), db.EventRegistrationStatusTypeCancelledByUser, cancelResp.Status)
	require.Equal(suite.T(), resp.ID, cancelResp.ID)

	// Verify registration is cancelled
	reg, err := suite.service.GetRegistration(suite.ctx, suite.testUser.ID, futureEvent.ID)
	require.NoError(suite.T(), err)
	require.Equal(suite.T(), db.EventRegistrationStatusTypeCancelledByUser, reg.Status)
}

// TestCancelRegistrationAfterDeadline tests that cancellation is prevented after the deadline
func (suite *RegistrationServiceIntegrationTestSuite) TestCancelRegistrationAfterDeadline() {
	// Create an event starting in 12 hours (past the 24-hour cancellation deadline)
	var soonEvent db.Event
	err := suite.store.ExecTx(suite.ctx, func(q db.Querier) error {
		startTime := time.Now().Add(12 * time.Hour)
		endTime := startTime.Add(2 * time.Hour)
		participantLimit := int32(100)

		event, err := q.CreateEvent(suite.ctx, db.CreateEventParams{
			OrganizationID:                  suite.testOrg.ID,
			Title:                           "Soon Event",
			DescriptionContent:              []byte(`{"type": "doc", "content": []}`),
			LocationType:                    db.EventLocationTypePhysical,
			LocationFullAddress:             &[]string{"123 Soon St"}[0],
			StartTime:                       startTime,
			EndTime:                         endTime,
			ParticipantLimit:                &participantLimit,
			Status:                          db.EventStatusTypePublished,
			RequiresApprovalForRegistration: false,
			CreatedByUserID:                 suite.testAdmin.ID,
		})
		if err != nil {
			return err
		}
		soonEvent, err = q.GetEventByID(suite.ctx, event.ID)
		return err
	})
	require.NoError(suite.T(), err)

	// Register for event
	resp, err := suite.service.RegisterForEvent(suite.ctx, suite.testUser.ID, soonEvent.ID)
	require.NoError(suite.T(), err)
	require.NotNil(suite.T(), resp)

	// Attempt to cancel - should fail
	cancelResp, err := suite.service.CancelRegistration(suite.ctx, suite.testUser.ID, resp.ID)
	require.Error(suite.T(), err)
	require.Nil(suite.T(), cancelResp)
	require.Equal(suite.T(), registration.ErrRegistrationNotCancellable, err)
}

// TestDoubleCancel tests that attempting to cancel an already cancelled registration fails
func (suite *RegistrationServiceIntegrationTestSuite) TestDoubleCancel() {
	// Create an event that's more than 24 hours away
	var futureEvent db.Event
	err := suite.store.ExecTx(suite.ctx, func(q db.Querier) error {
		startTime := time.Now().Add(48 * time.Hour) // 48 hours away
		endTime := startTime.Add(2 * time.Hour)
		participantLimit := int32(100)

		event, err := q.CreateEvent(suite.ctx, db.CreateEventParams{
			OrganizationID:                  suite.testOrg.ID,
			Title:                           "Future Event for Double Cancel Test",
			DescriptionContent:              []byte(`{"type": "doc", "content": []}`),
			LocationType:                    db.EventLocationTypePhysical,
			LocationFullAddress:             &[]string{"123 Future St"}[0],
			StartTime:                       startTime,
			EndTime:                         endTime,
			ParticipantLimit:                &participantLimit,
			Status:                          db.EventStatusTypePublished,
			RequiresApprovalForRegistration: false,
			CreatedByUserID:                 suite.testAdmin.ID,
		})
		if err != nil {
			return err
		}
		futureEvent, err = q.GetEventByID(suite.ctx, event.ID)
		return err
	})
	require.NoError(suite.T(), err)

	// Register for event
	resp, err := suite.service.RegisterForEvent(suite.ctx, suite.testUser.ID, futureEvent.ID)
	require.NoError(suite.T(), err)
	require.NotNil(suite.T(), resp)

	// Cancel registration
	cancelResp1, err := suite.service.CancelRegistration(suite.ctx, suite.testUser.ID, resp.ID)
	require.NoError(suite.T(), err)
	require.NotNil(suite.T(), cancelResp1)
	require.Equal(suite.T(), db.EventRegistrationStatusTypeCancelledByUser, cancelResp1.Status)

	// Attempt to cancel again - should fail
	cancelResp2, err := suite.service.CancelRegistration(suite.ctx, suite.testUser.ID, resp.ID)
	require.Error(suite.T(), err)
	require.Nil(suite.T(), cancelResp2)
	require.Equal(suite.T(), registration.ErrRegistrationAlreadyCancelled, err)
}

// TearDownSuite runs once after all tests in the suite
func (suite *RegistrationServiceIntegrationTestSuite) TearDownSuite() {
	suite.testDB.Cleanup()
}

// Run the test suite
func TestRegistrationServiceIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(RegistrationServiceIntegrationTestSuite))
}
