//go:build skip
// +build skip

// TODO: Enable this test when audit service implementation is complete

package integration

import (
	"context"
	"fmt"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/services"
	"Membership-SAAS-System-Backend/internal/testutil"
	"Membership-SAAS-System-Backend/pkg/logger"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

type SecurityAuditLoggingTestSuite struct {
	testutil.BaseServiceTestSuite
	auditService services.AuditServiceInterface
	logger       *logger.SecurityAuditLogger
	testOrg      db.Organization
	testUser     db.User
}

func TestSecurityAuditLoggingTestSuite(t *testing.T) {
	suite.Run(t, new(SecurityAuditLoggingTestSuite))
}

func (s *SecurityAuditLoggingTestSuite) SetupSuite() {
	s.BaseServiceTestSuite.SetupSuite()

	// Initialize audit service and logger
	s.auditService = audit.NewAuditService(s.Store)
	s.logger = logger.NewAuditLogger()

	// Create test data
	s.testOrg = s.createTestOrganization()
	s.testUser = s.createTestUser()
}

// GDPR Compliance Tests

func (s *SecurityAuditLoggingTestSuite) TestGDPRDataAccessLogging() {
	t := s.T()
	ctx := context.Background()

	// Test logging data access events
	dataAccessEvents := []audit.DataAccessEvent{
		{
			UserID:         s.testUser.ID,
			OrganizationID: &s.testOrg.ID,
			DataType:       "personal_data",
			DataSubject:    s.testUser.ID.String(),
			AccessType:     "read",
			Purpose:        "profile_view",
			LegalBasis:     "legitimate_interest",
			IPAddress:      "*************",
			UserAgent:      "Mozilla/5.0 (Test Browser)",
			Timestamp:      time.Now(),
		},
		{
			UserID:         s.testUser.ID,
			OrganizationID: &s.testOrg.ID,
			DataType:       "contact_information",
			DataSubject:    s.testUser.ID.String(),
			AccessType:     "update",
			Purpose:        "profile_update",
			LegalBasis:     "consent",
			IPAddress:      "*************",
			UserAgent:      "Mozilla/5.0 (Test Browser)",
			Timestamp:      time.Now(),
		},
		{
			UserID:         s.testUser.ID,
			OrganizationID: &s.testOrg.ID,
			DataType:       "behavioral_data",
			DataSubject:    s.testUser.ID.String(),
			AccessType:     "analytics",
			Purpose:        "service_improvement",
			LegalBasis:     "legitimate_interest",
			IPAddress:      "*************",
			UserAgent:      "Mozilla/5.0 (Test Browser)",
			Timestamp:      time.Now(),
		},
	}

	// Log data access events
	for _, event := range dataAccessEvents {
		err := s.auditService.LogDataAccess(ctx, event)
		require.NoError(t, err)
	}

	// Test GDPR data access retrieval
	accessLogs, err := s.auditService.GetDataAccessLogs(ctx, audit.DataAccessQuery{
		DataSubject:    s.testUser.ID.String(),
		OrganizationID: &s.testOrg.ID,
		StartTime:      time.Now().Add(-1 * time.Hour),
		EndTime:        time.Now().Add(1 * time.Hour),
		Limit:          100,
	})

	require.NoError(t, err)
	require.Len(t, accessLogs, len(dataAccessEvents))

	// Verify logged data structure for GDPR compliance
	for i, log := range accessLogs {
		expectedEvent := dataAccessEvents[i]
		require.Equal(t, expectedEvent.DataType, log.DataType)
		require.Equal(t, expectedEvent.AccessType, log.AccessType)
		require.Equal(t, expectedEvent.Purpose, log.Purpose)
		require.Equal(t, expectedEvent.LegalBasis, log.LegalBasis)
		require.NotEmpty(t, log.ID)
		require.NotNil(t, log.Timestamp)
	}

	// Test data subject access request logging
	dsarEvent := audit.DSAREvent{
		RequestID:      uuid.New(),
		DataSubject:    s.testUser.ID.String(),
		OrganizationID: s.testOrg.ID,
		RequestType:    "access_request",
		Status:         "pending",
		RequestedBy:    s.testUser.ID,
		IPAddress:      "*************",
		Timestamp:      time.Now(),
	}

	err = s.auditService.LogDSAR(ctx, dsarEvent)
	require.NoError(t, err)

	// Verify DSAR log retrieval
	dsarLogs, err := s.auditService.GetDSARLogs(ctx, audit.DSARQuery{
		OrganizationID: &s.testOrg.ID,
		DataSubject:    s.testUser.ID.String(),
		StartTime:      time.Now().Add(-1 * time.Hour),
		EndTime:        time.Now().Add(1 * time.Hour),
	})

	require.NoError(t, err)
	require.Len(t, dsarLogs, 1)
	require.Equal(t, dsarEvent.RequestType, dsarLogs[0].RequestType)
}

func (s *SecurityAuditLoggingTestSuite) TestGDPRConsentLogging() {
	t := s.T()
	ctx := context.Background()

	// Test consent logging
	consentEvents := []audit.ConsentEvent{
		{
			UserID:         s.testUser.ID,
			OrganizationID: s.testOrg.ID,
			ConsentType:    "marketing_communications",
			ConsentGiven:   true,
			Purpose:        "email_marketing",
			LegalBasis:     "consent",
			ConsentMethod:  "web_form",
			IPAddress:      "*************",
			UserAgent:      "Mozilla/5.0 (Test Browser)",
			Timestamp:      time.Now(),
		},
		{
			UserID:         s.testUser.ID,
			OrganizationID: s.testOrg.ID,
			ConsentType:    "analytics_tracking",
			ConsentGiven:   true,
			Purpose:        "service_improvement",
			LegalBasis:     "consent",
			ConsentMethod:  "cookie_banner",
			IPAddress:      "*************",
			UserAgent:      "Mozilla/5.0 (Test Browser)",
			Timestamp:      time.Now(),
		},
		{
			UserID:         s.testUser.ID,
			OrganizationID: s.testOrg.ID,
			ConsentType:    "marketing_communications",
			ConsentGiven:   false, // Withdrawal
			Purpose:        "email_marketing",
			LegalBasis:     "consent_withdrawal",
			ConsentMethod:  "preference_center",
			IPAddress:      "*************",
			UserAgent:      "Mozilla/5.0 (Test Browser)",
			Timestamp:      time.Now().Add(10 * time.Minute),
		},
	}

	// Log consent events
	for _, event := range consentEvents {
		err := s.auditService.LogConsent(ctx, event)
		require.NoError(t, err)
	}

	// Retrieve consent history
	consentHistory, err := s.auditService.GetConsentHistory(ctx, audit.ConsentQuery{
		UserID:         s.testUser.ID,
		OrganizationID: &s.testOrg.ID,
		ConsentType:    "marketing_communications",
		StartTime:      time.Now().Add(-1 * time.Hour),
		EndTime:        time.Now().Add(1 * time.Hour),
	})

	require.NoError(t, err)
	require.Len(t, consentHistory, 2) // Initial consent + withdrawal

	// Verify consent tracking shows withdrawal
	currentConsent := consentHistory[len(consentHistory)-1] // Latest event
	require.False(t, currentConsent.ConsentGiven)
	require.Equal(t, "consent_withdrawal", currentConsent.LegalBasis)

	// Test getting current consent status
	consentStatus, err := s.auditService.GetCurrentConsentStatus(ctx, s.testUser.ID, s.testOrg.ID)
	require.NoError(t, err)
	require.False(t, consentStatus["marketing_communications"]) // Should be withdrawn
	require.True(t, consentStatus["analytics_tracking"])        // Should still be granted
}

func (s *SecurityAuditLoggingTestSuite) TestDataRetentionCompliance() {
	t := s.T()
	ctx := context.Background()

	// Test data retention policy logging
	retentionEvent := audit.DataRetentionEvent{
		OrganizationID:  s.testOrg.ID,
		DataType:        "user_activity_logs",
		Action:          "retention_policy_applied",
		RetentionDays:   90,
		RecordsAffected: 1000,
		ExecutedBy:      s.testUser.ID,
		Timestamp:       time.Now(),
		Details: map[string]interface{}{
			"policy_id":      uuid.New().String(),
			"trigger":        "scheduled_cleanup",
			"records_kept":   500,
			"records_purged": 500,
		},
	}

	err := s.auditService.LogDataRetention(ctx, retentionEvent)
	require.NoError(t, err)

	// Test data deletion logging for GDPR compliance
	deletionEvent := audit.DataDeletionEvent{
		UserID:         s.testUser.ID,
		OrganizationID: s.testOrg.ID,
		DeletionType:   "user_requested",
		DataTypes:      []string{"personal_data", "behavioral_data", "communication_logs"},
		RequestID:      uuid.New(),
		ExecutedBy:     s.testUser.ID,
		Timestamp:      time.Now(),
		Details: map[string]interface{}{
			"verification_method": "email_confirmation",
			"grace_period_days":   30,
			"backup_retention":    false,
		},
	}

	err = s.auditService.LogDataDeletion(ctx, deletionEvent)
	require.NoError(t, err)

	// Verify retention and deletion logs
	retentionLogs, err := s.auditService.GetDataRetentionLogs(ctx, audit.DataRetentionQuery{
		OrganizationID: &s.testOrg.ID,
		StartTime:      time.Now().Add(-1 * time.Hour),
		EndTime:        time.Now().Add(1 * time.Hour),
	})

	require.NoError(t, err)
	require.Len(t, retentionLogs, 1)
	require.Equal(t, 90, retentionLogs[0].RetentionDays)
	require.Equal(t, 1000, retentionLogs[0].RecordsAffected)

	deletionLogs, err := s.auditService.GetDataDeletionLogs(ctx, audit.DataDeletionQuery{
		OrganizationID: &s.testOrg.ID,
		DataSubject:    s.testUser.ID.String(),
		StartTime:      time.Now().Add(-1 * time.Hour),
		EndTime:        time.Now().Add(1 * time.Hour),
	})

	require.NoError(t, err)
	require.Len(t, deletionLogs, 1)
	require.Equal(t, "user_requested", deletionLogs[0].DeletionType)
	require.Contains(t, deletionLogs[0].DataTypes, "personal_data")
}

// Security Event Logging Tests

func (s *SecurityAuditLoggingTestSuite) TestSecurityEventLogging() {
	t := s.T()
	ctx := context.Background()

	// Test various security events
	securityEvents := []audit.SecurityEvent{
		{
			EventType:      "authentication_failure",
			Severity:       "medium",
			UserID:         &s.testUser.ID,
			OrganizationID: &s.testOrg.ID,
			IPAddress:      "*************",
			UserAgent:      "Mozilla/5.0 (Suspicious Browser)",
			Details: map[string]interface{}{
				"reason":         "invalid_password",
				"attempt_count":  3,
				"locked_account": false,
			},
			Timestamp: time.Now(),
		},
		{
			EventType:      "privilege_escalation_attempt",
			Severity:       "high",
			UserID:         &s.testUser.ID,
			OrganizationID: &s.testOrg.ID,
			IPAddress:      "*********",
			UserAgent:      "Automated Tool/1.0",
			Details: map[string]interface{}{
				"attempted_role": "admin",
				"current_role":   "member",
				"endpoint":       "/api/v1/admin/users",
				"blocked":        true,
			},
			Timestamp: time.Now(),
		},
		{
			EventType:      "suspicious_api_usage",
			Severity:       "high",
			UserID:         &s.testUser.ID,
			OrganizationID: &s.testOrg.ID,
			IPAddress:      "***********",
			UserAgent:      "Bot/2.0",
			Details: map[string]interface{}{
				"requests_per_minute": 1000,
				"endpoints_accessed":  50,
				"rate_limited":        true,
				"patterns":            []string{"data_scraping", "brute_force"},
			},
			Timestamp: time.Now(),
		},
		{
			EventType:      "data_export_request",
			Severity:       "medium",
			UserID:         &s.testUser.ID,
			OrganizationID: &s.testOrg.ID,
			IPAddress:      "*************",
			UserAgent:      "Mozilla/5.0 (Normal Browser)",
			Details: map[string]interface{}{
				"export_type":   "user_data",
				"records_count": 500,
				"file_size_mb":  2.5,
				"approved_by":   s.testUser.ID.String(),
			},
			Timestamp: time.Now(),
		},
	}

	// Log security events
	for _, event := range securityEvents {
		err := s.auditService.LogSecurityEvent(ctx, event)
		require.NoError(t, err)
	}

	// Test retrieving security events with various filters
	allEvents, err := s.auditService.GetSecurityEvents(ctx, audit.SecurityEventQuery{
		OrganizationID: &s.testOrg.ID,
		StartTime:      time.Now().Add(-1 * time.Hour),
		EndTime:        time.Now().Add(1 * time.Hour),
		Limit:          100,
	})

	require.NoError(t, err)
	require.Len(t, allEvents, len(securityEvents))

	// Test filtering by severity
	highSeverityEvents, err := s.auditService.GetSecurityEvents(ctx, audit.SecurityEventQuery{
		OrganizationID: &s.testOrg.ID,
		Severity:       "high",
		StartTime:      time.Now().Add(-1 * time.Hour),
		EndTime:        time.Now().Add(1 * time.Hour),
		Limit:          100,
	})

	require.NoError(t, err)
	require.Len(t, highSeverityEvents, 2) // privilege_escalation_attempt and suspicious_api_usage

	// Test filtering by event type
	authFailures, err := s.auditService.GetSecurityEvents(ctx, audit.SecurityEventQuery{
		OrganizationID: &s.testOrg.ID,
		EventType:      "authentication_failure",
		StartTime:      time.Now().Add(-1 * time.Hour),
		EndTime:        time.Now().Add(1 * time.Hour),
		Limit:          100,
	})

	require.NoError(t, err)
	require.Len(t, authFailures, 1)
	require.Equal(t, "authentication_failure", authFailures[0].EventType)
}

func (s *SecurityAuditLoggingTestSuite) TestSecurityIncidentTracking() {
	t := s.T()
	ctx := context.Background()

	// Test security incident lifecycle
	incident := audit.SecurityIncident{
		IncidentID:      uuid.New(),
		Type:            "data_breach_attempt",
		Severity:        "critical",
		Status:          "detected",
		OrganizationID:  s.testOrg.ID,
		DetectedBy:      s.testUser.ID,
		DetectedAt:      time.Now(),
		Description:     "Attempted unauthorized access to user data",
		AffectedSystems: []string{"user_database", "authentication_service"},
		Details: map[string]interface{}{
			"attack_vector":    "sql_injection",
			"source_ip":        "*************",
			"blocked":          true,
			"potential_impact": "high",
		},
	}

	err := s.auditService.LogSecurityIncident(ctx, incident)
	require.NoError(t, err)

	// Test incident status updates
	statusUpdates := []audit.IncidentStatusUpdate{
		{
			IncidentID: incident.IncidentID,
			Status:     "investigating",
			UpdatedBy:  s.testUser.ID,
			UpdatedAt:  time.Now().Add(5 * time.Minute),
			Notes:      "Security team notified, investigating attack vector",
		},
		{
			IncidentID: incident.IncidentID,
			Status:     "contained",
			UpdatedBy:  s.testUser.ID,
			UpdatedAt:  time.Now().Add(15 * time.Minute),
			Notes:      "SQL injection attempt blocked, no data compromised",
		},
		{
			IncidentID: incident.IncidentID,
			Status:     "resolved",
			UpdatedBy:  s.testUser.ID,
			UpdatedAt:  time.Now().Add(30 * time.Minute),
			Notes:      "Security patches applied, monitoring enhanced",
		},
	}

	for _, update := range statusUpdates {
		err := s.auditService.UpdateIncidentStatus(ctx, update)
		require.NoError(t, err)
	}

	// Retrieve incident with status history
	retrievedIncident, err := s.auditService.GetSecurityIncident(ctx, incident.IncidentID)
	require.NoError(t, err)
	require.Equal(t, incident.Type, retrievedIncident.Type)
	require.Equal(t, "resolved", retrievedIncident.Status) // Latest status

	statusHistory, err := s.auditService.GetIncidentStatusHistory(ctx, incident.IncidentID)
	require.NoError(t, err)
	require.Len(t, statusHistory, len(statusUpdates))

	// Verify status progression
	expectedStatuses := []string{"investigating", "contained", "resolved"}
	for i, status := range statusHistory {
		require.Equal(t, expectedStatuses[i], status.Status)
	}
}

// Structured Logging Tests

func (s *SecurityAuditLoggingTestSuite) TestStructuredLogging() {
	t := s.T()
	ctx := context.Background()

	// Test structured audit log entry
	auditEntry := audit.AuditLogEntry{
		EventID:        uuid.New(),
		EventType:      "user_action",
		Action:         "profile_update",
		UserID:         s.testUser.ID,
		OrganizationID: s.testOrg.ID,
		Resource:       "user_profile",
		ResourceID:     s.testUser.ID.String(),
		IPAddress:      "*************",
		UserAgent:      "Mozilla/5.0 (Test Browser)",
		Success:        true,
		Timestamp:      time.Now(),
		Metadata: map[string]interface{}{
			"fields_changed": []string{"display_name", "email"},
			"previous_values": map[string]interface{}{
				"display_name": "Old Name",
				"email":        "<EMAIL>",
			},
			"new_values": map[string]interface{}{
				"display_name": "New Name",
				"email":        "<EMAIL>",
			},
			"change_reason": "user_requested",
		},
	}

	err := s.auditService.LogAuditEvent(ctx, auditEntry)
	require.NoError(t, err)

	// Test structured query capabilities
	query := audit.AuditLogQuery{
		OrganizationID: &s.testOrg.ID,
		UserID:         &s.testUser.ID,
		Action:         "profile_update",
		Resource:       "user_profile",
		Success:        true,
		StartTime:      time.Now().Add(-1 * time.Hour),
		EndTime:        time.Now().Add(1 * time.Hour),
		Limit:          100,
	}

	auditLogs, err := s.auditService.QueryAuditLogs(ctx, query)
	require.NoError(t, err)
	require.Len(t, auditLogs, 1)

	retrievedEntry := auditLogs[0]
	require.Equal(t, auditEntry.Action, retrievedEntry.Action)
	require.Equal(t, auditEntry.Resource, retrievedEntry.Resource)
	require.True(t, retrievedEntry.Success)

	// Verify metadata structure
	require.Contains(t, retrievedEntry.Metadata, "fields_changed")
	require.Contains(t, retrievedEntry.Metadata, "previous_values")
	require.Contains(t, retrievedEntry.Metadata, "new_values")
}

func (s *SecurityAuditLoggingTestSuite) TestAuditLogIndexingPerformance() {
	t := s.T()
	ctx := context.Background()

	if testing.Short() {
		t.Skip("Skipping performance test in short mode")
	}

	// Generate large volume of audit logs for performance testing
	logCount := 10000
	batchSize := 1000

	start := time.Now()

	for batch := 0; batch < logCount/batchSize; batch++ {
		entries := make([]audit.AuditLogEntry, batchSize)
		baseTime := time.Now().Add(-time.Duration(batch) * time.Hour)

		for i := 0; i < batchSize; i++ {
			entries[i] = audit.AuditLogEntry{
				EventID:        uuid.New(),
				EventType:      "user_action",
				Action:         fmt.Sprintf("action_%d", i%10),
				UserID:         s.testUser.ID,
				OrganizationID: s.testOrg.ID,
				Resource:       fmt.Sprintf("resource_%d", i%5),
				ResourceID:     uuid.New().String(),
				IPAddress:      fmt.Sprintf("192.168.1.%d", (i%250)+1),
				UserAgent:      "Performance Test Agent",
				Success:        i%10 != 0, // 90% success rate
				Timestamp:      baseTime.Add(time.Duration(i) * time.Second),
				Metadata: map[string]interface{}{
					"batch":     batch,
					"sequence":  i,
					"test_data": true,
				},
			}
		}

		err := s.auditService.BatchLogAuditEvents(ctx, entries)
		require.NoError(t, err)
	}

	insertDuration := time.Since(start)
	t.Logf("Inserted %d audit logs in %v", logCount, insertDuration)

	// Test query performance on large dataset
	queryStart := time.Now()

	// Complex query with multiple filters
	complexQuery := audit.AuditLogQuery{
		OrganizationID: &s.testOrg.ID,
		UserID:         &s.testUser.ID,
		Success:        true,
		StartTime:      time.Now().Add(-time.Duration(logCount/batchSize+1) * time.Hour),
		EndTime:        time.Now().Add(1 * time.Hour),
		Limit:          1000,
	}

	results, err := s.auditService.QueryAuditLogs(ctx, complexQuery)
	queryDuration := time.Since(queryStart)

	require.NoError(t, err)
	require.NotEmpty(t, results)
	require.Less(t, queryDuration, 2*time.Second, "Complex query should complete within 2 seconds")

	t.Logf("Complex query returned %d results in %v", len(results), queryDuration)

	// Test aggregation performance
	aggStart := time.Now()

	stats, err := s.auditService.GetAuditStatistics(ctx, audit.AuditStatsQuery{
		OrganizationID: &s.testOrg.ID,
		StartTime:      time.Now().Add(-time.Duration(logCount/batchSize+1) * time.Hour),
		EndTime:        time.Now().Add(1 * time.Hour),
		GroupBy:        []string{"action", "success"},
	})

	aggDuration := time.Since(aggStart)

	require.NoError(t, err)
	require.NotEmpty(t, stats)
	require.Less(t, aggDuration, 3*time.Second, "Aggregation query should complete within 3 seconds")

	t.Logf("Aggregation query completed in %v", aggDuration)
}

// Compliance Reporting Tests

func (s *SecurityAuditLoggingTestSuite) TestComplianceReporting() {
	t := s.T()
	ctx := context.Background()

	// Generate compliance-relevant events
	events := []audit.ComplianceEvent{
		{
			EventType:      "data_access",
			ComplianceType: "GDPR",
			UserID:         s.testUser.ID,
			OrganizationID: s.testOrg.ID,
			Details: map[string]interface{}{
				"data_subject": s.testUser.ID.String(),
				"purpose":      "service_delivery",
				"legal_basis":  "contract",
				"data_types":   []string{"personal_data", "contact_info"},
			},
			Timestamp: time.Now(),
		},
		{
			EventType:      "consent_processing",
			ComplianceType: "GDPR",
			UserID:         s.testUser.ID,
			OrganizationID: s.testOrg.ID,
			Details: map[string]interface{}{
				"consent_types": []string{"marketing", "analytics"},
				"consent_given": true,
				"method":        "explicit_opt_in",
			},
			Timestamp: time.Now(),
		},
		{
			EventType:      "data_retention",
			ComplianceType: "SOX",
			UserID:         s.testUser.ID,
			OrganizationID: s.testOrg.ID,
			Details: map[string]interface{}{
				"retention_period": "7_years",
				"data_type":        "financial_records",
				"policy_reference": "SOX-2024-001",
			},
			Timestamp: time.Now(),
		},
	}

	// Log compliance events
	for _, event := range events {
		err := s.auditService.LogComplianceEvent(ctx, event)
		require.NoError(t, err)
	}

	// Generate compliance reports
	gdprReport, err := s.auditService.GenerateComplianceReport(ctx, audit.ComplianceReportRequest{
		ComplianceType: "GDPR",
		OrganizationID: s.testOrg.ID,
		StartDate:      time.Now().Add(-24 * time.Hour),
		EndDate:        time.Now().Add(1 * time.Hour),
		IncludeSections: []string{
			"data_access_summary",
			"consent_management",
			"data_subject_requests",
			"retention_compliance",
		},
	})

	require.NoError(t, err)
	require.NotNil(t, gdprReport)
	require.Equal(t, "GDPR", gdprReport.ComplianceType)
	require.Contains(t, gdprReport.Sections, "data_access_summary")
	require.Contains(t, gdprReport.Sections, "consent_management")

	// Verify report contains expected data
	dataAccessSection := gdprReport.Sections["data_access_summary"]
	require.NotNil(t, dataAccessSection)
	require.Greater(t, dataAccessSection["total_events"].(int), 0)

	// Test audit trail integrity
	integrity, err := s.auditService.VerifyAuditTrailIntegrity(ctx, audit.IntegrityCheckRequest{
		OrganizationID: s.testOrg.ID,
		StartTime:      time.Now().Add(-1 * time.Hour),
		EndTime:        time.Now().Add(1 * time.Hour),
		CheckLevel:     "full",
	})

	require.NoError(t, err)
	require.True(t, integrity.IsValid)
	require.Empty(t, integrity.Anomalies)
	require.Greater(t, integrity.RecordsChecked, 0)
}

// Helper methods

func (s *SecurityAuditLoggingTestSuite) createTestOrganization() db.Organization {
	org, err := s.Store.CreateOrganization(context.Background(), db.CreateOrganizationParams{
		Name:        "Audit Test Org " + uuid.New().String()[:8],
		Type:        "test",
		Description: "Organization for audit logging testing",
		Status:      "active",
	})
	require.NoError(s.T(), err)
	return org
}

func (s *SecurityAuditLoggingTestSuite) createTestUser() db.User {
	user, err := s.Store.CreateUser(context.Background(), db.CreateUserParams{
		PhoneNumber: "+1234567" + uuid.New().String()[:3],
		DisplayName: "Audit Test User " + uuid.New().String()[:8],
		Language:    "en",
	})
	require.NoError(s.T(), err)
	return user
}
