// Package integration provides test server factory for consistent test initialization
package integration

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"os"
	"strconv"
	"testing"

	"Membership-SAAS-System-Backend/internal/config"
	"Membership-SAAS-System-Backend/internal/server"

	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/require"
)

// TestServer wraps the application server with test utilities
type TestServer struct {
	// AppServer is the actual application server instance
	AppServer *server.Server
	// HTTPServer is the httptest server for making HTTP requests
	HTTPServer *httptest.Server
	// URL is the base URL of the test server
	URL string
	// Config is the configuration used to create the server
	Config *config.Config
	// Echo provides direct access to the Echo instance for testing
	Echo *echo.Echo
	// dbConnString is the test database connection string
	dbConnString string
	// cleanup functions to run when server is closed
	cleanupFuncs []func()
}

// TestServerOption defines a function that modifies server configuration
type TestServerOption func(t *testing.T)

// WithRateLimiting enables rate limiting with specified parameters
func WithRateLimiting(enabled bool, ratePerMinute int) TestServerOption {
	return func(t *testing.T) {
		// Set environment variables BEFORE config.Load() is called
		t.Setenv("RATE_LIMIT_ENABLED", boolToString(enabled))
		t.Setenv("RATE_LIMIT_SKIP_IN_TESTS", boolToString(!enabled))
		t.Setenv("RATE_LIMIT_PER_MINUTE", intToString(ratePerMinute))
		t.Setenv("RATE_LIMIT_FALLBACK_RATE", intToString(ratePerMinute))
		t.Setenv("RATE_LIMIT_FALLBACK_BURST", intToString(ratePerMinute))

		// Force production mode if rate limiting is enabled
		if enabled {
			t.Setenv("GO_ENV", "production")
		}
	}
}

// WithoutRateLimiting explicitly disables rate limiting (default behavior)
func WithoutRateLimiting() TestServerOption {
	return WithRateLimiting(false, 0)
}

// WithRedis enables Redis for caching and rate limiting
func WithRedis(redisAddr string) TestServerOption {
	return func(t *testing.T) {
		t.Setenv("CACHE_ENABLED", "true")
		t.Setenv("CACHE_BACKEND", "redis")
		t.Setenv("REDIS_ADDR", redisAddr)
	}
}

// WithCORS configures CORS allowed origins
func WithCORS(origins ...string) TestServerOption {
	return func(t *testing.T) {
		originsStr := ""
		for i, origin := range origins {
			if i > 0 {
				originsStr += ","
			}
			originsStr += origin
		}
		t.Setenv("CORS_ALLOWED_ORIGINS", originsStr)
	}
}

// WithEnvironment sets the application environment
func WithEnvironment(env string) TestServerOption {
	return func(t *testing.T) {
		t.Setenv("GO_ENV", env)
	}
}

// WithTrustedProxies configures trusted proxy IPs for rate limiting
func WithTrustedProxies(proxies ...string) TestServerOption {
	return func(t *testing.T) {
		proxiesStr := ""
		for i, proxy := range proxies {
			if i > 0 {
				proxiesStr += ","
			}
			proxiesStr += proxy
		}
		t.Setenv("TRUSTED_PROXIES", proxiesStr)
	}
}

// NewTestServer creates a new test server with production-like initialization
func NewTestServer(t *testing.T, opts ...TestServerOption) *TestServer {
	// Save and restore environment to ensure test isolation
	originalEnv := saveEnvironment()
	t.Cleanup(func() {
		restoreEnvironment(originalEnv)
	})

	// Set up base test environment
	setupBaseTestEnvironment(t)

	// Apply all options BEFORE loading configuration
	for _, opt := range opts {
		opt(t)
	}

	// Set up test database using existing helper
	suite := SetupAPITest(t)

	// Override database URL to use test database
	t.Setenv("DATABASE_URL", suite.dbConnString)

	// Load configuration using production path - this is the key!
	cfg := config.Load()

	// Clear migrations path since SetupAPITest already ran migrations
	cfg.Database.MigrationsPath = ""

	// Create server using production initialization
	appServer, err := server.New(cfg)
	require.NoError(t, err, "Failed to create server")

	// Start HTTP test server for real connections (supports WebSockets)
	httpServer := httptest.NewServer(appServer.Echo())

	ts := &TestServer{
		AppServer:    appServer,
		HTTPServer:   httpServer,
		URL:          httpServer.URL,
		Config:       cfg,
		Echo:         appServer.Echo(),
		dbConnString: suite.dbConnString,
		cleanupFuncs: []func(){
			func() { suite.TeardownAPITest(t) },
			func() { httpServer.Close() },
		},
	}

	// Register cleanup
	t.Cleanup(ts.Close)

	return ts
}

// Close shuts down the test server and cleans up resources
func (ts *TestServer) Close() {
	// Run cleanup functions in reverse order
	for i := len(ts.cleanupFuncs) - 1; i >= 0; i-- {
		if cleanup := ts.cleanupFuncs[i]; cleanup != nil {
			cleanup()
		}
	}
}

// VerifyConfiguration provides methods to verify server configuration
func (ts *TestServer) VerifyConfiguration() ConfigVerifier {
	return ConfigVerifier{server: ts}
}

// ConfigVerifier provides configuration verification methods
type ConfigVerifier struct {
	server *TestServer
}

// RateLimitingEnabled checks if rate limiting is enabled
func (cv ConfigVerifier) RateLimitingEnabled() bool {
	// Rate limiting is considered enabled if RateLimitPerMinute > 0
	return cv.server.Config.Security.RateLimitPerMinute > 0
}

// RateLimitPerMinute returns the configured rate limit
func (cv ConfigVerifier) RateLimitPerMinute() int {
	return cv.server.Config.Security.RateLimitPerMinute
}

// CORSAllowedOrigins returns the configured CORS origins
func (cv ConfigVerifier) CORSAllowedOrigins() []string {
	return cv.server.Config.CORS.AllowedOrigins
}

// Environment returns the configured environment
func (cv ConfigVerifier) Environment() string {
	return cv.server.Config.App.Environment
}

// HasRedis checks if Redis is configured
func (cv ConfigVerifier) HasRedis() bool {
	return cv.server.Config.Cache.Enabled &&
		cv.server.Config.Cache.Backend == "redis"
}

// GetServiceContainer returns the service container for advanced testing
func (ts *TestServer) GetServiceContainer() *config.ServiceContainer {
	return ts.AppServer.ServiceContainer()
}

// MakeRequest creates and executes an HTTP request against the test server
func (ts *TestServer) MakeRequest(method, path string, body interface{}, headers map[string]string) (*httptest.ResponseRecorder, error) {
	req := createRequest(method, path, body)

	// Set headers
	if headers != nil {
		for k, v := range headers {
			req.Header.Set(k, v)
		}
	}

	rec := httptest.NewRecorder()
	ts.Echo.ServeHTTP(rec, req)

	return rec, nil
}

// Helper functions

func setupBaseTestEnvironment(t *testing.T) {
	// Set test environment flags
	t.Setenv("GO_ENV", "test")
	t.Setenv("TESTING", "true")

	// Set JWT secrets for testing
	t.Setenv("ACCESS_TOKEN_SECRET", "test-secret-key-for-testing")
	t.Setenv("REFRESH_TOKEN_SECRET", "test-refresh-secret-key-for-testing")
	t.Setenv("ACCESS_TOKEN_DURATION", "15m")
	t.Setenv("REFRESH_TOKEN_DURATION", "168h")

	// Enable mock mode for external services
	t.Setenv("MOCK_TWILIO_OTP", "true")
	t.Setenv("MOCK_TWILIO_NOTIFICATIONS", "true")
	t.Setenv("TEST_OTP_MODE", "true")

	// Default CORS for tests
	t.Setenv("CORS_ALLOWED_ORIGINS", "http://localhost:3000,http://localhost:8080")

	// Disable rate limiting by default in tests
	t.Setenv("RATE_LIMIT_ENABLED", "false")
	t.Setenv("RATE_LIMIT_SKIP_IN_TESTS", "true")
}

func boolToString(b bool) string {
	if b {
		return "true"
	}
	return "false"
}

func intToString(i int) string {
	return strconv.Itoa(i)
}

func saveEnvironment() map[string]string {
	env := make(map[string]string)
	// Save key environment variables
	keys := []string{
		"GO_ENV", "TESTING", "RATE_LIMIT_ENABLED", "RATE_LIMIT_SKIP_IN_TESTS",
		"RATE_LIMIT_PER_MINUTE", "CORS_ALLOWED_ORIGINS", "CACHE_ENABLED",
		"REDIS_ADDR", "TRUSTED_PROXIES",
	}
	for _, key := range keys {
		env[key] = os.Getenv(key)
	}
	return env
}

func restoreEnvironment(env map[string]string) {
	for key, value := range env {
		if value != "" {
			os.Setenv(key, value)
		} else {
			os.Unsetenv(key)
		}
	}
}

// createRequest is a helper to create HTTP requests
func createRequest(method, path string, body interface{}) *http.Request {
	var reqBody io.Reader

	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			panic(fmt.Sprintf("Failed to marshal request body: %v", err))
		}
		reqBody = bytes.NewBuffer(jsonBody)
	}

	req := httptest.NewRequest(method, path, reqBody)
	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	return req
}

// Example usage documentation:
//
// func TestWithRateLimiting(t *testing.T) {
//     server := NewTestServer(t, WithRateLimiting(true, 10))
//
//     // Verify configuration
//     require.True(t, server.VerifyConfiguration().RateLimitingEnabled())
//     require.Equal(t, 10, server.VerifyConfiguration().RateLimitPerMinute())
//
//     // Make requests against the server
//     rec, err := server.MakeRequest("GET", "/api/v1/health", nil, nil)
//     require.NoError(t, err)
//     require.Equal(t, 200, rec.Code)
// }
//
// func TestWebSocketConnection(t *testing.T) {
//     server := NewTestServer(t)
//
//     // Use server.URL for WebSocket connections
//     wsURL := strings.Replace(server.URL, "http", "ws", 1) + "/ws"
//
//     // Connect using websocket.Dialer
//     conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
//     require.NoError(t, err)
//     defer conn.Close()
// }
