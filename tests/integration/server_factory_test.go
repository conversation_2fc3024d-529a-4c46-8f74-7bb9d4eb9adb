package integration

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestServerFactoryBasic tests basic server creation
func TestServerFactoryBasic(t *testing.T) {
	server := NewTestServer(t)

	// Verify server was created
	assert.NotNil(t, server.AppServer)
	assert.NotNil(t, server.HTTPServer)
	assert.NotNil(t, server.Echo)
	assert.NotEmpty(t, server.URL)

	// Verify default configuration
	verifier := server.VerifyConfiguration()
	assert.False(t, verifier.RateLimitingEnabled(), "Rate limiting should be disabled by default")
	assert.Equal(t, "test", verifier.Environment())
}

// TestServerFactoryWithRateLimiting tests server with rate limiting enabled
func TestServerFactoryWithRateLimiting(t *testing.T) {
	server := NewTestServer(t, WithRateLimiting(true, 10))

	// Verify configuration
	verifier := server.VerifyConfiguration()
	assert.True(t, verifier.RateLimitingEnabled(), "Rate limiting should be enabled")
	assert.Equal(t, 10, verifier.RateLimitPerMinute())
	assert.Equal(t, "production", verifier.Environment(), "Should force production mode when rate limiting is enabled")
}

// TestServerFactoryWithCustomCORS tests CORS configuration
func TestServerFactoryWithCustomCORS(t *testing.T) {
	origins := []string{"https://example.com", "https://app.example.com"}
	server := NewTestServer(t, WithCORS(origins...))

	// Verify CORS configuration
	verifier := server.VerifyConfiguration()
	assert.Equal(t, origins, verifier.CORSAllowedOrigins())
}

// TestServerFactoryHealthEndpoint tests that server responds to requests
func TestServerFactoryHealthEndpoint(t *testing.T) {
	server := NewTestServer(t)

	// Make a request to health endpoint
	rec, err := server.MakeRequest("GET", "/health", nil, nil)
	require.NoError(t, err)

	assert.Equal(t, http.StatusOK, rec.Code)
	assert.Contains(t, rec.Body.String(), "ok")
}

// TestServerFactoryMultipleOptions tests combining multiple options
func TestServerFactoryMultipleOptions(t *testing.T) {
	server := NewTestServer(t,
		WithEnvironment("staging"),
		WithRateLimiting(true, 50),
		WithCORS("https://staging.example.com"),
		WithTrustedProxies("10.0.0.0/8", "**********/12"),
	)

	// Verify all configurations were applied
	verifier := server.VerifyConfiguration()
	assert.Equal(t, "staging", verifier.Environment(), "Environment override should work even with rate limiting")
	assert.True(t, verifier.RateLimitingEnabled())
	assert.Equal(t, 50, verifier.RateLimitPerMinute())
	assert.Equal(t, []string{"https://staging.example.com"}, verifier.CORSAllowedOrigins())
}

// TestServerFactoryIsolation tests that servers are isolated from each other
func TestServerFactoryIsolation(t *testing.T) {
	// Create first server with rate limiting
	server1 := NewTestServer(t, WithRateLimiting(true, 5))

	// Create second server without rate limiting
	server2 := NewTestServer(t, WithoutRateLimiting())

	// Verify configurations are independent
	assert.True(t, server1.VerifyConfiguration().RateLimitingEnabled())
	assert.False(t, server2.VerifyConfiguration().RateLimitingEnabled())

	// Verify they have different URLs (different instances)
	assert.NotEqual(t, server1.URL, server2.URL)
}

// TestServerFactoryServiceContainer tests access to service container
func TestServerFactoryServiceContainer(t *testing.T) {
	server := NewTestServer(t)

	container := server.GetServiceContainer()
	assert.NotNil(t, container)
	assert.NotNil(t, container.DB)
	assert.NotNil(t, container.Store)
	assert.NotNil(t, container.TwilioClient)
}
