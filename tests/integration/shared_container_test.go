package integration

import (
	"context"
	"fmt"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/testutil"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestSharedContainer demonstrates shared container usage for performance
func TestSharedContainer(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration tests in short mode")
	}

	// Use shared container setup for better performance
	store, cleanup := testutil.SetupSharedTestDB(t)
	defer cleanup()

	ctx := context.Background()

	t.Run("FirstTest", func(t *testing.T) {
		// Each test runs in its own transaction for isolation
		testutil.RunInTransactionWithStore(t, store, func(ctx context.Context, txStore db.Store) {
			user, err := txStore.CreateUserWithPhone(ctx, db.CreateUserWithPhoneParams{
				DisplayName:                 "Shared Container User 1",
				Phone:                       testutil.ToPtr("+1111111111"),
				PhoneVerifiedAt:             testutil.ToPtr(time.Now()),
				InterfaceLanguage:           "en",
				CommunicationLanguage:       "en",
				PhoneOtpChannel:             "sms",
				EnableAppNotifications:      true,
				EnableWhatsappNotifications: false,
				EnableSmsNotifications:      true,
				EnableEmailNotifications:    false,
			})
			require.NoError(t, err)
			assert.NotEqual(t, uuid.Nil, user.ID)
		})
	})

	t.Run("SecondTest", func(t *testing.T) {
		// This test should not see data from the first test
		testutil.RunInTransactionWithStore(t, store, func(ctx context.Context, txStore db.Store) {
			// Verify the user from the first test doesn't exist
			users, err := txStore.ListUsers(ctx, db.ListUsersParams{
				Limit:  10,
				Offset: 0,
			})
			require.NoError(t, err)

			for _, user := range users {
				if user.Phone != nil && *user.Phone == "+1111111111" {
					t.Errorf("User from first test should not exist in second test")
				}
			}

			// Create a different user
			user, err := txStore.CreateUserWithPhone(ctx, db.CreateUserWithPhoneParams{
				DisplayName:                 "Shared Container User 2",
				Phone:                       testutil.ToPtr("+2222222222"),
				PhoneVerifiedAt:             testutil.ToPtr(time.Now()),
				InterfaceLanguage:           "en",
				CommunicationLanguage:       "en",
				PhoneOtpChannel:             "sms",
				EnableAppNotifications:      true,
				EnableWhatsappNotifications: false,
				EnableSmsNotifications:      true,
				EnableEmailNotifications:    false,
			})
			require.NoError(t, err)
			assert.NotEqual(t, uuid.Nil, user.ID)
		})
	})

	t.Run("ThirdTest", func(t *testing.T) {
		// This test should not see data from either previous test
		testutil.RunInTransactionWithStore(t, store, func(ctx context.Context, txStore db.Store) {
			users, err := txStore.ListUsers(ctx, db.ListUsersParams{
				Limit:  10,
				Offset: 0,
			})
			require.NoError(t, err)

			// Should not see any test users
			for _, user := range users {
				if user.Phone != nil {
					phone := *user.Phone
					if phone == "+1111111111" || phone == "+2222222222" {
						t.Errorf("User from previous tests should not exist: %s", phone)
					}
				}
			}
		})
	})

	// Verify no test data persists outside transactions
	finalUsers, err := store.ListUsers(ctx, db.ListUsersParams{
		Limit:  100,
		Offset: 0,
	})
	require.NoError(t, err)

	for _, user := range finalUsers {
		if user.Phone != nil {
			phone := *user.Phone
			if phone == "+1111111111" || phone == "+2222222222" {
				t.Errorf("Test users should not persist outside transactions: %s", phone)
			}
		}
	}
}

// TestSharedContainerPerformance measures the performance benefits
func TestSharedContainerPerformance(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration tests in short mode")
	}

	// Measure shared container setup time
	start := time.Now()
	store, cleanup := testutil.SetupSharedTestDB(t)
	defer cleanup()
	setupDuration := time.Since(start)

	t.Logf("Shared container setup took: %v", setupDuration)

	// Run multiple tests to amortize container startup cost
	for i := 0; i < 5; i++ {
		t.Run(fmt.Sprintf("PerformanceTest%d", i), func(t *testing.T) {
			testStart := time.Now()
			testutil.RunInTransactionWithStore(t, store, func(ctx context.Context, txStore db.Store) {
				user, err := txStore.CreateUserWithPhone(ctx, db.CreateUserWithPhoneParams{
					DisplayName:                 fmt.Sprintf("Performance User %d", i),
					Phone:                       testutil.ToPtr(fmt.Sprintf("+333333%04d", i)),
					PhoneVerifiedAt:             testutil.ToPtr(time.Now()),
					InterfaceLanguage:           "en",
					CommunicationLanguage:       "en",
					PhoneOtpChannel:             "sms",
					EnableAppNotifications:      true,
					EnableWhatsappNotifications: false,
					EnableSmsNotifications:      true,
					EnableEmailNotifications:    false,
				})
				require.NoError(t, err)
				assert.NotEqual(t, uuid.Nil, user.ID)
			})
			testDuration := time.Since(testStart)
			t.Logf("Test %d took: %v", i, testDuration)
		})
	}
}
