package integration

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/internal/payloads"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

type SocialEndpointsTestSuite struct {
	suite      *APITestSuite
	testOrgID  uuid.UUID
	testUserID uuid.UUID
	testPostID uuid.UUID
	authToken  string
}

func TestSocialEndpointsTestSuite(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration tests in short mode")
	}

	s := &SocialEndpointsTestSuite{}
	s.SetupTest(t)
	defer s.TeardownTest(t)

	t.Run("TestBookmarkEndpoints", s.TestBookmarkEndpoints)
	t.Run("TestBookmarkErrors", s.TestBookmarkErrors)
	t.Run("TestModerationEndpoints", s.TestModerationEndpoints)
	t.Run("TestAnalyticsEndpoints", s.TestAnalyticsEndpoints)
	t.Run("TestNotificationPreferencesEndpoints", s.TestNotificationPreferencesEndpoints)
	t.Run("TestSocialRateLimiting", s.TestSocialRateLimiting)
	t.Run("TestAuthorizationErrors", s.TestAuthorizationErrors)
	t.Run("TestValidationErrors", s.TestValidationErrors)
}

func (s *SocialEndpointsTestSuite) SetupTest(t *testing.T) {
	s.suite = SetupAPITest(t)

	// Create test data
	testUser, err := s.suite.CreateTestUser("member", "+1234567891")
	require.NoError(t, err)

	userID := testUser.User.ID

	orgID, err := uuid.Parse(s.suite.GetOrgID())
	require.NoError(t, err)

	s.testOrgID = orgID
	s.testUserID = userID
	s.authToken = testUser.AccessToken
	s.testPostID = s.createTestPost(t)
}

func (s *SocialEndpointsTestSuite) TeardownTest(t *testing.T) {
	s.suite.TeardownAPITest(t)
}

func (s *SocialEndpointsTestSuite) createTestPost(t *testing.T) uuid.UUID {
	// Create a test post for social interactions
	reqBody := map[string]interface{}{
		"title": "Test Post for Social Features",
		"content": map[string]interface{}{
			"type": "doc",
			"content": []map[string]interface{}{
				{
					"type": "paragraph",
					"content": []map[string]interface{}{
						{
							"type": "text",
							"text": "This is a test post for social features",
						},
					},
				},
			},
		},
		"status": "published",
	}

	rec, err := s.suite.MakeRequest(http.MethodPost, "/api/v1/posts", reqBody, s.authToken)
	require.NoError(t, err)
	require.Equal(t, http.StatusCreated, rec.Code)

	var response map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	require.NoError(t, err)

	postIDStr := response["id"].(string)
	postID, err := uuid.Parse(postIDStr)
	require.NoError(t, err)

	return postID
}

// Bookmark Tests

func (s *SocialEndpointsTestSuite) TestBookmarkEndpoints(t *testing.T) {
	// Test 1: Add bookmark
	addReq := payloads.AddBookmarkRequest{
		ContentType: "post",
		ContentID:   s.testPostID.String(),
		FolderName:  func() *string { s := "Important Posts"; return &s }(),
		Notes:       func() *string { s := "This post has valuable information"; return &s }(),
	}

	rec, err := s.suite.MakeRequest(http.MethodPost, "/api/v1/social/bookmarks", addReq, s.authToken)
	require.NoError(t, err)
	assert.Equal(t, http.StatusCreated, rec.Code)

	var bookmarkResp payloads.BookmarkResponse
	err = json.Unmarshal(rec.Body.Bytes(), &bookmarkResp)
	require.NoError(t, err)
	assert.Equal(t, s.testUserID, bookmarkResp.UserID)
	assert.Equal(t, s.testOrgID, bookmarkResp.OrganizationID)
	assert.Equal(t, "post", bookmarkResp.ContentType)
	assert.Equal(t, s.testPostID, bookmarkResp.ContentID)
	assert.Equal(t, "Important Posts", *bookmarkResp.FolderName)

	// Test 2: Get user bookmarks
	rec, err = s.suite.MakeRequest(http.MethodGet, "/api/v1/social/bookmarks/user/me?limit=10&offset=0", nil, s.authToken)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, rec.Code)

	var bookmarksResp payloads.BookmarksListResponse
	err = json.Unmarshal(rec.Body.Bytes(), &bookmarksResp)
	require.NoError(t, err)
	assert.Len(t, bookmarksResp.Bookmarks, 1)
	assert.Equal(t, int64(1), bookmarksResp.Total)

	// Test 3: Remove bookmark
	rec, err = s.suite.MakeRequest(http.MethodDelete, fmt.Sprintf("/api/v1/social/bookmarks/post/%s", s.testPostID), nil, s.authToken)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, rec.Code)

	var deleteResp map[string]string
	err = json.Unmarshal(rec.Body.Bytes(), &deleteResp)
	require.NoError(t, err)
	assert.Equal(t, "Bookmark removed successfully", deleteResp["message"])

	// Test 4: Verify bookmark was removed
	rec, err = s.suite.MakeRequest(http.MethodGet, "/api/v1/social/bookmarks/user/me", nil, s.authToken)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, rec.Code)

	err = json.Unmarshal(rec.Body.Bytes(), &bookmarksResp)
	require.NoError(t, err)
	assert.Len(t, bookmarksResp.Bookmarks, 0)
	assert.Equal(t, int64(0), bookmarksResp.Total)
}

func (s *SocialEndpointsTestSuite) TestBookmarkErrors(t *testing.T) {
	// Test invalid content type
	addReq := payloads.AddBookmarkRequest{
		ContentType: "invalid",
		ContentID:   s.testPostID.String(),
	}

	rec, err := s.suite.MakeRequest(http.MethodPost, "/api/v1/social/bookmarks", addReq, s.authToken)
	require.NoError(t, err)
	assert.Equal(t, http.StatusBadRequest, rec.Code)

	// Test invalid content ID
	addReq.ContentType = "post"
	addReq.ContentID = "invalid-uuid"

	rec, err = s.suite.MakeRequest(http.MethodPost, "/api/v1/social/bookmarks", addReq, s.authToken)
	require.NoError(t, err)
	assert.Equal(t, http.StatusBadRequest, rec.Code)
}

// Moderation Tests

func (s *SocialEndpointsTestSuite) TestModerationEndpoints(t *testing.T) {
	// Create admin user for moderation tests
	adminOrgID, adminUserID, adminToken := s.CreateTestAdminUserAndOrg(t)

	// Test 1: Report content as regular user
	reportReq := payloads.ReportContentRequest{
		ContentType: "post",
		ContentID:   s.testPostID.String(),
		Reason:      "spam",
		Description: func() *string { s := "This post appears to be spam"; return &s }(),
	}

	rec, err := s.suite.MakeRequest(http.MethodPost, "/api/v1/social/moderation/reports", reportReq, s.authToken)
	require.NoError(t, err)
	assert.Equal(t, http.StatusCreated, rec.Code)

	var reportResp payloads.ContentReportResponse
	err = json.Unmarshal(rec.Body.Bytes(), &reportResp)
	require.NoError(t, err)
	assert.Equal(t, "post", reportResp.ContentType)
	assert.Equal(t, s.testPostID, reportResp.ContentID)
	assert.Equal(t, "spam", reportResp.Reason)
	assert.Equal(t, "pending", reportResp.Status)

	reportID := reportResp.ID

	// Test 2: Get pending reports as admin (should work)
	rec, err = s.suite.MakeRequest(http.MethodGet, "/api/v1/social/moderation/reports/pending", nil, adminToken)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, rec.Code)

	var reportsResp payloads.ReportsListResponse
	err = json.Unmarshal(rec.Body.Bytes(), &reportsResp)
	require.NoError(t, err)
	assert.GreaterOrEqual(t, len(reportsResp.Reports), 1)

	// Test 3: Try to get pending reports as regular user (should fail)
	rec, err = s.suite.MakeRequest(http.MethodGet, "/api/v1/social/moderation/reports/pending", nil, s.authToken)
	require.NoError(t, err)
	assert.Equal(t, http.StatusForbidden, rec.Code)

	// Test 4: Review report as admin
	reviewReq := payloads.ReviewReportRequest{
		Status:         "approved",
		ModeratorNotes: func() *string { s := "Report is valid, content removed"; return &s }(),
	}

	rec, err = s.suite.MakeRequest(http.MethodPut, fmt.Sprintf("/api/v1/social/moderation/reports/%s", reportID), reviewReq, adminToken)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, rec.Code)

	var reviewedResp payloads.ContentReportResponse
	err = json.Unmarshal(rec.Body.Bytes(), &reviewedResp)
	require.NoError(t, err)
	assert.Equal(t, "approved", reviewedResp.Status)
	assert.Equal(t, adminUserID, *reviewedResp.ReviewedBy)

	// Test 5: Try to review report as regular user (should fail)
	rec, err = s.suite.MakeRequest(http.MethodPut, fmt.Sprintf("/api/v1/social/moderation/reports/%s", reportID), reviewReq, s.authToken)
	require.NoError(t, err)
	assert.Equal(t, http.StatusForbidden, rec.Code)

	// Cleanup
	s.CleanupTestOrg(adminOrgID)
}

// Analytics Tests

func (s *SocialEndpointsTestSuite) TestAnalyticsEndpoints(t *testing.T) {
	// Create admin user for analytics tests
	adminOrgID, _, adminToken := s.CreateTestAdminUserAndOrg(t)

	// Test 1: Get engagement stats as admin (should work)
	rec, err := s.suite.MakeRequest(http.MethodGet, "/api/v1/social/analytics/engagement?period=week", nil, adminToken)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, rec.Code)

	var engagementResp payloads.EngagementStatsResponse
	err = json.Unmarshal(rec.Body.Bytes(), &engagementResp)
	require.NoError(t, err)
	assert.Equal(t, "week", engagementResp.Period)
	assert.GreaterOrEqual(t, engagementResp.TotalComments, int64(0))

	// Test 2: Try to get engagement stats as regular user (should fail)
	rec, err = s.suite.MakeRequest(http.MethodGet, "/api/v1/social/analytics/engagement", nil, s.authToken)
	require.NoError(t, err)
	assert.Equal(t, http.StatusForbidden, rec.Code)

	// Test 3: Get top commenters as admin
	rec, err = s.suite.MakeRequest(http.MethodGet, "/api/v1/social/analytics/top-commenters?period=month&limit=5", nil, adminToken)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, rec.Code)

	var commentersResp payloads.TopCommentersResponse
	err = json.Unmarshal(rec.Body.Bytes(), &commentersResp)
	require.NoError(t, err)
	assert.Equal(t, "month", commentersResp.Period)
	assert.GreaterOrEqual(t, len(commentersResp.Commenters), 0)

	// Test 4: Get most reacted content as admin
	rec, err = s.suite.MakeRequest(http.MethodGet, "/api/v1/social/analytics/most-reacted?period=month&limit=10", nil, adminToken)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, rec.Code)

	var reactedResp payloads.MostReactedListResponse
	err = json.Unmarshal(rec.Body.Bytes(), &reactedResp)
	require.NoError(t, err)
	assert.Equal(t, "month", reactedResp.Period)
	assert.GreaterOrEqual(t, len(reactedResp.Content), 0)

	// Cleanup
	s.CleanupTestOrg(adminOrgID)
}

// Notification Preferences Tests

func (s *SocialEndpointsTestSuite) TestNotificationPreferencesEndpoints(t *testing.T) {
	// Test 1: Get notification preferences (should return defaults if none exist)
	rec, err := s.suite.MakeRequest(http.MethodGet, "/api/v1/social/preferences/notifications", nil, s.authToken)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, rec.Code)

	var prefsResp payloads.SocialNotificationPreferencesResponse
	err = json.Unmarshal(rec.Body.Bytes(), &prefsResp)
	require.NoError(t, err)
	assert.Equal(t, s.testUserID, prefsResp.UserID)
	assert.Equal(t, s.testOrgID, prefsResp.OrganizationID)
	// Defaults should be true
	assert.True(t, prefsResp.NotifyOnCommentReply)
	assert.True(t, prefsResp.EmailEnabled)

	// Test 2: Update notification preferences
	updateReq := payloads.UpdateSocialNotificationPreferencesRequest{
		NotifyOnCommentReply:    boolPtr(false),
		NotifyOnCommentReaction: boolPtr(true),
		EmailEnabled:            boolPtr(false),
		PushEnabled:             boolPtr(true),
	}

	rec, err = s.suite.MakeRequest(http.MethodPut, "/api/v1/social/preferences/notifications", updateReq, s.authToken)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, rec.Code)

	var updatedPrefsResp payloads.SocialNotificationPreferencesResponse
	err = json.Unmarshal(rec.Body.Bytes(), &updatedPrefsResp)
	require.NoError(t, err)
	assert.False(t, updatedPrefsResp.NotifyOnCommentReply)   // Updated
	assert.True(t, updatedPrefsResp.NotifyOnCommentReaction) // Updated
	assert.False(t, updatedPrefsResp.EmailEnabled)           // Updated
	assert.True(t, updatedPrefsResp.PushEnabled)             // Updated
	assert.True(t, updatedPrefsResp.NotifyOnPostComment)     // Should remain default

	// Test 3: Get preferences again to verify they were saved
	rec, err = s.suite.MakeRequest(http.MethodGet, "/api/v1/social/preferences/notifications", nil, s.authToken)
	require.NoError(t, err)
	assert.Equal(t, http.StatusOK, rec.Code)

	err = json.Unmarshal(rec.Body.Bytes(), &prefsResp)
	require.NoError(t, err)
	assert.False(t, prefsResp.NotifyOnCommentReply)
	assert.False(t, prefsResp.EmailEnabled)
	assert.True(t, prefsResp.PushEnabled)
}

// Rate Limiting Tests

func (s *SocialEndpointsTestSuite) TestSocialRateLimiting(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping rate limiting test in short mode")
	}

	// Test bookmark rate limiting by adding multiple bookmarks quickly
	successCount := 0
	rateLimitCount := 0

	for i := 0; i < 10; i++ {
		addReq := payloads.AddBookmarkRequest{
			ContentType: "post",
			ContentID:   s.testPostID.String(),
			Notes:       func() *string { s := fmt.Sprintf("Rate limit test bookmark %d", i); return &s }(),
		}

		rec, err := s.suite.MakeRequest(http.MethodPost, "/api/v1/social/bookmarks", addReq, s.authToken)
		require.NoError(t, err)

		if rec.Code == http.StatusCreated {
			successCount++
		} else if rec.Code == http.StatusTooManyRequests {
			rateLimitCount++
		}

		// Small delay between requests
		time.Sleep(100 * time.Millisecond)
	}

	t.Logf("Bookmark rate limiting test: %d success, %d rate limited", successCount, rateLimitCount)
	// We expect at least some successful requests
	assert.Greater(t, successCount, 0)
}

// Error Handling Tests

func (s *SocialEndpointsTestSuite) TestAuthorizationErrors(t *testing.T) {
	// Test unauthorized access (no token)
	rec, err := s.suite.MakeRequest(http.MethodGet, "/api/v1/social/bookmarks/user/me", nil, "")
	require.NoError(t, err)
	assert.Equal(t, http.StatusUnauthorized, rec.Code)

	// Test invalid token
	rec, err = s.suite.MakeRequest(http.MethodGet, "/api/v1/social/bookmarks/user/me", nil, "invalid-token")
	require.NoError(t, err)
	assert.Equal(t, http.StatusUnauthorized, rec.Code)
}

func (s *SocialEndpointsTestSuite) TestValidationErrors(t *testing.T) {
	// Test invalid JSON by sending raw string
	req := httptest.NewRequest(http.MethodPost, "/api/v1/social/bookmarks", strings.NewReader("{invalid json}"))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+s.authToken)

	rec := s.suite.MakeRawRequest(req)
	assert.Equal(t, http.StatusBadRequest, rec.Code)

	// Test missing required fields
	emptyReq := payloads.AddBookmarkRequest{}
	rec, err := s.suite.MakeRequest(http.MethodPost, "/api/v1/social/bookmarks", emptyReq, s.authToken)
	require.NoError(t, err)
	assert.Equal(t, http.StatusBadRequest, rec.Code)
}

// Helper functions

func boolPtr(b bool) *bool {
	return &b
}

// CreateTestAdminUserAndOrg creates a test admin user and organization for testing admin endpoints
func (s *SocialEndpointsTestSuite) CreateTestAdminUserAndOrg(t *testing.T) (uuid.UUID, uuid.UUID, string) {
	// For testing purposes, use the existing admin token from the suite
	// In a real implementation, this would create a proper admin user
	userID, err := uuid.Parse("00000000-0000-0000-0000-000000000001") // Default superadmin
	require.NoError(t, err)
	orgID, err := uuid.Parse(s.suite.GetOrgID())
	require.NoError(t, err)

	return orgID, userID, s.suite.adminToken
}

// CleanupTestOrg cleans up test organization
func (s *SocialEndpointsTestSuite) CleanupTestOrg(orgID uuid.UUID) {
	// Implementation depends on your cleanup strategy
	// This is a placeholder - cleanup will happen when the test container is torn down
}
