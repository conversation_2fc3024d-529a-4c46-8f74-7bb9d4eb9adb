//go:build skip
// +build skip

// TODO: Enable this test when social service implementation is complete

package integration

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/db"
	socialhandlers "Membership-SAAS-System-Backend/internal/handlers/social"
	"Membership-SAAS-System-Backend/internal/services/social"
	"Membership-SAAS-System-Backend/internal/testutil"
	"Membership-SAAS-System-Backend/internal/token"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

type SocialFeaturesTestSuite struct {
	testutil.BaseServiceTestSuite
	socialService   social.SocialService
	commentHandler  *socialhandlers.CommentHandler
	reactionHandler *socialhandlers.ReactionHandler
	echo            *echo.Echo
	testOrg         db.Organization
	testUser        db.User
	testPost        db.Post
}

func TestSocialFeaturesTestSuite(t *testing.T) {
	suite.Run(t, new(SocialFeaturesTestSuite))
}

func (s *SocialFeaturesTestSuite) SetupTest() {
	s.BaseServiceTestSuite.SetupTest()

	// Initialize social service
	s.socialService = social.NewSocialService(s.MockStore)
	s.commentHandler = socialhandlers.NewCommentHandler(s.socialService)
	s.reactionHandler = socialhandlers.NewReactionHandler(s.socialService)

	// Setup Echo
	s.echo = echo.New()

	// Create test data
	s.testOrg = s.createTestOrganization()
	s.testUser = s.createTestUser()
	s.testPost = s.createTestPost()
}

// Comment System Tests

func (s *SocialFeaturesTestSuite) TestCommentCreationAndRetrieval() {
	t := s.T()
	ctx := context.Background()

	// Test creating a root comment
	createReq := social.CreateCommentRequest{
		OrganizationID: s.testOrg.ID,
		ContentType:    social.ContentTypePost,
		ContentID:      s.testPost.ID,
		Content:        "This is a test comment with **markdown** formatting",
		UserID:         s.testUser.ID,
		UserName:       s.testUser.DisplayName,
	}

	rootComment, err := s.socialService.CreateComment(ctx, createReq)
	require.NoError(t, err)
	require.NotNil(t, rootComment)
	require.Equal(t, createReq.Content, rootComment.Content)
	require.Equal(t, 0, rootComment.Depth)
	require.Equal(t, rootComment.ID, rootComment.ThreadID)
	require.Nil(t, rootComment.ParentCommentID)

	// Test creating a reply comment
	replyReq := social.CreateCommentRequest{
		OrganizationID:  s.testOrg.ID,
		ContentType:     social.ContentTypePost,
		ContentID:       s.testPost.ID,
		Content:         "This is a reply to the root comment",
		UserID:          s.testUser.ID,
		UserName:        s.testUser.DisplayName,
		ParentCommentID: &rootComment.ID,
	}

	replyComment, err := s.socialService.CreateComment(ctx, replyReq)
	require.NoError(t, err)
	require.NotNil(t, replyComment)
	require.Equal(t, replyReq.Content, replyComment.Content)
	require.Equal(t, 1, replyComment.Depth)
	require.Equal(t, rootComment.ID, replyComment.ThreadID)
	require.Equal(t, rootComment.ID, *replyComment.ParentCommentID)

	// Test retrieving comments for content
	getReq := social.GetCommentsRequest{
		OrganizationID: s.testOrg.ID,
		ContentType:    social.ContentTypePost,
		ContentID:      s.testPost.ID,
		Limit:          10,
		Offset:         0,
	}

	response, err := s.socialService.GetCommentsForContent(ctx, getReq)
	require.NoError(t, err)
	require.NotNil(t, response)
	require.Len(t, response.Comments, 1)                 // Only root comment should be returned
	require.Equal(t, 2, response.TotalCount)             // Total includes replies
	require.Equal(t, 1, response.Comments[0].ReplyCount) // Root comment should have 1 reply

	// Test retrieving replies
	repliesReq := social.GetRepliesRequest{
		OrganizationID:  s.testOrg.ID,
		ParentCommentID: rootComment.ID,
		Limit:           10,
		Offset:          0,
	}

	repliesResponse, err := s.socialService.GetCommentReplies(ctx, repliesReq)
	require.NoError(t, err)
	require.NotNil(t, repliesResponse)
	require.Len(t, repliesResponse.Replies, 1)
	require.Equal(t, replyComment.ID, repliesResponse.Replies[0].ID)
}

func (s *SocialFeaturesTestSuite) TestHierarchicalComments() {
	t := s.T()
	ctx := context.Background()

	// Create a deep comment hierarchy
	var currentParent *uuid.UUID
	comments := make([]social.Comment, 0)

	// Create up to max depth (should be limited to 10)
	for depth := 0; depth < 12; depth++ {
		createReq := social.CreateCommentRequest{
			OrganizationID:  s.testOrg.ID,
			ContentType:     social.ContentTypePost,
			ContentID:       s.testPost.ID,
			Content:         fmt.Sprintf("Comment at depth %d", depth),
			UserID:          s.testUser.ID,
			UserName:        s.testUser.DisplayName,
			ParentCommentID: currentParent,
		}

		if depth >= 10 {
			// Should fail due to depth limit
			_, err := s.socialService.CreateComment(ctx, createReq)
			require.Error(t, err)
			require.Contains(t, err.Error(), "maximum depth")
		} else {
			comment, err := s.socialService.CreateComment(ctx, createReq)
			require.NoError(t, err)
			require.Equal(t, depth, comment.Depth)

			comments = append(comments, *comment)
			currentParent = &comment.ID
		}
	}

	// Verify thread structure
	rootComment := comments[0]
	for i, comment := range comments {
		require.Equal(t, i, comment.Depth)
		require.Equal(t, rootComment.ID, comment.ThreadID)

		if i > 0 {
			require.Equal(t, comments[i-1].ID, *comment.ParentCommentID)
		}
	}
}

func (s *SocialFeaturesTestSuite) TestCommentModeration() {
	t := s.T()
	ctx := context.Background()

	// Create test moderator
	moderator := s.createTestUser()

	// Create a comment
	createReq := social.CreateCommentRequest{
		OrganizationID: s.testOrg.ID,
		ContentType:    social.ContentTypePost,
		ContentID:      s.testPost.ID,
		Content:        "This comment contains inappropriate content",
		UserID:         s.testUser.ID,
		UserName:       s.testUser.DisplayName,
	}

	comment, err := s.socialService.CreateComment(ctx, createReq)
	require.NoError(t, err)

	// Test content reporting
	reportReq := social.ReportContentRequest{
		OrganizationID: s.testOrg.ID,
		ContentType:    social.ContentTypeComment,
		ContentID:      comment.ID,
		ReportedBy:     s.testUser.ID,
		Reason:         "inappropriate_content",
		Description:    "This comment violates community guidelines",
	}

	report, err := s.socialService.ReportContent(ctx, reportReq)
	require.NoError(t, err)
	require.Equal(t, "pending", report.Status)

	// Test moderation action
	moderateReq := social.ModerateContentRequest{
		OrganizationID: s.testOrg.ID,
		ReportID:       report.ID,
		ModeratorID:    moderator.ID,
		Action:         "hide_content",
		Reason:         "Violation of community guidelines",
	}

	err = s.socialService.ModerateContent(ctx, moderateReq)
	require.NoError(t, err)

	// Verify comment status changed
	moderatedComment, err := s.socialService.GetComment(ctx, comment.ID, s.testOrg.ID)
	require.NoError(t, err)
	require.Equal(t, "hidden", moderatedComment.Status)
}

// Reaction System Tests

func (s *SocialFeaturesTestSuite) TestReactionSystem() {
	t := s.T()
	ctx := context.Background()

	// Create test comment
	comment := s.createTestComment()
	user2 := s.createTestUser()

	// Test adding reactions
	reactions := []string{"like", "heart", "laugh", "thumbs_up"}

	for _, reactionType := range reactions {
		// User 1 reacts
		addReq := social.AddReactionRequest{
			OrganizationID: s.testOrg.ID,
			ContentType:    social.ContentTypeComment,
			ContentID:      comment.ID,
			ReactionType:   social.ReactionType(reactionType),
			UserID:         s.testUser.ID,
		}

		reaction, err := s.socialService.AddReaction(ctx, addReq)
		require.NoError(t, err)
		require.Equal(t, reactionType, string(reaction.ReactionType))

		// User 2 reacts with same type (should work)
		addReq.UserID = user2.ID
		_, err = s.socialService.AddReaction(ctx, addReq)
		require.NoError(t, err)
	}

	// Test getting reactions for content
	reactionsReq := social.GetReactionsRequest{
		OrganizationID: s.testOrg.ID,
		ContentType:    social.ContentTypeComment,
		ContentID:      comment.ID,
		Limit:          20,
		Offset:         0,
	}

	reactionsResponse, err := s.socialService.GetReactionsForContent(ctx, reactionsReq)
	require.NoError(t, err)
	require.Len(t, reactionsResponse.Reactions, 8) // 4 reactions × 2 users
	require.Equal(t, 8, reactionsResponse.TotalCount)

	// Test reaction summary
	summary, err := s.socialService.GetReactionSummary(ctx, s.testOrg.ID, social.ContentTypeComment, comment.ID)
	require.NoError(t, err)
	require.NotNil(t, summary)

	for _, reactionType := range reactions {
		count, exists := summary.Counts[reactionType]
		require.True(t, exists)
		require.Equal(t, 2, count) // Each reaction type should have 2 reactions
	}

	// Test removing reaction
	removeReq := social.RemoveReactionRequest{
		OrganizationID: s.testOrg.ID,
		ContentType:    social.ContentTypeComment,
		ContentID:      comment.ID,
		ReactionType:   social.ReactionTypeLike,
		UserID:         s.testUser.ID,
	}

	err = s.socialService.RemoveReaction(ctx, removeReq)
	require.NoError(t, err)

	// Verify reaction was removed
	updatedSummary, err := s.socialService.GetReactionSummary(ctx, s.testOrg.ID, social.ContentTypeComment, comment.ID)
	require.NoError(t, err)
	require.Equal(t, 1, updatedSummary.Counts["like"]) // Should be 1 now
}

func (s *SocialFeaturesTestSuite) TestReactionConstraints() {
	t := s.T()
	ctx := context.Background()

	comment := s.createTestComment()

	// Test duplicate reaction (should replace)
	addReq := social.AddReactionRequest{
		OrganizationID: s.testOrg.ID,
		ContentType:    social.ContentTypeComment,
		ContentID:      comment.ID,
		ReactionType:   social.ReactionTypeLike,
		UserID:         s.testUser.ID,
	}

	// First reaction
	_, err := s.socialService.AddReaction(ctx, addReq)
	require.NoError(t, err)

	// Same reaction again (should replace, not create duplicate)
	_, err = s.socialService.AddReaction(ctx, addReq)
	require.NoError(t, err)

	// Verify only one reaction exists
	summary, err := s.socialService.GetReactionSummary(ctx, s.testOrg.ID, social.ContentTypeComment, comment.ID)
	require.NoError(t, err)
	require.Equal(t, 1, summary.Counts["like"])

	// Test changing reaction type
	addReq.ReactionType = social.ReactionTypeHeart
	_, err = s.socialService.AddReaction(ctx, addReq)
	require.NoError(t, err)

	// Verify reaction type changed
	updatedSummary, err := s.socialService.GetReactionSummary(ctx, s.testOrg.ID, social.ContentTypeComment, comment.ID)
	require.NoError(t, err)
	require.Equal(t, 0, updatedSummary.Counts["like"])  // Old reaction removed
	require.Equal(t, 1, updatedSummary.Counts["heart"]) // New reaction added
}

// Bookmark System Tests

func (s *SocialFeaturesTestSuite) TestBookmarkSystem() {
	t := s.T()
	ctx := context.Background()

	comment := s.createTestComment()

	// Test adding bookmark
	addReq := social.AddBookmarkRequest{
		OrganizationID: s.testOrg.ID,
		ContentType:    social.ContentTypeComment,
		ContentID:      comment.ID,
		UserID:         s.testUser.ID,
		FolderName:     "Important Comments",
		Notes:          "This comment has valuable information",
	}

	bookmark, err := s.socialService.AddBookmark(ctx, addReq)
	require.NoError(t, err)
	require.Equal(t, "Important Comments", bookmark.FolderName)
	require.Equal(t, "This comment has valuable information", bookmark.Notes)

	// Test getting user bookmarks
	getUserReq := social.GetUserBookmarksRequest{
		OrganizationID: s.testOrg.ID,
		UserID:         s.testUser.ID,
		FolderName:     "Important Comments",
		Limit:          10,
		Offset:         0,
	}

	bookmarksResponse, err := s.socialService.GetUserBookmarks(ctx, getUserReq)
	require.NoError(t, err)
	require.Len(t, bookmarksResponse.Bookmarks, 1)
	require.Equal(t, bookmark.ID, bookmarksResponse.Bookmarks[0].ID)

	// Test updating bookmark
	updateReq := social.UpdateBookmarkRequest{
		BookmarkID:     bookmark.ID,
		OrganizationID: s.testOrg.ID,
		UserID:         s.testUser.ID,
		FolderName:     "Archive",
		Notes:          "Moved to archive",
	}

	updatedBookmark, err := s.socialService.UpdateBookmark(ctx, updateReq)
	require.NoError(t, err)
	require.Equal(t, "Archive", updatedBookmark.FolderName)
	require.Equal(t, "Moved to archive", updatedBookmark.Notes)

	// Test removing bookmark
	err = s.socialService.RemoveBookmark(ctx, bookmark.ID, s.testOrg.ID, s.testUser.ID)
	require.NoError(t, err)

	// Verify bookmark was removed
	emptyResponse, err := s.socialService.GetUserBookmarks(ctx, getUserReq)
	require.NoError(t, err)
	require.Len(t, emptyResponse.Bookmarks, 0)
}

func (s *SocialFeaturesTestSuite) TestBookmarkFolders() {
	t := s.T()
	ctx := context.Background()

	// Create multiple comments and bookmarks
	comments := make([]social.Comment, 5)
	for i := 0; i < 5; i++ {
		comments[i] = *s.createTestComment()
	}

	folders := []string{"Work", "Personal", "Archive", "Work", "Personal"} // Some duplicates

	// Add bookmarks to different folders
	for i, comment := range comments {
		addReq := social.AddBookmarkRequest{
			OrganizationID: s.testOrg.ID,
			ContentType:    social.ContentTypeComment,
			ContentID:      comment.ID,
			UserID:         s.testUser.ID,
			FolderName:     folders[i],
			Notes:          fmt.Sprintf("Bookmark %d notes", i),
		}

		_, err := s.socialService.AddBookmark(ctx, addReq)
		require.NoError(t, err)
	}

	// Test getting bookmarks by folder
	workReq := social.GetUserBookmarksRequest{
		OrganizationID: s.testOrg.ID,
		UserID:         s.testUser.ID,
		FolderName:     "Work",
		Limit:          10,
		Offset:         0,
	}

	workBookmarks, err := s.socialService.GetUserBookmarks(ctx, workReq)
	require.NoError(t, err)
	require.Len(t, workBookmarks.Bookmarks, 2) // Two "Work" bookmarks

	// Test getting all user bookmarks
	allReq := social.GetUserBookmarksRequest{
		OrganizationID: s.testOrg.ID,
		UserID:         s.testUser.ID,
		Limit:          10,
		Offset:         0,
	}

	allBookmarks, err := s.socialService.GetUserBookmarks(ctx, allReq)
	require.NoError(t, err)
	require.Len(t, allBookmarks.Bookmarks, 5) // All bookmarks

	// Test getting bookmark folders
	folders, err := s.socialService.GetUserBookmarkFolders(ctx, s.testOrg.ID, s.testUser.ID)
	require.NoError(t, err)
	require.Len(t, folders, 3) // "Work", "Personal", "Archive"
	require.Contains(t, folders, "Work")
	require.Contains(t, folders, "Personal")
	require.Contains(t, folders, "Archive")
}

// Rate Limiting Tests

func (s *SocialFeaturesTestSuite) TestSocialRateLimiting() {
	t := s.T()
	ctx := context.Background()

	comment := s.createTestComment()

	// Test comment rate limiting
	commentRequests := 10 // Assume limit is 5 per hour
	successCount := 0
	rateLimitCount := 0

	for i := 0; i < commentRequests; i++ {
		createReq := social.CreateCommentRequest{
			OrganizationID: s.testOrg.ID,
			ContentType:    social.ContentTypePost,
			ContentID:      s.testPost.ID,
			Content:        fmt.Sprintf("Rate limit test comment %d", i),
			UserID:         s.testUser.ID,
			UserName:       s.testUser.DisplayName,
		}

		_, err := s.socialService.CreateComment(ctx, createReq)
		if err != nil {
			if strings.Contains(err.Error(), "rate limit") {
				rateLimitCount++
			} else {
				require.NoError(t, err) // Unexpected error
			}
		} else {
			successCount++
		}
	}

	t.Logf("Comment rate limiting: %d success, %d rate limited", successCount, rateLimitCount)
	require.Greater(t, rateLimitCount, 0, "Should hit rate limit")

	// Test reaction rate limiting
	reactionRequests := 20 // Assume limit is 10 per minute
	successCount = 0
	rateLimitCount = 0

	for i := 0; i < reactionRequests; i++ {
		// Alternate between like and unlike to test rate limiting
		if i%2 == 0 {
			addReq := social.AddReactionRequest{
				OrganizationID: s.testOrg.ID,
				ContentType:    social.ContentTypeComment,
				ContentID:      comment.ID,
				ReactionType:   social.ReactionTypeLike,
				UserID:         s.testUser.ID,
			}
			_, err := s.socialService.AddReaction(ctx, addReq)
			if err != nil && strings.Contains(err.Error(), "rate limit") {
				rateLimitCount++
			} else if err == nil {
				successCount++
			}
		} else {
			removeReq := social.RemoveReactionRequest{
				OrganizationID: s.testOrg.ID,
				ContentType:    social.ContentTypeComment,
				ContentID:      comment.ID,
				ReactionType:   social.ReactionTypeLike,
				UserID:         s.testUser.ID,
			}
			err := s.socialService.RemoveReaction(ctx, removeReq)
			if err != nil && strings.Contains(err.Error(), "rate limit") {
				rateLimitCount++
			} else if err == nil {
				successCount++
			}
		}
	}

	t.Logf("Reaction rate limiting: %d success, %d rate limited", successCount, rateLimitCount)
}

// Activity Logging Tests

func (s *SocialFeaturesTestSuite) TestSocialActivityLogging() {
	t := s.T()
	ctx := context.Background()

	comment := s.createTestComment()

	// Perform various social activities
	activities := []struct {
		action   string
		function func() error
	}{
		{"comment_created", func() error {
			_, err := s.socialService.CreateComment(ctx, social.CreateCommentRequest{
				OrganizationID: s.testOrg.ID,
				ContentType:    social.ContentTypePost,
				ContentID:      s.testPost.ID,
				Content:        "Activity logging test",
				UserID:         s.testUser.ID,
				UserName:       s.testUser.DisplayName,
			})
			return err
		}},
		{"reaction_added", func() error {
			_, err := s.socialService.AddReaction(ctx, social.AddReactionRequest{
				OrganizationID: s.testOrg.ID,
				ContentType:    social.ContentTypeComment,
				ContentID:      comment.ID,
				ReactionType:   social.ReactionTypeLike,
				UserID:         s.testUser.ID,
			})
			return err
		}},
		{"bookmark_added", func() error {
			_, err := s.socialService.AddBookmark(ctx, social.AddBookmarkRequest{
				OrganizationID: s.testOrg.ID,
				ContentType:    social.ContentTypeComment,
				ContentID:      comment.ID,
				UserID:         s.testUser.ID,
				FolderName:     "Test",
			})
			return err
		}},
	}

	// Perform activities and verify logging
	for _, activity := range activities {
		err := activity.function()
		require.NoError(t, err)
	}

	// Verify activity logs were created
	logs, err := s.socialService.GetUserActivityLogs(ctx, s.testOrg.ID, s.testUser.ID, 10, 0)
	require.NoError(t, err)
	require.GreaterOrEqual(t, len(logs), len(activities))

	// Verify activity types
	loggedActivities := make(map[string]bool)
	for _, log := range logs {
		loggedActivities[log.ActivityType] = true
	}

	for _, activity := range activities {
		require.True(t, loggedActivities[activity.action], "Activity %s should be logged", activity.action)
	}
}

// HTTP Handler Tests

func (s *SocialFeaturesTestSuite) TestCommentHTTPHandlers() {
	t := s.T()

	// Test creating comment via HTTP
	reqBody := fmt.Sprintf(`{
		"content_type": "post",
		"content_id": "%s",
		"content": "HTTP handler test comment"
	}`, s.testPost.ID)

	req := httptest.NewRequest(http.MethodPost, "/api/v1/social/comments", strings.NewReader(reqBody))
	req.Header.Set(echo.HeaderContentType, echo.MIMEApplicationJSON)

	rec := httptest.NewRecorder()
	c := s.echo.NewContext(req, rec)

	// Set user context (simulate auth middleware)
	enhancedClaims := &token.EnhancedClaims{
		UserID: s.testUser.ID,
		ActiveOrg: &token.OrgContext{
			ID:   s.testOrg.ID.String(),
			Role: "member",
		},
		PlatformRole: "user",
	}
	jwtToken := jwt.NewWithClaims(jwt.SigningMethodHS256, enhancedClaims)
	c.Set("user", jwtToken)

	err := s.commentHandler.CreateComment(c)
	require.NoError(t, err)
	require.Equal(t, http.StatusCreated, rec.Code)

	// Parse response
	var response map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	require.NoError(t, err)
	require.Equal(t, "Comment created successfully", response["message"])

	// Test getting comments via HTTP
	req = httptest.NewRequest(http.MethodGet, fmt.Sprintf("/api/v1/social/comments/post/%s", s.testPost.ID), nil)
	rec = httptest.NewRecorder()
	c = s.echo.NewContext(req, rec)
	c.SetParamNames("contentType", "contentId")
	c.SetParamValues("post", s.testPost.ID.String())
	enhancedClaims2 := &token.EnhancedClaims{
		UserID: s.testUser.ID,
		ActiveOrg: &token.OrgContext{
			ID:   s.testOrg.ID.String(),
			Role: "member",
		},
		PlatformRole: "user",
	}
	jwtToken2 := jwt.NewWithClaims(jwt.SigningMethodHS256, enhancedClaims2)
	c.Set("user", jwtToken2)

	err = s.commentHandler.GetCommentsForContent(c)
	require.NoError(t, err)
	require.Equal(t, http.StatusOK, rec.Code)
}

// Performance Tests

func (s *SocialFeaturesTestSuite) TestSocialFeaturesConcurrency() {
	t := s.T()
	ctx := context.Background()

	if testing.Short() {
		t.Skip("Skipping concurrency test in short mode")
	}

	comment := s.createTestComment()
	users := make([]db.User, 10)
	for i := 0; i < 10; i++ {
		users[i] = s.createTestUser()
	}

	// Test concurrent reactions
	concurrentGoroutines := 10
	errors := make(chan error, concurrentGoroutines)

	start := time.Now()

	for i := 0; i < concurrentGoroutines; i++ {
		go func(userIndex int) {
			user := users[userIndex]

			// Add reaction
			addReq := social.AddReactionRequest{
				OrganizationID: s.testOrg.ID,
				ContentType:    social.ContentTypeComment,
				ContentID:      comment.ID,
				ReactionType:   social.ReactionTypeLike,
				UserID:         user.ID,
			}

			_, err := s.socialService.AddReaction(ctx, addReq)
			errors <- err
		}(i)
	}

	// Wait for all goroutines
	for i := 0; i < concurrentGoroutines; i++ {
		err := <-errors
		require.NoError(t, err)
	}

	duration := time.Since(start)
	t.Logf("Concurrent reactions: %d operations in %v", concurrentGoroutines, duration)

	// Verify all reactions were added
	summary, err := s.socialService.GetReactionSummary(ctx, s.testOrg.ID, social.ContentTypeComment, comment.ID)
	require.NoError(t, err)
	require.Equal(t, 10, summary.Counts["like"])
}

// Helper methods

func (s *SocialFeaturesTestSuite) createTestOrganization() db.Organization {
	org, err := s.Store.CreateOrganization(context.Background(), db.CreateOrganizationParams{
		Name:        "Social Test Org " + uuid.New().String()[:8],
		Type:        "test",
		Description: "Organization for social features testing",
		Status:      "active",
	})
	require.NoError(s.T(), err)
	return org
}

func (s *SocialFeaturesTestSuite) createTestUser() db.User {
	user, err := s.Store.CreateUser(context.Background(), db.CreateUserParams{
		PhoneNumber: "+1234567" + uuid.New().String()[:3],
		DisplayName: "Test User " + uuid.New().String()[:8],
		Language:    "en",
	})
	require.NoError(s.T(), err)
	return user
}

func (s *SocialFeaturesTestSuite) createTestPost() db.Post {
	post, err := s.Store.CreatePost(context.Background(), db.CreatePostParams{
		OrganizationID: s.testOrg.ID,
		Title:          "Test Post for Social Features",
		Content:        json.RawMessage(`{"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "This is a test post"}]}]}`),
		AuthorID:       s.testUser.ID,
		Status:         "published",
	})
	require.NoError(s.T(), err)
	return post
}

func (s *SocialFeaturesTestSuite) createTestComment() *social.Comment {
	comment, err := s.socialService.CreateComment(context.Background(), social.CreateCommentRequest{
		OrganizationID: s.testOrg.ID,
		ContentType:    social.ContentTypePost,
		ContentID:      s.testPost.ID,
		Content:        "Test comment for social features",
		UserID:         s.testUser.ID,
		UserName:       s.testUser.DisplayName,
	})
	require.NoError(s.T(), err)
	return comment
}
