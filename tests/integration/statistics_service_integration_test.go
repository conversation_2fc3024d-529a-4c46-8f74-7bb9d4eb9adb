package integration

import (
	"context"
	"fmt"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/testutil"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// Helper functions (avoid conflicts with other test files)
func strPtr(s string) *string {
	return &s
}

func int32Ptr(i int32) *int32 {
	return &i
}

func numericZero() pgtype.Numeric {
	n := pgtype.Numeric{}
	n.Scan("0")
	return n
}

type StatisticsServiceIntegrationTestSuite struct {
	suite.Suite
	baseStore         db.Store
	ctx               context.Context
	testOrg           db.Organization
	testUsers         []db.User
	testEvents        []db.Event
	testRegistrations []db.EventRegistration
}

func (suite *StatisticsServiceIntegrationTestSuite) SetupSuite() {
	suite.ctx = context.Background()

	// Setup test database
	testDB := testutil.SetupTestDatabase(suite.T())
	suite.baseStore = db.NewStore(testDB.DB)
}

func (suite *StatisticsServiceIntegrationTestSuite) SetupTest() {
	// Create test organization
	org, err := suite.baseStore.CreateOrganization(suite.ctx, db.CreateOrganizationParams{
		Name:         "Test Statistics Org",
		Description:  strPtr("Organization for statistics testing"),
		OwnerUserID:  uuid.New(),
		IsDefaultOrg: false,
		Status:       "active",
	})
	require.NoError(suite.T(), err)
	suite.testOrg = org

	// Create test users
	suite.testUsers = make([]db.User, 10)
	now := time.Now()
	for i := 0; i < 10; i++ {
		user, err := suite.baseStore.CreateUserWithPhone(suite.ctx, db.CreateUserWithPhoneParams{
			Phone:                       strPtr(fmt.Sprintf("+1234567%04d", i)),
			DisplayName:                 fmt.Sprintf("Test User %d", i),
			PhoneVerifiedAt:             &now,
			InterfaceLanguage:           "en",
			CommunicationLanguage:       "en",
			EnableAppNotifications:      true,
			EnableWhatsappNotifications: false,
			EnableSmsNotifications:      false,
			EnableEmailNotifications:    true,
			PhoneOtpChannel:             "sms",
		})
		require.NoError(suite.T(), err)
		suite.testUsers[i] = user

		// Add user to organization
		_, err = suite.baseStore.AddUserToOrganization(suite.ctx, db.AddUserToOrganizationParams{
			UserID:               user.ID,
			OrganizationID:       org.ID,
			Role:                 "member",
			IsActive:             true,
			NotificationsEnabled: true,
		})
		require.NoError(suite.T(), err)
	}

	// Create test events
	suite.testEvents = make([]db.Event, 5)
	for i := 0; i < 5; i++ {
		startTime := time.Now().Add(time.Duration(i*24) * time.Hour)
		event, err := suite.baseStore.CreateEvent(suite.ctx, db.CreateEventParams{
			OrganizationID:                  org.ID,
			Title:                           fmt.Sprintf("Test Event %d", i),
			DescriptionContent:              []byte(fmt.Sprintf("Event %d for statistics testing", i)),
			StartTime:                       startTime,
			EndTime:                         startTime.Add(2 * time.Hour),
			LocationType:                    db.EventLocationTypePhysical,
			LocationFullAddress:             strPtr("123 Test St"),
			ParticipantLimit:                int32Ptr(100),
			Status:                          db.EventStatusTypePublished,
			CreatedByUserID:                 org.OwnerUserID,
			Price:                           strPtr("0"),
			RequiresApprovalForRegistration: false,
		})
		require.NoError(suite.T(), err)
		suite.testEvents[i] = event
	}

	// Create test registrations
	suite.testRegistrations = []db.EventRegistration{}
	for eventIdx, event := range suite.testEvents {
		// Register different number of users for each event
		numRegistrations := (eventIdx + 1) * 2 // 2, 4, 6, 8, 10 registrations
		for userIdx := 0; userIdx < numRegistrations && userIdx < len(suite.testUsers); userIdx++ {
			regResult, err := suite.baseStore.CreateEventRegistration(suite.ctx, db.CreateEventRegistrationParams{
				EventID:          event.ID,
				UserID:           suite.testUsers[userIdx].ID,
				Status:           db.EventRegistrationStatusTypeRegistered,
				PaymentStatus:    db.PaymentStatusTypeNotRequired,
				RegistrationRole: db.EventRegistrationRoleTypeParticipant,
			})
			require.NoError(suite.T(), err)

			// Store the registration (converting from CreateEventRegistrationRow to EventRegistration)
			reg := db.EventRegistration{
				ID:                       regResult.ID,
				EventID:                  regResult.EventID,
				UserID:                   regResult.UserID,
				Status:                   regResult.Status,
				PaymentStatus:            regResult.PaymentStatus,
				RegistrationRole:         regResult.RegistrationRole,
				RegisteredAt:             regResult.RegisteredAt,
				AttendedAt:               regResult.AttendedAt,
				CancellationReasonByUser: regResult.CancellationReasonByUser,
				AdminNotesOnRegistration: regResult.AdminNotesOnRegistration,
				WaitlistPriority:         regResult.WaitlistPriority,
				CreatedAt:                regResult.CreatedAt,
				UpdatedAt:                regResult.UpdatedAt,
			}

			suite.testRegistrations = append(suite.testRegistrations, reg)
		}
	}
}

func (suite *StatisticsServiceIntegrationTestSuite) TearDownTest() {
	// Clean up test data in reverse order
	// Note: Event registrations are deleted via cascade when events are deleted
	for _, event := range suite.testEvents {
		_ = suite.baseStore.DeleteEvent(suite.ctx, event.ID)
	}
	// Remove users from organization before deleting
	for _, user := range suite.testUsers {
		_ = suite.baseStore.RemoveUserFromOrganization(suite.ctx, db.RemoveUserFromOrganizationParams{
			UserID:         user.ID,
			OrganizationID: suite.testOrg.ID,
		})
		// Note: User deletion might not be available in the schema
	}
	_ = suite.baseStore.DeleteOrganization(suite.ctx, suite.testOrg.ID)
}

func (suite *StatisticsServiceIntegrationTestSuite) TestEventRegistrationStatistics() {
	t := suite.T()

	// Test registration counts for each event
	for i, event := range suite.testEvents {
		// Count registrations for this event
		count, err := suite.baseStore.CountEventRegistrations(suite.ctx, event.ID)
		require.NoError(t, err)

		expectedRegistrations := int64((i + 1) * 2)
		assert.Equal(t, expectedRegistrations, count,
			"Event %s should have %d registrations", event.Title, expectedRegistrations)
	}
}

func (suite *StatisticsServiceIntegrationTestSuite) TestUserEventStatistics() {
	t := suite.T()

	// Test statistics for first user (registered to all events)
	firstUser := suite.testUsers[0]

	// Get user's attended events
	attendedEvents, err := suite.baseStore.GetUserTotalAttendedEvents(suite.ctx, firstUser.ID)
	require.NoError(t, err)

	// First user is registered to all 5 events but none are marked as attended yet
	assert.Equal(t, int64(0), attendedEvents,
		"User should have 0 attended events (only registered, not attended)")
}

func (suite *StatisticsServiceIntegrationTestSuite) TestOrganizationMemberCount() {
	t := suite.T()

	// Get organization members
	members, err := suite.baseStore.ListOrganizationMembers(suite.ctx, suite.testOrg.ID)
	require.NoError(t, err)

	assert.Len(t, members, 10, "Organization should have 10 members")
}

func (suite *StatisticsServiceIntegrationTestSuite) TestEventCountByOrganization() {
	t := suite.T()

	// Count events for organization
	count, err := suite.baseStore.CountEventsByOrganization(suite.ctx, db.CountEventsByOrganizationParams{
		OrganizationID: suite.testOrg.ID,
		// Use nil for status filter to count all events
	})
	require.NoError(t, err)

	assert.Equal(t, int64(5), count, "Organization should have 5 events")
}

func (suite *StatisticsServiceIntegrationTestSuite) TestUserStatisticsMethods() {
	t := suite.T()

	user := suite.testUsers[0]

	// Test GetUserRegistrationDate
	regDate, err := suite.baseStore.GetUserRegistrationDate(suite.ctx, user.ID)
	require.NoError(t, err)
	assert.NotZero(t, regDate)

	// Test GetUserTotalVolunteerEvents
	volunteerCount, err := suite.baseStore.GetUserTotalVolunteerEvents(suite.ctx, user.ID)
	require.NoError(t, err)
	assert.Equal(t, int64(0), volunteerCount, "User should have 0 volunteer events")

	// Test GetMonthlyAttendedEvents
	monthlyStats, err := suite.baseStore.GetMonthlyAttendedEvents(suite.ctx, user.ID)
	require.NoError(t, err)
	assert.Len(t, monthlyStats, 0, "User should have no attended events yet")
}

func (suite *StatisticsServiceIntegrationTestSuite) TestConcurrentStatisticsAccess() {
	t := suite.T()

	// Test concurrent access to statistics
	concurrentRequests := 10
	errors := make(chan error, concurrentRequests)
	results := make(chan int64, concurrentRequests)

	for i := 0; i < concurrentRequests; i++ {
		go func(eventIdx int) {
			event := suite.testEvents[eventIdx%len(suite.testEvents)]
			count, err := suite.baseStore.CountEventRegistrations(suite.ctx, event.ID)
			if err != nil {
				errors <- err
			} else {
				results <- count
			}
		}(i)
	}

	// Collect results
	for i := 0; i < concurrentRequests; i++ {
		select {
		case err := <-errors:
			t.Fatalf("Concurrent access failed: %v", err)
		case count := <-results:
			assert.NotNil(t, count)
		}
	}
}

func (suite *StatisticsServiceIntegrationTestSuite) TestStatisticsPerformance() {
	t := suite.T()

	// Measure performance of statistics queries
	start := time.Now()

	// Run multiple statistics queries
	for i := 0; i < 100; i++ {
		event := suite.testEvents[i%len(suite.testEvents)]
		_, err := suite.baseStore.CountEventRegistrations(suite.ctx, event.ID)
		require.NoError(t, err)
	}

	elapsed := time.Since(start)
	avgQueryTime := elapsed / 100

	// Statistics queries should be fast (under 10ms average)
	assert.Less(t, avgQueryTime, 10*time.Millisecond,
		"Statistics queries taking too long: %v average", avgQueryTime)
}

// TestStatisticsDataIntegrity verifies data consistency
func (suite *StatisticsServiceIntegrationTestSuite) TestStatisticsDataIntegrity() {
	t := suite.T()

	// Verify total registrations across all events
	totalRegistrations := 0
	for i, event := range suite.testEvents {
		count, err := suite.baseStore.CountEventRegistrations(suite.ctx, event.ID)
		require.NoError(t, err)

		expectedForEvent := (i + 1) * 2
		assert.Equal(t, int64(expectedForEvent), count,
			"Event %d should have %d registrations", i, expectedForEvent)

		totalRegistrations += expectedForEvent
	}

	// Total should be 2+4+6+8+10 = 30
	assert.Equal(t, 30, totalRegistrations, "Total registrations should be 30")
}

func TestStatisticsServiceIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(StatisticsServiceIntegrationTestSuite))
}
