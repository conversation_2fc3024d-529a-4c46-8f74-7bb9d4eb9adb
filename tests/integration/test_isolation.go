package integration

import (
	"context"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/testutil"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/stretchr/testify/require"
)

// IsolatedTest runs a test in a database transaction that rolls back
// This ensures complete data isolation between tests
func IsolatedTest(t *testing.T, pool *pgxpool.Pool, fn func(ctx context.Context, queries *db.Queries)) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Begin transaction
	tx, err := pool.Begin(ctx)
	require.NoError(t, err, "Failed to begin transaction")

	// Ensure rollback on completion
	defer func() {
		rollbackCtx, rollbackCancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer rollbackCancel()

		if rollErr := tx.Rollback(rollbackCtx); rollErr != nil && rollErr != pgx.ErrTxClosed {
			t.<PERSON><PERSON><PERSON>("Failed to rollback test transaction: %v", rollErr)
		}
	}()

	// Create transaction-scoped queries
	queries := db.New(tx)

	// Execute test function
	fn(ctx, queries)
}

// IsolatedTestWithStore runs a test with a transaction-scoped store
// This is the preferred method for tests that need the full Store interface
func IsolatedTestWithStore(t *testing.T, pool *pgxpool.Pool, fn func(ctx context.Context, store db.Store)) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Begin transaction
	tx, err := pool.Begin(ctx)
	require.NoError(t, err, "Failed to begin transaction")

	// Ensure rollback on completion
	defer func() {
		rollbackCtx, rollbackCancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer rollbackCancel()

		if rollErr := tx.Rollback(rollbackCtx); rollErr != nil && rollErr != pgx.ErrTxClosed {
			t.Errorf("Failed to rollback test transaction: %v", rollErr)
		}
	}()

	// Create transaction-scoped store
	store := &TransactionStore{
		Querier: db.New(tx),
		tx:      tx,
	}

	// Execute test function
	fn(ctx, store)
}

// TransactionStore implements db.Store interface for transaction-scoped operations
type TransactionStore struct {
	db.Querier
	tx pgx.Tx
}

// ExecTx implements the Store interface method
// Since we're already in a transaction, we just execute the function with our Querier
func (s *TransactionStore) ExecTx(ctx context.Context, fn func(db.Querier) error) error {
	// No need to start a new transaction - we're already in one
	return fn(s.Querier)
}

// Exec implements direct SQL execution required by Store interface
func (s *TransactionStore) Exec(ctx context.Context, sql string, args ...any) (pgconn.CommandTag, error) {
	return s.tx.Exec(ctx, sql, args...)
}

// Pool is not supported in transaction context - returns nil
func (s *TransactionStore) Pool() *pgxpool.Pool {
	return nil
}

// IsolatedTestSetup provides a convenient way to set up isolated tests with test data
type IsolatedTestSetup struct {
	t    *testing.T
	pool *pgxpool.Pool
}

// NewIsolatedTestSetup creates a new isolated test setup
func NewIsolatedTestSetup(t *testing.T) *IsolatedTestSetup {
	// Get or create shared test database
	testDB := testutil.SetupTestDatabase(t)
	testDB.LoadSchema(t)

	return &IsolatedTestSetup{
		t:    t,
		pool: testDB.DB,
	}
}

// Run executes a test function in an isolated transaction with test data setup
func (s *IsolatedTestSetup) Run(fn func(ctx context.Context, store db.Store, data *TestDataSetup)) {
	IsolatedTestWithStore(s.t, s.pool, func(ctx context.Context, store db.Store) {
		// Get transaction-scoped queries from the store
		txStore := store.(*TransactionStore)
		txQueries := db.New(txStore.tx)

		// Create factory within the transaction scope
		factory := testutil.NewUniqueTestDataFactory(s.t, txQueries, ctx)

		// Create test data with transaction-scoped queries
		data := CreateTestDataWithFactory(s.t, ctx, factory, txQueries)

		// Execute test with data
		fn(ctx, store, data)
	})
}

// RunWithCustomData executes a test function allowing custom test data setup
func (s *IsolatedTestSetup) RunWithCustomData(
	setupFn func(ctx context.Context, store db.Store) interface{},
	testFn func(ctx context.Context, store db.Store, data interface{}),
) {
	IsolatedTestWithStore(s.t, s.pool, func(ctx context.Context, store db.Store) {
		// Set up custom test data
		data := setupFn(ctx, store)

		// Execute test with custom data
		testFn(ctx, store, data)
	})
}

// RunWithQueries executes a test function with direct access to transaction-scoped queries
// This is useful for tests that need to interact with the database directly
func (s *IsolatedTestSetup) RunWithQueries(fn func(ctx context.Context, queries *db.Queries)) {
	IsolatedTest(s.t, s.pool, fn)
}
