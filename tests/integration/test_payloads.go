package integration

import (
	"time"

	"github.com/google/uuid"
)

// Placeholder payload types for integration tests
// These represent the expected API response structures

type EventResponse struct {
	ID          uuid.UUID     `json:"id"`
	Title       string        `json:"title"`
	Description string        `json:"description"`
	Location    string        `json:"location"`
	StartTime   time.Time     `json:"start_time"`
	EndTime     time.Time     `json:"end_time"`
	Capacity    int32         `json:"capacity"`
	Status      string        `json:"status"`
	PublishedAt *time.Time    `json:"published_at"`
	CreatedAt   time.Time     `json:"created_at"`
	UpdatedAt   time.Time     `json:"updated_at"`
	Tags        []TagResponse `json:"tags,omitempty"`
}

type TagResponse struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type PaginatedEventsResponse struct {
	Events     []EventResponse `json:"events"`
	Pagination PaginationInfo  `json:"pagination"`
}

type PaginationInfo struct {
	Page       int   `json:"page"`
	Limit      int   `json:"limit"`
	TotalPages int   `json:"total_pages"`
	TotalItems int64 `json:"total_items"`
}

type EventRegistrationResponse struct {
	ID           uuid.UUID `json:"id"`
	EventID      uuid.UUID `json:"event_id"`
	UserID       uuid.UUID `json:"user_id"`
	Status       string    `json:"status"`
	RegisteredAt time.Time `json:"registered_at"`
}

type PaginatedEventRegistrationResponse struct {
	Registrations []EventRegistrationResponse `json:"registrations"`
	Pagination    PaginationInfo              `json:"pagination"`
}

type VolunteerApplicationResponse struct {
	ID        uuid.UUID `json:"id"`
	EventID   uuid.UUID `json:"event_id"`
	UserID    uuid.UUID `json:"user_id"`
	Status    string    `json:"status"`
	CreatedAt time.Time `json:"created_at"`
}

type PaginatedVolunteerApplicationResponse struct {
	Applications []VolunteerApplicationResponse `json:"applications"`
	Pagination   PaginationInfo                 `json:"pagination"`
}

type EventStatisticsResponse struct {
	RegistrationStats RegistrationStats `json:"registration_stats"`
	VolunteerStats    VolunteerStats    `json:"volunteer_stats"`
}

type RegistrationStats struct {
	Total     int32 `json:"total"`
	Attending int32 `json:"attending"`
	Waitlist  int32 `json:"waitlist"`
}

type VolunteerStats struct {
	Total    int32 `json:"total"`
	Approved int32 `json:"approved"`
	Pending  int32 `json:"pending"`
}

// Event Media
type EventMediaResponse struct {
	ID          uuid.UUID `json:"id"`
	EventID     uuid.UUID `json:"event_id"`
	FileName    string    `json:"file_name"`
	FileURL     string    `json:"file_url"`
	Description string    `json:"description"`
	IsBanner    bool      `json:"is_banner"`
	CreatedAt   time.Time `json:"created_at"`
}

// User related
type UserProfileResponse struct {
	ID                          uuid.UUID  `json:"id"`
	DisplayName                 string     `json:"display_name"`
	Phone                       *string    `json:"phone,omitempty"`
	PhoneVerifiedAt             *time.Time `json:"phone_verified_at,omitempty"`
	Email                       *string    `json:"email,omitempty"`
	EmailVerifiedAt             *time.Time `json:"email_verified_at,omitempty"`
	ProfilePictureURL           *string    `json:"profile_picture_url,omitempty"`
	PhoneOtpChannel             *string    `json:"phone_otp_channel,omitempty"`
	InterfaceLanguage           string     `json:"interface_language"`
	CommunicationLanguage       string     `json:"communication_language"`
	EnableAppNotifications      bool       `json:"enable_app_notifications"`
	EnableWhatsappNotifications bool       `json:"enable_whatsapp_notifications"`
	EnableSmsNotifications      bool       `json:"enable_sms_notifications"`
	EnableEmailNotifications    bool       `json:"enable_email_notifications"`
	CreatedAt                   time.Time  `json:"created_at"`
	UpdatedAt                   time.Time  `json:"updated_at"`
}

type UserVerificationResponse struct {
	ID               uuid.UUID `json:"id"`
	UserID           uuid.UUID `json:"user_id"`
	VerificationType string    `json:"verification_type"`
	Status           string    `json:"status"`
	CreatedAt        time.Time `json:"created_at"`
}

type PaginatedUserVerificationResponse struct {
	Requests   []UserVerificationResponse `json:"requests"`
	Pagination PaginationInfo             `json:"pagination"`
}

type UserPreferencesResponse struct {
	Language                string `json:"language"`
	Timezone                string `json:"timezone"`
	EmailNotifications      bool   `json:"email_notifications"`
	SMSNotifications        bool   `json:"sms_notifications"`
	MarketingCommunications bool   `json:"marketing_communications"`
}

type UserProfilePictureResponse struct {
	ImageURL string `json:"image_url"`
	FileName string `json:"file_name"`
}

type UserSessionsResponse struct {
	Sessions []UserSession `json:"sessions"`
}

type UserSession struct {
	ID        string    `json:"id"`
	Device    string    `json:"device"`
	Location  string    `json:"location"`
	CreatedAt time.Time `json:"created_at"`
	LastUsed  time.Time `json:"last_used"`
}

type DataExportResponse struct {
	RequestID string    `json:"request_id"`
	Status    string    `json:"status"`
	CreatedAt time.Time `json:"created_at"`
}

type PaginatedNotificationResponse struct {
	Notifications []NotificationResponse `json:"notifications"`
	Pagination    PaginationInfo         `json:"pagination"`
}

type NotificationResponse struct {
	ID       string    `json:"id"`
	Title    string    `json:"title"`
	Content  string    `json:"content"`
	IsRead   bool      `json:"is_read"`
	Type     string    `json:"type"`
	CreateAt time.Time `json:"created_at"`
}

// Organization related
type OrganizationResponse struct {
	ID          uuid.UUID `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	Address     string    `json:"address"`
	Phone       string    `json:"phone"`
	Email       string    `json:"email"`
	Website     string    `json:"website"`
	ThemeColor  string    `json:"theme_color"`
	Status      string    `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type PaginatedOrganizationMembersResponse struct {
	Members    []OrganizationMemberResponse `json:"members"`
	Pagination PaginationInfo               `json:"pagination"`
}

type OrganizationMemberResponse struct {
	ID       uuid.UUID `json:"id"`
	UserID   uuid.UUID `json:"user_id"`
	Role     string    `json:"role"`
	Status   string    `json:"status"`
	JoinedAt time.Time `json:"joined_at"`
}

type MemberInvitationResponse struct {
	ID              uuid.UUID `json:"id"`
	Email           string    `json:"email"`
	Role            string    `json:"role"`
	Status          string    `json:"status"`
	InvitationToken string    `json:"invitation_token"`
	CreatedAt       time.Time `json:"created_at"`
}

type ResourceResponse struct {
	ID          uuid.UUID `json:"id"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	Type        string    `json:"type"`
	Category    string    `json:"category"`
	AccessLevel string    `json:"access_level"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type PaginatedResourceResponse struct {
	Resources  []ResourceResponse `json:"resources"`
	Pagination PaginationInfo     `json:"pagination"`
}

type ResourceFileResponse struct {
	ID          uuid.UUID `json:"id"`
	ResourceID  uuid.UUID `json:"resource_id"`
	FileName    string    `json:"file_name"`
	FileURL     string    `json:"file_url"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"created_at"`
}

type OrganizationStatisticsResponse struct {
	MemberStats    MemberStats    `json:"member_stats"`
	EventStats     EventStats     `json:"event_stats"`
	VolunteerStats VolunteerStats `json:"volunteer_stats"`
}

type MemberStats struct {
	Total  int32 `json:"total"`
	Active int32 `json:"active"`
	New    int32 `json:"new"`
}

type EventStats struct {
	Total     int32 `json:"total"`
	Published int32 `json:"published"`
	Draft     int32 `json:"draft"`
}

type OrganizationSettingsResponse struct {
	Features    OrganizationFeatures    `json:"features"`
	Preferences OrganizationPreferences `json:"preferences"`
}

type OrganizationFeatures struct {
	EventRegistration   bool `json:"event_registration"`
	VolunteerManagement bool `json:"volunteer_management"`
	ResourceSharing     bool `json:"resource_sharing"`
	MemberVerification  bool `json:"member_verification"`
}

type OrganizationPreferences struct {
	DefaultLanguage    string `json:"default_language"`
	Timezone           string `json:"timezone"`
	PublicProfile      bool   `json:"public_profile"`
	AutoApproveMembers bool   `json:"auto_approve_members"`
}

type OrganizationLogoResponse struct {
	ImageURL string `json:"image_url"`
	FileName string `json:"file_name"`
}

type PaginatedAuditLogResponse struct {
	Entries    []AuditLogEntry `json:"entries"`
	Pagination PaginationInfo  `json:"pagination"`
}

type AuditLogEntry struct {
	ID        uuid.UUID `json:"id"`
	Action    string    `json:"action"`
	UserID    uuid.UUID `json:"user_id"`
	Timestamp time.Time `json:"timestamp"`
	Details   string    `json:"details"`
}

// Post related
type PostResponse struct {
	ID        uuid.UUID `json:"id"`
	Title     string    `json:"title"`
	Content   string    `json:"content"`
	Status    string    `json:"status"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type PostMediaResponse struct {
	ID          uuid.UUID `json:"id"`
	PostID      uuid.UUID `json:"post_id"`
	FileName    string    `json:"file_name"`
	FileURL     string    `json:"file_url"`
	Description string    `json:"description"`
	IsBanner    bool      `json:"is_banner"`
	CreatedAt   time.Time `json:"created_at"`
}

// File upload
type BatchUploadResponse struct {
	UploadedFiles []UploadedFile `json:"uploaded_files"`
	FailedFiles   []FailedFile   `json:"failed_files"`
}

type UploadedFile struct {
	FileName string `json:"file_name"`
	FileURL  string `json:"file_url"`
}

type FailedFile struct {
	FileName string `json:"file_name"`
	Error    string `json:"error"`
}
