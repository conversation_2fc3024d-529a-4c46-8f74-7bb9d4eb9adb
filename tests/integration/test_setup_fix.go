package integration

import (
	"context"
	"fmt"
	"testing"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/testutil"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
)

// TestDataSetup provides a standardized way to create test data with proper relationships
type TestDataSetup struct {
	// Core entities
	AdminUser    db.User
	RegularUser  db.User
	Organization db.Organization
	Event        db.Event

	// Additional entities
	SecondOrganization db.Organization
	SecondUser         db.User
}

// CreateTestDataSetup creates a complete test data setup with all relationships properly established
func CreateTestDataSetup(t *testing.T, ctx context.Context, queries *db.Queries) *TestDataSetup {
	// Step 1: Create users first (they are independent)
	adminUser := testutil.CreateDBUser(t, queries, func(params *db.CreateUserWithPhoneParams) {
		params.DisplayName = "Test Admin"
		params.Phone = testutil.ToPtr("+1555000001")
	})

	regularUser := testutil.CreateDBUser(t, queries, func(params *db.CreateUserWithPhoneParams) {
		params.DisplayName = "Test User"
		params.Phone = testutil.ToPtr("+1555000002")
	})

	secondUser := testutil.CreateDBUser(t, queries, func(params *db.CreateUserWithPhoneParams) {
		params.DisplayName = "Second User"
		params.Phone = testutil.ToPtr("+1555000003")
	})

	// Step 2: Create organizations with valid owner IDs
	organization := testutil.CreateDBOrganization(t, queries, func(params *db.CreateOrganizationParams) {
		params.Name = "Test Organization"
		params.OwnerUserID = adminUser.ID
		params.Status = "active"
	})

	secondOrganization := testutil.CreateDBOrganization(t, queries, func(params *db.CreateOrganizationParams) {
		params.Name = "Second Organization"
		params.OwnerUserID = adminUser.ID
		params.Status = "active"
	})

	// Step 3: Add users to organizations with proper memberships
	_, err := queries.AddUserToOrganization(ctx, db.AddUserToOrganizationParams{
		UserID:         adminUser.ID,
		OrganizationID: organization.ID,
		Role:           "admin",
		IsActive:       true,
	})
	require.NoError(t, err, "Should add admin to organization")

	_, err = queries.AddUserToOrganization(ctx, db.AddUserToOrganizationParams{
		UserID:         regularUser.ID,
		OrganizationID: organization.ID,
		Role:           "member",
		IsActive:       true,
	})
	require.NoError(t, err, "Should add regular user to organization")

	// Step 4: Create event with valid organization and user IDs
	event := testutil.CreateDBEvent(t, queries, func(params *db.CreateEventParams) {
		params.OrganizationID = organization.ID
		params.CreatedByUserID = adminUser.ID
		params.Title = "Test Event"
		params.Status = db.EventStatusTypePublished
	})

	return &TestDataSetup{
		AdminUser:          adminUser,
		RegularUser:        regularUser,
		Organization:       organization,
		Event:              event,
		SecondOrganization: secondOrganization,
		SecondUser:         secondUser,
	}
}

// CreateTestDataWithFactory creates test data using the UniqueTestDataFactory for transaction-based tests
func CreateTestDataWithFactory(t *testing.T, ctx context.Context, factory *testutil.UniqueTestDataFactory, queries *db.Queries) *TestDataSetup {
	// Step 1: Create users with unique data
	adminUser, err := factory.CreateUniqueUser()
	require.NoError(t, err, "Should create admin user")

	regularUser, err := factory.CreateUniqueUser()
	require.NoError(t, err, "Should create regular user")

	secondUser, err := factory.CreateUniqueUser()
	require.NoError(t, err, "Should create second user")

	// Step 2: Create organizations
	organization, err := factory.CreateUniqueOrganization(adminUser.ID)
	require.NoError(t, err, "Should create organization")

	secondOrganization, err := factory.CreateUniqueOrganization(adminUser.ID)
	require.NoError(t, err, "Should create second organization")

	// Step 3: Add memberships
	_, err = queries.AddUserToOrganization(ctx, db.AddUserToOrganizationParams{
		UserID:         adminUser.ID,
		OrganizationID: organization.ID,
		Role:           "admin",
		IsActive:       true,
	})
	require.NoError(t, err, "Should add admin to organization")

	_, err = queries.AddUserToOrganization(ctx, db.AddUserToOrganizationParams{
		UserID:         regularUser.ID,
		OrganizationID: organization.ID,
		Role:           "member",
		IsActive:       true,
	})
	require.NoError(t, err, "Should add regular user to organization")

	// Step 4: Create event
	event, err := factory.CreateUniqueEvent(organization.ID, adminUser.ID)
	require.NoError(t, err, "Should create event")

	return &TestDataSetup{
		AdminUser:          adminUser,
		RegularUser:        regularUser,
		Organization:       organization,
		Event:              event,
		SecondOrganization: secondOrganization,
		SecondUser:         secondUser,
	}
}

// SuiteTestDataHelper helps manage test data for test suites
type SuiteTestDataHelper struct {
	t       *testing.T
	queries *db.Queries
	ctx     context.Context
}

// NewSuiteTestDataHelper creates a new helper for test suites
func NewSuiteTestDataHelper(t *testing.T, queries *db.Queries, ctx context.Context) *SuiteTestDataHelper {
	return &SuiteTestDataHelper{
		t:       t,
		queries: queries,
		ctx:     ctx,
	}
}

// SetupSuiteData initializes test data for a test suite
// This should be called in SetupTest() for suites that don't use transaction-based tests
func (h *SuiteTestDataHelper) SetupSuiteData() *TestDataSetup {
	return CreateTestDataSetup(h.t, h.ctx, h.queries)
}

// CreateAdditionalUser creates an additional user with a unique phone number
func (h *SuiteTestDataHelper) CreateAdditionalUser(displayName string, phoneNum int) db.User {
	return testutil.CreateDBUser(h.t, h.queries, func(params *db.CreateUserWithPhoneParams) {
		params.DisplayName = displayName
		params.Phone = testutil.ToPtr(fmt.Sprintf("+155500%05d", phoneNum))
	})
}

// CreateAdditionalOrganization creates an additional organization
func (h *SuiteTestDataHelper) CreateAdditionalOrganization(name string, ownerID uuid.UUID) db.Organization {
	return testutil.CreateDBOrganization(h.t, h.queries, func(params *db.CreateOrganizationParams) {
		params.Name = name
		params.OwnerUserID = ownerID
		params.Status = "active"
	})
}

// CreateAdditionalEvent creates an additional event with proper relationships
func (h *SuiteTestDataHelper) CreateAdditionalEvent(title string, orgID, creatorID uuid.UUID) db.Event {
	return testutil.CreateDBEvent(h.t, h.queries, func(params *db.CreateEventParams) {
		params.Title = title
		params.OrganizationID = orgID
		params.CreatedByUserID = creatorID
		params.Status = db.EventStatusTypePublished
	})
}

// AddUserToOrganization adds a user to an organization with the specified role
func (h *SuiteTestDataHelper) AddUserToOrganization(userID, orgID uuid.UUID, role string) error {
	_, err := h.queries.AddUserToOrganization(h.ctx, db.AddUserToOrganizationParams{
		UserID:         userID,
		OrganizationID: orgID,
		Role:           role,
		IsActive:       true,
	})
	return err
}
