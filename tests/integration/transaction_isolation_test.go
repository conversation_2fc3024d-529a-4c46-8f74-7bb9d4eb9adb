package integration

import (
	"context"
	"fmt"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/testutil"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestTransactionIsolation demonstrates transaction-based test isolation using Store pattern
func TestTransactionIsolation(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration tests in short mode")
	}

	// Setup test database with Store pattern
	_, store := testutil.SetupTestDatabaseWithStore(t)

	// Test 1: Create data in transaction (will be rolled back)
	testutil.RunInTransactionWithStore(t, store, func(ctx context.Context, txStore db.Store) {
		// Create a user in the transaction
		user, err := txStore.CreateUserWithPhone(ctx, db.CreateUserWithPhoneParams{
			DisplayName:                 "Transaction Test User",
			Phone:                       testutil.ToPtr("+19999999999"),
			PhoneVerifiedAt:             testutil.ToPtr(time.Now()),
			InterfaceLanguage:           "en",
			CommunicationLanguage:       "en",
			PhoneOtpChannel:             "sms",
			EnableAppNotifications:      true,
			EnableWhatsappNotifications: false,
			EnableSmsNotifications:      true,
			EnableEmailNotifications:    false,
		})
		require.NoError(t, err)
		assert.NotEqual(t, uuid.Nil, user.ID)

		// Verify user exists within transaction
		foundUser, err := txStore.GetUserByID(ctx, user.ID)
		require.NoError(t, err)
		assert.Equal(t, user.ID, foundUser.ID)

		// Create organization with the user as owner
		org, err := txStore.CreateOrganization(ctx, db.CreateOrganizationParams{
			Name:         "Transaction Test Org",
			OwnerUserID:  user.ID,
			IsDefaultOrg: false,
			Status:       "active",
		})
		require.NoError(t, err)
		assert.NotEqual(t, uuid.Nil, org.ID)

		// Verify organization exists within transaction
		foundOrg, err := txStore.GetOrganizationByID(ctx, org.ID)
		require.NoError(t, err)
		assert.Equal(t, org.ID, foundOrg.ID)
	})

	// Test 2: Verify data doesn't exist outside transaction
	ctx := context.Background()

	// Try to find users with our test phone number
	users, err := store.ListUsers(ctx, db.ListUsersParams{
		Limit:  10,
		Offset: 0,
	})
	require.NoError(t, err)

	// Verify our test user doesn't exist
	for _, user := range users {
		if user.Phone != nil && *user.Phone == "+19999999999" {
			t.Errorf("Test user should not exist after transaction rollback")
		}
	}

	// Test 3: Multiple isolated transactions
	var createdUserIDs []uuid.UUID

	// First transaction
	testutil.RunInTransactionWithStore(t, store, func(ctx context.Context, txStore db.Store) {
		user, err := txStore.CreateUserWithPhone(ctx, db.CreateUserWithPhoneParams{
			DisplayName:                 "Isolated User 1",
			Phone:                       testutil.ToPtr("+18888888881"),
			InterfaceLanguage:           "en",
			CommunicationLanguage:       "en",
			PhoneOtpChannel:             "sms",
			EnableAppNotifications:      true,
			EnableWhatsappNotifications: false,
			EnableSmsNotifications:      true,
			EnableEmailNotifications:    false,
		})
		require.NoError(t, err)
		createdUserIDs = append(createdUserIDs, user.ID)
	})

	// Second transaction (should not see first transaction's data)
	testutil.RunInTransactionWithStore(t, store, func(ctx context.Context, txStore db.Store) {
		// Try to find the user from first transaction
		_, err := txStore.GetUserByID(ctx, createdUserIDs[0])
		assert.Error(t, err, "Should not find user from rolled back transaction")

		// Create another user
		user, err := txStore.CreateUserWithPhone(ctx, db.CreateUserWithPhoneParams{
			DisplayName:                 "Isolated User 2",
			Phone:                       testutil.ToPtr("+18888888882"),
			InterfaceLanguage:           "en",
			CommunicationLanguage:       "en",
			PhoneOtpChannel:             "sms",
			EnableAppNotifications:      true,
			EnableWhatsappNotifications: false,
			EnableSmsNotifications:      true,
			EnableEmailNotifications:    false,
		})
		require.NoError(t, err)
		createdUserIDs = append(createdUserIDs, user.ID)
	})

	// Verify neither user exists after both transactions
	for _, userID := range createdUserIDs {
		_, err := store.GetUserByID(ctx, userID)
		assert.Error(t, err, "Users from rolled back transactions should not exist")
	}
}

// TestConcurrentTransactionIsolation tests that concurrent transactions are properly isolated using Store pattern
func TestConcurrentTransactionIsolation(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration tests in short mode")
	}

	// Setup test database with Store pattern
	_, store := testutil.SetupTestDatabaseWithStore(t)

	// Number of concurrent transactions
	numGoroutines := 10
	done := make(chan bool, numGoroutines)

	// Run concurrent transactions
	for i := 0; i < numGoroutines; i++ {
		go func(index int) {
			testutil.RunInTransactionWithStore(t, store, func(ctx context.Context, txStore db.Store) {
				// Each transaction creates a user with unique phone
				phoneNumber := testutil.ToPtr(fmt.Sprintf("+1777777%04d", index))

				user, err := txStore.CreateUserWithPhone(ctx, db.CreateUserWithPhoneParams{
					DisplayName:                 fmt.Sprintf("Concurrent User %d", index),
					Phone:                       phoneNumber,
					InterfaceLanguage:           "en",
					CommunicationLanguage:       "en",
					PhoneOtpChannel:             "sms",
					EnableAppNotifications:      true,
					EnableWhatsappNotifications: false,
					EnableSmsNotifications:      true,
					EnableEmailNotifications:    false,
				})
				require.NoError(t, err)

				// Create organization owned by this user
				org, err := txStore.CreateOrganization(ctx, db.CreateOrganizationParams{
					Name:         fmt.Sprintf("Concurrent Org %d", index),
					OwnerUserID:  user.ID,
					IsDefaultOrg: false,
					Status:       "active",
				})
				require.NoError(t, err)

				// Verify data exists within transaction
				foundOrg, err := txStore.GetOrganizationByID(ctx, org.ID)
				require.NoError(t, err)
				assert.Equal(t, org.Name, foundOrg.Name)

				// Simulate some work
				time.Sleep(10 * time.Millisecond)
			})
			done <- true
		}(i)
	}

	// Wait for all goroutines to complete
	for i := 0; i < numGoroutines; i++ {
		<-done
	}

	// Verify no data persisted (all transactions rolled back)
	ctx := context.Background()

	// Count organizations (should only have seeded data if any)
	orgs, err := store.ListOrganizations(ctx, db.ListOrganizationsParams{
		Limit:  100,
		Offset: 0,
	})
	require.NoError(t, err)

	// Verify none of our test organizations exist
	for _, org := range orgs {
		assert.NotContains(t, org.Name, "Concurrent Org",
			"Concurrent test organizations should not exist after rollback")
	}
}

// TestTransactionIsolationWithPanics verifies that panics are handled correctly using Store pattern
func TestTransactionIsolationWithPanics(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration tests in short mode")
	}

	// Setup test database with Store pattern
	_, store := testutil.SetupTestDatabaseWithStore(t)

	// Test panic recovery - Note: RunInTransactionWithStore doesn't panic by design
	// It uses controlled rollback, so we test this differently
	testutil.RunInTransactionWithStore(t, store, func(ctx context.Context, txStore db.Store) {
		// Create a user
		user, err := txStore.CreateUserWithPhone(ctx, db.CreateUserWithPhoneParams{
			DisplayName:                 "Transaction Test User",
			Phone:                       testutil.ToPtr("+16666666666"),
			InterfaceLanguage:           "en",
			CommunicationLanguage:       "en",
			PhoneOtpChannel:             "sms",
			EnableAppNotifications:      true,
			EnableWhatsappNotifications: false,
			EnableSmsNotifications:      true,
			EnableEmailNotifications:    false,
		})
		require.NoError(t, err)

		// Verify user exists within transaction
		foundUser, err := txStore.GetUserByID(ctx, user.ID)
		require.NoError(t, err)
		assert.Equal(t, user.ID, foundUser.ID)

		// Transaction will be automatically rolled back after this function completes
	})

	// Verify user was not created (transaction rolled back automatically)
	ctx := context.Background()
	users, err := store.ListUsers(ctx, db.ListUsersParams{
		Limit:  100,
		Offset: 0,
	})
	require.NoError(t, err)

	for _, user := range users {
		if user.Phone != nil && *user.Phone == "+16666666666" {
			t.Errorf("User should not exist after transaction rollback")
		}
	}
}
