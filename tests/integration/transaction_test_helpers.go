package integration

import (
	"context"
	"fmt"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/config"
	"Membership-SAAS-System-Backend/internal/server"
	"Membership-SAAS-System-Backend/internal/testutil"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/stretchr/testify/require"
)

// TransactionalAPITestSuite provides transaction-isolated API testing
type TransactionalAPITestSuite struct {
	*APITestSuite
	tx      pgx.Tx
	queries *db.Queries
}

// SetupTransactionalAPITest sets up a test with automatic transaction rollback
func SetupTransactionalAPITest(t *testing.T) *TransactionalAPITestSuite {
	// Set up base API test
	baseSuite := SetupAPITest(t)

	// Get database pool from server
	pool := baseSuite.Server.ServiceContainer().DB.Pool()

	// Start transaction
	ctx := context.Background()
	tx, err := pool.Begin(ctx)
	require.NoError(t, err)

	// Create transaction-scoped queries
	queries := db.New(tx)

	// Set up cleanup to rollback transaction
	t.Cleanup(func() {
		rollbackCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		err := tx.Rollback(rollbackCtx)
		if err != nil && err != pgx.ErrTxClosed {
			t.Errorf("Failed to rollback test transaction: %v", err)
		}
	})

	return &TransactionalAPITestSuite{
		APITestSuite: baseSuite,
		tx:           tx,
		queries:      queries,
	}
}

// TransactionalTestRunner provides a simpler interface for transaction-based tests
type TransactionalTestRunner struct {
	t      *testing.T
	testDB *testutil.TestDatabase
	pool   *pgxpool.Pool
	config *config.Config
}

// NewTransactionalTestRunner creates a new runner for transaction-based tests
func NewTransactionalTestRunner(t *testing.T) *TransactionalTestRunner {
	testDB := testutil.SetupTestDatabase(t)
	testDB.LoadSchema(t)

	config := &config.Config{
		Database: config.DatabaseConfig{
			URL: testDB.URL,
		},
		Server: config.ServerConfig{
			Port:    "8082",
			Address: "0.0.0.0:8082",
		},
		JWT: config.JWTConfig{
			AccessTokenSecret:  "test-secret-key",
			RefreshTokenSecret: "test-refresh-secret",
			AccessTokenTTL:     15 * time.Minute,
			RefreshTokenTTL:    7 * 24 * time.Hour,
		},
	}

	return &TransactionalTestRunner{
		t:      t,
		testDB: testDB,
		pool:   testDB.DB,
		config: config,
	}
}

// RunInTransaction executes a test function within a transaction
func (r *TransactionalTestRunner) RunInTransaction(testFunc func(ctx context.Context, queries *db.Queries, srv *server.Server)) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Begin transaction
	tx, err := r.pool.Begin(ctx)
	require.NoError(r.t, err)

	// Ensure rollback
	defer func() {
		rollbackCtx, rollbackCancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer rollbackCancel()

		err := tx.Rollback(rollbackCtx)
		if err != nil && err != pgx.ErrTxClosed {
			r.t.Errorf("Failed to rollback test transaction: %v", err)
		}
	}()

	// Create transaction-scoped queries
	queries := db.New(tx)

	// Create a modified config that uses the transaction
	txConfig := *r.config

	// Create server with transaction-scoped database
	srv, err := server.New(&txConfig)
	require.NoError(r.t, err)

	// Replace the store in service container with transaction-scoped store
	if srv.ServiceContainer() != nil {
		// Create a transaction-scoped store
		txStore := &transactionStoreComplete{
			Querier: queries,
			tx:      tx,
		}

		// Update all services to use transaction-scoped store
		updateServicesWithTxStore(srv.ServiceContainer(), txStore)
	}

	// Execute test function
	testFunc(ctx, queries, srv)
}

// transactionStore implements db.Store for transaction-scoped operations
type transactionStore struct {
	queries *db.Queries
	tx      pgx.Tx
}

// Implement Store interface
func (s *transactionStore) ExecTx(ctx context.Context, fn func(db.Querier) error) error {
	// Since we're already in a transaction, just execute the function
	return fn(s.queries)
}

// Embed all Querier methods
func (s *transactionStore) AddEventRequiredVerificationType(ctx context.Context, arg db.AddEventRequiredVerificationTypeParams) error {
	return s.queries.AddEventRequiredVerificationType(ctx, arg)
}

// Note: In a real implementation, you would need to implement ALL methods from the Querier interface
// For brevity, I'm showing the pattern. You can use interface embedding to simplify this:
type transactionStoreComplete struct {
	db.Querier
	tx pgx.Tx
}

func (s *transactionStoreComplete) ExecTx(ctx context.Context, fn func(db.Querier) error) error {
	return fn(s)
}

func (s *transactionStoreComplete) Exec(ctx context.Context, sql string, args ...any) (pgconn.CommandTag, error) {
	return s.tx.Exec(ctx, sql, args...)
}

// updateServicesWithTxStore updates all services to use transaction-scoped store
func updateServicesWithTxStore(sc *config.ServiceContainer, txStore db.Store) {
	// This is a simplified version - in practice, you'd need to update
	// each service that uses the store. Since services are already initialized,
	// this might require refactoring to support store replacement or
	// creating services with the transaction store from the beginning.

	// For now, this serves as a placeholder to show the concept
	fmt.Printf("Note: Service replacement with transaction store not fully implemented\n")
}

// WithTransactionIsolation wraps a test function with transaction isolation
func WithTransactionIsolation(t *testing.T, pool *pgxpool.Pool, testFunc func(ctx context.Context, queries *db.Queries)) {
	testutil.RunInTransaction(t, &testutil.TestDatabase{DB: pool}, testFunc)
}
