package integration

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// UserIntegrationTestSuite tests user management endpoints with real API calls
type UserIntegrationTestSuite struct {
	suite.Suite
	apiSuite *APITestSuite
}

func (suite *UserIntegrationTestSuite) SetupSuite() {
	suite.apiSuite = SetupAPITest(suite.T())
}

func (suite *UserIntegrationTestSuite) TearDownSuite() {
	suite.apiSuite.TeardownAPITest(suite.T())
}

func (suite *UserIntegrationTestSuite) SetupTest() {
	suite.apiSuite.CleanupTestData(suite.T())
}

// TestUserProfile tests user profile management
func (suite *UserIntegrationTestSuite) TestUserProfile() {
	// Get current user profile
	rec, err := suite.apiSuite.MakeUserRequest("GET", "/api/v1/users/me", nil)
	require.NoError(suite.T(), err)

	var response UserProfileResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	require.NotEmpty(suite.T(), response.ID)
	require.NotEmpty(suite.T(), response.DisplayName)
	require.Equal(suite.T(), authorization.PlatformRoleSuperAdmin, response.PlatformRole)
}

// TestUserProfileUpdate tests user profile update functionality
func (suite *UserIntegrationTestSuite) TestUserProfileUpdate() {
	updateReq := map[string]interface{}{
		"display_name":           "Updated Display Name",
		"interface_language":     "zh_HK",
		"communication_language": "zh_HK",
	}

	rec, err := suite.apiSuite.MakeUserRequest("PATCH", "/api/v1/users/me", updateReq)
	require.NoError(suite.T(), err)

	var response UserProfileResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	require.Equal(suite.T(), "Updated Display Name", response.DisplayName)
	require.Equal(suite.T(), "zh_HK", response.InterfaceLanguage)
	require.Equal(suite.T(), "zh_HK", response.CommunicationLanguage)
}

// TestUserProfileUpdate_ValidationErrors tests profile update with invalid data
func (suite *UserIntegrationTestSuite) TestUserProfileUpdate_ValidationErrors() {
	// Test invalid language code
	updateReq := map[string]interface{}{
		"interface_language": "invalid-lang-code-that-is-too-long",
	}

	rec, err := suite.apiSuite.MakeUserRequest("PATCH", "/api/v1/users/me", updateReq)
	require.NoError(suite.T(), err)
	// The API might accept this or reject it based on validation rules
	// Let's check the status code is either 200 (accepted) or 400 (rejected)
	require.True(suite.T(), rec.Code == http.StatusOK || rec.Code == http.StatusBadRequest)
}

// TestPhoneNumberChange tests phone number change workflow
func (suite *UserIntegrationTestSuite) TestPhoneNumberChange() {
	newPhoneNumber := "+1555999888"

	// Step 1: Initiate phone change
	initiateReq := map[string]interface{}{
		"new_phone_number": newPhoneNumber,
		"code_challenge":   "test-challenge",
		"state":            "test-state",
	}

	rec, err := suite.apiSuite.MakeUserRequest("POST", "/api/v1/users/me/phone/initiate-change", initiateReq)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, nil)

	// Step 2: Verify phone change
	verifyReq := map[string]interface{}{
		"new_phone_number": newPhoneNumber,
		"otp_code":         "123456", // Test OTP
		"code_verifier":    "test-verifier",
		"state":            "test-state",
	}

	rec, err = suite.apiSuite.MakeUserRequest("POST", "/api/v1/users/me/phone/verify-change", verifyReq)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, nil)

	// Verify phone number was changed
	rec, err = suite.apiSuite.MakeUserRequest("GET", "/api/v1/users/me", nil)
	require.NoError(suite.T(), err)

	var response UserProfileResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	require.NotNil(suite.T(), response.Phone)
	require.Equal(suite.T(), newPhoneNumber, *response.Phone)
}

// TestPhoneNumberChange_DuplicatePhone tests phone change to existing number
func (suite *UserIntegrationTestSuite) TestPhoneNumberChange_DuplicatePhone() {
	existingPhone := "+1234567890" // Assuming this phone exists

	initiateReq := map[string]interface{}{
		"new_phone_number": existingPhone,
		"code_challenge":   "test-challenge",
		"state":            "test-state",
	}

	rec, err := suite.apiSuite.MakeUserRequest("POST", "/api/v1/users/me/phone/initiate-change", initiateReq)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertErrorResponse(suite.T(), rec, http.StatusConflict, "phone number already in use")
}

// TestUserVerification tests user verification request workflow
func (suite *UserIntegrationTestSuite) TestUserVerification() {
	// Initiate verification request
	verifyReq := map[string]interface{}{
		"verification_type": "identity_card",
		"document_type":     "hong_kong_id",
		"document_number":   "A1234567",
		"full_name":         "Test User",
		"date_of_birth":     "1990-01-01",
	}

	rec, err := suite.apiSuite.MakeUserRequest("POST", "/api/v1/users/me/verifications", verifyReq)
	require.NoError(suite.T(), err)

	var response UserVerificationResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusCreated, &response)

	require.Equal(suite.T(), "identity_card", response.VerificationType)
	require.Equal(suite.T(), "pending", response.Status)
	require.NotEmpty(suite.T(), response.ID)
}

// TestUserVerification_DuplicateRequest tests duplicate verification request
func (suite *UserIntegrationTestSuite) TestUserVerification_DuplicateRequest() {
	// Create first verification request
	verifyReq := map[string]interface{}{
		"verification_type": "identity_card",
		"document_type":     "hong_kong_id",
		"document_number":   "A1234567",
		"full_name":         "Test User",
		"date_of_birth":     "1990-01-01",
	}

	rec, err := suite.apiSuite.MakeUserRequest("POST", "/api/v1/users/me/verifications", verifyReq)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusCreated, nil)

	// Try to create duplicate request
	rec, err = suite.apiSuite.MakeUserRequest("POST", "/api/v1/users/me/verifications", verifyReq)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertErrorResponse(suite.T(), rec, http.StatusConflict, "verification request already exists")
}

// TestUserVerificationList tests listing user verification requests
func (suite *UserIntegrationTestSuite) TestUserVerificationList() {
	// Create a verification request first
	verifyReq := map[string]interface{}{
		"verification_type": "identity_card",
		"document_type":     "hong_kong_id",
		"document_number":   "A1234567",
		"full_name":         "Test User",
		"date_of_birth":     "1990-01-01",
	}

	rec, err := suite.apiSuite.MakeUserRequest("POST", "/api/v1/users/me/verifications", verifyReq)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusCreated, nil)

	// List verification requests
	rec, err = suite.apiSuite.MakeUserRequest("GET", "/api/v1/users/me/verifications", nil)
	require.NoError(suite.T(), err)

	var response PaginatedUserVerificationResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	require.GreaterOrEqual(suite.T(), len(response.Requests), 1)
	require.NotNil(suite.T(), response.Pagination)
}

// TestUserEventRegistrations tests listing user's event registrations
func (suite *UserIntegrationTestSuite) TestUserEventRegistrations() {
	// Get user's event registrations
	rec, err := suite.apiSuite.MakeUserRequest("GET", "/api/v1/me/event-registrations", nil)
	require.NoError(suite.T(), err)

	var response PaginatedEventRegistrationResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	require.NotNil(suite.T(), response.Registrations)
	require.NotNil(suite.T(), response.Pagination)
}

// TestUserEventRegistrations_WithFilters tests registration listing with filters
func (suite *UserIntegrationTestSuite) TestUserEventRegistrations_WithFilters() {
	// Test with status filter
	rec, err := suite.apiSuite.MakeUserRequest("GET", "/api/v1/users/events/registrations?status=registered", nil)
	require.NoError(suite.T(), err)

	var response PaginatedEventRegistrationResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	// All registrations should have 'registered' status
	for _, registration := range response.Registrations {
		require.Equal(suite.T(), "registered", registration.Status)
	}
}

// TestUserVolunteerApplications tests listing user's volunteer applications
func (suite *UserIntegrationTestSuite) TestUserVolunteerApplications() {
	// Get user's volunteer applications
	rec, err := suite.apiSuite.MakeUserRequest("GET", "/api/v1/users/me/volunteer/applications", nil)
	require.NoError(suite.T(), err)

	var response PaginatedVolunteerApplicationResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &response)

	require.NotNil(suite.T(), response.Applications)
	require.NotNil(suite.T(), response.Pagination)
}

// TestUserAccountDeletion tests user account deletion workflow
func (suite *UserIntegrationTestSuite) TestUserAccountDeletion() {
	// Request account deletion
	deleteReq := map[string]interface{}{
		"confirmation": "DELETE",
		"reason":       "Testing account deletion",
	}

	rec, err := suite.apiSuite.MakeUserRequest("DELETE", "/api/v1/users/account", deleteReq)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, nil)

	// Verify user cannot access profile after deletion request
	rec, err = suite.apiSuite.MakeUserRequest("GET", "/api/v1/users/me", nil)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertErrorResponse(suite.T(), rec, http.StatusUnauthorized, "account deletion pending")
}

// TestUserAccountDeletion_InvalidConfirmation tests account deletion with invalid confirmation
func (suite *UserIntegrationTestSuite) TestUserAccountDeletion_InvalidConfirmation() {
	deleteReq := map[string]interface{}{
		"confirmation": "INVALID",
		"reason":       "Testing",
	}

	rec, err := suite.apiSuite.MakeUserRequest("DELETE", "/api/v1/users/account", deleteReq)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertErrorResponse(suite.T(), rec, http.StatusBadRequest, "invalid confirmation")
}

// TestUserPreferences tests user preferences management
func (suite *UserIntegrationTestSuite) TestUserPreferences() {
	// Get current preferences
	rec, err := suite.apiSuite.MakeUserRequest("GET", "/api/v1/users/preferences", nil)
	require.NoError(suite.T(), err)

	var preferences UserPreferencesResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &preferences)

	// Update preferences
	updateReq := map[string]interface{}{
		"language":                 "en",
		"timezone":                 "Asia/Hong_Kong",
		"email_notifications":      true,
		"sms_notifications":        false,
		"marketing_communications": false,
	}

	rec, err = suite.apiSuite.MakeUserRequest("PUT", "/api/v1/users/preferences", updateReq)
	require.NoError(suite.T(), err)

	var updated UserPreferencesResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &updated)

	require.Equal(suite.T(), "en", updated.Language)
	require.Equal(suite.T(), "Asia/Hong_Kong", updated.Timezone)
	require.True(suite.T(), updated.EmailNotifications)
	require.False(suite.T(), updated.SMSNotifications)
	require.False(suite.T(), updated.MarketingCommunications)
}

// TestUserProfilePicture tests profile picture upload and management
func (suite *UserIntegrationTestSuite) TestUserProfilePicture() {
	// This is a simplified test - in a real implementation, you'd handle multipart form data
	req := map[string]interface{}{
		"action": "upload_profile_picture",
	}

	rec, err := suite.apiSuite.MakeUserRequest("POST", "/api/v1/users/me/profile-picture", req)
	require.NoError(suite.T(), err)

	// The response depends on your implementation
	require.True(suite.T(), rec.Code == http.StatusOK || rec.Code == http.StatusCreated)
}

// TestUserSessions tests user session management
func (suite *UserIntegrationTestSuite) TestUserSessions() {
	// Get active sessions
	rec, err := suite.apiSuite.MakeUserRequest("GET", "/api/v1/users/sessions", nil)
	require.NoError(suite.T(), err)

	var sessions UserSessionsResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &sessions)

	require.NotNil(suite.T(), sessions.Sessions)
	require.GreaterOrEqual(suite.T(), len(sessions.Sessions), 1) // Current session should exist
}

// TestUserSessionRevoke tests revoking user sessions
func (suite *UserIntegrationTestSuite) TestUserSessionRevoke() {
	// Get sessions first
	rec, err := suite.apiSuite.MakeUserRequest("GET", "/api/v1/users/sessions", nil)
	require.NoError(suite.T(), err)

	var sessions UserSessionsResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &sessions)

	if len(sessions.Sessions) > 1 {
		// Revoke a specific session (not the current one)
		sessionID := sessions.Sessions[1].ID
		rec, err = suite.apiSuite.MakeUserRequest("DELETE", fmt.Sprintf("/api/v1/users/sessions/%s", sessionID), nil)
		require.NoError(suite.T(), err)
		suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, nil)
	}

	// Revoke all other sessions
	rec, err = suite.apiSuite.MakeUserRequest("DELETE", "/api/v1/users/sessions/others", nil)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, nil)
}

// TestUnauthorizedUserAccess tests unauthorized access to user endpoints
func (suite *UserIntegrationTestSuite) TestUnauthorizedUserAccess() {
	// Test accessing profile without authentication
	rec, err := suite.apiSuite.MakeUnauthenticatedRequest("GET", "/api/v1/users/me", nil)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertErrorResponse(suite.T(), rec, http.StatusUnauthorized, "unauthorized")

	// Test updating profile without authentication
	updateReq := map[string]interface{}{
		"first_name": "Unauthorized",
	}

	rec, err = suite.apiSuite.MakeUnauthenticatedRequest("PUT", "/api/v1/users/me", updateReq)
	require.NoError(suite.T(), err)
	suite.apiSuite.AssertErrorResponse(suite.T(), rec, http.StatusUnauthorized, "unauthorized")
}

// TestUserDataExport tests user data export functionality
func (suite *UserIntegrationTestSuite) TestUserDataExport() {
	// Request data export
	rec, err := suite.apiSuite.MakeUserRequest("POST", "/api/v1/users/data/export", nil)
	require.NoError(suite.T(), err)

	var response DataExportResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusAccepted, &response)

	require.NotEmpty(suite.T(), response.RequestID)
	require.Equal(suite.T(), "processing", response.Status)
}

// TestUserNotifications tests user notification preferences and management
func (suite *UserIntegrationTestSuite) TestUserNotifications() {
	// Get notifications
	rec, err := suite.apiSuite.MakeUserRequest("GET", "/api/v1/users/notifications", nil)
	require.NoError(suite.T(), err)

	var notifications PaginatedNotificationResponse
	suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, &notifications)

	require.NotNil(suite.T(), notifications.Notifications)
	require.NotNil(suite.T(), notifications.Pagination)

	// Mark notifications as read if any exist
	if len(notifications.Notifications) > 0 {
		notificationID := notifications.Notifications[0].ID
		rec, err = suite.apiSuite.MakeUserRequest("PUT", fmt.Sprintf("/api/v1/users/notifications/%s/read", notificationID), nil)
		require.NoError(suite.T(), err)
		suite.apiSuite.AssertJSONResponse(suite.T(), rec, http.StatusOK, nil)
	}
}

func TestUserIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(UserIntegrationTestSuite))
}
