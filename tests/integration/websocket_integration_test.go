package integration

import (
	"fmt"
	"net/http"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/gorilla/websocket"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestWebSocketConnectionEstablishment tests basic WebSocket connection setup
func TestWebSocketConnectionEstablishment(t *testing.T) {
	// Create test server using the factory
	server := NewTestServer(t)

	// Convert HTTP URL to WebSocket URL
	wsURL := strings.Replace(server.URL, "http", "ws", 1) + "/ws"

	// Create WebSocket connection
	dialer := websocket.Dialer{
		HandshakeTimeout: 30 * time.Second,
	}

	conn, resp, err := dialer.Dial(wsURL, nil)
	if err != nil {
		t.Logf("Connection error: %v", err)
		if resp != nil {
			t.Logf("Response status: %d", resp.StatusCode)
		}
	}
	require.NoError(t, err, "Should establish WebSocket connection")
	require.NotNil(t, conn, "Connection should not be nil")
	defer conn.Close()

	// Verify connection is established by trying to set a deadline
	err = conn.SetReadDeadline(time.Now().Add(30 * time.Second))
	assert.NoError(t, err, "Should be able to set deadline on open connection")
}

// TestWebSocketPingPong tests ping/pong message exchange
func TestWebSocketPingPong(t *testing.T) {
	server := NewTestServer(t)

	wsURL := strings.Replace(server.URL, "http", "ws", 1) + "/ws"

	// Set up connection with ping/pong handlers
	dialer := websocket.Dialer{
		HandshakeTimeout: 30 * time.Second,
	}

	conn, _, err := dialer.Dial(wsURL, nil)
	require.NoError(t, err)
	defer conn.Close()

	// Set up pong handler to track pongs
	pongReceived := make(chan bool, 1)
	conn.SetPongHandler(func(appData string) error {
		select {
		case pongReceived <- true:
		default:
		}
		return nil
	})

	// Send ping
	err = conn.WriteControl(websocket.PingMessage, []byte("ping"), time.Now().Add(time.Second))
	require.NoError(t, err, "Should send ping message")

	// Start read pump to process pong
	go func() {
		for {
			_, _, err := conn.ReadMessage()
			if err != nil {
				return
			}
		}
	}()

	// Wait for pong response
	select {
	case <-pongReceived:
		// Success
	case <-time.After(2 * time.Second):
		t.Error("Timeout waiting for pong response")
	}
}

// TestWebSocketMessageExchange tests sending and receiving messages
func TestWebSocketMessageExchange(t *testing.T) {
	server := NewTestServer(t)

	wsURL := strings.Replace(server.URL, "http", "ws", 1) + "/ws"

	conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
	require.NoError(t, err)
	defer conn.Close()

	// Test message types
	testMessages := []struct {
		name    string
		message map[string]interface{}
	}{
		{
			name: "echo_message",
			message: map[string]interface{}{
				"type": "echo",
				"data": "Hello, WebSocket!",
			},
		},
		{
			name: "ping_message",
			message: map[string]interface{}{
				"type": "ping",
			},
		},
	}

	for _, tc := range testMessages {
		t.Run(tc.name, func(t *testing.T) {
			// Send message
			err := conn.WriteJSON(tc.message)
			require.NoError(t, err, "Should send message")

			// Set read deadline
			conn.SetReadDeadline(time.Now().Add(30 * time.Second))

			// Read response
			var response map[string]interface{}
			err = conn.ReadJSON(&response)
			// Some messages might not get a response, which is OK
			if err != nil {
				if websocket.IsCloseError(err, websocket.CloseNormalClosure) {
					return
				}
				if strings.Contains(err.Error(), "timeout") {
					t.Logf("No response received for %s (might be expected)", tc.name)
					return
				}
				require.NoError(t, err, "Error reading response")
			}

			// Log the response
			t.Logf("Received response for %s: %+v", tc.name, response)
		})
	}
}

// TestWebSocketAuthentication tests authenticated WebSocket connections
func TestWebSocketAuthentication(t *testing.T) {
	server := NewTestServer(t)

	// Get a valid JWT token (using the test helper)
	token := generateTestJWT(t, "<EMAIL>", "user")

	wsURL := strings.Replace(server.URL, "http", "ws", 1) + "/ws"

	// Test cases
	testCases := []struct {
		name          string
		headers       http.Header
		expectSuccess bool
	}{
		{
			name:          "without_auth",
			headers:       nil,
			expectSuccess: true, // WebSocket might allow unauthenticated connections
		},
		{
			name: "with_valid_token",
			headers: http.Header{
				"Authorization": []string{fmt.Sprintf("Bearer %s", token)},
			},
			expectSuccess: true,
		},
		{
			name: "with_invalid_token",
			headers: http.Header{
				"Authorization": []string{"Bearer invalid-token"},
			},
			expectSuccess: true, // Connection might succeed but with limited functionality
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			dialer := websocket.Dialer{
				HandshakeTimeout: 5 * time.Second,
			}

			conn, resp, err := dialer.Dial(wsURL, tc.headers)

			if tc.expectSuccess {
				require.NoError(t, err, "Should establish connection")
				require.NotNil(t, conn)
				defer conn.Close()

				// Try to send a message to verify connection works
				testMsg := map[string]interface{}{
					"type": "test",
					"data": "authenticated test",
				}
				err = conn.WriteJSON(testMsg)
				assert.NoError(t, err, "Should be able to send message")
			} else {
				// Log the error and response for debugging
				if err != nil {
					t.Logf("Connection failed as expected: %v", err)
				}
				if resp != nil {
					t.Logf("Response status: %d", resp.StatusCode)
				}
			}
		})
	}
}

// TestWebSocketConcurrentConnections tests multiple concurrent WebSocket connections
func TestWebSocketConcurrentConnections(t *testing.T) {
	server := NewTestServer(t)

	wsURL := strings.Replace(server.URL, "http", "ws", 1) + "/ws"

	const numConnections = 10
	var wg sync.WaitGroup
	connections := make([]*websocket.Conn, numConnections)
	errors := make([]error, numConnections)

	// Create multiple concurrent connections
	for i := 0; i < numConnections; i++ {
		wg.Add(1)
		go func(idx int) {
			defer wg.Done()

			dialer := websocket.Dialer{
				HandshakeTimeout: 5 * time.Second,
			}

			conn, _, err := dialer.Dial(wsURL, nil)
			if err != nil {
				errors[idx] = err
				return
			}
			connections[idx] = conn
		}(i)
	}

	wg.Wait()

	// Check results
	successCount := 0
	for i, conn := range connections {
		if conn != nil {
			successCount++
			defer conn.Close()
		} else if errors[i] != nil {
			t.Logf("Connection %d failed: %v", i, errors[i])
		}
	}

	assert.Greater(t, successCount, 0, "At least some connections should succeed")
	t.Logf("Successfully established %d/%d connections", successCount, numConnections)

	// Test sending messages on all successful connections
	for i, conn := range connections {
		if conn != nil {
			msg := map[string]interface{}{
				"type":  "test",
				"index": i,
			}
			err := conn.WriteJSON(msg)
			assert.NoError(t, err, "Should send message on connection %d", i)
		}
	}
}

// TestWebSocketReconnection tests reconnection scenarios
func TestWebSocketReconnection(t *testing.T) {
	server := NewTestServer(t)

	wsURL := strings.Replace(server.URL, "http", "ws", 1) + "/ws"

	// Establish initial connection
	conn1, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
	require.NoError(t, err)
	require.NotNil(t, conn1)

	// Send a message to verify connection works
	err = conn1.WriteJSON(map[string]interface{}{
		"type": "test",
		"data": "first connection",
	})
	require.NoError(t, err)

	// Close the connection
	err = conn1.Close()
	require.NoError(t, err)

	// Wait a bit
	time.Sleep(100 * time.Millisecond)

	// Establish new connection (reconnect)
	conn2, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
	require.NoError(t, err)
	require.NotNil(t, conn2)
	defer conn2.Close()

	// Send a message on the new connection
	err = conn2.WriteJSON(map[string]interface{}{
		"type": "test",
		"data": "reconnected",
	})
	require.NoError(t, err)
}

// TestWebSocketConnectionLifecycle tests proper cleanup and lifecycle management
func TestWebSocketConnectionLifecycle(t *testing.T) {
	server := NewTestServer(t)

	wsURL := strings.Replace(server.URL, "http", "ws", 1) + "/ws"

	// Track connection states
	var connectionsCreated, connectionsClosed int
	var mu sync.Mutex

	// Create connections and close them properly
	for i := 0; i < 5; i++ {
		func() {
			conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
			require.NoError(t, err)

			mu.Lock()
			connectionsCreated++
			mu.Unlock()

			// Set close handler to track closures
			conn.SetCloseHandler(func(code int, text string) error {
				mu.Lock()
				connectionsClosed++
				mu.Unlock()
				return nil
			})

			// Do some work
			conn.WriteJSON(map[string]interface{}{
				"type": "test",
				"id":   i,
			})

			// Properly close the connection
			err = conn.WriteMessage(
				websocket.CloseMessage,
				websocket.FormatCloseMessage(websocket.CloseNormalClosure, ""),
			)
			if err != nil {
				t.Logf("Error sending close message: %v", err)
			}

			// Close the connection
			conn.Close()
		}()

		// Small delay between connections
		time.Sleep(50 * time.Millisecond)
	}

	// Log results
	t.Logf("Connections created: %d", connectionsCreated)
	t.Logf("Connections closed: %d", connectionsClosed)
	assert.Equal(t, 5, connectionsCreated, "Should create all connections")
}

// TestWebSocketErrorHandling tests error scenarios
func TestWebSocketErrorHandling(t *testing.T) {
	server := NewTestServer(t)

	wsURL := strings.Replace(server.URL, "http", "ws", 1) + "/ws"

	conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
	require.NoError(t, err)
	defer conn.Close()

	testCases := []struct {
		name        string
		testFunc    func(*testing.T, *websocket.Conn)
		expectError bool
	}{
		{
			name: "invalid_message_type",
			testFunc: func(t *testing.T, conn *websocket.Conn) {
				// Try to send binary data when text is expected
				err := conn.WriteMessage(websocket.BinaryMessage, []byte{0x01, 0x02, 0x03})
				// This might not error immediately
				if err != nil {
					t.Logf("Binary message error: %v", err)
				}
			},
			expectError: false,
		},
		{
			name: "large_message",
			testFunc: func(t *testing.T, conn *websocket.Conn) {
				// Create a large message
				largeData := make([]byte, 1024*1024) // 1MB
				for i := range largeData {
					largeData[i] = byte(i % 256)
				}

				msg := map[string]interface{}{
					"type": "test",
					"data": string(largeData),
				}

				err := conn.WriteJSON(msg)
				if err != nil {
					t.Logf("Large message error: %v", err)
				}
			},
			expectError: false,
		},
		{
			name: "rapid_messages",
			testFunc: func(t *testing.T, conn *websocket.Conn) {
				// Send many messages rapidly
				for i := 0; i < 100; i++ {
					err := conn.WriteJSON(map[string]interface{}{
						"type":  "rapid",
						"index": i,
					})
					if err != nil {
						t.Logf("Rapid message %d error: %v", i, err)
						break
					}
				}
			},
			expectError: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			tc.testFunc(t, conn)
		})
	}
}

// TestWebSocketWithServerOptions tests WebSocket behavior with different server configurations
func TestWebSocketWithServerOptions(t *testing.T) {
	testCases := []struct {
		name    string
		options []TestServerOption
		test    func(t *testing.T, wsURL string)
	}{
		{
			name:    "with_rate_limiting",
			options: []TestServerOption{WithRateLimiting(true, 10)},
			test: func(t *testing.T, wsURL string) {
				// Rate limiting might affect WebSocket connections
				var successCount int
				for i := 0; i < 15; i++ {
					conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
					if err == nil {
						successCount++
						conn.Close()
					}
				}
				assert.Greater(t, successCount, 0, "Some connections should succeed")
			},
		},
		{
			name:    "with_cors",
			options: []TestServerOption{WithCORS("https://example.com")},
			test: func(t *testing.T, wsURL string) {
				// Test with Origin header
				headers := http.Header{
					"Origin": []string{"https://example.com"},
				}
				conn, _, err := websocket.DefaultDialer.Dial(wsURL, headers)
				require.NoError(t, err)
				conn.Close()
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			server := NewTestServer(t, tc.options...)

			wsURL := strings.Replace(server.URL, "http", "ws", 1) + "/ws"
			tc.test(t, wsURL)
		})
	}
}
