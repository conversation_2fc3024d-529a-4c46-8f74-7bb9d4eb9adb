package integration

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"net/url"
	"strings"
	"sync"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/internal/token"

	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"github.com/labstack/echo/v4"
	"github.com/rs/zerolog/log"
)

// WebSocketTestServer provides a real HTTP server for WebSocket testing
type WebSocketTestServer struct {
	Server       *httptest.Server
	BaseURL      string
	WebSocketURL string
	Echo         *echo.Echo
	Handler      http.Handler
	cleanup      []func()
	mu           sync.Mutex
}

// NewWebSocketTestServer creates a new test server with WebSocket support
func NewWebSocketTestServer(t *testing.T, handler http.Handler) *WebSocketTestServer {
	// Create the httptest server
	ts := httptest.NewServer(handler)

	// Parse the test server URL
	serverURL, err := url.Parse(ts.URL)
	if err != nil {
		t.Fatalf("Failed to parse test server URL: %v", err)
	}

	// Construct WebSocket URL by replacing http with ws
	wsURL := fmt.Sprintf("ws://%s", serverURL.Host)

	return &WebSocketTestServer{
		Server:       ts,
		BaseURL:      ts.URL,
		WebSocketURL: wsURL,
		Handler:      handler,
		cleanup:      make([]func(), 0),
	}
}

// Close shuts down the test server and performs cleanup
func (wts *WebSocketTestServer) Close() {
	wts.mu.Lock()
	defer wts.mu.Unlock()

	// Run cleanup functions in reverse order
	for i := len(wts.cleanup) - 1; i >= 0; i-- {
		wts.cleanup[i]()
	}

	// Close the test server
	wts.Server.Close()
}

// CreateAuthenticatedConnection creates an authenticated WebSocket connection
func (wts *WebSocketTestServer) CreateAuthenticatedConnection(t *testing.T, token string, path string) (*websocket.Conn, error) {
	// Construct the full WebSocket URL
	wsURL := fmt.Sprintf("%s%s", wts.WebSocketURL, path)

	// Create dialer with appropriate settings
	dialer := websocket.Dialer{
		HandshakeTimeout: 10 * time.Second,
	}

	// Set up headers with authentication
	headers := http.Header{}
	if token != "" {
		headers.Set("Authorization", fmt.Sprintf("Bearer %s", token))
	}

	// Implement retry logic for connection stability
	maxRetries := 3
	retryDelay := 100 * time.Millisecond

	var conn *websocket.Conn
	var lastErr error

	for attempt := 0; attempt < maxRetries; attempt++ {
		if attempt > 0 {
			log.Info().
				Int("attempt", attempt+1).
				Int("max_attempts", maxRetries).
				Dur("retry_delay", retryDelay).
				Msg("Retrying WebSocket connection")
			time.Sleep(retryDelay)
			retryDelay *= 2 // Exponential backoff
		}

		// Attempt connection
		conn, _, lastErr = dialer.Dial(wsURL, headers)
		if lastErr == nil {
			log.Info().
				Str("url", wsURL).
				Int("attempt", attempt+1).
				Msg("WebSocket connection established")

			// Register cleanup
			wts.mu.Lock()
			wts.cleanup = append(wts.cleanup, func() {
				if err := conn.Close(); err != nil {
					log.Warn().Err(err).Msg("Error closing WebSocket connection during cleanup")
				}
			})
			wts.mu.Unlock()

			return conn, nil
		}

		log.Warn().
			Err(lastErr).
			Int("attempt", attempt+1).
			Str("url", wsURL).
			Msg("WebSocket connection attempt failed")
	}

	return nil, fmt.Errorf("WebSocket connection failed after %d attempts: %w", maxRetries, lastErr)
}

// CreateUnauthenticatedConnection creates an unauthenticated WebSocket connection
func (wts *WebSocketTestServer) CreateUnauthenticatedConnection(t *testing.T, path string) (*websocket.Conn, error) {
	return wts.CreateAuthenticatedConnection(t, "", path)
}

// MockAuthContext creates a mock Echo context with authentication for testing
func (wts *WebSocketTestServer) MockAuthContext(userID uuid.UUID, platformRole string, orgRole string) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// Create test claims
			claims := &token.AppClaims{
				UserID: userID,
			}

			// Set claims in context (matching what the WebSocket service expects)
			c.Set("user_claims", claims)

			// Also set as user for compatibility
			c.Set("user", claims)

			return next(c)
		}
	}
}

// GenerateTestToken generates a test JWT token for WebSocket authentication
func (wts *WebSocketTestServer) GenerateTestToken(t *testing.T, userID uuid.UUID) string {
	// This should use the same token generation as used in other tests
	// For now, returning a simple implementation
	return generateTestJWT(t, "<EMAIL>", "user")
}

// SendMessage sends a JSON message over the WebSocket connection
func (wts *WebSocketTestServer) SendMessage(conn *websocket.Conn, message interface{}) error {
	conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
	return conn.WriteJSON(message)
}

// ReadMessage reads a JSON message from the WebSocket connection
func (wts *WebSocketTestServer) ReadMessage(conn *websocket.Conn, timeout time.Duration) (map[string]interface{}, error) {
	conn.SetReadDeadline(time.Now().Add(timeout))

	var message map[string]interface{}
	err := conn.ReadJSON(&message)
	if err != nil {
		return nil, err
	}

	return message, nil
}

// WaitForMessage waits for a specific message type
func (wts *WebSocketTestServer) WaitForMessage(conn *websocket.Conn, messageType string, timeout time.Duration) (map[string]interface{}, error) {
	deadline := time.Now().Add(timeout)

	for time.Now().Before(deadline) {
		message, err := wts.ReadMessage(conn, time.Until(deadline))
		if err != nil {
			if websocket.IsCloseError(err, websocket.CloseNormalClosure) {
				return nil, fmt.Errorf("connection closed while waiting for message type: %s", messageType)
			}
			// For timeout errors, continue trying
			if strings.Contains(err.Error(), "timeout") {
				continue
			}
			return nil, err
		}

		if msgType, ok := message["type"].(string); ok && msgType == messageType {
			return message, nil
		}

		log.Debug().
			Str("expected_type", messageType).
			Interface("received_message", message).
			Msg("Received message of different type, continuing to wait")
	}

	return nil, fmt.Errorf("timeout waiting for message type: %s", messageType)
}
