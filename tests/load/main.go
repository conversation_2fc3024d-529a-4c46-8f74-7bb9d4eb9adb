package main

import (
	"bytes"
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"math/rand"
	"net/http"
	"os"
	"os/signal"
	"sort"
	"sync"
	"sync/atomic"
	"syscall"
	"time"

	"github.com/google/uuid"
)

type LoadTestConfig struct {
	BaseURL     string
	Duration    time.Duration
	Users       int
	RPS         int // Requests per second
	AccessToken string
	OrgID       string
}

type LoadTestStats struct {
	TotalRequests   int64
	SuccessRequests int64
	FailedRequests  int64
	TotalLatency    int64 // in milliseconds
	MinLatency      int64
	MaxLatency      int64
	ErrorCounts     map[string]int64
	Latencies       []int64 // Store all latencies for percentile calculation
	mu              sync.Mutex
}

type LoadTestScenario struct {
	Name     string
	Weight   int // Relative weight for scenario selection
	Endpoint string
	Method   string
	Body     interface{}
	Setup    func() error
}

var scenarios = []LoadTestScenario{
	{
		Name:     "GetUserProfile",
		Weight:   30,
		Endpoint: "/api/v1/users/me",
		Method:   "GET",
	},
	{
		Name:     "ListEvents",
		Weight:   40,
		Endpoint: "/api/v1/events",
		Method:   "GET",
	},
	{
		Name:     "ListOrganizations",
		Weight:   20,
		Endpoint: "/api/v1/organizations",
		Method:   "GET",
	},
	{
		Name:     "SearchEvents",
		Weight:   10,
		Endpoint: "/api/v1/events?q=test&limit=20",
		Method:   "GET",
	},
}

func main() {
	var (
		baseURL  = flag.String("url", "http://localhost:8080", "Base URL of the API")
		duration = flag.Duration("duration", 1*time.Minute, "Duration of the load test")
		users    = flag.Int("users", 10, "Number of concurrent users")
		rps      = flag.Int("rps", 100, "Target requests per second")
		token    = flag.String("token", "", "Access token for authentication")
		orgID    = flag.String("org", "", "Organization ID for context")
	)
	flag.Parse()

	config := &LoadTestConfig{
		BaseURL:     *baseURL,
		Duration:    *duration,
		Users:       *users,
		RPS:         *rps,
		AccessToken: *token,
		OrgID:       *orgID,
	}

	// If no token provided, try to get one
	if config.AccessToken == "" {
		log.Println("No access token provided. Running in anonymous mode.")
	}

	stats := &LoadTestStats{
		MinLatency:  int64(^uint64(0) >> 1), // Max int64
		ErrorCounts: make(map[string]int64),
		Latencies:   make([]int64, 0, config.RPS*int(config.Duration.Seconds())), // Pre-allocate for expected requests
	}

	fmt.Printf("Starting load test:\n")
	fmt.Printf("- Base URL: %s\n", config.BaseURL)
	fmt.Printf("- Duration: %s\n", config.Duration)
	fmt.Printf("- Users: %d\n", config.Users)
	fmt.Printf("- Target RPS: %d\n", config.RPS)
	fmt.Println()

	// Create context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), config.Duration)
	defer cancel()

	// Handle interrupts
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, os.Interrupt, syscall.SIGTERM)
	go func() {
		<-sigChan
		fmt.Println("\nInterrupted, shutting down...")
		cancel()
	}()

	// Start load test
	runLoadTest(ctx, config, stats)

	// Print results
	printResults(stats, config.Duration)
}

func runLoadTest(ctx context.Context, config *LoadTestConfig, stats *LoadTestStats) {
	var wg sync.WaitGroup

	// Rate limiter
	rateLimiter := time.NewTicker(time.Second / time.Duration(config.RPS))
	defer rateLimiter.Stop()

	// Start user goroutines
	for i := 0; i < config.Users; i++ {
		wg.Add(1)
		go func(userID int) {
			defer wg.Done()
			runUser(ctx, userID, config, stats, rateLimiter)
		}(i)
	}

	wg.Wait()
}

func runUser(ctx context.Context, userID int, config *LoadTestConfig, stats *LoadTestStats, rateLimiter *time.Ticker) {
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	for {
		select {
		case <-ctx.Done():
			return
		case <-rateLimiter.C:
			// Select scenario based on weights
			scenario := selectScenario()
			executeRequest(client, config, stats, scenario)
		}
	}
}

func selectScenario() *LoadTestScenario {
	totalWeight := 0
	for _, s := range scenarios {
		totalWeight += s.Weight
	}

	r := rand.Intn(totalWeight)
	cumWeight := 0

	for i := range scenarios {
		cumWeight += scenarios[i].Weight
		if r < cumWeight {
			return &scenarios[i]
		}
	}

	return &scenarios[0] // Fallback
}

func executeRequest(client *http.Client, config *LoadTestConfig, stats *LoadTestStats, scenario *LoadTestScenario) {
	start := time.Now()

	// Build request
	var body []byte
	if scenario.Body != nil {
		var err error
		body, err = json.Marshal(scenario.Body)
		if err != nil {
			recordError(stats, "marshal_error")
			return
		}
	}

	req, err := http.NewRequest(scenario.Method, config.BaseURL+scenario.Endpoint, bytes.NewReader(body))
	if err != nil {
		recordError(stats, "request_creation_error")
		return
	}

	// Add headers
	req.Header.Set("Content-Type", "application/json")
	if config.AccessToken != "" {
		req.Header.Set("Authorization", "Bearer "+config.AccessToken)
	}

	// Execute request
	resp, err := client.Do(req)
	latency := time.Since(start).Milliseconds()

	// Update stats
	atomic.AddInt64(&stats.TotalRequests, 1)
	atomic.AddInt64(&stats.TotalLatency, latency)

	if err != nil {
		atomic.AddInt64(&stats.FailedRequests, 1)
		recordError(stats, "network_error")
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		atomic.AddInt64(&stats.SuccessRequests, 1)
	} else {
		atomic.AddInt64(&stats.FailedRequests, 1)
		recordError(stats, fmt.Sprintf("http_%d", resp.StatusCode))
	}

	// Update latency stats
	updateLatencyStats(stats, latency)

	// Store latency for percentile calculation
	stats.mu.Lock()
	stats.Latencies = append(stats.Latencies, latency)
	stats.mu.Unlock()
}

func recordError(stats *LoadTestStats, errorType string) {
	stats.mu.Lock()
	defer stats.mu.Unlock()
	stats.ErrorCounts[errorType]++
}

func updateLatencyStats(stats *LoadTestStats, latency int64) {
	// Update min latency
	for {
		min := atomic.LoadInt64(&stats.MinLatency)
		if latency >= min || atomic.CompareAndSwapInt64(&stats.MinLatency, min, latency) {
			break
		}
	}

	// Update max latency
	for {
		max := atomic.LoadInt64(&stats.MaxLatency)
		if latency <= max || atomic.CompareAndSwapInt64(&stats.MaxLatency, max, latency) {
			break
		}
	}
}

func printResults(stats *LoadTestStats, duration time.Duration) {
	total := atomic.LoadInt64(&stats.TotalRequests)
	success := atomic.LoadInt64(&stats.SuccessRequests)
	failed := atomic.LoadInt64(&stats.FailedRequests)
	totalLatency := atomic.LoadInt64(&stats.TotalLatency)
	minLatency := atomic.LoadInt64(&stats.MinLatency)
	maxLatency := atomic.LoadInt64(&stats.MaxLatency)

	successRate := float64(success) / float64(total) * 100
	avgLatency := float64(totalLatency) / float64(total)
	actualRPS := float64(total) / duration.Seconds()

	fmt.Println("\n=== Load Test Results ===")
	fmt.Printf("Total Requests: %d\n", total)
	fmt.Printf("Successful: %d\n", success)
	fmt.Printf("Failed: %d\n", failed)
	fmt.Printf("Success Rate: %.2f%%\n", successRate)
	fmt.Printf("Actual RPS: %.2f\n", actualRPS)
	fmt.Println()
	fmt.Println("=== Latency Statistics ===")
	fmt.Printf("Average Latency: %.2fms\n", avgLatency)
	fmt.Printf("Min Latency: %dms\n", minLatency)
	fmt.Printf("Max Latency: %dms\n", maxLatency)

	// Calculate actual percentiles
	stats.mu.Lock()
	latencies := make([]int64, len(stats.Latencies))
	copy(latencies, stats.Latencies)
	stats.mu.Unlock()

	if len(latencies) > 0 {
		sort.Slice(latencies, func(i, j int) bool {
			return latencies[i] < latencies[j]
		})

		p50 := latencies[len(latencies)*50/100]
		p95 := latencies[len(latencies)*95/100]
		p99 := latencies[len(latencies)*99/100]

		fmt.Printf("P50 Latency: %dms\n", p50)
		fmt.Printf("P95 Latency: %dms\n", p95)
		fmt.Printf("P99 Latency: %dms\n", p99)
	}

	fmt.Println()
	fmt.Println("=== Error Breakdown ===")
	stats.mu.Lock()
	for errType, count := range stats.ErrorCounts {
		fmt.Printf("%s: %d\n", errType, count)
	}
	stats.mu.Unlock()

	// Write to file for CI/CD parsing
	writeResultsToFile(stats, duration)
}

func writeResultsToFile(stats *LoadTestStats, duration time.Duration) {
	file, err := os.Create("load_results.txt")
	if err != nil {
		log.Printf("Failed to create results file: %v", err)
		return
	}
	defer file.Close()

	total := atomic.LoadInt64(&stats.TotalRequests)
	success := atomic.LoadInt64(&stats.SuccessRequests)
	totalLatency := atomic.LoadInt64(&stats.TotalLatency)

	successRate := float64(success) / float64(total) * 100
	avgLatency := float64(totalLatency) / float64(total)
	actualRPS := float64(total) / duration.Seconds()

	// Calculate actual P95 for file output
	stats.mu.Lock()
	latencies := make([]int64, len(stats.Latencies))
	copy(latencies, stats.Latencies)
	stats.mu.Unlock()

	p95 := int64(0)
	if len(latencies) > 0 {
		sort.Slice(latencies, func(i, j int) bool {
			return latencies[i] < latencies[j]
		})
		p95 = latencies[len(latencies)*95/100]
	}

	fmt.Fprintf(file, "Success Rate: %.2f%%\n", successRate)
	fmt.Fprintf(file, "Average Latency: %.2fms\n", avgLatency)
	fmt.Fprintf(file, "P95 Latency: %dms\n", p95)
	fmt.Fprintf(file, "Actual RPS: %.2f\n", actualRPS)
}

// Additional load test scenarios can be added here
func createEventScenario() *LoadTestScenario {
	return &LoadTestScenario{
		Name:     "CreateEvent",
		Weight:   5,
		Endpoint: "/api/v1/events",
		Method:   "POST",
		Body: map[string]interface{}{
			"title":       fmt.Sprintf("Load Test Event %s", uuid.New().String()[:8]),
			"description": "Event created during load testing",
			"start_date":  time.Now().Add(24 * time.Hour).Format(time.RFC3339),
			"end_date":    time.Now().Add(25 * time.Hour).Format(time.RFC3339),
			"location":    "Load Test Location",
			"capacity":    100,
		},
	}
}
