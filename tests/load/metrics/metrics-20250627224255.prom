# HELP test_duration_seconds Test suite execution time
# TYPE test_duration_seconds gauge
test_duration_seconds{suite="performance-test"} 60

# HELP test_coverage_percent Test coverage percentage
# TYPE test_coverage_percent gauge
test_coverage_percent{suite="performance-test"} 85

# HELP test_total_count Total number of tests
# TYPE test_total_count gauge
test_total_count{suite="performance-test"} 100

# HELP test_passed_count Number of passed tests
# TYPE test_passed_count gauge
test_passed_count{suite="performance-test"} 95

# HELP test_failed_count Number of failed tests
# TYPE test_failed_count gauge
test_failed_count{suite="performance-test"} 5

# HELP test_success_rate_percent Test success rate percentage
# TYPE test_success_rate_percent gauge
test_success_rate_percent{suite="performance-test"} 95.00

# HELP system_load_average System load average
# TYPE system_load_average gauge
system_load_average 1.29

# HELP system_memory_usage_percent System memory usage percentage
# TYPE system_memory_usage_percent gauge
system_memory_usage_percent 18.67

# HELP test_timestamp_seconds Unix timestamp of test execution
# TYPE test_timestamp_seconds gauge
test_timestamp_seconds{suite="performance-test"} 1751064176
