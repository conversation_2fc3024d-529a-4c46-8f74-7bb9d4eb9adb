package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"mime/multipart"
	"net/http"
	"strings"
	"sync"
	"sync/atomic"
	"time"
)

// LoadTestClient represents a client for load testing
type LoadTestClient struct {
	httpClient *http.Client
}

// MakeRequest makes an HTTP request
func (c *LoadTestClient) MakeRequest(method, url string, data interface{}, token string) (*http.Response, error) {
	var body []byte
	if data != nil {
		var err error
		body, err = json.Marshal(data)
		if err != nil {
			return nil, err
		}
	}

	req, err := http.NewRequest(method, url, bytes.NewReader(body))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	if token != "" {
		req.Header.Set("Authorization", "Bearer "+token)
	}

	return c.httpClient.Do(req)
}

// LoadTestResult represents test results
type LoadTestResult struct {
	Scenario  string
	StartTime time.Time
	EndTime   time.Time
	Duration  time.Duration
	Metrics   *Metrics
}

// Metrics represents test metrics
type Metrics struct {
	RequestCount  int64
	SuccessCount  int64
	ErrorCount    int64
	TotalDuration time.Duration
	Latencies     []time.Duration
	ErrorsByType  map[string]int
	latencyMutex  *sync.Mutex
	errorMutex    *sync.Mutex
}

func (m *Metrics) recordError(errorType string, err error) {
	m.errorMutex.Lock()
	defer m.errorMutex.Unlock()
	atomic.AddInt64(&m.ErrorCount, 1)
	m.ErrorsByType[errorType]++
}

func (m *Metrics) recordLatency(latency time.Duration) {
	m.latencyMutex.Lock()
	defer m.latencyMutex.Unlock()
	m.Latencies = append(m.Latencies, latency)
	m.TotalDuration += latency
}

// ExtendedScenarios contains additional load test scenarios
type ExtendedScenarios struct {
	client    *LoadTestClient
	baseURL   string
	authToken string
	orgID     string
}

// NewExtendedScenarios creates a new instance of extended scenarios
func NewExtendedScenarios(client *LoadTestClient, baseURL, authToken, orgID string) *ExtendedScenarios {
	return &ExtendedScenarios{
		client:    client,
		baseURL:   baseURL,
		authToken: authToken,
		orgID:     orgID,
	}
}

// TokenRefreshLoadTest simulates heavy token refresh load
func (es *ExtendedScenarios) TokenRefreshLoadTest(ctx context.Context, users int, duration time.Duration) *LoadTestResult {
	result := &LoadTestResult{
		Scenario:  "Token Refresh Load Test",
		StartTime: time.Now(),
		Metrics: &Metrics{
			RequestCount:  0,
			SuccessCount:  0,
			ErrorCount:    0,
			TotalDuration: 0,
			Latencies:     []time.Duration{},
			ErrorsByType:  make(map[string]int),
			latencyMutex:  &sync.Mutex{},
			errorMutex:    &sync.Mutex{},
		},
	}

	// Create refresh tokens for test users
	refreshTokens := make([]string, users)
	for i := 0; i < users; i++ {
		token, err := es.createTestUserAndGetRefreshToken(fmt.Sprintf("loadtest-refresh-%d", i))
		if err != nil {
			result.Metrics.recordError("setup", err)
			continue
		}
		refreshTokens[i] = token
	}

	// Run concurrent token refresh operations
	var wg sync.WaitGroup
	stopChan := make(chan struct{})

	// Start timer to stop after duration
	go func() {
		time.Sleep(duration)
		close(stopChan)
	}()

	for i := 0; i < users; i++ {
		wg.Add(1)
		go func(userIdx int) {
			defer wg.Done()

			for {
				select {
				case <-stopChan:
					return
				case <-ctx.Done():
					return
				default:
					// Refresh token
					start := time.Now()
					err := es.refreshToken(refreshTokens[userIdx])
					elapsed := time.Since(start)

					atomic.AddInt64(&result.Metrics.RequestCount, 1)
					if err != nil {
						result.Metrics.recordError("refresh_token", err)
					} else {
						atomic.AddInt64(&result.Metrics.SuccessCount, 1)
						result.Metrics.recordLatency(elapsed)
					}

					// Small delay between refreshes
					time.Sleep(time.Duration(rand.Intn(1000)) * time.Millisecond)
				}
			}
		}(i)
	}

	wg.Wait()
	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)
	return result
}

// TransactionHeavyLoadTest simulates transaction-heavy operations
func (es *ExtendedScenarios) TransactionHeavyLoadTest(ctx context.Context, concurrency int, duration time.Duration) *LoadTestResult {
	result := &LoadTestResult{
		Scenario:  "Transaction Heavy Load Test",
		StartTime: time.Now(),
		Metrics: &Metrics{
			RequestCount:  0,
			SuccessCount:  0,
			ErrorCount:    0,
			TotalDuration: 0,
			Latencies:     []time.Duration{},
			ErrorsByType:  make(map[string]int),
			latencyMutex:  &sync.Mutex{},
			errorMutex:    &sync.Mutex{},
		},
	}

	// Create test event for registrations
	eventID, err := es.createTestEvent()
	if err != nil {
		result.Metrics.recordError("setup", err)
		return result
	}

	var wg sync.WaitGroup
	stopChan := make(chan struct{})

	go func() {
		time.Sleep(duration)
		close(stopChan)
	}()

	// Run concurrent transaction-heavy operations
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()

			for {
				select {
				case <-stopChan:
					return
				case <-ctx.Done():
					return
				default:
					// Execute transaction-heavy operation (event registration with payment)
					start := time.Now()
					err := es.performComplexRegistration(eventID, workerID)
					elapsed := time.Since(start)

					atomic.AddInt64(&result.Metrics.RequestCount, 1)
					if err != nil {
						result.Metrics.recordError("complex_registration", err)
					} else {
						atomic.AddInt64(&result.Metrics.SuccessCount, 1)
						result.Metrics.recordLatency(elapsed)
					}

					// Delay between operations
					time.Sleep(time.Duration(100+rand.Intn(400)) * time.Millisecond)
				}
			}
		}(i)
	}

	wg.Wait()
	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)
	return result
}

// StatisticsAPILoadTest tests statistics endpoints under load
func (es *ExtendedScenarios) StatisticsAPILoadTest(ctx context.Context, concurrency int, duration time.Duration) *LoadTestResult {
	result := &LoadTestResult{
		Scenario:  "Statistics API Load Test",
		StartTime: time.Now(),
		Metrics: &Metrics{
			RequestCount:  0,
			SuccessCount:  0,
			ErrorCount:    0,
			TotalDuration: 0,
			Latencies:     []time.Duration{},
			ErrorsByType:  make(map[string]int),
			latencyMutex:  &sync.Mutex{},
			errorMutex:    &sync.Mutex{},
		},
	}

	// Create test data
	eventIDs := make([]string, 10)
	for i := 0; i < 10; i++ {
		eventID, err := es.createTestEvent()
		if err != nil {
			result.Metrics.recordError("setup", err)
			continue
		}
		eventIDs[i] = eventID
	}

	var wg sync.WaitGroup
	stopChan := make(chan struct{})

	go func() {
		time.Sleep(duration)
		close(stopChan)
	}()

	// Statistics endpoints to test
	statisticsEndpoints := []string{
		"/api/v1/organizations/%s/statistics",
		"/api/v1/organizations/%s/statistics/events",
		"/api/v1/organizations/%s/statistics/members",
		"/api/v1/organizations/%s/statistics/trends",
		"/api/v1/events/%s/statistics",
		"/api/v1/events/%s/statistics/registrations",
		"/api/v1/users/me/statistics",
	}

	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()

			for {
				select {
				case <-stopChan:
					return
				case <-ctx.Done():
					return
				default:
					// Select random statistics endpoint
					endpoint := statisticsEndpoints[rand.Intn(len(statisticsEndpoints))]

					var url string
					if contains(endpoint, "/events/") {
						eventID := eventIDs[rand.Intn(len(eventIDs))]
						url = fmt.Sprintf(es.baseURL+endpoint, eventID)
					} else if contains(endpoint, "/organizations/") {
						url = fmt.Sprintf(es.baseURL+endpoint, es.orgID)
					} else {
						url = es.baseURL + endpoint
					}

					// Execute statistics query
					start := time.Now()
					err := es.queryStatistics(url)
					elapsed := time.Since(start)

					atomic.AddInt64(&result.Metrics.RequestCount, 1)
					if err != nil {
						result.Metrics.recordError("statistics_query", err)
					} else {
						atomic.AddInt64(&result.Metrics.SuccessCount, 1)
						result.Metrics.recordLatency(elapsed)
					}

					// Small delay between queries
					time.Sleep(time.Duration(50+rand.Intn(150)) * time.Millisecond)
				}
			}
		}(i)
	}

	wg.Wait()
	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)
	return result
}

// ConcurrentFileUploadTest tests file upload under concurrent load
func (es *ExtendedScenarios) ConcurrentFileUploadTest(ctx context.Context, concurrency int, fileSize int, duration time.Duration) *LoadTestResult {
	result := &LoadTestResult{
		Scenario:  fmt.Sprintf("Concurrent File Upload Test (%d KB files)", fileSize/1024),
		StartTime: time.Now(),
		Metrics: &Metrics{
			RequestCount:  0,
			SuccessCount:  0,
			ErrorCount:    0,
			TotalDuration: 0,
			Latencies:     []time.Duration{},
			ErrorsByType:  make(map[string]int),
			latencyMutex:  &sync.Mutex{},
			errorMutex:    &sync.Mutex{},
		},
	}

	// Create test event for file uploads
	eventID, err := es.createTestEvent()
	if err != nil {
		result.Metrics.recordError("setup", err)
		return result
	}

	var wg sync.WaitGroup
	stopChan := make(chan struct{})
	uploadLimiter := make(chan struct{}, 5) // Limit concurrent uploads

	go func() {
		time.Sleep(duration)
		close(stopChan)
	}()

	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()

			for {
				select {
				case <-stopChan:
					return
				case <-ctx.Done():
					return
				case uploadLimiter <- struct{}{}:
					// Perform file upload
					start := time.Now()
					err := es.uploadTestFile(eventID, workerID, fileSize)
					elapsed := time.Since(start)
					<-uploadLimiter // Release slot

					atomic.AddInt64(&result.Metrics.RequestCount, 1)
					if err != nil {
						result.Metrics.recordError("file_upload", err)
					} else {
						atomic.AddInt64(&result.Metrics.SuccessCount, 1)
						result.Metrics.recordLatency(elapsed)
					}

					// Delay between uploads
					time.Sleep(time.Duration(500+rand.Intn(1500)) * time.Millisecond)
				}
			}
		}(i)
	}

	wg.Wait()
	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)
	return result
}

// Helper methods

func (es *ExtendedScenarios) createTestUserAndGetRefreshToken(username string) (string, error) {
	// Register user
	registerData := map[string]interface{}{
		"phone":                 fmt.Sprintf("+1555%07d", rand.Intn(10000000)),
		"display_name":          username,
		"email":                 fmt.Sprintf("%<EMAIL>", username),
		"client_id":             "load-test-client",
		"code_challenge":        "test-challenge",
		"code_challenge_method": "S256",
		"state":                 "test-state",
	}

	// Initiate registration
	resp, err := es.client.MakeRequest("POST", es.baseURL+"/api/v1/authn/register/phone/initiate", registerData, "")
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	var initResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&initResp); err != nil {
		return "", err
	}

	// Verify with OTP
	verifyData := map[string]interface{}{
		"auth_flow_id":  initResp["auth_flow_id"],
		"otp":           "123456", // Test OTP
		"code_verifier": "test-verifier",
		"state":         "test-state",
	}

	resp, err = es.client.MakeRequest("POST", es.baseURL+"/api/v1/authn/register/phone/verify", verifyData, "")
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	var authResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&authResp); err != nil {
		return "", err
	}

	refreshToken, ok := authResp["refresh_token"].(string)
	if !ok {
		return "", fmt.Errorf("no refresh token in response")
	}

	return refreshToken, nil
}

func (es *ExtendedScenarios) refreshToken(refreshToken string) error {
	data := map[string]interface{}{
		"refresh_token": refreshToken,
	}

	resp, err := es.client.MakeRequest("POST", es.baseURL+"/api/v1/auth/refresh", data, "")
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("refresh failed with status %d", resp.StatusCode)
	}

	return nil
}

func (es *ExtendedScenarios) createTestEvent() (string, error) {
	eventData := map[string]interface{}{
		"title":         fmt.Sprintf("Load Test Event %d", time.Now().Unix()),
		"description":   "Event for load testing",
		"start_date":    time.Now().Add(24 * time.Hour).Format(time.RFC3339),
		"end_date":      time.Now().Add(26 * time.Hour).Format(time.RFC3339),
		"location":      "Load Test Venue",
		"location_type": "physical",
		"capacity":      1000,
	}

	resp, err := es.client.MakeRequest("POST", fmt.Sprintf("%s/api/v1/organizations/%s/events", es.baseURL, es.orgID), eventData, es.authToken)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	var eventResp map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&eventResp); err != nil {
		return "", err
	}

	eventID, ok := eventResp["id"].(string)
	if !ok {
		return "", fmt.Errorf("no event ID in response")
	}

	return eventID, nil
}

func (es *ExtendedScenarios) performComplexRegistration(eventID string, workerID int) error {
	// This simulates a complex registration with multiple database operations
	registrationData := map[string]interface{}{
		"payment_method": "credit_card",
		"amount":         50.00,
		"metadata": map[string]interface{}{
			"worker_id": workerID,
			"timestamp": time.Now().Unix(),
		},
	}

	resp, err := es.client.MakeRequest("POST", fmt.Sprintf("%s/api/v1/events/%s/register", es.baseURL, eventID), registrationData, es.authToken)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated && resp.StatusCode != http.StatusOK {
		return fmt.Errorf("registration failed with status %d", resp.StatusCode)
	}

	return nil
}

func (es *ExtendedScenarios) queryStatistics(url string) error {
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return err
	}

	req.Header.Set("Authorization", "Bearer "+es.authToken)
	req.Header.Set("Content-Type", "application/json")

	resp, err := es.client.httpClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("statistics query failed with status %d", resp.StatusCode)
	}

	return nil
}

func (es *ExtendedScenarios) uploadTestFile(eventID string, workerID int, fileSize int) error {
	// Generate test file content
	fileContent := make([]byte, fileSize)
	rand.Read(fileContent)

	// Create multipart form
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	part, err := writer.CreateFormFile("file", fmt.Sprintf("test-%d-%d.jpg", workerID, time.Now().Unix()))
	if err != nil {
		return err
	}

	// Add JPEG header to make it look like a real image
	jpegHeader := []byte{0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46}
	part.Write(jpegHeader)
	part.Write(fileContent[len(jpegHeader):])

	writer.Close()

	req, err := http.NewRequest("POST", fmt.Sprintf("%s/api/v1/events/%s/media", es.baseURL, eventID), body)
	if err != nil {
		return err
	}

	req.Header.Set("Authorization", "Bearer "+es.authToken)
	req.Header.Set("Content-Type", writer.FormDataContentType())

	resp, err := es.client.httpClient.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated && resp.StatusCode != http.StatusOK {
		return fmt.Errorf("file upload failed with status %d", resp.StatusCode)
	}

	return nil
}

func contains(str, substr string) bool {
	return len(str) >= len(substr) && (strings.Contains(str, substr))
}
