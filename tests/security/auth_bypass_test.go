package security

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	"Membership-SAAS-System-Backend/tests/integration"
)

// AuthBypassTestSuite tests authentication bypass vulnerabilities
type AuthBypassTestSuite struct {
	suite.Suite
	apiSuite *integration.APITestSuite
}

func (suite *AuthBypassTestSuite) SetupSuite() {
	suite.apiSuite = integration.SetupAPITest(suite.T())
}

func (suite *AuthBypassTestSuite) TearDownSuite() {
	suite.apiSuite.TeardownAPITest(suite.T())
}

func (suite *AuthBypassTestSuite) SetupTest() {
	suite.apiSuite.CleanupTestData(suite.T())
}

// Protected endpoints that should require authentication
var protectedEndpoints = []struct {
	method      string
	path        string
	body        interface{}
	description string
}{
	{"GET", "/api/v1/users/me", nil, "Get current user profile"},
	{"PATCH", "/api/v1/users/me", map[string]interface{}{"display_name": "Hacker"}, "Update user profile"},
	{"POST", "/api/v1/authn/logout", map[string]interface{}{"refresh_token": "fake"}, "Logout"},
	{"GET", "/api/v1/organizations/00000000-0000-0000-0000-000000000002", nil, "Get organization"},
	{"PATCH", "/api/v1/organizations/00000000-0000-0000-0000-000000000002", map[string]interface{}{"name": "Hacked"}, "Update organization"},
	{"POST", "/api/v1/organizations/00000000-0000-0000-0000-000000000002/events", map[string]interface{}{
		"title":       "Malicious Event",
		"description": "Test",
		"start_date":  "2024-12-31T10:00:00Z",
		"end_date":    "2024-12-31T12:00:00Z",
		"location":    "Test",
	}, "Create event"},
	{"GET", "/api/v1/events", nil, "List events"},
	{"DELETE", "/api/v1/organizations/00000000-0000-0000-0000-000000000002", nil, "Delete organization"},
}

// Admin-only endpoints that should require elevated privileges
var adminOnlyEndpoints = []struct {
	method      string
	path        string
	body        interface{}
	description string
}{
	{"POST", "/api/v1/organizations", map[string]interface{}{
		"name":        "Admin Only Org",
		"description": "Should require admin",
	}, "Create organization (admin only)"},
	{"GET", "/api/v1/admin/users", nil, "List all users (admin only)"},
	{"GET", "/api/v1/admin/organizations", nil, "List all organizations (admin only)"},
	{"GET", "/api/v1/admin/verifications/pending", nil, "List pending verifications (admin only)"},
}

// TestUnauthenticatedAccess tests that protected endpoints reject unauthenticated requests
func (suite *AuthBypassTestSuite) TestUnauthenticatedAccess() {
	t := suite.T()

	for _, endpoint := range protectedEndpoints {
		t.Run(fmt.Sprintf("Unauthenticated %s %s", endpoint.method, endpoint.path), func(t *testing.T) {
			rec, err := suite.apiSuite.MakeUnauthenticatedRequest(endpoint.method, endpoint.path, endpoint.body)
			require.NoError(t, err)

			// Should return 401 Unauthorized
			require.Equal(t, http.StatusUnauthorized, rec.Code,
				"Endpoint %s %s should require authentication, got %d. Description: %s. Response: %s",
				endpoint.method, endpoint.path, rec.Code, endpoint.description, rec.Body.String())

			// Response should indicate authentication required
			body := rec.Body.String()
			require.True(t, strings.Contains(strings.ToLower(body), "unauthorized") ||
				strings.Contains(strings.ToLower(body), "authentication") ||
				strings.Contains(strings.ToLower(body), "token"),
				"Response should indicate authentication failure for %s %s: %s",
				endpoint.method, endpoint.path, body)
		})
	}
}

// TestEmptyAuthorizationHeader tests with empty or missing Authorization header
func (suite *AuthBypassTestSuite) TestEmptyAuthorizationHeader() {
	t := suite.T()

	emptyHeaders := []string{
		"",        // No header
		"Bearer",  // Just Bearer
		"Bearer ", // Bearer with space but no token
		"   ",     // Whitespace only
	}

	for _, headerValue := range emptyHeaders {
		t.Run(fmt.Sprintf("Empty auth header: '%s'", headerValue), func(t *testing.T) {
			rec, err := suite.apiSuite.MakeRequest("GET", "/api/v1/users/me", nil, headerValue)
			require.NoError(t, err)
			require.Equal(t, http.StatusUnauthorized, rec.Code,
				"Empty auth header '%s' should be rejected with 401, got %d. Response: %s",
				headerValue, rec.Code, rec.Body.String())
		})
	}
}

// TestInvalidTokenFormats tests various invalid token formats
func (suite *AuthBypassTestSuite) TestInvalidTokenFormats() {
	t := suite.T()

	invalidTokens := []string{
		"Bearer invalid-token",
		"Bearer 123",
		"Bearer !@#$%^&*()",
		"Bearer " + strings.Repeat("a", 1000), // Very long token
		"Bearer <script>alert('xss')</script>",
		"Bearer '; DROP TABLE users; --",
		"Basic dXNlcjpwYXNzd29yZA==", // Basic auth instead of Bearer
		"API-Key secret-key",         // Wrong auth type
		"Bearer \x00\x01\x02",        // Binary data
	}

	for _, token := range invalidTokens {
		t.Run(fmt.Sprintf("Invalid token format: %s", strings.ReplaceAll(token, "\x00", "\\x00")), func(t *testing.T) {
			rec, err := suite.apiSuite.MakeRequest("GET", "/api/v1/users/me", nil, token)
			require.NoError(t, err)
			require.Equal(t, http.StatusUnauthorized, rec.Code,
				"Invalid token format should be rejected with 401, got %d for token: %s. Response: %s",
				rec.Code, token, rec.Body.String())
		})
	}
}

// TestUserPrivilegeEscalation tests that regular users cannot access admin endpoints
func (suite *AuthBypassTestSuite) TestUserPrivilegeEscalation() {
	t := suite.T()

	// Use regular user token
	userToken := suite.apiSuite.GetUserToken()

	for _, endpoint := range adminOnlyEndpoints {
		t.Run(fmt.Sprintf("User access to admin endpoint %s %s", endpoint.method, endpoint.path), func(t *testing.T) {
			rec, err := suite.apiSuite.MakeRequest(endpoint.method, endpoint.path, endpoint.body, userToken)
			require.NoError(t, err)

			// Should return 403 Forbidden or 401 Unauthorized
			require.True(t, rec.Code == http.StatusForbidden || rec.Code == http.StatusUnauthorized,
				"User should not access admin endpoint %s %s, got %d. Description: %s. Response: %s",
				endpoint.method, endpoint.path, rec.Code, endpoint.description, rec.Body.String())
		})
	}
}

// TestCrossOrganizationAccess tests that users cannot access other organizations' data
func (suite *AuthBypassTestSuite) TestCrossOrganizationAccess() {
	t := suite.T()

	// Use user token which belongs to default org
	userToken := suite.apiSuite.GetUserToken()

	// Try to access a different (non-existent) organization
	fakeOrgID := "11111111-1111-1111-1111-111111111111"

	crossOrgEndpoints := []struct {
		method string
		path   string
		body   interface{}
	}{
		{"GET", fmt.Sprintf("/api/v1/organizations/%s", fakeOrgID), nil},
		{"PATCH", fmt.Sprintf("/api/v1/organizations/%s", fakeOrgID), map[string]interface{}{"name": "Hacked"}},
		{"POST", fmt.Sprintf("/api/v1/organizations/%s/events", fakeOrgID), map[string]interface{}{
			"title":       "Cross Org Event",
			"description": "Test",
			"start_date":  "2024-12-31T10:00:00Z",
			"end_date":    "2024-12-31T12:00:00Z",
			"location":    "Test",
		}},
		{"DELETE", fmt.Sprintf("/api/v1/organizations/%s", fakeOrgID), nil},
	}

	for _, endpoint := range crossOrgEndpoints {
		t.Run(fmt.Sprintf("Cross-org access %s %s", endpoint.method, endpoint.path), func(t *testing.T) {
			rec, err := suite.apiSuite.MakeRequest(endpoint.method, endpoint.path, endpoint.body, userToken)
			require.NoError(t, err)

			// Should return 403 Forbidden or 404 Not Found
			require.True(t, rec.Code == http.StatusForbidden || rec.Code == http.StatusNotFound || rec.Code == http.StatusUnauthorized,
				"Cross-org access should be denied for %s %s, got %d. Response: %s",
				endpoint.method, endpoint.path, rec.Code, rec.Body.String())
		})
	}
}

// TestMethodOverrideBypass tests protection against HTTP method override attacks
func (suite *AuthBypassTestSuite) TestMethodOverrideBypass() {
	t := suite.T()

	// Test common method override headers
	methodOverrideHeaders := []string{
		"X-HTTP-Method-Override",
		"X-HTTP-Method",
		"X-Method-Override",
		"_method",
	}

	userToken := suite.apiSuite.GetUserToken()

	for _, header := range methodOverrideHeaders {
		t.Run(fmt.Sprintf("Method override via %s header", header), func(t *testing.T) {
			// Try to override GET with DELETE using header
			rec, err := suite.apiSuite.MakeRequest("GET", fmt.Sprintf("/api/v1/organizations/%s", suite.apiSuite.GetOrgID()), nil, userToken)
			require.NoError(t, err)

			// Normal GET should work for user
			if rec.Code == http.StatusOK {
				// Method override should not allow privilege escalation
				// This test would require custom request creation to add override headers
				// For now, just verify the normal request works as expected
				t.Log("Method override test requires custom HTTP client - normal GET works as expected")
			}
		})
	}
}

// TestPathTraversalInRoutes tests path traversal attempts in route parameters
func (suite *AuthBypassTestSuite) TestPathTraversalInRoutes() {
	t := suite.T()

	pathTraversalPayloads := []string{
		"../",
		"../../admin",
		"..%2F..%2Fadmin",
		"%2e%2e%2f",
		"....//",
		"..\\..\\admin",
		"%252e%252e%252f", // Double URL encoding
		"./../../admin",
		"../admin/../users",
	}

	userToken := suite.apiSuite.GetUserToken()

	for _, payload := range pathTraversalPayloads {
		t.Run(fmt.Sprintf("Path traversal: %s", payload), func(t *testing.T) {
			// Try path traversal in organization ID parameter
			rec, err := suite.apiSuite.MakeRequest("GET", fmt.Sprintf("/api/v1/organizations/%s", payload), nil, userToken)
			require.NoError(t, err)

			// Should return 400 Bad Request or 404 Not Found, not 200 OK
			require.True(t, rec.Code == http.StatusBadRequest || rec.Code == http.StatusNotFound || rec.Code == http.StatusUnauthorized,
				"Path traversal should be blocked for payload %s, got %d. Response: %s",
				payload, rec.Code, rec.Body.String())
		})
	}
}

// TestSessionFixation tests protection against session fixation attacks
func (suite *AuthBypassTestSuite) TestSessionFixation() {
	t := suite.T()

	// Test that using someone else's token doesn't work
	// This is a basic test - in practice, tokens should be properly scoped to users

	validToken := suite.apiSuite.GetUserToken()

	// Try to use the token for different user operations
	// For example, try to update user profile - should only work for the token owner
	updateReq := map[string]interface{}{
		"display_name": "Session Fixation Attempt",
	}

	rec, err := suite.apiSuite.MakeRequest("PATCH", "/api/v1/users/me", updateReq, validToken)
	require.NoError(t, err)

	// This should work since it's a valid token for the user
	// The key is that the token is properly scoped to the authenticated user
	if rec.Code == http.StatusOK {
		// Verify the response contains expected user data, not other users' data
		var response map[string]interface{}
		err = json.Unmarshal(rec.Body.Bytes(), &response)
		require.NoError(t, err)

		// Should contain user data, not reveal other users' information
		require.Contains(t, response, "id")
		require.Contains(t, response, "display_name")
	}
}

// TestTokenLeakageInErrorMessages tests that tokens aren't leaked in error responses
func (suite *AuthBypassTestSuite) TestTokenLeakageInErrorMessages() {
	t := suite.T()

	sensitiveToken := "Bearer very-secret-token-that-should-not-leak"

	rec, err := suite.apiSuite.MakeRequest("GET", "/api/v1/users/me", nil, sensitiveToken)
	require.NoError(t, err)
	require.Equal(t, http.StatusUnauthorized, rec.Code)

	body := rec.Body.String()

	// Ensure the token value is not leaked in error response
	require.NotContains(t, body, "very-secret-token-that-should-not-leak",
		"Token should not be leaked in error response")
	require.NotContains(t, body, sensitiveToken,
		"Full token should not be leaked in error response")
}

// TestConcurrentAuthenticationBypass tests authentication under concurrent load
func (suite *AuthBypassTestSuite) TestConcurrentAuthenticationBypass() {
	t := suite.T()

	const numRequests = 10
	results := make(chan int, numRequests)

	// Test concurrent unauthenticated requests
	for i := 0; i < numRequests; i++ {
		go func() {
			rec, err := suite.apiSuite.MakeUnauthenticatedRequest("GET", "/api/v1/users/me", nil)
			if err != nil {
				results <- 500
			} else {
				results <- rec.Code
			}
		}()
	}

	// All should be rejected with 401
	unauthorizedCount := 0
	for i := 0; i < numRequests; i++ {
		code := <-results
		if code == http.StatusUnauthorized {
			unauthorizedCount++
		}
	}

	require.Equal(t, numRequests, unauthorizedCount,
		"All concurrent unauthenticated requests should be rejected, got %d/%d rejections",
		unauthorizedCount, numRequests)
}

// TestParameterPollution tests HTTP parameter pollution attacks
func (suite *AuthBypassTestSuite) TestParameterPollution() {
	t := suite.T()

	userToken := suite.apiSuite.GetUserToken()

	// Test with parameter pollution in query string
	pollutedEndpoints := []string{
		"/api/v1/events?limit=10&limit=1000",                                              // Try to bypass pagination limits
		"/api/v1/events?page=1&page=admin",                                                // Try to access admin pages
		"/api/v1/organizations/00000000-0000-0000-0000-000000000002?role=user&role=admin", // Try role pollution
	}

	for _, endpoint := range pollutedEndpoints {
		t.Run(fmt.Sprintf("Parameter pollution: %s", endpoint), func(t *testing.T) {
			rec, err := suite.apiSuite.MakeRequest("GET", endpoint, nil, userToken)
			require.NoError(t, err)

			// Should handle gracefully - either work normally or return validation error
			// Should not grant elevated privileges
			require.True(t, rec.Code == http.StatusOK || rec.Code == http.StatusBadRequest || rec.Code == http.StatusUnauthorized,
				"Parameter pollution should be handled gracefully for %s, got %d. Response: %s",
				endpoint, rec.Code, rec.Body.String())
		})
	}
}

// TestCaseInsensitiveBypass tests case sensitivity in authentication
func (suite *AuthBypassTestSuite) TestCaseInsensitiveBypass() {
	t := suite.T()

	validToken := suite.apiSuite.GetUserToken()

	// Test various case manipulations of the Bearer token
	casedHeaders := []string{
		strings.ToUpper(validToken),                           // All uppercase
		strings.ToLower(validToken),                           // All lowercase
		"bearer " + strings.TrimPrefix(validToken, "Bearer "), // lowercase bearer
		"BEARER " + strings.TrimPrefix(validToken, "Bearer "), // uppercase bearer
	}

	for _, header := range casedHeaders {
		t.Run(fmt.Sprintf("Case sensitivity test: %s", header[:min(len(header), 30)]), func(t *testing.T) {
			rec, err := suite.apiSuite.MakeRequest("GET", "/api/v1/users/me", nil, header)
			require.NoError(t, err)

			// Behavior depends on implementation - some may accept lowercase "bearer"
			// The key is consistent behavior and no bypasses
			if strings.HasPrefix(header, "bearer ") {
				// Lowercase bearer might be accepted by some implementations
				require.True(t, rec.Code == http.StatusOK || rec.Code == http.StatusUnauthorized,
					"Lowercase bearer should have consistent behavior, got %d", rec.Code)
			} else {
				// Other case variations should generally be rejected
				require.True(t, rec.Code == http.StatusOK || rec.Code == http.StatusUnauthorized,
					"Case variations should have consistent behavior, got %d", rec.Code)
			}
		})
	}
}

// Helper function for minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func TestAuthBypassTestSuite(t *testing.T) {
	suite.Run(t, new(AuthBypassTestSuite))
}
