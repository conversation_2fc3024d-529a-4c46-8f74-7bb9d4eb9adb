package security

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	"Membership-SAAS-System-Backend/tests/integration"
)

// InputValidationTestSuite tests input validation security across all endpoints
type InputValidationTestSuite struct {
	suite.Suite
	apiSuite *integration.APITestSuite
}

func (suite *InputValidationTestSuite) SetupSuite() {
	suite.apiSuite = integration.SetupAPITest(suite.T())
}

func (suite *InputValidationTestSuite) TearDownSuite() {
	suite.apiSuite.TeardownAPITest(suite.T())
}

func (suite *InputValidationTestSuite) SetupTest() {
	suite.apiSuite.CleanupTestData(suite.T())
}

// TestOversizedPayloads tests protection against oversized request payloads
func (suite *InputValidationTestSuite) TestOversizedPayloads() {
	t := suite.T()

	// Generate very large strings
	largeString := strings.Repeat("A", 10*1024*1024) // 10MB string
	mediumString := strings.Repeat("B", 1024*1024)   // 1MB string

	oversizedPayloads := []struct {
		name string
		data map[string]interface{}
	}{
		{
			name: "Oversized user display name",
			data: map[string]interface{}{
				"display_name": largeString,
				"email":        "<EMAIL>",
			},
		},
		{
			name: "Oversized organization description",
			data: map[string]interface{}{
				"name":        "Test Org",
				"description": largeString,
			},
		},
		{
			name: "Oversized event title",
			data: map[string]interface{}{
				"title":       largeString,
				"description": "Normal description",
				"start_date":  "2024-12-31T10:00:00Z",
				"end_date":    "2024-12-31T12:00:00Z",
				"location":    "Test Location",
			},
		},
		{
			name: "Multiple oversized fields",
			data: map[string]interface{}{
				"title":       mediumString,
				"description": mediumString,
				"location":    mediumString,
				"start_date":  "2024-12-31T10:00:00Z",
				"end_date":    "2024-12-31T12:00:00Z",
			},
		},
	}

	for _, payload := range oversizedPayloads {
		t.Run(payload.name, func(t *testing.T) {
			// Test with user update endpoint
			rec, err := suite.apiSuite.MakeAuthenticatedRequest("PATCH", "/api/v1/users/me", payload.data)
			require.NoError(t, err)

			// Should reject with 400 Bad Request or 413 Payload Too Large
			require.True(t, rec.Code == http.StatusBadRequest || rec.Code == http.StatusRequestEntityTooLarge,
				"Oversized payload should be rejected, got %d for %s. Response: %s",
				rec.Code, payload.name, rec.Body.String())
		})
	}
}

// TestMalformedJSON tests handling of malformed JSON payloads
func (suite *InputValidationTestSuite) TestMalformedJSON() {
	t := suite.T()

	malformedJSONs := []struct {
		name string
		body string
	}{
		{"Unclosed object", `{"name": "test"`},
		{"Unclosed array", `{"tags": ["tag1", "tag2"`},
		{"Invalid quotes", `{"name": 'test'}`},
		{"Missing comma", `{"name": "test" "email": "<EMAIL>"}`},
		{"Trailing comma", `{"name": "test",}`},
		{"Invalid escape", `{"name": "test\x"}`},
		{"Null bytes", "{\x00\"name\": \"test\"}\x00"},
		{"Non-UTF8", `{"name": "test` + string([]byte{0xFF, 0xFE}) + `"}`},
		{"Deeply nested", strings.Repeat(`{"level":`, 1000) + `"deep"` + strings.Repeat("}", 1000)},
		{"Large numbers", `{"number": 123456789012345678901234567890123456789}`},
		{"Invalid unicode", `{"name": "\uD800"}`}, // Unpaired surrogate
	}

	endpoints := []struct {
		method string
		path   string
	}{
		{"PATCH", "/api/v1/users/me"},
		{"POST", fmt.Sprintf("/api/v1/organizations/%s/events", suite.apiSuite.GetOrgID())},
		{"PATCH", fmt.Sprintf("/api/v1/organizations/%s", suite.apiSuite.GetOrgID())},
	}

	for _, endpoint := range endpoints {
		for _, malformed := range malformedJSONs {
			t.Run(fmt.Sprintf("%s %s with %s", endpoint.method, endpoint.path, malformed.name), func(t *testing.T) {
				// Create request with malformed JSON
				req, err := http.NewRequest(endpoint.method, endpoint.path, bytes.NewBufferString(malformed.body))
				require.NoError(t, err)

				req.Header.Set("Content-Type", "application/json")
				req.Header.Set("Authorization", suite.apiSuite.GetUserToken())

				// Use echo's ServeHTTP directly for malformed content
				rec := suite.apiSuite.MakeRawRequest(req)

				// Should return 400 Bad Request for malformed JSON
				require.Equal(t, http.StatusBadRequest, rec.Code,
					"Malformed JSON should be rejected with 400 for %s, got %d. Response: %s",
					malformed.name, rec.Code, rec.Body.String())
			})
		}
	}
}

// TestScriptInjectionAttempts tests protection against script injection
func (suite *InputValidationTestSuite) TestScriptInjectionAttempts() {
	t := suite.T()

	scriptPayloads := []string{
		"<script>alert('xss')</script>",
		"javascript:alert('xss')",
		"<img src=x onerror=alert('xss')>",
		"';alert('xss');//",
		"\"><script>alert('xss')</script>",
		"<svg onload=alert('xss')>",
		"<iframe src=javascript:alert('xss')>",
		"<body onload=alert('xss')>",
		"<input onfocus=alert('xss') autofocus>",
		"<select onfocus=alert('xss') autofocus><option>",
		"data:text/html,<script>alert('xss')</script>",
		"vbscript:msgbox('xss')",
		"<object data=\"data:text/html,<script>alert('xss')</script>\">",
		"<embed src=\"data:text/html,<script>alert('xss')</script>\">",
	}

	testFields := []struct {
		endpoint string
		method   string
		field    string
		baseData map[string]interface{}
	}{
		{
			endpoint: "/api/v1/users/me",
			method:   "PATCH",
			field:    "display_name",
			baseData: map[string]interface{}{},
		},
		{
			endpoint: fmt.Sprintf("/api/v1/organizations/%s", suite.apiSuite.GetOrgID()),
			method:   "PATCH",
			field:    "name",
			baseData: map[string]interface{}{},
		},
		{
			endpoint: fmt.Sprintf("/api/v1/organizations/%s", suite.apiSuite.GetOrgID()),
			method:   "PATCH",
			field:    "description",
			baseData: map[string]interface{}{"name": "Test Org"},
		},
		{
			endpoint: fmt.Sprintf("/api/v1/organizations/%s/events", suite.apiSuite.GetOrgID()),
			method:   "POST",
			field:    "title",
			baseData: map[string]interface{}{
				"description": "Test event",
				"start_date":  "2024-12-31T10:00:00Z",
				"end_date":    "2024-12-31T12:00:00Z",
				"location":    "Test Location",
			},
		},
	}

	for _, testField := range testFields {
		for _, payload := range scriptPayloads {
			t.Run(fmt.Sprintf("%s %s field %s with script", testField.method, testField.endpoint, testField.field), func(t *testing.T) {
				// Create request data with script injection attempt
				data := make(map[string]interface{})
				for k, v := range testField.baseData {
					data[k] = v
				}
				data[testField.field] = payload

				rec, err := suite.apiSuite.MakeAuthenticatedRequest(testField.method, testField.endpoint, data)
				require.NoError(t, err)

				// Should either reject the input or sanitize it
				// Check response doesn't contain the script payload unchanged
				body := rec.Body.String()
				if rec.Code == http.StatusOK || rec.Code == http.StatusCreated {
					// If accepted, ensure script is sanitized
					require.NotContains(t, body, "<script>",
						"Script tags should be sanitized from response")
					require.NotContains(t, body, "javascript:",
						"JavaScript protocols should be sanitized from response")
					require.NotContains(t, body, "alert('xss')",
						"Script content should be sanitized from response")
				} else {
					// If rejected, should be validation error
					require.True(t, rec.Code == http.StatusBadRequest || rec.Code == http.StatusUnprocessableEntity,
						"Script injection should be rejected with validation error, got %d. Response: %s",
						rec.Code, body)
				}
			})
		}
	}
}

// TestSQLInjectionInFields tests SQL injection attempts in form fields
func (suite *InputValidationTestSuite) TestSQLInjectionInFields() {
	t := suite.T()

	sqlPayloads := []string{
		"'; DROP TABLE users; --",
		"' OR '1'='1",
		"' UNION SELECT * FROM users --",
		"'; UPDATE users SET email='hacked' WHERE id=1; --",
		"' OR 1=1 --",
		"admin'; --",
		"' OR 'x'='x",
		"\"; DROP TABLE events; --",
		"'; EXEC xp_cmdshell('dir'); --",
		"' AND (SELECT COUNT(*) FROM users) > 0 --",
	}

	// Test various text input fields
	testCases := []struct {
		endpoint string
		method   string
		data     map[string]interface{}
	}{
		{
			endpoint: "/api/v1/users/me",
			method:   "PATCH",
			data:     map[string]interface{}{"display_name": "PAYLOAD_HERE"},
		},
		{
			endpoint: "/api/v1/authn/phone/check",
			method:   "POST",
			data:     map[string]interface{}{"phone": "PAYLOAD_HERE"},
		},
		{
			endpoint: "/api/v1/authn/staff/login/initiate",
			method:   "POST",
			data: map[string]interface{}{
				"email":                 "PAYLOAD_HERE",
				"client_id":             "test-client",
				"code_challenge":        "test-challenge",
				"code_challenge_method": "S256",
				"state":                 "test-state",
			},
		},
	}

	for _, testCase := range testCases {
		for _, payload := range sqlPayloads {
			t.Run(fmt.Sprintf("SQL injection in %s %s", testCase.method, testCase.endpoint), func(t *testing.T) {
				// Replace PAYLOAD_HERE with actual SQL injection payload
				data := make(map[string]interface{})
				for k, v := range testCase.data {
					if v == "PAYLOAD_HERE" {
						data[k] = payload
					} else {
						data[k] = v
					}
				}

				var rec *httptest.ResponseRecorder
				var err error

				if testCase.endpoint == "/api/v1/users/me" {
					rec, err = suite.apiSuite.MakeAuthenticatedRequest(testCase.method, testCase.endpoint, data)
				} else {
					rec, err = suite.apiSuite.MakeUnauthenticatedRequest(testCase.method, testCase.endpoint, data)
				}
				require.NoError(t, err)

				// Should handle gracefully without SQL errors
				body := rec.Body.String()
				require.NotContains(t, strings.ToLower(body), "sql",
					"Response should not contain SQL error messages")
				require.NotContains(t, strings.ToLower(body), "postgresql",
					"Response should not contain database error messages")
				require.NotContains(t, strings.ToLower(body), "syntax error",
					"Response should not contain syntax error messages")
			})
		}
	}
}

// TestExcessiveArraySizes tests handling of arrays with excessive elements
func (suite *InputValidationTestSuite) TestExcessiveArraySizes() {
	t := suite.T()

	// Create arrays of various sizes
	smallArray := make([]string, 10)
	largeArray := make([]string, 10000)
	extremeArray := make([]string, 100000)

	for i := range smallArray {
		smallArray[i] = fmt.Sprintf("item-%d", i)
	}
	for i := range largeArray {
		largeArray[i] = fmt.Sprintf("item-%d", i)
	}
	for i := range extremeArray {
		extremeArray[i] = fmt.Sprintf("item-%d", i)
	}

	arrayTests := []struct {
		name  string
		array []string
	}{
		{"Small array (10 items)", smallArray},
		{"Large array (10k items)", largeArray},
		{"Extreme array (100k items)", extremeArray},
	}

	for _, test := range arrayTests {
		t.Run(test.name, func(t *testing.T) {
			// Test with event creation that might accept array fields
			eventData := map[string]interface{}{
				"title":       "Test Event",
				"description": "Test description",
				"start_date":  "2024-12-31T10:00:00Z",
				"end_date":    "2024-12-31T12:00:00Z",
				"location":    "Test Location",
				"tags":        test.array, // This might not be a real field, but test validation
			}

			rec, err := suite.apiSuite.MakeAuthenticatedRequest("POST",
				fmt.Sprintf("/api/v1/organizations/%s/events", suite.apiSuite.GetOrgID()),
				eventData)
			require.NoError(t, err)

			// Large arrays should be rejected or handled gracefully
			if len(test.array) > 1000 {
				require.True(t, rec.Code == http.StatusBadRequest || rec.Code == http.StatusRequestEntityTooLarge,
					"Large array should be rejected, got %d for %s. Response: %s",
					rec.Code, test.name, rec.Body.String())
			}
		})
	}
}

// TestSpecialCharactersHandling tests handling of special characters and encodings
func (suite *InputValidationTestSuite) TestSpecialCharactersHandling() {
	t := suite.T()

	specialCharInputs := []struct {
		name  string
		value string
	}{
		{"Null bytes", "test\x00user"},
		{"Control characters", "test\x01\x02\x03user"},
		{"Unicode special", "test\u0000\u0001user"},
		{"Emoji", "test🎉user👨‍💻"},
		{"Unicode normalization", "test\u0065\u0301"}, // é using combining characters
		{"RTL override", "test\u202Euser"},
		{"Zero width chars", "test\u200Buser\u200C"},
		{"Homoglyphs", "аdmin"}, // Cyrillic 'а' instead of Latin 'a'
		{"Mixed scripts", "adminАдмин"},
		{"Long unicode", strings.Repeat("🎉", 1000)},
		{"Byte order mark", "\uFEFFtest"},
		{"Line separators", "test\u2028\u2029user"},
	}

	for _, input := range specialCharInputs {
		t.Run(fmt.Sprintf("Special characters: %s", input.name), func(t *testing.T) {
			// Test with user display name update
			userData := map[string]interface{}{
				"display_name": input.value,
			}

			rec, err := suite.apiSuite.MakeAuthenticatedRequest("PATCH", "/api/v1/users/me", userData)
			require.NoError(t, err)

			// Should handle gracefully - either accept valid Unicode or reject invalid sequences
			body := rec.Body.String()

			if strings.Contains(input.value, "\x00") || strings.Contains(input.value, "\x01") {
				// Null bytes and control characters should be rejected
				require.True(t, rec.Code == http.StatusBadRequest || rec.Code == http.StatusUnprocessableEntity,
					"Null bytes and control characters should be rejected, got %d for %s. Response: %s",
					rec.Code, input.name, body)
			} else if strings.Contains(input.name, "Emoji") || strings.Contains(input.name, "Unicode") {
				// Valid Unicode should be handled properly
				if rec.Code == http.StatusOK {
					// If accepted, should not corrupt the data
					var response map[string]interface{}
					err = json.Unmarshal(rec.Body.Bytes(), &response)
					require.NoError(t, err, "Valid Unicode response should be parseable JSON")
				}
			}
		})
	}
}

// TestNestedObjectDepth tests handling of deeply nested JSON objects
func (suite *InputValidationTestSuite) TestNestedObjectDepth() {
	t := suite.T()

	// Create deeply nested objects
	depths := []int{10, 100, 1000}

	for _, depth := range depths {
		t.Run(fmt.Sprintf("Nested depth %d", depth), func(t *testing.T) {
			// Build nested object
			nested := make(map[string]interface{})
			current := nested

			for i := 0; i < depth-1; i++ {
				current["level"] = make(map[string]interface{})
				current = current["level"].(map[string]interface{})
			}
			current["value"] = "deep"

			// Test with user update
			userData := map[string]interface{}{
				"display_name": "test",
				"metadata":     nested, // This might not be a real field
			}

			rec, err := suite.apiSuite.MakeAuthenticatedRequest("PATCH", "/api/v1/users/me", userData)
			require.NoError(t, err)

			// Deep nesting should be rejected or limited
			if depth > 100 {
				require.True(t, rec.Code == http.StatusBadRequest || rec.Code == http.StatusRequestEntityTooLarge,
					"Deep nesting should be rejected, got %d for depth %d. Response: %s",
					rec.Code, depth, rec.Body.String())
			}
		})
	}
}

// TestFileUploadValidation tests file upload security (if applicable)
func (suite *InputValidationTestSuite) TestFileUploadValidation() {
	t := suite.T()

	// This is a placeholder for file upload validation tests
	// In practice, this would test:
	// - File size limits
	// - File type validation
	// - Malicious file content detection
	// - Path traversal in filenames

	maliciousFilenames := []string{
		"../../../etc/passwd",
		"..\\..\\windows\\system32\\config\\sam",
		"file.php.jpg",                     // Double extension
		"file.exe",                         // Executable extension
		"<script>.jpg",                     // Script in filename
		"file\x00.jpg",                     // Null byte injection
		strings.Repeat("a", 1000) + ".jpg", // Very long filename
	}

	for _, filename := range maliciousFilenames {
		t.Run(fmt.Sprintf("Malicious filename: %s", filename), func(t *testing.T) {
			// Note: This would require actual multipart form testing
			// For now, just verify the test structure is in place
			t.Logf("File upload validation test placeholder for filename: %s", filename)

			// In a real implementation, this would:
			// 1. Create multipart form data with malicious filename
			// 2. Send to file upload endpoint
			// 3. Verify proper validation and rejection
		})
	}
}

// TestConcurrentInputValidation tests input validation under concurrent load
func (suite *InputValidationTestSuite) TestConcurrentInputValidation() {
	t := suite.T()

	const numRequests = 10
	results := make(chan int, numRequests)

	// Test concurrent requests with malicious input
	maliciousData := map[string]interface{}{
		"display_name": "<script>alert('xss')</script>",
	}

	for i := 0; i < numRequests; i++ {
		go func() {
			rec, err := suite.apiSuite.MakeAuthenticatedRequest("PATCH", "/api/v1/users/me", maliciousData)
			if err != nil {
				results <- 500
			} else {
				results <- rec.Code
			}
		}()
	}

	// Collect results
	validationCount := 0
	for i := 0; i < numRequests; i++ {
		code := <-results
		if code == http.StatusBadRequest || code == http.StatusUnprocessableEntity {
			validationCount++
		}
	}

	// All should consistently apply validation
	require.True(t, validationCount == numRequests || validationCount == 0,
		"Input validation should be consistent across concurrent requests, got %d/%d validation rejections",
		validationCount, numRequests)
}

func TestInputValidationTestSuite(t *testing.T) {
	suite.Run(t, new(InputValidationTestSuite))
}
