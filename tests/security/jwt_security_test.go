package security

import (
	"fmt"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	"Membership-SAAS-System-Backend/tests/integration"
)

// JWTSecurityTestSuite tests JWT token security across all scenarios
type JWTSecurityTestSuite struct {
	suite.Suite
	apiSuite *integration.APITestSuite
}

func (suite *JWTSecurityTestSuite) SetupSuite() {
	suite.apiSuite = integration.SetupAPITest(suite.T())
}

func (suite *JWTSecurityTestSuite) TearDownSuite() {
	suite.apiSuite.TeardownAPITest(suite.T())
}

func (suite *JWTSecurityTestSuite) SetupTest() {
	suite.apiSuite.CleanupTestData(suite.T())
}

// generateMaliciousJWT creates various types of malicious JWT tokens for testing
func (suite *JWTSecurityTestSuite) generateMaliciousJWT(maliciousType string) string {
	switch maliciousType {
	case "expired":
		// Create an expired token
		claims := jwt.MapClaims{
			"user_id":       uuid.New().String(),
			"platform_role": "user",
			"active_org": map[string]interface{}{
				"id":   uuid.New().String(),
				"role": "member",
			},
			"exp": time.Now().Add(-time.Hour).Unix(), // Expired 1 hour ago
			"iat": time.Now().Add(-2 * time.Hour).Unix(),
			"nbf": time.Now().Add(-2 * time.Hour).Unix(),
			"iss": "MembershipSaas",
			"sub": uuid.New().String(),
		}
		token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
		tokenString, _ := token.SignedString([]byte("test-secret-key-for-testing"))
		return tokenString

	case "wrong_signature":
		// Create token with wrong signing key
		claims := jwt.MapClaims{
			"user_id":       uuid.New().String(),
			"platform_role": "user",
			"active_org": map[string]interface{}{
				"id":   uuid.New().String(),
				"role": "member",
			},
			"exp": time.Now().Add(time.Hour).Unix(),
			"iat": time.Now().Unix(),
			"nbf": time.Now().Unix(),
			"iss": "MembershipSaas",
			"sub": uuid.New().String(),
		}
		token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
		tokenString, _ := token.SignedString([]byte("wrong-secret-key"))
		return tokenString

	case "tampered":
		// Create a valid token then tamper with it
		claims := jwt.MapClaims{
			"user_id":       uuid.New().String(),
			"platform_role": "user",
			"active_org": map[string]interface{}{
				"id":   uuid.New().String(),
				"role": "member",
			},
			"exp": time.Now().Add(time.Hour).Unix(),
			"iat": time.Now().Unix(),
			"nbf": time.Now().Unix(),
			"iss": "MembershipSaas",
			"sub": uuid.New().String(),
		}
		token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
		tokenString, _ := token.SignedString([]byte("test-secret-key-for-testing"))

		// Tamper with the payload part (change user role from user to admin)
		parts := strings.Split(tokenString, ".")
		if len(parts) == 3 {
			// Decode payload, modify it, encode it back
			payload := parts[1]
			// Simple string replacement to change role (this will break signature)
			tamperedPayload := strings.Replace(payload, "user", "admin", 1)
			return parts[0] + "." + tamperedPayload + "." + parts[2]
		}
		return tokenString

	case "none_algorithm":
		// Create token with "none" algorithm (algorithm confusion attack)
		claims := jwt.MapClaims{
			"user_id":       uuid.New().String(),
			"platform_role": "super_admin", // Elevated privilege
			"active_org": map[string]interface{}{
				"id":   uuid.New().String(),
				"role": "owner",
			},
			"exp": time.Now().Add(time.Hour).Unix(),
			"iat": time.Now().Unix(),
			"nbf": time.Now().Unix(),
			"iss": "MembershipSaas",
			"sub": uuid.New().String(),
		}
		token := jwt.NewWithClaims(jwt.SigningMethodNone, claims)
		tokenString, _ := token.SignedString(jwt.UnsafeAllowNoneSignatureType)
		return tokenString

	case "privilege_escalation":
		// Create token with elevated privileges
		claims := jwt.MapClaims{
			"user_id":       "00000000-0000-0000-0000-000000000001", // Try to impersonate superadmin
			"platform_role": "super_admin",
			"active_org": map[string]interface{}{
				"id":   "00000000-0000-0000-0000-000000000002",
				"role": "owner",
			},
			"exp":      time.Now().Add(time.Hour).Unix(),
			"iat":      time.Now().Unix(),
			"nbf":      time.Now().Unix(),
			"iss":      "MembershipSaas",
			"sub":      "00000000-0000-0000-0000-000000000001",
		}
		token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
		tokenString, _ := token.SignedString([]byte("wrong-secret-key")) // Wrong key so it should fail
		return tokenString

	case "malformed":
		// Return malformed JWT
		return "invalid.jwt.token"

	case "empty_claims":
		// Token with missing required claims
		claims := jwt.MapClaims{
			"exp": time.Now().Add(time.Hour).Unix(),
		}
		token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
		tokenString, _ := token.SignedString([]byte("test-secret-key-for-testing"))
		return tokenString

	default:
		return "Bearer invalid-token"
	}
}

// TestExpiredTokenRejection tests that expired tokens are properly rejected
func (suite *JWTSecurityTestSuite) TestExpiredTokenRejection() {
	t := suite.T()

	expiredToken := suite.generateMaliciousJWT("expired")

	// Test various protected endpoints with expired token
	endpoints := []struct {
		method string
		path   string
		body   interface{}
	}{
		{"GET", "/api/v1/users/me", nil},
		{"GET", fmt.Sprintf("/api/v1/organizations/%s", suite.apiSuite.GetOrgID()), nil},
		{"GET", "/api/v1/events", nil},
		{"POST", "/api/v1/authn/logout", map[string]interface{}{"refresh_token": "test"}},
	}

	for _, endpoint := range endpoints {
		t.Run(fmt.Sprintf("%s %s with expired token", endpoint.method, endpoint.path), func(t *testing.T) {
			rec, err := suite.apiSuite.MakeRequest(endpoint.method, endpoint.path, endpoint.body, expiredToken)
			require.NoError(t, err)

			// Should return 401 Unauthorized
			require.Equal(t, http.StatusUnauthorized, rec.Code,
				"Expected 401 for expired token on %s %s, got %d. Response: %s",
				endpoint.method, endpoint.path, rec.Code, rec.Body.String())

			// Response should indicate token issue
			body := rec.Body.String()
			require.True(t, strings.Contains(strings.ToLower(body), "unauthorized") ||
				strings.Contains(strings.ToLower(body), "token") ||
				strings.Contains(strings.ToLower(body), "expired"),
				"Response should indicate authorization failure: %s", body)
		})
	}
}

// TestTamperedTokenRejection tests that tampered tokens are rejected
func (suite *JWTSecurityTestSuite) TestTamperedTokenRejection() {
	t := suite.T()

	tamperedToken := suite.generateMaliciousJWT("tampered")

	// Test that tampered token is rejected
	rec, err := suite.apiSuite.MakeRequest("GET", "/api/v1/users/me", nil, tamperedToken)
	require.NoError(t, err)
	require.Equal(t, http.StatusUnauthorized, rec.Code,
		"Tampered token should be rejected with 401, got %d. Response: %s", rec.Code, rec.Body.String())
}

// TestWrongSignatureRejection tests tokens signed with wrong key
func (suite *JWTSecurityTestSuite) TestWrongSignatureRejection() {
	t := suite.T()

	wrongSigToken := suite.generateMaliciousJWT("wrong_signature")

	rec, err := suite.apiSuite.MakeRequest("GET", "/api/v1/users/me", nil, wrongSigToken)
	require.NoError(t, err)
	require.Equal(t, http.StatusUnauthorized, rec.Code,
		"Token with wrong signature should be rejected with 401, got %d. Response: %s", rec.Code, rec.Body.String())
}

// TestAlgorithmConfusionAttack tests protection against algorithm confusion attacks
func (suite *JWTSecurityTestSuite) TestAlgorithmConfusionAttack() {
	t := suite.T()

	noneAlgToken := suite.generateMaliciousJWT("none_algorithm")

	// Try to access admin endpoint with "none" algorithm token
	rec, err := suite.apiSuite.MakeRequest("GET", "/api/v1/users/me", nil, noneAlgToken)
	require.NoError(t, err)
	require.Equal(t, http.StatusUnauthorized, rec.Code,
		"'None' algorithm token should be rejected with 401, got %d. Response: %s", rec.Code, rec.Body.String())
}

// TestPrivilegeEscalationAttempt tests attempts to escalate privileges via JWT manipulation
func (suite *JWTSecurityTestSuite) TestPrivilegeEscalationAttempt() {
	t := suite.T()

	escalationToken := suite.generateMaliciousJWT("privilege_escalation")

	// Try to access admin-only endpoint
	rec, err := suite.apiSuite.MakeRequest("GET", fmt.Sprintf("/api/v1/organizations/%s", suite.apiSuite.GetOrgID()), nil, escalationToken)
	require.NoError(t, err)
	require.Equal(t, http.StatusUnauthorized, rec.Code,
		"Privilege escalation attempt should be rejected with 401, got %d. Response: %s", rec.Code, rec.Body.String())
}

// TestMalformedTokenHandling tests handling of malformed JWT tokens
func (suite *JWTSecurityTestSuite) TestMalformedTokenHandling() {
	t := suite.T()

	malformedTokens := []string{
		"not-a-jwt-token",
		"Bearer",
		"Bearer ",
		"Bearer invalid.jwt",
		"Bearer ..",
		"Bearer a.b.c.d", // Too many parts
		"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9", // Only header
		"InvalidPrefix eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
	}

	for _, malformedToken := range malformedTokens {
		t.Run(fmt.Sprintf("Malformed token: %s", malformedToken), func(t *testing.T) {
			rec, err := suite.apiSuite.MakeRequest("GET", "/api/v1/users/me", nil, malformedToken)
			require.NoError(t, err)
			require.Equal(t, http.StatusUnauthorized, rec.Code,
				"Malformed token should be rejected with 401, got %d for token: %s. Response: %s",
				rec.Code, malformedToken, rec.Body.String())
		})
	}
}

// TestEmptyClaimsToken tests tokens with missing required claims
func (suite *JWTSecurityTestSuite) TestEmptyClaimsToken() {
	t := suite.T()

	emptyClaimsToken := suite.generateMaliciousJWT("empty_claims")

	rec, err := suite.apiSuite.MakeRequest("GET", "/api/v1/users/me", nil, emptyClaimsToken)
	require.NoError(t, err)
	require.Equal(t, http.StatusUnauthorized, rec.Code,
		"Token with empty claims should be rejected with 401, got %d. Response: %s", rec.Code, rec.Body.String())
}

// TestTokenReuse tests that refresh tokens can't be reused
func (suite *JWTSecurityTestSuite) TestTokenReuse() {
	t := suite.T()

	// First, attempt to refresh with an invalid refresh token
	refreshReq := map[string]interface{}{
		"refresh_token": "fake-refresh-token-that-should-fail",
	}

	rec, err := suite.apiSuite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/token/refresh", refreshReq)
	require.NoError(t, err)
	require.Equal(t, http.StatusUnauthorized, rec.Code,
		"Invalid refresh token should be rejected with 401, got %d. Response: %s", rec.Code, rec.Body.String())

	// Test the same token again - should still fail
	rec, err = suite.apiSuite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/token/refresh", refreshReq)
	require.NoError(t, err)
	require.Equal(t, http.StatusUnauthorized, rec.Code,
		"Reused invalid refresh token should still be rejected with 401, got %d. Response: %s", rec.Code, rec.Body.String())
}

// TestJWTHeaderManipulation tests various JWT header manipulations
func (suite *JWTSecurityTestSuite) TestJWTHeaderManipulation() {
	t := suite.T()

	// Test with various manipulated Authorization headers
	manipulatedHeaders := []string{
		"bearer " + suite.apiSuite.GetUserToken(),  // lowercase bearer
		"BEARER " + suite.apiSuite.GetUserToken(),  // uppercase bearer
		"Token " + suite.apiSuite.GetUserToken(),   // wrong prefix
		suite.apiSuite.GetUserToken(),              // no Bearer prefix
		"Bearer\t" + suite.apiSuite.GetUserToken(), // tab instead of space
		"Bearer  " + suite.apiSuite.GetUserToken(), // double space
		" Bearer " + suite.apiSuite.GetUserToken(), // leading space
	}

	for _, header := range manipulatedHeaders {
		t.Run(fmt.Sprintf("Header manipulation: %s", strings.ReplaceAll(header, "\t", "\\t")), func(t *testing.T) {
			rec, err := suite.apiSuite.MakeRequest("GET", "/api/v1/users/me", nil, header)
			require.NoError(t, err)

			// Most should fail except potentially the lowercase bearer (depends on implementation)
			if strings.HasPrefix(header, "bearer ") {
				// Some implementations might accept lowercase "bearer"
				require.True(t, rec.Code == http.StatusOK || rec.Code == http.StatusUnauthorized,
					"Lowercase bearer might be accepted or rejected, got %d for header: %s", rec.Code, header)
			} else {
				require.Equal(t, http.StatusUnauthorized, rec.Code,
					"Invalid header format should be rejected with 401, got %d for header: %s. Response: %s",
					rec.Code, header, rec.Body.String())
			}
		})
	}
}

// TestConcurrentTokenValidation tests token validation under concurrent load
func (suite *JWTSecurityTestSuite) TestConcurrentTokenValidation() {
	t := suite.T()

	// Test multiple concurrent requests with the same valid token
	const numRequests = 10
	results := make(chan int, numRequests)

	validToken := suite.apiSuite.GetUserToken()

	for i := 0; i < numRequests; i++ {
		go func() {
			rec, err := suite.apiSuite.MakeRequest("GET", "/api/v1/users/me", nil, validToken)
			if err != nil {
				results <- 500 // Internal error
			} else {
				results <- rec.Code
			}
		}()
	}

	// Collect all results
	successCount := 0
	for i := 0; i < numRequests; i++ {
		code := <-results
		if code == http.StatusOK {
			successCount++
		}
	}

	// All concurrent requests with valid token should succeed
	require.Equal(t, numRequests, successCount,
		"All concurrent requests with valid token should succeed, got %d/%d successes", successCount, numRequests)
}

// TestJWTTimingAttacks tests for potential timing attack vulnerabilities
func (suite *JWTSecurityTestSuite) TestJWTTimingAttacks() {
	t := suite.T()

	// Test that invalid tokens are handled consistently regardless of failure reason
	invalidTokens := []string{
		"Bearer invalid-token-1",
		"Bearer invalid-token-2-much-longer-to-test-timing",
		"Bearer " + suite.generateMaliciousJWT("wrong_signature"),
		"Bearer " + suite.generateMaliciousJWT("expired"),
		"Bearer " + suite.generateMaliciousJWT("tampered"),
	}

	var responseTimes []time.Duration

	for _, token := range invalidTokens {
		start := time.Now()
		rec, err := suite.apiSuite.MakeRequest("GET", "/api/v1/users/me", nil, token)
		elapsed := time.Since(start)

		require.NoError(t, err)
		require.Equal(t, http.StatusUnauthorized, rec.Code,
			"Invalid token should return 401, got %d", rec.Code)

		responseTimes = append(responseTimes, elapsed)
	}

	// Check that response times are relatively consistent (within reasonable bounds)
	// This is a basic check - sophisticated timing attacks would require more precise measurement
	avgTime := time.Duration(0)
	for _, rt := range responseTimes {
		avgTime += rt
	}
	avgTime /= time.Duration(len(responseTimes))

	for i, rt := range responseTimes {
		ratio := float64(rt) / float64(avgTime)
		require.True(t, ratio > 0.1 && ratio < 10.0,
			"Response time %d (%v) significantly different from average (%v), ratio: %.2f",
			i, rt, avgTime, ratio)
	}
}

// TestSensitiveDataLeakage tests that JWT validation errors don't leak sensitive information
func (suite *JWTSecurityTestSuite) TestSensitiveDataLeakage() {
	t := suite.T()

	invalidToken := suite.generateMaliciousJWT("wrong_signature")

	rec, err := suite.apiSuite.MakeRequest("GET", "/api/v1/users/me", nil, invalidToken)
	require.NoError(t, err)
	require.Equal(t, http.StatusUnauthorized, rec.Code)

	body := rec.Body.String()

	// Ensure response doesn't leak sensitive information
	forbiddenStrings := []string{
		"secret",
		"key",
		"signature",
		"algorithm",
		"HS256",
		"test-secret-key-for-testing",
		"wrong-secret-key",
		"claims",
		"payload",
	}

	for _, forbidden := range forbiddenStrings {
		require.NotContains(t, strings.ToLower(body), strings.ToLower(forbidden),
			"JWT error response should not contain sensitive information: %s", forbidden)
	}
}

func TestJWTSecurityTestSuite(t *testing.T) {
	suite.Run(t, new(JWTSecurityTestSuite))
}
