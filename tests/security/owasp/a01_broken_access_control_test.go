package owasp

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	"Membership-SAAS-System-Backend/tests/integration"
)

// A01BrokenAccessControlTestSuite tests OWASP A01:2024 - Broken Access Control
type A01BrokenAccessControlTestSuite struct {
	suite.Suite
	apiSuite *integration.APITestSuite

	// Test data
	adminUserID    string
	regularUserID  string
	regularUser2ID string
	org1ID         string
	org2ID         string
	adminToken     string
	userToken      string
	user2Token     string
}

func (suite *A01BrokenAccessControlTestSuite) SetupSuite() {
	suite.apiSuite = integration.SetupAPITest(suite.T())
	suite.setupTestUsers()
}

func (suite *A01BrokenAccessControlTestSuite) TearDownSuite() {
	suite.apiSuite.TeardownAPITest(suite.T())
}

func (suite *A01BrokenAccessControlTestSuite) SetupTest() {
	// Clean any test data between tests
}

func (suite *A01BrokenAccessControlTestSuite) setupTestUsers() {
	t := suite.T()

	// Create admin user
	adminResp, err := suite.apiSuite.CreateTestUser("admin", "+1234567890")
	require.NoError(t, err)
	suite.adminUserID = adminResp.User.ID.String()
	suite.adminToken = adminResp.AccessToken

	// Create regular users
	user1Resp, err := suite.apiSuite.CreateTestUser("user1", "+1234567891")
	require.NoError(t, err)
	suite.regularUserID = user1Resp.User.ID.String()
	suite.userToken = user1Resp.AccessToken

	user2Resp, err := suite.apiSuite.CreateTestUser("user2", "+1234567892")
	require.NoError(t, err)
	suite.regularUser2ID = user2Resp.User.ID.String()
	suite.user2Token = user2Resp.AccessToken

	// Create organizations
	org1, err := suite.apiSuite.CreateTestOrganization("test-org-1", suite.userToken)
	require.NoError(t, err)
	suite.org1ID = org1.ID

	org2, err := suite.apiSuite.CreateTestOrganization("test-org-2", suite.user2Token)
	require.NoError(t, err)
	suite.org2ID = org2.ID
}

// TestHorizontalPrivilegeEscalation tests accessing other users' resources
func (suite *A01BrokenAccessControlTestSuite) TestHorizontalPrivilegeEscalation() {
	t := suite.T()

	testCases := []struct {
		name     string
		method   string
		path     string
		body     interface{}
		expected int
	}{
		{
			name:     "Access other user's profile",
			method:   "GET",
			path:     fmt.Sprintf("/api/v1/users/%s", suite.regularUser2ID),
			expected: http.StatusForbidden,
		},
		{
			name:     "Update other user's profile",
			method:   "PATCH",
			path:     fmt.Sprintf("/api/v1/users/%s", suite.regularUser2ID),
			body:     map[string]string{"display_name": "Hacked"},
			expected: http.StatusForbidden,
		},
		{
			name:     "Delete other user's account",
			method:   "DELETE",
			path:     fmt.Sprintf("/api/v1/users/%s", suite.regularUser2ID),
			expected: http.StatusForbidden,
		},
		{
			name:     "Access other organization's data",
			method:   "GET",
			path:     fmt.Sprintf("/api/v1/organizations/%s", suite.org2ID),
			expected: http.StatusForbidden,
		},
		{
			name:     "Update other organization",
			method:   "PATCH",
			path:     fmt.Sprintf("/api/v1/organizations/%s", suite.org2ID),
			body:     map[string]string{"name": "Hacked Org"},
			expected: http.StatusForbidden,
		},
		{
			name:     "Create event in other organization",
			method:   "POST",
			path:     fmt.Sprintf("/api/v1/organizations/%s/events", suite.org2ID),
			body:     map[string]interface{}{"title": "Unauthorized Event"},
			expected: http.StatusForbidden,
		},
		{
			name:     "Access other org's members",
			method:   "GET",
			path:     fmt.Sprintf("/api/v1/organizations/%s/members", suite.org2ID),
			expected: http.StatusForbidden,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				tc.method, tc.path, tc.body, suite.userToken,
			)
			require.NoError(t, err)
			require.Equal(t, tc.expected, rec.Code,
				"Expected %d but got %d. Response: %s", tc.expected, rec.Code, rec.Body.String())
		})
	}
}

// TestVerticalPrivilegeEscalation tests accessing admin-only resources
func (suite *A01BrokenAccessControlTestSuite) TestVerticalPrivilegeEscalation() {
	t := suite.T()

	adminEndpoints := []struct {
		name   string
		method string
		path   string
		body   interface{}
	}{
		{
			name:   "Access admin dashboard",
			method: "GET",
			path:   "/api/v1/admin/dashboard",
		},
		{
			name:   "Access all users list",
			method: "GET",
			path:   "/api/v1/admin/users",
		},
		{
			name:   "Access system configuration",
			method: "GET",
			path:   "/api/v1/admin/config",
		},
		{
			name:   "Update system settings",
			method: "PATCH",
			path:   "/api/v1/admin/settings",
			body:   map[string]interface{}{"maintenance": true},
		},
		{
			name:   "Access audit logs",
			method: "GET",
			path:   "/api/v1/admin/audit-logs",
		},
		{
			name:   "Suspend user account",
			method: "POST",
			path:   fmt.Sprintf("/api/v1/admin/users/%s/suspend", suite.regularUser2ID),
		},
		{
			name:   "Access billing information",
			method: "GET",
			path:   "/api/v1/admin/billing",
		},
	}

	for _, endpoint := range adminEndpoints {
		t.Run(endpoint.name, func(t *testing.T) {
			// Regular user should not access admin endpoints
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				endpoint.method, endpoint.path, endpoint.body, suite.userToken,
			)
			require.NoError(t, err)
			require.True(t, rec.Code == http.StatusForbidden || rec.Code == http.StatusUnauthorized,
				"Expected 403/401 for admin endpoint, got %d. Response: %s", rec.Code, rec.Body.String())
		})
	}
}

// TestIDORVulnerabilities tests Insecure Direct Object References
func (suite *A01BrokenAccessControlTestSuite) TestIDORVulnerabilities() {
	t := suite.T()

	// Create some test resources
	// Note: CreateTestEvent doesn't exist, so we'll skip event tests for now
	// TODO: Add event creation helper or use direct API calls
	// eventID1 := uuid.New().String() // Unused for now
	eventID2 := uuid.New().String()

	idorTests := []struct {
		name     string
		method   string
		path     string
		body     interface{}
		expected int
	}{
		{
			name:     "Access event by guessing UUID",
			method:   "GET",
			path:     fmt.Sprintf("/api/v1/events/%s", eventID2),
			expected: http.StatusForbidden,
		},
		{
			name:     "Update event by direct ID reference",
			method:   "PATCH",
			path:     fmt.Sprintf("/api/v1/events/%s", eventID2),
			body:     map[string]string{"title": "Hacked Event"},
			expected: http.StatusForbidden,
		},
		{
			name:     "Delete resource by ID",
			method:   "DELETE",
			path:     fmt.Sprintf("/api/v1/events/%s", eventID2),
			expected: http.StatusForbidden,
		},
		{
			name:     "Access file by direct reference",
			method:   "GET",
			path:     fmt.Sprintf("/api/v1/files/%s", uuid.New().String()),
			expected: http.StatusNotFound,
		},
		{
			name:     "Enumerate users by sequential IDs",
			method:   "GET",
			path:     "/api/v1/users/1",
			expected: http.StatusBadRequest,
		},
	}

	for _, test := range idorTests {
		t.Run(test.name, func(t *testing.T) {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				test.method, test.path, test.body, suite.userToken,
			)
			require.NoError(t, err)
			require.Equal(t, test.expected, rec.Code,
				"Expected %d but got %d. Response: %s", test.expected, rec.Code, rec.Body.String())
		})
	}
}

// TestPathTraversalAttacks tests directory traversal vulnerabilities
func (suite *A01BrokenAccessControlTestSuite) TestPathTraversalAttacks() {
	t := suite.T()

	pathTraversalPayloads := []string{
		"../../../etc/passwd",
		"..\\..\\..\\windows\\system32\\config\\sam",
		"....//....//....//etc/passwd",
		"%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
		"..%252f..%252f..%252fetc%252fpasswd",
		"..%c0%af..%c0%af..%c0%afetc%c0%afpasswd",
		"../../../../../../../../etc/passwd%00",
		"....\\....\\....\\etc\\passwd",
		"..%5c..%5c..%5cetc%5cpasswd",
		"..%255c..%255c..%255cetc%255cpasswd",
	}

	for _, payload := range pathTraversalPayloads {
		t.Run(fmt.Sprintf("Path traversal with: %s", payload), func(t *testing.T) {
			// Test file access endpoints
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"GET", fmt.Sprintf("/api/v1/files/%s", payload), nil, suite.userToken,
			)
			require.NoError(t, err)
			require.True(t, rec.Code == http.StatusBadRequest || rec.Code == http.StatusNotFound,
				"Expected 400/404 for path traversal, got %d. Response: %s", rec.Code, rec.Body.String())

			// Test file download endpoints
			rec, err = suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"GET", fmt.Sprintf("/api/v1/download/%s", payload), nil, suite.userToken,
			)
			require.NoError(t, err)
			require.True(t, rec.Code == http.StatusBadRequest || rec.Code == http.StatusNotFound,
				"Expected 400/404 for path traversal, got %d", rec.Code)

			// Ensure no system file content is exposed
			body := rec.Body.String()
			require.NotContains(t, body, "root:")
			require.NotContains(t, body, "/bin/bash")
			require.NotContains(t, body, "Administrator")
		})
	}
}

// TestForcedBrowsing tests accessing hidden/unlinked resources
func (suite *A01BrokenAccessControlTestSuite) TestForcedBrowsing() {
	t := suite.T()

	hiddenEndpoints := []string{
		"/api/v1/debug",
		"/api/v1/admin/backup",
		"/api/v1/internal/metrics",
		"/api/v1/test",
		"/api/v1/.git/config",
		"/api/v1/config.json",
		"/api/v1/.env",
		"/api/v1/swagger.json",
		"/api/v1/phpinfo.php",
		"/api/v1/admin/logs",
	}

	for _, endpoint := range hiddenEndpoints {
		t.Run(fmt.Sprintf("Forced browsing to: %s", endpoint), func(t *testing.T) {
			// Try without authentication
			rec, err := suite.apiSuite.MakeUnauthenticatedRequest("GET", endpoint, nil)
			require.NoError(t, err)
			require.True(t, rec.Code == http.StatusUnauthorized || rec.Code == http.StatusNotFound,
				"Expected 401/404 for hidden endpoint, got %d", rec.Code)

			// Try with regular user authentication
			rec, err = suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"GET", endpoint, nil, suite.userToken,
			)
			require.NoError(t, err)
			require.True(t, rec.Code == http.StatusForbidden || rec.Code == http.StatusNotFound,
				"Expected 403/404 for hidden endpoint with auth, got %d", rec.Code)
		})
	}
}

// TestMethodTampering tests HTTP method override attacks
func (suite *A01BrokenAccessControlTestSuite) TestMethodTampering() {
	t := suite.T()

	// Test method override headers
	overrideHeaders := []struct {
		header string
		value  string
	}{
		{"X-HTTP-Method-Override", "DELETE"},
		{"X-HTTP-Method", "DELETE"},
		{"X-Method-Override", "DELETE"},
		{"_method", "DELETE"},
	}

	for _, oh := range overrideHeaders {
		t.Run(fmt.Sprintf("Method override with %s", oh.header), func(t *testing.T) {
			req, err := http.NewRequest("POST",
				fmt.Sprintf("http://localhost:8081/api/v1/users/%s", suite.regularUser2ID),
				nil)
			require.NoError(t, err)

			req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", suite.userToken))
			req.Header.Set(oh.header, oh.value)

			client := &http.Client{}
			resp, err := client.Do(req)
			require.NoError(t, err)
			defer resp.Body.Close()

			// Should not allow DELETE via method override
			require.NotEqual(t, http.StatusOK, resp.StatusCode,
				"Method override should not bypass access control")
		})
	}
}

// TestRoleManipulation tests attempts to manipulate user roles
func (suite *A01BrokenAccessControlTestSuite) TestRoleManipulation() {
	t := suite.T()

	roleManipulationTests := []struct {
		name string
		path string
		body interface{}
	}{
		{
			name: "Elevate to admin role",
			path: "/api/v1/users/me",
			body: map[string]interface{}{
				"role":          "admin",
				"platform_role": "super_admin",
			},
		},
		{
			name: "Add admin permissions",
			path: "/api/v1/users/me",
			body: map[string]interface{}{
				"permissions": []string{"admin.all", "system.manage"},
			},
		},
		{
			name: "Modify organization role",
			path: fmt.Sprintf("/api/v1/organizations/%s/members/%s", suite.org1ID, suite.regularUserID),
			body: map[string]interface{}{
				"role": "owner",
			},
		},
		{
			name: "Grant system privileges",
			path: "/api/v1/users/me",
			body: map[string]interface{}{
				"platform_role": "super_admin",
			},
		},
	}

	for _, test := range roleManipulationTests {
		t.Run(test.name, func(t *testing.T) {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"PATCH", test.path, test.body, suite.userToken,
			)
			require.NoError(t, err)

			// Should not allow role elevation
			if rec.Code == http.StatusOK {
				// If the update succeeded, verify role wasn't actually changed
				var resp map[string]interface{}
				err = json.Unmarshal(rec.Body.Bytes(), &resp)
				require.NoError(t, err)

				// Check that sensitive fields weren't modified
				require.NotEqual(t, "admin", resp["role"])
				require.NotEqual(t, "super_admin", resp["platform_role"])
				// Platform role should not be elevated
				if respRole, ok := resp["platform_role"]; ok {
					require.NotEqual(t, "staff", respRole)
					require.NotEqual(t, "admin", respRole)
					require.NotEqual(t, "super_admin", respRole)
				}
			}
		})
	}
}

// TestCORSMisconfiguration tests Cross-Origin Resource Sharing issues
func (suite *A01BrokenAccessControlTestSuite) TestCORSMisconfiguration() {
	t := suite.T()

	maliciousOrigins := []string{
		"http://evil.com",
		"https://attacker.com",
		"null",
		"file://",
		"*",
	}

	for _, origin := range maliciousOrigins {
		t.Run(fmt.Sprintf("CORS with origin: %s", origin), func(t *testing.T) {
			req, err := http.NewRequest("OPTIONS",
				"http://localhost:8081/api/v1/users/me",
				nil)
			require.NoError(t, err)

			req.Header.Set("Origin", origin)
			req.Header.Set("Access-Control-Request-Method", "GET")
			req.Header.Set("Access-Control-Request-Headers", "Authorization")

			client := &http.Client{}
			resp, err := client.Do(req)
			require.NoError(t, err)
			defer resp.Body.Close()

			// Check CORS headers
			allowedOrigin := resp.Header.Get("Access-Control-Allow-Origin")
			allowCredentials := resp.Header.Get("Access-Control-Allow-Credentials")

			// Should not allow arbitrary origins with credentials
			if allowedOrigin == origin || allowedOrigin == "*" {
				require.NotEqual(t, "true", allowCredentials,
					"Should not allow credentials with wildcard or untrusted origin")
			}
		})
	}
}

// TestRateLimitingBypass tests rate limiting bypass attempts
func (suite *A01BrokenAccessControlTestSuite) TestRateLimitingBypass() {
	t := suite.T()

	// Test various rate limit bypass techniques
	bypassHeaders := []map[string]string{
		{"X-Forwarded-For": "***********"},
		{"X-Real-IP": "********"},
		{"X-Originating-IP": "**********"},
		{"X-Remote-IP": "***********"},
		{"X-Client-IP": "********"},
		{"CF-Connecting-IP": "*******"},
		{"True-Client-IP": "*******"},
	}

	// Make multiple rapid requests with different spoofed IPs
	for i, headers := range bypassHeaders {
		t.Run(fmt.Sprintf("Rate limit bypass attempt %d", i), func(t *testing.T) {
			// Make 10 rapid requests
			for j := 0; j < 10; j++ {
				reqBody, _ := json.Marshal(map[string]string{
					"phone": "+1234567890",
				})
				req, err := http.NewRequest("POST",
					"http://localhost:8081/api/v1/authn/phone/check",
					bytes.NewBuffer(reqBody))
				require.NoError(t, err)
				req.Header.Set("Content-Type", "application/json")

				// Add bypass headers
				for k, v := range headers {
					req.Header.Set(k, v)
				}

				client := &http.Client{}
				resp, err := client.Do(req)
				require.NoError(t, err)
				resp.Body.Close()
			}
		})
	}
}

func TestA01BrokenAccessControlTestSuite(t *testing.T) {
	suite.Run(t, new(A01BrokenAccessControlTestSuite))
}
