package owasp

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	"Membership-SAAS-System-Backend/tests/integration"
)

// A02CryptographicFailuresTestSuite tests OWASP A02:2024 - Cryptographic Failures
type A02CryptographicFailuresTestSuite struct {
	suite.Suite
	apiSuite  *integration.APITestSuite
	userToken string
}

func (suite *A02CryptographicFailuresTestSuite) SetupSuite() {
	suite.apiSuite = integration.SetupAPITest(suite.T())

	// Create test user
	userResp, err := suite.apiSuite.CreateTestUser("crypto-test", "+1234567890")
	require.NoError(suite.T(), err)
	suite.userToken = userResp.AccessToken
}

func (suite *A02CryptographicFailuresTestSuite) TearDownSuite() {
	suite.apiSuite.TeardownAPITest(suite.T())
}

// TestSensitiveDataExposure tests for exposure of sensitive data
func (suite *A02CryptographicFailuresTestSuite) TestSensitiveDataExposure() {
	t := suite.T()

	// Test various endpoints for sensitive data leakage
	endpoints := []struct {
		name   string
		method string
		path   string
		check  func(body string)
	}{
		{
			name:   "User profile shouldn't expose password hash",
			method: "GET",
			path:   "/api/v1/users/me",
			check: func(body string) {
				require.NotContains(t, body, "password")
				require.NotContains(t, body, "password_hash")
				require.NotContains(t, body, "bcrypt")
				require.NotContains(t, body, "$2a$")
				require.NotContains(t, body, "$2b$")
			},
		},
		{
			name:   "Error messages shouldn't expose sensitive info",
			method: "POST",
			path:   "/api/v1/authn/login",
			check: func(body string) {
				require.NotContains(t, body, "salt")
				require.NotContains(t, body, "hash")
				require.NotContains(t, body, "secret")
				require.NotContains(t, body, "key")
			},
		},
		{
			name:   "API responses shouldn't include internal IDs",
			method: "GET",
			path:   "/api/v1/organizations",
			check: func(body string) {
				require.NotContains(t, body, "internal_id")
				require.NotContains(t, body, "database_id")
			},
		},
	}

	for _, endpoint := range endpoints {
		t.Run(endpoint.name, func(t *testing.T) {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				endpoint.method, endpoint.path, nil, suite.userToken,
			)
			require.NoError(t, err)
			endpoint.check(rec.Body.String())
		})
	}
}

// TestWeakCryptography tests for weak cryptographic implementations
func (suite *A02CryptographicFailuresTestSuite) TestWeakCryptography() {
	t := suite.T()

	// Test weak encryption algorithms
	weakAlgorithms := []string{
		"none", "None", "NONE",
		"HS256", // When expecting RS256
		"MD5", "md5",
		"SHA1", "sha1",
		"DES", "3DES",
		"RC4",
	}

	for _, alg := range weakAlgorithms {
		t.Run(fmt.Sprintf("Reject weak algorithm: %s", alg), func(t *testing.T) {
			// Try to use weak algorithm in JWT
			// Create a fake JWT token (the actual algorithm in the token doesn't matter for this test)
			token := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"

			req, err := http.NewRequest("GET",
				"http://localhost:8081/api/v1/users/me", nil)
			require.NoError(t, err)

			req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))

			client := &http.Client{}
			resp, err := client.Do(req)
			require.NoError(t, err)
			defer resp.Body.Close()

			// Should reject weak algorithms
			require.Equal(t, http.StatusUnauthorized, resp.StatusCode,
				"Should reject JWT with weak algorithm: %s", alg)
		})
	}
}

// TestInsecureRandomness tests for predictable random values
func (suite *A02CryptographicFailuresTestSuite) TestInsecureRandomness() {
	t := suite.T()

	// Collect multiple tokens/IDs to check for patterns
	var otpCodes []string
	var sessionIDs []string

	// Generate multiple OTP codes
	for i := 0; i < 10; i++ {
		rec, err := suite.apiSuite.MakeUnauthenticatedRequest("POST",
			"/api/v1/authn/phone/otp/initiate",
			map[string]string{"phone": fmt.Sprintf("+123456789%d", i)})
		require.NoError(t, err)

		if rec.Code == http.StatusOK {
			var resp map[string]interface{}
			json.Unmarshal(rec.Body.Bytes(), &resp)
			if otp, ok := resp["otp"].(string); ok {
				otpCodes = append(otpCodes, otp)
			}
		}
	}

	// Check for patterns in generated values
	t.Run("OTP codes should be unpredictable", func(t *testing.T) {
		if len(otpCodes) > 1 {
			// Check for sequential patterns
			for i := 1; i < len(otpCodes); i++ {
				require.NotEqual(t, otpCodes[i-1], otpCodes[i],
					"OTP codes should not be identical")

				// Simple sequential check
				if len(otpCodes[i]) == len(otpCodes[i-1]) {
					diff := 0
					for j := 0; j < len(otpCodes[i]); j++ {
						if otpCodes[i][j] != otpCodes[i-1][j] {
							diff++
						}
					}
					require.True(t, diff > 1,
						"OTP codes should differ by more than one character")
				}
			}
		}
	})

	// Test session ID randomness
	t.Run("Session IDs should be cryptographically random", func(t *testing.T) {
		for i := 0; i < 5; i++ {
			// Login to get session
			resp, err := suite.apiSuite.CreateTestUser(fmt.Sprintf("random-test-%d", i),
				fmt.Sprintf("+12345678%02d", i))
			require.NoError(t, err)

			if resp.AccessToken != "" {
				sessionIDs = append(sessionIDs, resp.AccessToken)
			}
		}

		// Check for uniqueness and sufficient length
		uniqueSessions := make(map[string]bool)
		for _, sid := range sessionIDs {
			require.True(t, len(sid) >= 32,
				"Session ID should be sufficiently long")
			uniqueSessions[sid] = true
		}
		require.Equal(t, len(sessionIDs), len(uniqueSessions),
			"All session IDs should be unique")
	})
}

// TestPasswordStorage tests secure password storage
func (suite *A02CryptographicFailuresTestSuite) TestPasswordStorage() {
	t := suite.T()

	// Test password requirements
	weakPasswords := []string{
		"password",
		"12345678",
		"qwerty",
		"admin",
		"letmein",
		"Password1", // Common pattern
		"",          // Empty
		"a",         // Too short
	}

	for _, pwd := range weakPasswords {
		t.Run(fmt.Sprintf("Reject weak password: %s", pwd), func(t *testing.T) {
			rec, err := suite.apiSuite.MakeUnauthenticatedRequest("POST",
				"/api/v1/authn/register",
				map[string]interface{}{
					"phone":    "+19999999999",
					"password": pwd,
					"name":     "Test User",
				})
			require.NoError(t, err)

			// Should reject weak passwords
			require.NotEqual(t, http.StatusOK, rec.Code,
				"Should reject weak password: %s", pwd)
		})
	}

	// Test timing attacks on password comparison
	t.Run("Password verification timing consistency", func(t *testing.T) {
		validPhone := "+11234567890"
		invalidPhone := "+19999999999"

		var validTimes []time.Duration
		var invalidTimes []time.Duration

		// Measure timing for valid vs invalid credentials
		for i := 0; i < 5; i++ {
			// Valid user
			start := time.Now()
			suite.apiSuite.MakeUnauthenticatedRequest("POST",
				"/api/v1/authn/login",
				map[string]string{
					"phone":    validPhone,
					"password": "wrongpassword",
				})
			validTimes = append(validTimes, time.Since(start))

			// Invalid user
			start = time.Now()
			suite.apiSuite.MakeUnauthenticatedRequest("POST",
				"/api/v1/authn/login",
				map[string]string{
					"phone":    invalidPhone,
					"password": "wrongpassword",
				})
			invalidTimes = append(invalidTimes, time.Since(start))
		}

		// Calculate average times
		var avgValid, avgInvalid time.Duration
		for i := range validTimes {
			avgValid += validTimes[i]
			avgInvalid += invalidTimes[i]
		}
		avgValid /= time.Duration(len(validTimes))
		avgInvalid /= time.Duration(len(invalidTimes))

		// Times should be similar to prevent user enumeration
		diff := avgValid - avgInvalid
		if diff < 0 {
			diff = -diff
		}

		// Allow up to 100ms difference
		require.True(t, diff < 100*time.Millisecond,
			"Timing difference too large: %v", diff)
	})
}

// TestDataInTransit tests encryption of data in transit
func (suite *A02CryptographicFailuresTestSuite) TestDataInTransit() {
	t := suite.T()

	// Test TLS configuration
	t.Run("Enforce strong TLS", func(t *testing.T) {
		// Skip if not using HTTPS
		if !strings.HasPrefix("http://localhost:8081", "https") {
			t.Skip("Test requires HTTPS endpoint")
		}

		// Test weak TLS versions
		weakTLSVersions := []uint16{
			tls.VersionSSL30,
			tls.VersionTLS10,
			tls.VersionTLS11,
		}

		for _, version := range weakTLSVersions {
			client := &http.Client{
				Transport: &http.Transport{
					TLSClientConfig: &tls.Config{
						MaxVersion: version,
						MinVersion: version,
					},
				},
			}

			_, err := client.Get("http://localhost:8081/api/v1/health")
			require.Error(t, err,
				"Should reject TLS version: %x", version)
		}
	})

	// Test for secure headers
	t.Run("Security headers present", func(t *testing.T) {
		rec, err := suite.apiSuite.MakeUnauthenticatedRequest("GET", "/api/v1/health", nil)
		require.NoError(t, err)

		// Check for security headers
		headers := map[string]string{
			"Strict-Transport-Security": "max-age=",
			"X-Content-Type-Options":    "nosniff",
			"X-Frame-Options":           "DENY",
			"Content-Security-Policy":   "",
		}

		for header, expected := range headers {
			value := rec.Header().Get(header)
			if expected != "" {
				require.Contains(t, value, expected,
					"Header %s should contain %s", header, expected)
			} else {
				require.NotEmpty(t, value,
					"Header %s should be present", header)
			}
		}
	})
}

// TestCryptographicKeyManagement tests key management security
func (suite *A02CryptographicFailuresTestSuite) TestCryptographicKeyManagement() {
	t := suite.T()

	// Test for hardcoded secrets in responses
	t.Run("No hardcoded secrets exposed", func(t *testing.T) {
		endpoints := []string{
			"/api/v1/health",
			"/api/v1/config",
			"/api/v1/info",
			"/api/v1/version",
		}

		secretPatterns := []string{
			"secret",
			"private",
			"key",
			"token",
			"password",
			"api_key",
			"apikey",
			"access_key",
			"secret_key",
		}

		for _, endpoint := range endpoints {
			rec, err := suite.apiSuite.MakeUnauthenticatedRequest("GET", endpoint, nil)
			require.NoError(t, err)

			body := strings.ToLower(rec.Body.String())
			for _, pattern := range secretPatterns {
				if strings.Contains(body, pattern) {
					// Check if it's actually exposing a value
					require.NotContains(t, body, pattern+"\":",
						"Potential secret exposed in %s", endpoint)
				}
			}
		}
	})

	// Test JWT secret strength
	t.Run("JWT uses strong secrets", func(t *testing.T) {
		// Try common/weak JWT secrets
		weakSecrets := []string{
			"secret",
			"supersecret",
			"password",
			"123456",
			"jwt-secret",
			"your-256-bit-secret",
			"your-secret-key",
		}

		for _, secret := range weakSecrets {
			// Create JWT with weak secret
			token := createJWTWithSecret(secret)

			req, err := http.NewRequest("GET",
				"http://localhost:8081/api/v1/users/me", nil)
			require.NoError(t, err)

			req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))

			client := &http.Client{}
			resp, err := client.Do(req)
			require.NoError(t, err)
			defer resp.Body.Close()

			require.Equal(t, http.StatusUnauthorized, resp.StatusCode,
				"Should reject JWT signed with weak secret: %s", secret)
		}
	})
}

// TestCachingOfSensitiveData tests that sensitive data isn't cached
func (suite *A02CryptographicFailuresTestSuite) TestCachingOfSensitiveData() {
	t := suite.T()

	sensitiveEndpoints := []string{
		"/api/v1/users/me",
		"/api/v1/authn/refresh",
		"/api/v1/organizations/*/billing",
		"/api/v1/users/*/sensitive",
	}

	for _, endpoint := range sensitiveEndpoints {
		t.Run(fmt.Sprintf("No caching for %s", endpoint), func(t *testing.T) {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"GET", endpoint, nil, suite.userToken,
			)
			require.NoError(t, err)

			// Check cache control headers
			cacheControl := rec.Header().Get("Cache-Control")
			require.Contains(t, cacheControl, "no-store",
				"Sensitive endpoint should have no-store cache control")
			require.Contains(t, cacheControl, "no-cache",
				"Sensitive endpoint should have no-cache directive")

			pragma := rec.Header().Get("Pragma")
			require.Equal(t, "no-cache", pragma,
				"Sensitive endpoint should have Pragma: no-cache")
		})
	}
}

// TestEncryptionAtRest tests for encryption of data at rest
func (suite *A02CryptographicFailuresTestSuite) TestEncryptionAtRest() {
	t := suite.T()

	// Test that sensitive fields are encrypted when stored
	t.Run("Sensitive data encryption indicators", func(t *testing.T) {
		// Create user with sensitive data
		testData := map[string]interface{}{
			"phone":       "+15555555555",
			"email":       "<EMAIL>",
			"ssn":         "***********",
			"credit_card": "****************",
		}

		rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"PATCH", "/api/v1/users/me", testData, suite.userToken,
		)
		require.NoError(t, err)

		// Get user data back
		rec, err = suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"GET", "/api/v1/users/me", nil, suite.userToken,
		)
		require.NoError(t, err)

		var userData map[string]interface{}
		json.Unmarshal(rec.Body.Bytes(), &userData)

		// Sensitive fields should be masked or encrypted
		if ssn, ok := userData["ssn"].(string); ok {
			require.True(t, strings.Contains(ssn, "*") || len(ssn) < 9,
				"SSN should be masked")
		}

		if cc, ok := userData["credit_card"].(string); ok {
			require.True(t, strings.Contains(cc, "*") || len(cc) < 16,
				"Credit card should be masked")
		}
	})
}

// Helper function to create JWT with specific secret
func createJWTWithSecret(secret string) string {
	// This is a simplified example - in real tests you'd use proper JWT library
	// header := `{"alg":"HS256","typ":"JWT"}`
	// payload := fmt.Sprintf(`{"sub":"test","exp":%d}`, time.Now().Add(time.Hour).Unix())

	// In real implementation, this would properly sign with the secret
	return "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ0ZXN0IiwiZXhwIjoxNjE2MjM5MDIyfQ.fake_signature"
}

func TestA02CryptographicFailuresTestSuite(t *testing.T) {
	suite.Run(t, new(A02CryptographicFailuresTestSuite))
}
