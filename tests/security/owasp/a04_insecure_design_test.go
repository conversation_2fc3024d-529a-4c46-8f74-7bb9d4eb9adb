package owasp

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	"Membership-SAAS-System-Backend/tests/integration"
)

// A04InsecureDesignTestSuite tests OWASP A04:2024 - Insecure Design
type A04InsecureDesignTestSuite struct {
	suite.Suite
	apiSuite  *integration.APITestSuite
	userToken string
	orgID     string
}

func (suite *A04InsecureDesignTestSuite) SetupSuite() {
	suite.apiSuite = integration.SetupAPITest(suite.T())

	// Create test user and org
	userResp, err := suite.apiSuite.CreateTestUser("design-test", "+1234567890")
	require.NoError(suite.T(), err)
	suite.userToken = userResp.AccessToken

	org, err := suite.apiSuite.CreateTestOrganization("test-design-org", suite.userToken)
	require.NoError(suite.T(), err)
	suite.orgID = org.ID
}

func (suite *A04InsecureDesignTestSuite) TearDownSuite() {
	suite.apiSuite.TeardownAPITest(suite.T())
}

// TestBusinessLogicFlaws tests for business logic vulnerabilities
func (suite *A04InsecureDesignTestSuite) TestBusinessLogicFlaws() {
	t := suite.T()

	// Test negative amount transfers
	t.Run("Negative amount validation", func(t *testing.T) {
		amounts := []interface{}{
			-100,
			-0.01,
			"-100",
			"0",
			0,
			-999999999,
		}

		for _, amount := range amounts {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"POST", "/api/v1/payments/transfer",
				map[string]interface{}{
					"amount":  amount,
					"to_user": "test-user-2",
				},
				suite.userToken,
			)
			require.NoError(t, err)

			// Should reject negative or zero amounts
			require.NotEqual(t, http.StatusOK, rec.Code,
				"Should reject amount: %v", amount)
		}
	})

	// Test race conditions in critical operations
	t.Run("Race condition prevention", func(t *testing.T) {
		var wg sync.WaitGroup
		results := make([]int, 10)

		// Try to create multiple resources simultaneously
		for i := 0; i < 10; i++ {
			wg.Add(1)
			go func(idx int) {
				defer wg.Done()

				rec, _ := suite.apiSuite.MakeAuthenticatedRequestWithToken(
					"POST", fmt.Sprintf("/api/v1/organizations/%s/events", suite.orgID),
					map[string]interface{}{
						"title": "Race Test Event",
						"seats": 1, // Only 1 seat available
					},
					suite.userToken,
				)
				results[idx] = rec.Code
			}(i)
		}

		wg.Wait()

		// Count successful creates
		successCount := 0
		for _, code := range results {
			if code == http.StatusCreated || code == http.StatusOK {
				successCount++
			}
		}

		// Should handle race conditions properly
		require.LessOrEqual(t, successCount, 1,
			"Race condition detected: multiple successful creates")
	})

	// Test workflow bypass attempts
	t.Run("Workflow bypass prevention", func(t *testing.T) {
		// Try to skip steps in multi-step processes

		// Skip OTP verification
		rec, err := suite.apiSuite.MakeUnauthenticatedRequest(
			"POST", "/api/v1/authn/phone/otp/verify",
			map[string]interface{}{
				"phone": "+19999999999",
				"otp":   "123456",
			},
		)
		require.NoError(t, err)
		require.NotEqual(t, http.StatusOK, rec.Code,
			"Should not verify OTP without initiation")

		// Try to access protected resource without proper flow
		rec, err = suite.apiSuite.MakeUnauthenticatedRequest(
			"POST", "/api/v1/password/reset/confirm",
			map[string]interface{}{
				"token":    "invalid-token",
				"password": "newpassword",
			},
		)
		require.NoError(t, err)
		require.NotEqual(t, http.StatusOK, rec.Code,
			"Should not allow password reset without valid token")
	})
}

// TestRateLimitingDesign tests rate limiting implementation
func (suite *A04InsecureDesignTestSuite) TestRateLimitingDesign() {
	t := suite.T()

	criticalEndpoints := []struct {
		name   string
		method string
		path   string
		body   interface{}
		limit  int
	}{
		{
			name:   "Login attempts",
			method: "POST",
			path:   "/api/v1/authn/login",
			body:   map[string]string{"phone": "+11234567890", "password": "wrong"},
			limit:  5,
		},
		{
			name:   "OTP requests",
			method: "POST",
			path:   "/api/v1/authn/phone/otp/initiate",
			body:   map[string]string{"phone": "+11234567890"},
			limit:  3,
		},
		{
			name:   "Password reset",
			method: "POST",
			path:   "/api/v1/password/reset",
			body:   map[string]string{"email": "<EMAIL>"},
			limit:  3,
		},
		{
			name:   "API key generation",
			method: "POST",
			path:   "/api/v1/api-keys",
			body:   map[string]string{"name": "test-key"},
			limit:  10,
		},
	}

	for _, endpoint := range criticalEndpoints {
		t.Run(endpoint.name, func(t *testing.T) {
			hitLimit := false

			// Make requests until we hit rate limit
			for i := 0; i < endpoint.limit*2; i++ {
				var rec *httptest.ResponseRecorder
				var err error

				if strings.Contains(endpoint.path, "authn") {
					rec, err = suite.apiSuite.MakeUnauthenticatedRequest(
						endpoint.method, endpoint.path, endpoint.body,
					)
				} else {
					rec, err = suite.apiSuite.MakeAuthenticatedRequestWithToken(
						endpoint.method, endpoint.path, endpoint.body, suite.userToken,
					)
				}
				require.NoError(t, err)

				// Check if we hit rate limit
				if rec.Code == http.StatusTooManyRequests {
					hitLimit = true

					// Check for Retry-After header
					retryAfter := rec.Header().Get("Retry-After")
					require.NotEmpty(t, retryAfter,
						"Rate limit response should include Retry-After header")
					break
				}
			}

			require.True(t, hitLimit,
				"Should enforce rate limiting on %s", endpoint.name)
		})
	}
}

// TestInputValidationDesign tests comprehensive input validation
func (suite *A04InsecureDesignTestSuite) TestInputValidationDesign() {
	t := suite.T()

	// Test missing required field validation
	t.Run("Required field validation", func(t *testing.T) {
		testCases := []struct {
			endpoint string
			body     map[string]interface{}
			missing  string
		}{
			{
				endpoint: "/api/v1/organizations",
				body:     map[string]interface{}{"description": "Test"},
				missing:  "name",
			},
			{
				endpoint: fmt.Sprintf("/api/v1/organizations/%s/events", suite.orgID),
				body:     map[string]interface{}{"description": "Test"},
				missing:  "title",
			},
			{
				endpoint: "/api/v1/authn/register",
				body:     map[string]interface{}{"name": "Test"},
				missing:  "phone",
			},
		}

		for _, tc := range testCases {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"POST", tc.endpoint, tc.body, suite.userToken,
			)
			require.NoError(t, err)

			require.Equal(t, http.StatusBadRequest, rec.Code,
				"Should require field: %s", tc.missing)

			// Error should mention missing field
			body := rec.Body.String()
			require.True(t,
				strings.Contains(body, tc.missing) || strings.Contains(body, "required"),
				"Error should indicate missing required field")
		}
	})

	// Test data type validation
	t.Run("Data type validation", func(t *testing.T) {
		invalidTypes := []struct {
			field string
			value interface{}
		}{
			{"age", "not-a-number"},
			{"count", "abc"},
			{"date", "invalid-date"},
			{"email", 12345},
			{"active", "yes"}, // Should be boolean
			{"tags", "not-an-array"},
		}

		for _, tc := range invalidTypes {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"PATCH", "/api/v1/users/me",
				map[string]interface{}{tc.field: tc.value},
				suite.userToken,
			)
			require.NoError(t, err)

			// Should reject invalid types
			require.NotEqual(t, http.StatusOK, rec.Code,
				"Should reject invalid type for %s: %v", tc.field, tc.value)
		}
	})
}

// TestAuthorizationDesignFlaws tests authorization design issues
func (suite *A04InsecureDesignTestSuite) TestAuthorizationDesignFlaws() {
	t := suite.T()

	// Test missing authorization checks
	t.Run("Default deny authorization", func(t *testing.T) {
		// New/unknown endpoints should default to deny
		unknownEndpoints := []string{
			"/api/v1/internal/debug",
			"/api/v1/admin/system",
			"/api/v1/private/data",
			"/api/v2/users", // Version that doesn't exist
		}

		for _, endpoint := range unknownEndpoints {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"GET", endpoint, nil, suite.userToken,
			)
			require.NoError(t, err)

			// Should deny by default
			require.True(t,
				rec.Code == http.StatusNotFound || rec.Code == http.StatusForbidden,
				"Unknown endpoint should be denied: %s got %d", endpoint, rec.Code)
		}
	})

	// Test authorization caching issues
	t.Run("Authorization cache invalidation", func(t *testing.T) {
		// Create a resource
		eventReq := map[string]interface{}{
			"title":       "Test Event",
			"description": "Test event for cache invalidation",
			"start_time":  time.Now().Add(24 * time.Hour).Format(time.RFC3339),
			"end_time":    time.Now().Add(25 * time.Hour).Format(time.RFC3339),
			"location":    "Test Location",
		}
		rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"POST", fmt.Sprintf("/api/v1/organizations/%s/events", suite.orgID), eventReq, suite.userToken,
		)
		require.NoError(t, err)
		require.Equal(t, http.StatusCreated, rec.Code)

		var event struct {
			ID string `json:"id"`
		}
		err = json.Unmarshal(rec.Body.Bytes(), &event)
		require.NoError(t, err)

		// Verify access
		rec, err = suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"GET", fmt.Sprintf("/api/v1/events/%s", event.ID), nil, suite.userToken,
		)
		require.NoError(t, err)
		require.Equal(t, http.StatusOK, rec.Code)

		// Remove user from organization (simulation)
		// In real test, would remove user's org membership

		// Access should be denied immediately (no stale cache)
		// This tests that authorization isn't improperly cached
	})
}

// TestAPIAbuseProtection tests protection against API abuse
func (suite *A04InsecureDesignTestSuite) TestAPIAbuseProtection() {
	t := suite.T()

	// Test resource exhaustion protection
	t.Run("Resource exhaustion protection", func(t *testing.T) {
		// Try to create excessive resources
		created := 0
		maxAttempts := 1000

		for i := 0; i < maxAttempts; i++ {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"POST", "/api/v1/organizations",
				map[string]interface{}{
					"name": fmt.Sprintf("spam-org-%d", i),
				},
				suite.userToken,
			)
			require.NoError(t, err)

			if rec.Code == http.StatusCreated || rec.Code == http.StatusOK {
				created++
			} else if rec.Code == http.StatusTooManyRequests || rec.Code == http.StatusForbidden {
				// Hit limit - good
				break
			}
		}

		// Should have limits to prevent abuse
		require.Less(t, created, 100,
			"Should limit resource creation to prevent abuse")
	})

	// Test expensive operation protection
	t.Run("Expensive operation protection", func(t *testing.T) {
		// Operations that are computationally expensive
		expensiveOps := []struct {
			name   string
			method string
			path   string
			body   interface{}
		}{
			{
				name:   "Large export",
				method: "POST",
				path:   "/api/v1/export/all",
				body:   map[string]interface{}{"format": "csv", "include_all": true},
			},
			{
				name:   "Complex search",
				method: "POST",
				path:   "/api/v1/search/advanced",
				body: map[string]interface{}{
					"query": strings.Repeat("test ", 1000),
					"deep":  true,
				},
			},
			{
				name:   "Bulk operation",
				method: "POST",
				path:   "/api/v1/bulk/process",
				body: map[string]interface{}{
					"ids": make([]string, 10000),
				},
			},
		}

		for _, op := range expensiveOps {
			t.Run(op.name, func(t *testing.T) {
				// Should have protection against expensive operations
				rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
					op.method, op.path, op.body, suite.userToken,
				)
				require.NoError(t, err)

				// Should either limit, require special permission, or queue
				require.True(t,
					rec.Code == http.StatusTooManyRequests ||
						rec.Code == http.StatusForbidden ||
						rec.Code == http.StatusAccepted, // Queued for processing
					"Should protect against expensive operation: %s", op.name)
			})
		}
	})
}

// TestSecurityMisconfigurationByDesign tests design-level security configurations
func (suite *A04InsecureDesignTestSuite) TestSecurityMisconfigurationByDesign() {
	t := suite.T()

	// Test default security settings
	t.Run("Secure defaults", func(t *testing.T) {
		// Check that security features are enabled by default
		rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"GET", "/api/v1/users/me/settings", nil, suite.userToken,
		)
		require.NoError(t, err)

		if rec.Code == http.StatusOK {
			var settings map[string]interface{}
			json.Unmarshal(rec.Body.Bytes(), &settings)

			// Check secure defaults
			secureDefaults := map[string]interface{}{
				"two_factor_enabled": false, // Should encourage but not force
				"session_timeout":    3600,  // Reasonable timeout
				"api_rate_limit":     1000,  // Default rate limit
			}

			for key, expected := range secureDefaults {
				if actual, exists := settings[key]; exists {
					require.Equal(t, expected, actual,
						"Insecure default for %s", key)
				}
			}
		}
	})

	// Test fail-safe vs fail-open
	t.Run("Fail-safe design", func(t *testing.T) {
		// Simulate authorization service failure
		// System should fail closed (deny access) not open

		// This would require mocking internal services
		// but we can test the principle with malformed requests

		malformedAuth := []string{
			"Bearer",                 // Missing token
			"InvalidScheme abc123",   // Wrong scheme
			"Bearer corrupted.token", // Malformed token
			"",                       // Empty
		}

		for _, auth := range malformedAuth {
			req, err := http.NewRequest("GET",
				"http://localhost:8081/api/v1/users/me", nil)
			require.NoError(t, err)

			if auth != "" {
				req.Header.Set("Authorization", auth)
			}

			client := &http.Client{}
			resp, err := client.Do(req)
			require.NoError(t, err)
			defer resp.Body.Close()

			// Should fail closed (deny access)
			require.Equal(t, http.StatusUnauthorized, resp.StatusCode,
				"Should deny access with malformed auth: %s", auth)
		}
	})
}

// TestTrustBoundaryViolations tests trust boundary issues
func (suite *A04InsecureDesignTestSuite) TestTrustBoundaryViolations() {
	t := suite.T()

	// Test client-side security reliance
	t.Run("Server-side validation enforcement", func(t *testing.T) {
		// Even if client claims validation passed, server must validate
		rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"POST", fmt.Sprintf("/api/v1/organizations/%s/events", suite.orgID),
			map[string]interface{}{
				"title":            "Test",
				"client_validated": true, // Client claims it validated
				"skip_validation":  true, // Try to skip validation
				"admin_override":   true, // Try to use admin powers
				"max_attendees":    -1,   // Invalid value
			},
			suite.userToken,
		)
		require.NoError(t, err)

		// Server should still validate
		require.NotEqual(t, http.StatusOK, rec.Code,
			"Server must validate regardless of client claims")
	})

	// Test internal API exposure
	t.Run("Internal API protection", func(t *testing.T) {
		internalAPIs := []string{
			"/internal/api/v1/users",
			"/private/api/v1/admin",
			"/api/internal/v1/system",
			"/api/v1/internal/metrics",
		}

		for _, api := range internalAPIs {
			// Even with valid token, internal APIs should be protected
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"GET", api, nil, suite.userToken,
			)
			require.NoError(t, err)

			require.True(t,
				rec.Code == http.StatusNotFound || rec.Code == http.StatusForbidden,
				"Internal API should not be exposed: %s", api)
		}
	})
}

// TestCryptographicAgility tests ability to upgrade crypto
func (suite *A04InsecureDesignTestSuite) TestCryptographicAgility() {
	t := suite.T()

	// Test algorithm version in tokens
	t.Run("Algorithm versioning", func(t *testing.T) {
		// Check that tokens include algorithm version
		rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"POST", "/api/v1/authn/refresh", nil, suite.userToken,
		)
		require.NoError(t, err)

		if rec.Code == http.StatusOK {
			var resp map[string]interface{}
			json.Unmarshal(rec.Body.Bytes(), &resp)

			// New tokens should use strong algorithms
			if token, ok := resp["access_token"].(string); ok {
				// Parse JWT header (simplified)
				parts := strings.Split(token, ".")
				require.Len(t, parts, 3, "Invalid JWT format")

				// In real test, would decode and check algorithm
				// Here we just verify structure exists for upgrade path
			}
		}
	})
}

// TestDataPrivacyByDesign tests privacy-first design
func (suite *A04InsecureDesignTestSuite) TestDataPrivacyByDesign() {
	t := suite.T()

	// Test data minimization
	t.Run("Data minimization", func(t *testing.T) {
		// Public endpoints should not expose unnecessary data
		rec, err := suite.apiSuite.MakeUnauthenticatedRequest(
			"GET", "/api/v1/organizations/public", nil,
		)
		require.NoError(t, err)

		if rec.Code == http.StatusOK {
			var orgs []map[string]interface{}
			json.Unmarshal(rec.Body.Bytes(), &orgs)

			// Check that private data is not exposed
			for _, org := range orgs {
				require.Nil(t, org["internal_notes"])
				require.Nil(t, org["billing_info"])
				require.Nil(t, org["member_emails"])
				require.Nil(t, org["created_by_ip"])
			}
		}
	})

	// Test audit trail design
	t.Run("Audit trail without PII", func(t *testing.T) {
		// Audit logs should exist but not expose full PII
		rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"GET", "/api/v1/audit/logs", nil, suite.userToken,
		)
		require.NoError(t, err)

		if rec.Code == http.StatusOK {
			var logs []map[string]interface{}
			json.Unmarshal(rec.Body.Bytes(), &logs)

			for _, log := range logs {
				// Should have anonymized/hashed identifiers
				if phone, ok := log["phone"].(string); ok {
					require.True(t,
						strings.Contains(phone, "*") || len(phone) < 10,
						"Phone should be masked in audit logs")
				}

				if email, ok := log["email"].(string); ok {
					require.True(t,
						strings.Contains(email, "*") || !strings.Contains(email, "@"),
						"Email should be masked in audit logs")
				}
			}
		}
	})
}

// TestSecurityPatternImplementation tests security patterns
func (suite *A04InsecureDesignTestSuite) TestSecurityPatternImplementation() {
	t := suite.T()

	// Test defense in depth
	t.Run("Defense in depth", func(t *testing.T) {
		// Multiple security layers should be present

		// Layer 1: Authentication required
		rec, err := suite.apiSuite.MakeUnauthenticatedRequest(
			"GET", fmt.Sprintf("/api/v1/organizations/%s", suite.orgID), nil,
		)
		require.NoError(t, err)
		require.Equal(t, http.StatusUnauthorized, rec.Code)

		// Layer 2: Authorization even with valid token
		// Would need second user token without access to test

		// Layer 3: Rate limiting even for authorized users
		for i := 0; i < 100; i++ {
			suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"GET", fmt.Sprintf("/api/v1/organizations/%s", suite.orgID),
				nil, suite.userToken,
			)
		}
		// Should eventually hit rate limit
	})

	// Test least privilege principle
	t.Run("Least privilege", func(t *testing.T) {
		// Regular users shouldn't have admin capabilities
		adminOnlyOps := []struct {
			method string
			path   string
		}{
			{"GET", "/api/v1/admin/users"},
			{"POST", "/api/v1/admin/broadcast"},
			{"DELETE", "/api/v1/admin/cache/clear"},
			{"PATCH", "/api/v1/admin/settings"},
		}

		for _, op := range adminOnlyOps {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				op.method, op.path, nil, suite.userToken,
			)
			require.NoError(t, err)

			require.True(t,
				rec.Code == http.StatusForbidden || rec.Code == http.StatusNotFound,
				"Regular user should not access admin operation: %s %s", op.method, op.path)
		}
	})
}

func TestA04InsecureDesignTestSuite(t *testing.T) {
	suite.Run(t, new(A04InsecureDesignTestSuite))
}
