package owasp

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	"Membership-SAAS-System-Backend/tests/integration"
)

// A05SecurityMisconfigurationTestSuite tests OWASP A05:2024 - Security Misconfiguration
type A05SecurityMisconfigurationTestSuite struct {
	suite.Suite
	apiSuite  *integration.APITestSuite
	userToken string
}

func (suite *A05SecurityMisconfigurationTestSuite) SetupSuite() {
	suite.apiSuite = integration.SetupAPITest(suite.T())

	// Create test user
	userResp, err := suite.apiSuite.CreateTestUser("config-test", "+**********")
	require.NoError(suite.T(), err)
	suite.userToken = userResp.AccessToken
}

func (suite *A05SecurityMisconfigurationTestSuite) TearDownSuite() {
	suite.apiSuite.TeardownAPITest(suite.T())
}

// TestDefaultCredentials tests for default credentials
func (suite *A05SecurityMisconfigurationTestSuite) TestDefaultCredentials() {
	t := suite.T()

	defaultCreds := []struct {
		username string
		password string
	}{
		{"admin", "admin"},
		{"admin", "password"},
		{"admin", "123456"},
		{"administrator", "administrator"},
		{"root", "root"},
		{"root", "toor"},
		{"test", "test"},
		{"demo", "demo"},
		{"guest", "guest"},
		{"user", "user"},
		{"postgres", "postgres"},
		{"mysql", "mysql"},
		{"sa", "sa"},
		{"admin", "admin123"},
		{"admin", "Admin@123"},
	}

	for _, cred := range defaultCreds {
		t.Run(fmt.Sprintf("Default creds: %s/%s", cred.username, cred.password), func(t *testing.T) {
			// Test various login endpoints
			endpoints := []string{
				"/api/v1/authn/login",
				"/api/v1/authn/staff/login",
				"/api/v1/admin/login",
			}

			for _, endpoint := range endpoints {
				rec, err := suite.apiSuite.MakeUnauthenticatedRequest(
					"POST", endpoint,
					map[string]string{
						"username": cred.username,
						"password": cred.password,
					},
				)
				require.NoError(t, err)

				// Should not accept default credentials
				require.NotEqual(t, http.StatusOK, rec.Code,
					"Default credentials should not work: %s/%s on %s",
					cred.username, cred.password, endpoint)
			}
		})
	}
}

// TestUnnecessaryFeaturesEnabled tests for unnecessary features
func (suite *A05SecurityMisconfigurationTestSuite) TestUnnecessaryFeaturesEnabled() {
	t := suite.T()

	// Test for exposed debug endpoints
	t.Run("Debug endpoints disabled", func(t *testing.T) {
		debugEndpoints := []string{
			"/debug/pprof/",
			"/debug/vars",
			"/api/v1/debug",
			"/api/debug",
			"/_debug",
			"/test",
			"/api/test",
			"/phpinfo.php",
			"/info.php",
			"/adminer.php",
		}

		for _, endpoint := range debugEndpoints {
			rec, err := suite.apiSuite.MakeUnauthenticatedRequest("GET", endpoint, nil)
			require.NoError(t, err)

			require.True(t,
				rec.Code == http.StatusNotFound || rec.Code == http.StatusForbidden,
				"Debug endpoint should not be accessible: %s got %d", endpoint, rec.Code)

			// Even with auth
			rec, err = suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"GET", endpoint, nil, suite.userToken,
			)
			require.NoError(t, err)

			require.True(t,
				rec.Code == http.StatusNotFound || rec.Code == http.StatusForbidden,
				"Debug endpoint should not be accessible with auth: %s", endpoint)
		}
	})

	// Test for directory listing
	t.Run("Directory listing disabled", func(t *testing.T) {
		directories := []string{
			"/",
			"/api/",
			"/api/v1/",
			"/assets/",
			"/static/",
			"/uploads/",
			"/files/",
			"/images/",
			"/css/",
			"/js/",
		}

		for _, dir := range directories {
			rec, err := suite.apiSuite.MakeUnauthenticatedRequest("GET", dir, nil)
			require.NoError(t, err)

			// Should not show directory listing
			body := rec.Body.String()
			require.NotContains(t, body, "Index of")
			require.NotContains(t, body, "Directory listing")
			require.NotContains(t, body, "<pre>")
			require.NotContains(t, body, "Parent Directory")
		}
	})
}

// TestSecurityHeaders tests for proper security headers
func (suite *A05SecurityMisconfigurationTestSuite) TestSecurityHeaders() {
	t := suite.T()

	requiredHeaders := map[string][]string{
		"X-Content-Type-Options":    {"nosniff"},
		"X-Frame-Options":           {"DENY", "SAMEORIGIN"},
		"X-XSS-Protection":          {"1; mode=block"},
		"Strict-Transport-Security": {"max-age="},
		"Content-Security-Policy":   {"default-src"},
		"Referrer-Policy":           {"no-referrer", "strict-origin"},
		"Permissions-Policy":        {"geolocation=()"},
	}

	// Test both authenticated and unauthenticated requests
	endpoints := []struct {
		method string
		path   string
		auth   bool
	}{
		{"GET", "/api/v1/health", false},
		{"GET", "/api/v1/users/me", true},
		{"GET", "/api/v1/organizations", true},
		{"POST", "/api/v1/authn/login", false},
	}

	for _, endpoint := range endpoints {
		t.Run(fmt.Sprintf("%s %s", endpoint.method, endpoint.path), func(t *testing.T) {
			var rec *httptest.ResponseRecorder
			var err error

			if endpoint.auth {
				rec, err = suite.apiSuite.MakeAuthenticatedRequestWithToken(
					endpoint.method, endpoint.path, nil, suite.userToken,
				)
			} else {
				rec, err = suite.apiSuite.MakeUnauthenticatedRequest(
					endpoint.method, endpoint.path, nil,
				)
			}
			require.NoError(t, err)

			// Check security headers
			for header, validValues := range requiredHeaders {
				value := rec.Header().Get(header)

				if header == "Strict-Transport-Security" && !strings.HasPrefix("http://localhost:8081", "https") {
					// Skip HSTS check for non-HTTPS
					continue
				}

				// Header should be present
				require.NotEmpty(t, value,
					"Security header %s should be present", header)

				// Check if value is valid
				valid := false
				for _, validValue := range validValues {
					if strings.Contains(value, validValue) {
						valid = true
						break
					}
				}
				require.True(t, valid,
					"Security header %s has invalid value: %s", header, value)
			}
		})
	}
}

// TestErrorHandling tests error message information disclosure
func (suite *A05SecurityMisconfigurationTestSuite) TestErrorHandling() {
	t := suite.T()

	// Test various error conditions
	errorTests := []struct {
		name   string
		method string
		path   string
		body   interface{}
		check  func(t *testing.T, body string)
	}{
		{
			name:   "Database error exposure",
			method: "GET",
			path:   "/api/v1/users/invalid-uuid-format",
			check: func(t *testing.T, body string) {
				// Should not expose database errors
				require.NotContains(t, strings.ToLower(body), "sql")
				require.NotContains(t, strings.ToLower(body), "postgres")
				require.NotContains(t, strings.ToLower(body), "mysql")
				require.NotContains(t, strings.ToLower(body), "database")
				require.NotContains(t, strings.ToLower(body), "syntax error")
			},
		},
		{
			name:   "Stack trace exposure",
			method: "POST",
			path:   "/api/v1/cause-error",
			body:   map[string]interface{}{"trigger": "error"},
			check: func(t *testing.T, body string) {
				// Should not expose stack traces
				require.NotContains(t, body, "at ")
				require.NotContains(t, body, "stack")
				require.NotContains(t, body, "trace")
				require.NotContains(t, body, ".go:")
				require.NotContains(t, body, "goroutine")
				require.NotContains(t, body, "panic")
			},
		},
		{
			name:   "File path exposure",
			method: "GET",
			path:   "/api/v1/files/../../etc/passwd",
			check: func(t *testing.T, body string) {
				// Should not expose file paths
				require.NotContains(t, body, "/home/")
				require.NotContains(t, body, "/usr/")
				require.NotContains(t, body, "/var/")
				require.NotContains(t, body, "C:\\")
				require.NotContains(t, body, "/etc/")
			},
		},
		{
			name:   "Internal service exposure",
			method: "GET",
			path:   "/api/v1/internal-error",
			check: func(t *testing.T, body string) {
				// Should not expose internal service names
				require.NotContains(t, strings.ToLower(body), "redis")
				require.NotContains(t, strings.ToLower(body), "elasticsearch")
				require.NotContains(t, strings.ToLower(body), "rabbitmq")
				require.NotContains(t, strings.ToLower(body), "kafka")
				require.NotContains(t, strings.ToLower(body), "connection refused")
			},
		},
	}

	for _, test := range errorTests {
		t.Run(test.name, func(t *testing.T) {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				test.method, test.path, test.body, suite.userToken,
			)
			require.NoError(t, err)

			body := rec.Body.String()
			test.check(t, body)
		})
	}
}

// TestCORSConfiguration tests CORS settings
func (suite *A05SecurityMisconfigurationTestSuite) TestCORSConfiguration() {
	t := suite.T()

	// Test various origin headers
	origins := []struct {
		origin          string
		shouldBeAllowed bool
	}{
		{"https://evil.com", false},
		{"http://localhost:3000", true}, // May be allowed for dev
		{"null", false},
		{"file://", false},
		{"*", false},
		{"https://attacker.com", false},
	}

	for _, o := range origins {
		t.Run(fmt.Sprintf("CORS origin: %s", o.origin), func(t *testing.T) {
			req, err := http.NewRequest("OPTIONS",
				fmt.Sprintf("%s/api/v1/users/me", "http://localhost:8081"), nil)
			require.NoError(t, err)

			req.Header.Set("Origin", o.origin)
			req.Header.Set("Access-Control-Request-Method", "GET")
			req.Header.Set("Access-Control-Request-Headers", "Authorization")

			client := &http.Client{}
			resp, err := client.Do(req)
			require.NoError(t, err)
			defer resp.Body.Close()

			allowOrigin := resp.Header.Get("Access-Control-Allow-Origin")
			allowCredentials := resp.Header.Get("Access-Control-Allow-Credentials")

			if !o.shouldBeAllowed {
				// Should not allow untrusted origins
				require.NotEqual(t, o.origin, allowOrigin,
					"Should not allow origin: %s", o.origin)

				// Especially not with credentials
				if allowOrigin == o.origin {
					require.NotEqual(t, "true", allowCredentials,
						"Should not allow credentials with untrusted origin")
				}
			}

			// Should never allow wildcard with credentials
			if allowOrigin == "*" {
				require.NotEqual(t, "true", allowCredentials,
					"Should not allow credentials with wildcard origin")
			}
		})
	}
}

// TestHTTPMethodsConfiguration tests HTTP method restrictions
func (suite *A05SecurityMisconfigurationTestSuite) TestHTTPMethodsConfiguration() {
	t := suite.T()

	// Test dangerous HTTP methods
	dangerousMethods := []string{
		"TRACE",
		"TRACK",
		"CONNECT",
		"PROPFIND",
		"PROPPATCH",
		"MKCOL",
		"COPY",
		"MOVE",
		"LOCK",
		"UNLOCK",
	}

	endpoints := []string{
		"/api/v1/health",
		"/api/v1/users/me",
		"/api/v1/organizations",
	}

	for _, method := range dangerousMethods {
		for _, endpoint := range endpoints {
			t.Run(fmt.Sprintf("%s %s", method, endpoint), func(t *testing.T) {
				req, err := http.NewRequest(method,
					fmt.Sprintf("%s%s", "http://localhost:8081", endpoint), nil)
				require.NoError(t, err)

				if endpoint != "/api/v1/health" {
					req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", suite.userToken))
				}

				client := &http.Client{}
				resp, err := client.Do(req)
				require.NoError(t, err)
				defer resp.Body.Close()

				// Should not allow dangerous methods
				require.True(t,
					resp.StatusCode == http.StatusMethodNotAllowed ||
						resp.StatusCode == http.StatusNotFound ||
						resp.StatusCode == http.StatusForbidden,
					"Should not allow method %s, got %d", method, resp.StatusCode)
			})
		}
	}
}

// TestSessionConfiguration tests session security settings
func (suite *A05SecurityMisconfigurationTestSuite) TestSessionConfiguration() {
	t := suite.T()

	// Test session cookie configuration
	t.Run("Session cookie security", func(t *testing.T) {
		// Login to get session cookie
		rec, err := suite.apiSuite.MakeUnauthenticatedRequest(
			"POST", "/api/v1/authn/login",
			map[string]string{
				"phone":    "+**********",
				"password": "testpassword",
			},
		)
		require.NoError(t, err)

		// Check cookie settings
		cookies := rec.Result().Cookies()
		for _, cookie := range cookies {
			if strings.Contains(strings.ToLower(cookie.Name), "session") ||
				strings.Contains(strings.ToLower(cookie.Name), "auth") {
				// Security flags
				require.True(t, cookie.HttpOnly,
					"Session cookie should have HttpOnly flag")

				if strings.HasPrefix("http://localhost:8081", "https") {
					require.True(t, cookie.Secure,
						"Session cookie should have Secure flag on HTTPS")
				}

				// SameSite protection
				require.NotEqual(t, http.SameSiteNoneMode, cookie.SameSite,
					"Session cookie should not have SameSite=None")
			}
		}
	})

	// Test session timeout
	t.Run("Session timeout configuration", func(t *testing.T) {
		// Get user profile to check session
		rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"GET", "/api/v1/users/me", nil, suite.userToken,
		)
		require.NoError(t, err)

		if rec.Code == http.StatusOK {
			// Check for session timeout headers or response
			var resp map[string]interface{}
			json.Unmarshal(rec.Body.Bytes(), &resp)

			// Session should have reasonable timeout
			if timeout, ok := resp["session_timeout"].(float64); ok {
				require.True(t, timeout > 0 && timeout <= 86400,
					"Session timeout should be reasonable: %v", timeout)
			}
		}
	})
}

// TestDatabaseConfiguration tests database security settings
func (suite *A05SecurityMisconfigurationTestSuite) TestDatabaseConfiguration() {
	t := suite.T()

	// Test for SQL injection through various techniques
	t.Run("Parameterized queries enforcement", func(t *testing.T) {
		// This is tested more thoroughly in A03, but we check configuration
		sqlTests := []string{
			"admin'--",
			"1 OR 1=1",
			"'; DROP TABLE users; --",
		}

		for _, sql := range sqlTests {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"GET", fmt.Sprintf("/api/v1/users/search?q=%s", sql), nil, suite.userToken,
			)
			require.NoError(t, err)

			// Should handle safely
			require.NotEqual(t, http.StatusInternalServerError, rec.Code)

			// No SQL errors exposed
			body := strings.ToLower(rec.Body.String())
			require.NotContains(t, body, "sql")
			require.NotContains(t, body, "syntax")
		}
	})
}

// TestAPIVersioning tests API versioning configuration
func (suite *A05SecurityMisconfigurationTestSuite) TestAPIVersioning() {
	t := suite.T()

	// Test deprecated API versions
	t.Run("Deprecated API versions disabled", func(t *testing.T) {
		deprecatedVersions := []string{
			"/api/v0",
			"/api/beta",
			"/api/alpha",
			"/v0",
			"/beta",
		}

		for _, version := range deprecatedVersions {
			rec, err := suite.apiSuite.MakeUnauthenticatedRequest(
				"GET", fmt.Sprintf("%s/health", version), nil,
			)
			require.NoError(t, err)

			require.Equal(t, http.StatusNotFound, rec.Code,
				"Deprecated API version should not be accessible: %s", version)
		}
	})

	// Test version disclosure
	t.Run("Version information disclosure", func(t *testing.T) {
		rec, err := suite.apiSuite.MakeUnauthenticatedRequest(
			"GET", "/api/v1/version", nil,
		)
		require.NoError(t, err)

		if rec.Code == http.StatusOK {
			var version map[string]interface{}
			json.Unmarshal(rec.Body.Bytes(), &version)

			// Should not expose sensitive version details
			sensitiveKeys := []string{
				"git_commit",
				"build_host",
				"compiler_version",
				"internal_version",
				"debug_mode",
			}

			for _, key := range sensitiveKeys {
				require.Nil(t, version[key],
					"Should not expose sensitive version info: %s", key)
			}
		}
	})
}

// TestLoggingConfiguration tests logging security
func (suite *A05SecurityMisconfigurationTestSuite) TestLoggingConfiguration() {
	t := suite.T()

	// Test that sensitive data is not logged
	t.Run("Sensitive data not in logs", func(t *testing.T) {
		// Perform operations with sensitive data
		sensitiveData := map[string]interface{}{
			"password":    "mysecretpassword",
			"credit_card": "****************",
			"ssn":         "***********",
			"api_key":     "secret_key_12345",
		}

		rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"PATCH", "/api/v1/users/me", sensitiveData, suite.userToken,
		)
		require.NoError(t, err)

		// We can't directly check logs, but we can verify
		// the response doesn't accidentally include sensitive data
		body := rec.Body.String()
		require.NotContains(t, body, "mysecretpassword")
		require.NotContains(t, body, "****************")
		require.NotContains(t, body, "***********")
		require.NotContains(t, body, "secret_key_12345")
	})
}

// TestFileUploadConfiguration tests file upload security settings
func (suite *A05SecurityMisconfigurationTestSuite) TestFileUploadConfiguration() {
	t := suite.T()

	// Test file type restrictions
	t.Run("Dangerous file types blocked", func(t *testing.T) {
		dangerousExtensions := []string{
			".exe", ".bat", ".cmd", ".com", ".pif", ".scr",
			".vbs", ".js", ".jar", ".zip", ".rar",
			".sh", ".bash", ".ps1", ".psm1",
			".asp", ".aspx", ".php", ".jsp",
			".htaccess", ".htpasswd",
		}

		for _, ext := range dangerousExtensions {
			filename := fmt.Sprintf("test%s", ext)

			// Attempt file upload (mock)
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"POST", "/api/v1/files/upload",
				map[string]interface{}{
					"filename": filename,
					"content":  "malicious content",
				},
				suite.userToken,
			)
			require.NoError(t, err)

			// Should reject dangerous file types
			require.NotEqual(t, http.StatusOK, rec.Code,
				"Should reject file type: %s", ext)
		}
	})

	// Test file size limits
	t.Run("File size limits enforced", func(t *testing.T) {
		// Try to upload oversized file
		largeContent := strings.Repeat("A", 100*1024*1024) // 100MB

		rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"POST", "/api/v1/files/upload",
			map[string]interface{}{
				"filename": "large.txt",
				"content":  largeContent,
			},
			suite.userToken,
		)
		require.NoError(t, err)

		// Should reject oversized files
		require.True(t,
			rec.Code == http.StatusRequestEntityTooLarge ||
				rec.Code == http.StatusBadRequest,
			"Should reject oversized files, got %d", rec.Code)
	})
}

// TestServerInformationDisclosure tests server info leakage
func (suite *A05SecurityMisconfigurationTestSuite) TestServerInformationDisclosure() {
	t := suite.T()

	// Check response headers for information disclosure
	t.Run("Server headers disclosure", func(t *testing.T) {
		rec, err := suite.apiSuite.MakeUnauthenticatedRequest(
			"GET", "/api/v1/health", nil,
		)
		require.NoError(t, err)

		// Headers that should not be present or should be generic
		sensitiveHeaders := map[string]func(string) bool{
			"Server": func(value string) bool {
				// Should not expose specific version
				return !strings.Contains(strings.ToLower(value), "apache") &&
					!strings.Contains(strings.ToLower(value), "nginx") &&
					!strings.Contains(strings.ToLower(value), "iis") ||
					!strings.Contains(value, "/")
			},
			"X-Powered-By": func(value string) bool {
				// Should not be present
				return value == ""
			},
			"X-AspNet-Version": func(value string) bool {
				return value == ""
			},
			"X-AspNetMvc-Version": func(value string) bool {
				return value == ""
			},
			"X-Drupal-Cache": func(value string) bool {
				return value == ""
			},
		}

		for header, check := range sensitiveHeaders {
			value := rec.Header().Get(header)
			require.True(t, check(value),
				"Header %s exposes information: %s", header, value)
		}
	})
}

func TestA05SecurityMisconfigurationTestSuite(t *testing.T) {
	suite.Run(t, new(A05SecurityMisconfigurationTestSuite))
}
