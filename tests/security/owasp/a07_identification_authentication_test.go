package owasp

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	"Membership-SAAS-System-Backend/tests/integration"
)

// A07IdentificationAuthenticationTestSuite tests OWASP A07:2024 - Identification and Authentication Failures
type A07IdentificationAuthenticationTestSuite struct {
	suite.Suite
	apiSuite  *integration.APITestSuite
	userToken string
	testPhone string
}

func (suite *A07IdentificationAuthenticationTestSuite) SetupSuite() {
	suite.apiSuite = integration.SetupAPITest(suite.T())
	suite.testPhone = "+1234567890"

	// Create test user
	userResp, err := suite.apiSuite.CreateTestUser("auth-test", suite.testPhone)
	require.NoError(suite.T(), err)
	suite.userToken = userResp.AccessToken
}

func (suite *A07IdentificationAuthenticationTestSuite) TearDownSuite() {
	suite.apiSuite.TeardownAPITest(suite.T())
}

// TestWeakPasswordPolicy tests password strength requirements
func (suite *A07IdentificationAuthenticationTestSuite) TestWeakPasswordPolicy() {
	t := suite.T()

	weakPasswords := []struct {
		password string
		reason   string
	}{
		{"123456", "numeric only"},
		{"password", "dictionary word"},
		{"Password", "dictionary with capital"},
		{"Password1", "predictable pattern"},
		{"qwerty", "keyboard pattern"},
		{"12345678", "sequential numbers"},
		{"abcdefgh", "sequential letters"},
		{"11111111", "repeated characters"},
		{"password123", "common pattern"},
		{"admin123", "default pattern"},
		{"letmein", "common phrase"},
		{"iloveyou", "common phrase"},
		{"", "empty password"},
		{"a", "too short"},
		{"ab", "too short"},
		{"abc", "too short"},
		{"test", "too short"},
		{suite.testPhone, "same as username"},
		{"auth-test", "contains username"},
	}

	for _, wp := range weakPasswords {
		t.Run(fmt.Sprintf("Reject weak password: %s (%s)", wp.password, wp.reason), func(t *testing.T) {
			rec, err := suite.apiSuite.MakeUnauthenticatedRequest(
				"POST", "/api/v1/authn/register",
				map[string]interface{}{
					"phone":    "+***********",
					"password": wp.password,
					"name":     "Test User",
				},
			)
			require.NoError(t, err)

			// Should reject weak passwords
			require.NotEqual(t, http.StatusOK, rec.Code,
				"Should reject weak password: %s (%s)", wp.password, wp.reason)
		})
	}
}

// TestBruteForceProtection tests brute force attack prevention
func (suite *A07IdentificationAuthenticationTestSuite) TestBruteForceProtection() {
	t := suite.T()

	// Test account lockout after failed attempts
	t.Run("Account lockout mechanism", func(t *testing.T) {
		testPhone := "+***********"
		maxAttempts := 10 // Reasonable limit

		blocked := false
		for i := 0; i < maxAttempts; i++ {
			rec, err := suite.apiSuite.MakeUnauthenticatedRequest(
				"POST", "/api/v1/authn/login",
				map[string]string{
					"phone":    testPhone,
					"password": fmt.Sprintf("wrong%d", i),
				},
			)
			require.NoError(t, err)

			// Check if account is locked
			if rec.Code == http.StatusTooManyRequests ||
				rec.Code == http.StatusForbidden ||
				strings.Contains(rec.Body.String(), "locked") ||
				strings.Contains(rec.Body.String(), "blocked") {
				blocked = true
				break
			}
		}

		require.True(t, blocked,
			"Should implement account lockout after %d failed attempts", maxAttempts)
	})

	// Test rate limiting on authentication endpoints
	t.Run("Authentication rate limiting", func(t *testing.T) {
		endpoints := []string{
			"/api/v1/authn/login",
			"/api/v1/authn/phone/otp/initiate",
			"/api/v1/authn/phone/otp/verify",
		}

		for _, endpoint := range endpoints {
			rateLimited := false

			// Make rapid requests
			for i := 0; i < 20; i++ {
				rec, err := suite.apiSuite.MakeUnauthenticatedRequest(
					"POST", endpoint,
					map[string]string{
						"phone": fmt.Sprintf("+1555555%04d", i),
					},
				)
				require.NoError(t, err)

				if rec.Code == http.StatusTooManyRequests {
					rateLimited = true

					// Check for rate limit headers
					retryAfter := rec.Header().Get("Retry-After")
					require.NotEmpty(t, retryAfter,
						"Rate limit should include Retry-After header")
					break
				}
			}

			require.True(t, rateLimited,
				"Should rate limit endpoint: %s", endpoint)
		}
	})

	// Test distributed brute force protection
	t.Run("Distributed attack protection", func(t *testing.T) {
		var wg sync.WaitGroup
		blocked := make(chan bool, 50)

		// Simulate distributed attack from multiple "IPs"
		for i := 0; i < 50; i++ {
			wg.Add(1)
			go func(idx int) {
				defer wg.Done()

				req, err := http.NewRequest("POST",
					"http://localhost:8081/api/v1/authn/login",
					strings.NewReader(`{"phone":"+15555555555","password":"wrong"}`))
				require.NoError(t, err)

				req.Header.Set("Content-Type", "application/json")
				req.Header.Set("X-Forwarded-For", fmt.Sprintf("192.168.1.%d", idx))

				client := &http.Client{}
				resp, err := client.Do(req)
				if err == nil {
					defer resp.Body.Close()
					if resp.StatusCode == http.StatusTooManyRequests {
						blocked <- true
					}
				}
			}(i)
		}

		wg.Wait()
		close(blocked)

		// Should detect and block distributed attacks
		blockedCount := 0
		for range blocked {
			blockedCount++
		}

		require.Greater(t, blockedCount, 0,
			"Should detect distributed brute force attempts")
	})
}

// TestSessionManagement tests session security
func (suite *A07IdentificationAuthenticationTestSuite) TestSessionManagement() {
	t := suite.T()

	// Test session fixation protection
	t.Run("Session fixation protection", func(t *testing.T) {
		// Get initial session
		rec1, err := suite.apiSuite.MakeUnauthenticatedRequest(
			"GET", "/api/v1/health", nil,
		)
		require.NoError(t, err)

		var sessionID1 string
		for _, cookie := range rec1.Result().Cookies() {
			if strings.Contains(strings.ToLower(cookie.Name), "session") {
				sessionID1 = cookie.Value
				break
			}
		}

		// Login
		rec2, err := suite.apiSuite.MakeUnauthenticatedRequest(
			"POST", "/api/v1/authn/login",
			map[string]string{
				"phone":    suite.testPhone,
				"password": "testpassword",
			},
		)
		require.NoError(t, err)

		// Session ID should change after login
		var sessionID2 string
		for _, cookie := range rec2.Result().Cookies() {
			if strings.Contains(strings.ToLower(cookie.Name), "session") {
				sessionID2 = cookie.Value
				break
			}
		}

		if sessionID1 != "" && sessionID2 != "" {
			require.NotEqual(t, sessionID1, sessionID2,
				"Session ID should regenerate after login")
		}
	})

	// Test concurrent session limits
	t.Run("Concurrent session limits", func(t *testing.T) {
		tokens := []string{}
		maxSessions := 10

		// Create multiple sessions
		for i := 0; i < maxSessions; i++ {
			resp, err := suite.apiSuite.CreateTestUser(
				fmt.Sprintf("session-test-%d", i),
				fmt.Sprintf("+1555666%04d", i),
			)
			require.NoError(t, err)
			tokens = append(tokens, resp.AccessToken)
		}

		// All tokens should work initially
		for i, token := range tokens {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"GET", "/api/v1/users/me", nil, token,
			)
			require.NoError(t, err)

			// Should either work or enforce session limit
			if rec.Code == http.StatusUnauthorized && i < 5 {
				// Early sessions might be invalidated - this is good
				t.Log("Session limit enforced, old session invalidated")
			}
		}
	})

	// Test session timeout
	t.Run("Session timeout enforcement", func(t *testing.T) {
		// This test would need to wait for actual timeout
		// so we just verify the configuration
		rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"GET", "/api/v1/auth/session/info", nil, suite.userToken,
		)
		require.NoError(t, err)

		if rec.Code == http.StatusOK {
			var info map[string]interface{}
			json.Unmarshal(rec.Body.Bytes(), &info)

			// Check for reasonable timeout
			if timeout, ok := info["timeout"].(float64); ok {
				require.True(t, timeout > 0 && timeout <= 86400,
					"Session timeout should be reasonable: %v seconds", timeout)
			}
		}
	})
}

// TestMultiFactorAuthentication tests MFA implementation
func (suite *A07IdentificationAuthenticationTestSuite) TestMultiFactorAuthentication() {
	t := suite.T()

	// Test MFA enrollment
	t.Run("MFA enrollment process", func(t *testing.T) {
		// Check if MFA is available
		rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"GET", "/api/v1/auth/mfa/status", nil, suite.userToken,
		)
		require.NoError(t, err)

		if rec.Code == http.StatusOK {
			var status map[string]interface{}
			json.Unmarshal(rec.Body.Bytes(), &status)

			// MFA should be available
			if available, ok := status["available"].(bool); ok && available {
				// Test enrollment
				rec, err = suite.apiSuite.MakeAuthenticatedRequestWithToken(
					"POST", "/api/v1/auth/mfa/enroll",
					map[string]string{"method": "totp"},
					suite.userToken,
				)
				require.NoError(t, err)

				// Should provide QR code or secret
				if rec.Code == http.StatusOK {
					var resp map[string]interface{}
					json.Unmarshal(rec.Body.Bytes(), &resp)

					require.NotNil(t, resp["secret"],
						"MFA enrollment should provide secret")
				}
			}
		}
	})

	// Test MFA bypass attempts
	t.Run("MFA bypass prevention", func(t *testing.T) {
		// Try to access protected resource without MFA
		endpoints := []string{
			"/api/v1/admin/settings",
			"/api/v1/security/keys",
			"/api/v1/billing/cards",
		}

		for _, endpoint := range endpoints {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"GET", endpoint, nil, suite.userToken,
			)
			require.NoError(t, err)

			// If endpoint requires MFA, should be blocked
			if rec.Code == http.StatusForbidden {
				body := rec.Body.String()
				if strings.Contains(body, "mfa") || strings.Contains(body, "verification") {
					// Good - MFA is required
					t.Logf("MFA required for %s", endpoint)
				}
			}
		}
	})
}

// TestCredentialRecovery tests account recovery security
func (suite *A07IdentificationAuthenticationTestSuite) TestCredentialRecovery() {
	t := suite.T()

	// Test account enumeration through recovery
	t.Run("Account enumeration prevention", func(t *testing.T) {
		validPhone := suite.testPhone
		invalidPhone := "+***********"

		// Request recovery for both valid and invalid accounts
		rec1, err := suite.apiSuite.MakeUnauthenticatedRequest(
			"POST", "/api/v1/password/forgot",
			map[string]string{"phone": validPhone},
		)
		require.NoError(t, err)

		rec2, err := suite.apiSuite.MakeUnauthenticatedRequest(
			"POST", "/api/v1/password/forgot",
			map[string]string{"phone": invalidPhone},
		)
		require.NoError(t, err)

		// Responses should be identical to prevent enumeration
		require.Equal(t, rec1.Code, rec2.Code,
			"Response codes should match to prevent enumeration")

		// Response messages should be generic
		body1 := rec1.Body.String()
		body2 := rec2.Body.String()

		// Should not indicate whether account exists
		require.NotContains(t, body1, "not found")
		require.NotContains(t, body2, "not found")
		require.NotContains(t, body1, "doesn't exist")
		require.NotContains(t, body2, "doesn't exist")
	})

	// Test recovery token security
	t.Run("Recovery token security", func(t *testing.T) {
		// Test weak/predictable tokens
		weakTokens := []string{
			"123456",
			"password",
			"reset",
			"token",
			"",
		}

		for _, token := range weakTokens {
			rec, err := suite.apiSuite.MakeUnauthenticatedRequest(
				"POST", "/api/v1/password/reset",
				map[string]interface{}{
					"token":        token,
					"new_password": "NewPassword123!",
				},
			)
			require.NoError(t, err)

			// Should reject weak tokens
			require.NotEqual(t, http.StatusOK, rec.Code,
				"Should reject weak recovery token: %s", token)
		}
	})

	// Test recovery rate limiting
	t.Run("Recovery rate limiting", func(t *testing.T) {
		rateLimited := false

		// Make multiple recovery requests
		for i := 0; i < 10; i++ {
			rec, err := suite.apiSuite.MakeUnauthenticatedRequest(
				"POST", "/api/v1/password/forgot",
				map[string]string{"phone": suite.testPhone},
			)
			require.NoError(t, err)

			if rec.Code == http.StatusTooManyRequests {
				rateLimited = true
				break
			}
		}

		require.True(t, rateLimited,
			"Should rate limit password recovery requests")
	})
}

// TestCredentialStuffing tests protection against credential stuffing
func (suite *A07IdentificationAuthenticationTestSuite) TestCredentialStuffing() {
	t := suite.T()

	// Common username/password combinations
	commonCreds := []struct {
		username string
		password string
	}{
		{"<EMAIL>", "admin123"},
		{"<EMAIL>", "test123"},
		{"<EMAIL>", "password"},
		{"<EMAIL>", "demo123"},
		{"+11234567890", "password123"},
		{"+10000000000", "12345678"},
	}

	blockedCount := 0
	for _, cred := range commonCreds {
		rec, err := suite.apiSuite.MakeUnauthenticatedRequest(
			"POST", "/api/v1/authn/login",
			map[string]string{
				"phone":    cred.username,
				"password": cred.password,
			},
		)
		require.NoError(t, err)

		// Should not succeed with common credentials
		require.NotEqual(t, http.StatusOK, rec.Code,
			"Common credentials should not work: %s/%s", cred.username, cred.password)

		// Check if we're being rate limited
		if rec.Code == http.StatusTooManyRequests {
			blockedCount++
		}
	}

	// Should detect pattern and rate limit
	require.Greater(t, blockedCount, 0,
		"Should detect and rate limit credential stuffing attempts")
}

// TestPasswordHistory tests password reuse prevention
func (suite *A07IdentificationAuthenticationTestSuite) TestPasswordHistory() {
	t := suite.T()

	// Create test user with known password
	testUser, err := suite.apiSuite.CreateTestUser("history-test", "+18885551234")
	require.NoError(t, err)

	passwords := []string{
		"FirstPassword123!",
		"SecondPassword123!",
		"ThirdPassword123!",
	}

	// Change password multiple times
	for _, pwd := range passwords {
		rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"POST", "/api/v1/auth/password/change",
			map[string]interface{}{
				"current_password": "testpassword", // or previous
				"new_password":     pwd,
			},
			testUser.AccessToken,
		)
		require.NoError(t, err)

		if rec.Code == http.StatusOK {
			// Update token if needed
			var resp map[string]interface{}
			json.Unmarshal(rec.Body.Bytes(), &resp)
			if token, ok := resp["access_token"].(string); ok {
				testUser.AccessToken = token
			}
		}
	}

	// Try to reuse old password
	rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
		"POST", "/api/v1/auth/password/change",
		map[string]interface{}{
			"current_password": passwords[len(passwords)-1],
			"new_password":     passwords[0], // Try first password again
		},
		testUser.AccessToken,
	)
	require.NoError(t, err)

	// Should prevent password reuse
	if rec.Code != http.StatusOK {
		body := rec.Body.String()
		require.True(t,
			strings.Contains(body, "previous") ||
				strings.Contains(body, "reuse") ||
				strings.Contains(body, "history"),
			"Should indicate password reuse is not allowed")
	}
}

// TestAuthenticationLogging tests security event logging
func (suite *A07IdentificationAuthenticationTestSuite) TestAuthenticationLogging() {
	t := suite.T()

	// Test that authentication events are logged
	t.Run("Authentication event logging", func(t *testing.T) {
		// Perform various auth actions
		actions := []struct {
			name     string
			endpoint string
			body     interface{}
			success  bool
		}{
			{
				name:     "Failed login",
				endpoint: "/api/v1/authn/login",
				body:     map[string]string{"phone": suite.testPhone, "password": "wrong"},
				success:  false,
			},
			{
				name:     "Password change",
				endpoint: "/api/v1/auth/password/change",
				body:     map[string]string{"current_password": "test", "new_password": "test2"},
				success:  true,
			},
			{
				name:     "Account lockout",
				endpoint: "/api/v1/authn/login",
				body:     map[string]string{"phone": "+***********", "password": "wrong"},
				success:  false,
			},
		}

		for _, action := range actions {
			if action.endpoint == "/api/v1/auth/password/change" {
				suite.apiSuite.MakeAuthenticatedRequestWithToken(
					"POST", action.endpoint, action.body, suite.userToken,
				)
			} else {
				suite.apiSuite.MakeUnauthenticatedRequest(
					"POST", action.endpoint, action.body,
				)
			}
		}

		// Check if events are queryable (if endpoint exists)
		rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"GET", "/api/v1/auth/events", nil, suite.userToken,
		)
		require.NoError(t, err)

		if rec.Code == http.StatusOK {
			var events []map[string]interface{}
			json.Unmarshal(rec.Body.Bytes(), &events)

			// Should have security events
			require.NotEmpty(t, events,
				"Authentication events should be logged")
		}
	})
}

// TestTimingAttacks tests protection against timing-based attacks
func (suite *A07IdentificationAuthenticationTestSuite) TestTimingAttacks() {
	t := suite.T()

	// Test username enumeration via timing
	t.Run("Constant time authentication", func(t *testing.T) {
		validPhone := suite.testPhone
		invalidPhone := "+***********"

		// Measure response times
		validTimes := []time.Duration{}
		invalidTimes := []time.Duration{}

		for i := 0; i < 5; i++ {
			// Valid username, wrong password
			start := time.Now()
			suite.apiSuite.MakeUnauthenticatedRequest(
				"POST", "/api/v1/authn/login",
				map[string]string{
					"phone":    validPhone,
					"password": "wrongpassword",
				},
			)
			validTimes = append(validTimes, time.Since(start))

			// Invalid username
			start = time.Now()
			suite.apiSuite.MakeUnauthenticatedRequest(
				"POST", "/api/v1/authn/login",
				map[string]string{
					"phone":    invalidPhone,
					"password": "wrongpassword",
				},
			)
			invalidTimes = append(invalidTimes, time.Since(start))
		}

		// Calculate averages
		var avgValid, avgInvalid time.Duration
		for i := range validTimes {
			avgValid += validTimes[i]
			avgInvalid += invalidTimes[i]
		}
		avgValid /= time.Duration(len(validTimes))
		avgInvalid /= time.Duration(len(invalidTimes))

		// Difference should be minimal
		diff := avgValid - avgInvalid
		if diff < 0 {
			diff = -diff
		}

		// Allow up to 50ms difference
		require.Less(t, diff, 50*time.Millisecond,
			"Timing difference too large: %v (may allow enumeration)", diff)
	})
}

func TestA07IdentificationAuthenticationTestSuite(t *testing.T) {
	suite.Run(t, new(A07IdentificationAuthenticationTestSuite))
}
