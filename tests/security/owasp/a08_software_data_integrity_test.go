package owasp

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	"Membership-SAAS-System-Backend/tests/integration"
)

// A08SoftwareDataIntegrityTestSuite tests OWASP A08:2024 - Software and Data Integrity Failures
type A08SoftwareDataIntegrityTestSuite struct {
	suite.Suite
	apiSuite  *integration.APITestSuite
	userToken string
	orgID     string
}

func (suite *A08SoftwareDataIntegrityTestSuite) SetupSuite() {
	suite.apiSuite = integration.SetupAPITest(suite.T())

	// Create test user and org
	userResp, err := suite.apiSuite.CreateTestUser("integrity-test", "+1234567890")
	require.NoError(suite.T(), err)
	suite.userToken = userResp.AccessToken

	org, err := suite.apiSuite.CreateTestOrganization("test-integrity-org", suite.userToken)
	require.NoError(suite.T(), err)
	suite.orgID = org.ID
}

func (suite *A08SoftwareDataIntegrityTestSuite) TearDownSuite() {
	suite.apiSuite.TeardownAPITest(suite.T())
}

// TestDeserializationAttacks tests for insecure deserialization
func (suite *A08SoftwareDataIntegrityTestSuite) TestDeserializationAttacks() {
	t := suite.T()

	// Test various deserialization payloads
	payloads := []struct {
		name        string
		contentType string
		payload     string
	}{
		// Java serialization
		{
			name:        "Java serialized object",
			contentType: "application/x-java-serialized-object",
			payload:     base64.StdEncoding.EncodeToString([]byte("\xac\xed\x00\x05")),
		},
		// Python pickle
		{
			name:        "Python pickle",
			contentType: "application/octet-stream",
			payload:     base64.StdEncoding.EncodeToString([]byte("cos\nsystem\n(S'id'\ntR.")),
		},
		// PHP serialization
		{
			name:        "PHP serialized",
			contentType: "application/vnd.php.serialized",
			payload:     `O:8:"stdClass":1:{s:4:"evil";s:10:"phpinfo();";}`,
		},
		// .NET serialization
		{
			name:        ".NET serialized",
			contentType: "application/octet-stream",
			payload:     base64.StdEncoding.EncodeToString([]byte("AAEAAAD///")),
		},
		// YAML with code execution
		{
			name:        "YAML code execution",
			contentType: "application/x-yaml",
			payload:     `!!python/object/apply:os.system ["id"]`,
		},
		// XML with external entities
		{
			name:        "XML with entities",
			contentType: "application/xml",
			payload:     `<?xml version="1.0"?><!DOCTYPE root [<!ENTITY test SYSTEM "file:///etc/passwd">]><root>&test;</root>`,
		},
	}

	for _, p := range payloads {
		t.Run(p.name, func(t *testing.T) {
			req, err := http.NewRequest("POST",
				"http://localhost:8081/api/v1/data/import",
				strings.NewReader(p.payload))
			require.NoError(t, err)

			req.Header.Set("Content-Type", p.contentType)
			req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", suite.userToken))

			client := &http.Client{}
			resp, err := client.Do(req)
			require.NoError(t, err)
			defer resp.Body.Close()

			// Should reject dangerous deserialization
			require.NotEqual(t, http.StatusOK, resp.StatusCode,
				"Should reject dangerous deserialization: %s", p.name)
		})
	}
}

// TestCodeInjection tests for code injection through data
func (suite *A08SoftwareDataIntegrityTestSuite) TestCodeInjection() {
	t := suite.T()

	// Test expression language injection
	codeInjectionPayloads := []struct {
		field   string
		payload string
	}{
		// JavaScript injection
		{"name", "'); alert('xss'); //"},
		{"description", "<script>alert('xss')</script>"},

		// Template injection
		{"template", "{{7*7}}"},
		{"template", "${7*7}"},
		{"template", "<%= 7*7 %>"},
		{"template", "#{7*7}"},

		// Expression language
		{"expression", "${T(java.lang.Runtime).getRuntime().exec('id')}"},
		{"formula", "=1+1+cmd|' /C calc'!A0"},

		// Server-side includes
		{"content", "<!--#exec cmd='id' -->"},
		{"content", "<!--#include virtual='/etc/passwd' -->"},
	}

	for _, ci := range codeInjectionPayloads {
		t.Run(fmt.Sprintf("%s: %s", ci.field, ci.payload), func(t *testing.T) {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"POST", fmt.Sprintf("/api/v1/organizations/%s/content", suite.orgID),
				map[string]interface{}{
					ci.field: ci.payload,
					"type":   "post",
				},
				suite.userToken,
			)
			require.NoError(t, err)

			// Check response doesn't execute code
			body := rec.Body.String()

			// Should not contain execution results
			require.NotContains(t, body, "49") // 7*7
			require.NotContains(t, body, "uid=")
			require.NotContains(t, body, "root:")
			require.NotContains(t, body, "<script")
		})
	}
}

// TestDataIntegrityValidation tests data integrity checks
func (suite *A08SoftwareDataIntegrityTestSuite) TestDataIntegrityValidation() {
	t := suite.T()

	// Test HMAC/signature validation
	t.Run("Signature validation", func(t *testing.T) {
		// Create data with signature
		data := map[string]interface{}{
			"user_id":   "test-user",
			"amount":    100,
			"timestamp": time.Now().Unix(),
		}

		// Test with invalid signatures
		invalidSignatures := []string{
			"",
			"invalid",
			"12345",
			"forged-signature",
			strings.Repeat("a", 64), // Right length, wrong value
		}

		for _, sig := range invalidSignatures {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"POST", "/api/v1/verify",
				map[string]interface{}{
					"data":      data,
					"signature": sig,
				},
				suite.userToken,
			)
			require.NoError(t, err)

			// Should reject invalid signatures
			require.NotEqual(t, http.StatusOK, rec.Code,
				"Should reject invalid signature: %s", sig)
		}
	})

	// Test checksum validation
	t.Run("Checksum validation", func(t *testing.T) {
		content := "Important data that should not be modified"
		validChecksum := fmt.Sprintf("%x", sha256.Sum256([]byte(content)))

		// Test with tampered data
		tamperedTests := []struct {
			content  string
			checksum string
		}{
			{content + " ", validChecksum}, // Extra space
			{content, "invalid-checksum"},
			{content[:len(content)-1], validChecksum}, // Truncated
			{"Tampered: " + content, validChecksum},
		}

		for _, tt := range tamperedTests {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"POST", "/api/v1/data/verify",
				map[string]interface{}{
					"content":  tt.content,
					"checksum": tt.checksum,
				},
				suite.userToken,
			)
			require.NoError(t, err)

			// Should detect tampering
			require.NotEqual(t, http.StatusOK, rec.Code,
				"Should detect data tampering")
		}
	})
}

// TestAutoUpdateSecurity tests auto-update mechanism security
func (suite *A08SoftwareDataIntegrityTestSuite) TestAutoUpdateSecurity() {
	t := suite.T()

	// Test update source validation
	t.Run("Update source validation", func(t *testing.T) {
		maliciousUpdates := []struct {
			source string
			url    string
		}{
			{"malicious", "http://evil.com/update.zip"},
			{"untrusted", "https://attacker.com/backdoor.tar.gz"},
			{"local", "file:///etc/passwd"},
			{"ftp", "ftp://<EMAIL>/update"},
		}

		for _, update := range maliciousUpdates {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"POST", "/api/v1/admin/update",
				map[string]interface{}{
					"source": update.source,
					"url":    update.url,
				},
				suite.userToken,
			)
			require.NoError(t, err)

			// Should reject untrusted update sources
			require.NotEqual(t, http.StatusOK, rec.Code,
				"Should reject untrusted update source: %s", update.url)
		}
	})

	// Test update signature verification
	t.Run("Update signature verification", func(t *testing.T) {
		// Mock update package
		updatePackage := map[string]interface{}{
			"version":   "2.0.0",
			"url":       "https://trusted.com/update.zip",
			"hash":      "abc123def456",
			"signature": "invalid-signature",
		}

		rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"POST", "/api/v1/admin/update/verify",
			updatePackage,
			suite.userToken,
		)
		require.NoError(t, err)

		// Should require valid signature
		require.NotEqual(t, http.StatusOK, rec.Code,
			"Should require valid update signature")
	})
}

// TestCICDSecurity tests CI/CD pipeline security
func (suite *A08SoftwareDataIntegrityTestSuite) TestCICDSecurity() {
	t := suite.T()

	// Test webhook validation
	t.Run("Webhook signature validation", func(t *testing.T) {
		webhookBody := `{"ref":"refs/heads/main","commits":[{"id":"abc123"}]}`

		// Test without signature
		req, err := http.NewRequest("POST",
			"http://localhost:8081/api/v1/webhooks/github",
			strings.NewReader(webhookBody))
		require.NoError(t, err)

		client := &http.Client{}
		resp, err := client.Do(req)
		require.NoError(t, err)
		defer resp.Body.Close()

		// Should reject unsigned webhook
		require.NotEqual(t, http.StatusOK, resp.StatusCode,
			"Should reject unsigned webhook")

		// Test with invalid signature
		req, err = http.NewRequest("POST",
			"http://localhost:8081/api/v1/webhooks/github",
			strings.NewReader(webhookBody))
		require.NoError(t, err)

		req.Header.Set("X-Hub-Signature-256", "sha256=invalid")

		resp, err = client.Do(req)
		require.NoError(t, err)
		defer resp.Body.Close()

		// Should reject invalid signature
		require.NotEqual(t, http.StatusOK, resp.StatusCode,
			"Should reject webhook with invalid signature")
	})

	// Test build artifact integrity
	t.Run("Build artifact verification", func(t *testing.T) {
		// Test deployment with tampered artifacts
		deploymentTests := []struct {
			artifact  string
			checksum  string
			signature string
		}{
			{"app.tar.gz", "invalid-checksum", "valid-sig"},
			{"app.tar.gz", "valid-checksum", "invalid-sig"},
			{"malicious.exe", "any-checksum", "any-sig"},
		}

		for _, dt := range deploymentTests {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"POST", "/api/v1/deploy",
				map[string]interface{}{
					"artifact":  dt.artifact,
					"checksum":  dt.checksum,
					"signature": dt.signature,
				},
				suite.userToken,
			)
			require.NoError(t, err)

			// Should validate artifacts before deployment
			require.NotEqual(t, http.StatusOK, rec.Code,
				"Should validate artifact: %s", dt.artifact)
		}
	})
}

// TestDataValidation tests input data validation
func (suite *A08SoftwareDataIntegrityTestSuite) TestDataValidation() {
	t := suite.T()

	// Test schema validation
	t.Run("JSON schema validation", func(t *testing.T) {
		invalidData := []map[string]interface{}{
			// Missing required fields
			{"name": "Test"},
			// Wrong types
			{"name": "Test", "age": "not-a-number"},
			// Additional properties
			{"name": "Test", "age": 25, "malicious": "data"},
			// Nested validation
			{"name": "Test", "address": map[string]interface{}{"street": 123}},
		}

		for i, data := range invalidData {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"POST", "/api/v1/users/create",
				data,
				suite.userToken,
			)
			require.NoError(t, err)

			// Should validate against schema
			require.NotEqual(t, http.StatusOK, rec.Code,
				"Should validate data schema: test %d", i)
		}
	})

	// Test business logic validation
	t.Run("Business logic validation", func(t *testing.T) {
		// Test logical inconsistencies
		illogicalData := []map[string]interface{}{
			// End date before start date
			{
				"start_date": "2024-12-31",
				"end_date":   "2024-01-01",
			},
			// Negative quantities
			{
				"quantity": -5,
				"price":    10.00,
			},
			// Invalid state transitions
			{
				"status":     "completed",
				"transition": "draft",
			},
		}

		for _, data := range illogicalData {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"POST", "/api/v1/orders/create",
				data,
				suite.userToken,
			)
			require.NoError(t, err)

			// Should enforce business logic
			require.NotEqual(t, http.StatusOK, rec.Code,
				"Should validate business logic")
		}
	})
}

// TestDependencyIntegrity tests third-party dependency security
func (suite *A08SoftwareDataIntegrityTestSuite) TestDependencyIntegrity() {
	t := suite.T()

	// Test package integrity verification
	t.Run("Package integrity checks", func(t *testing.T) {
		// Check if dependency info endpoint exists
		rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"GET", "/api/v1/admin/dependencies", nil, suite.userToken,
		)
		require.NoError(t, err)

		if rec.Code == http.StatusOK {
			var deps []map[string]interface{}
			json.Unmarshal(rec.Body.Bytes(), &deps)

			// Each dependency should have integrity info
			for _, dep := range deps {
				// Check for security metadata
				if name, ok := dep["name"].(string); ok {
					// Should have version pinning
					require.NotNil(t, dep["version"],
						"Dependency %s should have pinned version", name)

					// Should have integrity hash or signature
					hasIntegrity := dep["integrity"] != nil ||
						dep["checksum"] != nil ||
						dep["signature"] != nil
					require.True(t, hasIntegrity,
						"Dependency %s should have integrity verification", name)
				}
			}
		}
	})

	// Test malicious package detection
	t.Run("Malicious package prevention", func(t *testing.T) {
		maliciousPackages := []string{
			"malicious-package",
			"node-ipc-exploit",
			"colors-1.4.1",
			"faker-5.5.4",
		}

		for _, pkg := range maliciousPackages {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"POST", "/api/v1/admin/packages/install",
				map[string]interface{}{
					"package": pkg,
					"version": "latest",
				},
				suite.userToken,
			)
			require.NoError(t, err)

			// Should block known malicious packages
			require.NotEqual(t, http.StatusOK, rec.Code,
				"Should block malicious package: %s", pkg)
		}
	})
}

// TestConfigurationIntegrity tests configuration tampering
func (suite *A08SoftwareDataIntegrityTestSuite) TestConfigurationIntegrity() {
	t := suite.T()

	// Test configuration tampering detection
	t.Run("Configuration tampering detection", func(t *testing.T) {
		// Try to modify critical configurations
		criticalConfigs := []struct {
			path  string
			value interface{}
		}{
			{"/api/v1/config/security/authentication", map[string]interface{}{"enabled": false}},
			{"/api/v1/config/security/encryption", map[string]interface{}{"algorithm": "none"}},
			{"/api/v1/config/logging/level", "disabled"},
			{"/api/v1/config/database/connection", "malicious-server:5432"},
		}

		for _, config := range criticalConfigs {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"PUT", config.path,
				config.value,
				suite.userToken,
			)
			require.NoError(t, err)

			// Should protect critical configurations
			require.NotEqual(t, http.StatusOK, rec.Code,
				"Should protect critical config: %s", config.path)
		}
	})

	// Test environment variable injection
	t.Run("Environment variable protection", func(t *testing.T) {
		// Try to inject through various means
		envInjections := []map[string]interface{}{
			{"env": "NODE_ENV=production NODE_DEBUG=*"},
			{"command": "export MALICIOUS=true && run"},
			{"variable": "${MALICIOUS:-default}"},
		}

		for _, injection := range envInjections {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"POST", "/api/v1/execute",
				injection,
				suite.userToken,
			)
			require.NoError(t, err)

			// Should not allow environment manipulation
			require.NotEqual(t, http.StatusOK, rec.Code,
				"Should prevent environment injection")
		}
	})
}

// TestCacheIntegrity tests cache poisoning prevention
func (suite *A08SoftwareDataIntegrityTestSuite) TestCacheIntegrity() {
	t := suite.T()

	// Test cache poisoning attempts
	t.Run("Cache poisoning prevention", func(t *testing.T) {
		// Try various cache poisoning techniques
		poisonAttempts := []struct {
			headers map[string]string
			path    string
		}{
			{
				headers: map[string]string{
					"X-Forwarded-Host":  "evil.com",
					"X-Forwarded-Proto": "https",
				},
				path: "/api/v1/public/data",
			},
			{
				headers: map[string]string{
					"X-Original-URL": "/admin",
					"X-Rewrite-URL":  "/admin",
				},
				path: "/api/v1/public/info",
			},
			{
				headers: map[string]string{
					"Cache-Control": "public, max-age=31536000",
				},
				path: "/api/v1/sensitive/data",
			},
		}

		for _, attempt := range poisonAttempts {
			req, err := http.NewRequest("GET",
				fmt.Sprintf("http://localhost:8081%s", attempt.path), nil)
			require.NoError(t, err)

			// Add poisoning headers
			for k, v := range attempt.headers {
				req.Header.Set(k, v)
			}

			if strings.Contains(attempt.path, "sensitive") {
				req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", suite.userToken))
			}

			client := &http.Client{}
			resp, err := client.Do(req)
			require.NoError(t, err)
			defer resp.Body.Close()

			// Check that cache wasn't poisoned
			cacheControl := resp.Header.Get("Cache-Control")
			if strings.Contains(attempt.path, "sensitive") {
				require.Contains(t, cacheControl, "no-store",
					"Sensitive data should not be cached")
			}

			// Verify headers weren't reflected
			require.NotEqual(t, "evil.com", resp.Header.Get("X-Forwarded-Host"),
				"Should not reflect malicious headers")
		}
	})
}

// TestMessageIntegrity tests message/event integrity
func (suite *A08SoftwareDataIntegrityTestSuite) TestMessageIntegrity() {
	t := suite.T()

	// Test message tampering
	t.Run("Message tampering detection", func(t *testing.T) {
		// Original message
		message := map[string]interface{}{
			"type":      "transfer",
			"from":      "user1",
			"to":        "user2",
			"amount":    100,
			"timestamp": time.Now().Unix(),
		}

		// Calculate HMAC
		msgBytes, _ := json.Marshal(message)
		h := hmac.New(sha256.New, []byte("test-secret"))
		h.Write(msgBytes)
		validMAC := hex.EncodeToString(h.Sum(nil))

		// Test with tampered message
		message["amount"] = 1000000 // Changed amount

		rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"POST", "/api/v1/messages/process",
			map[string]interface{}{
				"message": message,
				"mac":     validMAC, // MAC for original message
			},
			suite.userToken,
		)
		require.NoError(t, err)

		// Should detect tampering
		require.NotEqual(t, http.StatusOK, rec.Code,
			"Should detect message tampering")
	})

	// Test replay attack prevention
	t.Run("Replay attack prevention", func(t *testing.T) {
		// Create valid message with timestamp and nonce
		message := map[string]interface{}{
			"action":    "important",
			"timestamp": time.Now().Unix(),
			"nonce":     "unique-123",
		}

		// First request should succeed
		rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"POST", "/api/v1/messages/submit",
			message,
			suite.userToken,
		)
		require.NoError(t, err)

		originalCode := rec.Code

		// Replay the same message
		rec, err = suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"POST", "/api/v1/messages/submit",
			message,
			suite.userToken,
		)
		require.NoError(t, err)

		// Should reject replay
		if originalCode == http.StatusOK {
			require.NotEqual(t, http.StatusOK, rec.Code,
				"Should prevent replay attacks")
		}
	})
}

func TestA08SoftwareDataIntegrityTestSuite(t *testing.T) {
	suite.Run(t, new(A08SoftwareDataIntegrityTestSuite))
}
