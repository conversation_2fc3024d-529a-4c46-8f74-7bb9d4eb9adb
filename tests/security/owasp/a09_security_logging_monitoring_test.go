package owasp

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	"Membership-SAAS-System-Backend/tests/integration"
)

// A09SecurityLoggingMonitoringTestSuite tests OWASP A09:2024 - Security Logging and Monitoring Failures
type A09SecurityLoggingMonitoringTestSuite struct {
	suite.Suite
	apiSuite   *integration.APITestSuite
	userToken  string
	adminToken string
	orgID      string
}

func (suite *A09SecurityLoggingMonitoringTestSuite) SetupSuite() {
	suite.apiSuite = integration.SetupAPITest(suite.T())

	// Create test user and org
	userResp, err := suite.apiSuite.CreateTestUser("logging-test", "+**********")
	require.NoError(suite.T(), err)
	suite.userToken = userResp.AccessToken

	// Create admin user
	adminResp, err := suite.apiSuite.CreateTestUser("admin-logging", "+1234567891")
	require.NoError(suite.T(), err)
	suite.adminToken = adminResp.AccessToken

	org, err := suite.apiSuite.CreateTestOrganization("test-logging-org", suite.userToken)
	require.NoError(suite.T(), err)
	suite.orgID = org.ID
}

func (suite *A09SecurityLoggingMonitoringTestSuite) TearDownSuite() {
	suite.apiSuite.TeardownAPITest(suite.T())
}

// TestSecurityEventLogging tests that security events are properly logged
func (suite *A09SecurityLoggingMonitoringTestSuite) TestSecurityEventLogging() {
	t := suite.T()

	// Events that MUST be logged
	securityEvents := []struct {
		name      string
		action    func() (*httptest.ResponseRecorder, error)
		eventType string
		shouldLog bool
	}{
		{
			name: "Failed login attempt",
			action: func() (*httptest.ResponseRecorder, error) {
				return suite.apiSuite.MakeUnauthenticatedRequest(
					"POST", "/api/v1/authn/login",
					map[string]string{
						"phone":    "+***********",
						"password": "wrongpassword",
					},
				)
			},
			eventType: "auth_failure",
			shouldLog: true,
		},
		{
			name: "Successful login",
			action: func() (*httptest.ResponseRecorder, error) {
				return suite.apiSuite.MakeUnauthenticatedRequest(
					"POST", "/api/v1/authn/login",
					map[string]string{
						"phone":    "+**********",
						"password": "testpassword",
					},
				)
			},
			eventType: "auth_success",
			shouldLog: true,
		},
		{
			name: "Account lockout",
			action: func() (*httptest.ResponseRecorder, error) {
				// Multiple failed attempts
				for i := 0; i < 10; i++ {
					suite.apiSuite.MakeUnauthenticatedRequest(
						"POST", "/api/v1/authn/login",
						map[string]string{
							"phone":    "+***********",
							"password": "wrong",
						},
					)
				}
				return suite.apiSuite.MakeUnauthenticatedRequest(
					"POST", "/api/v1/authn/login",
					map[string]string{
						"phone":    "+***********",
						"password": "wrong",
					},
				)
			},
			eventType: "account_locked",
			shouldLog: true,
		},
		{
			name: "Privilege escalation attempt",
			action: func() (*httptest.ResponseRecorder, error) {
				return suite.apiSuite.MakeAuthenticatedRequestWithToken(
					"PATCH", "/api/v1/users/me",
					map[string]interface{}{
						"role":          "admin",
						"platform_role": "super_admin",
					},
					suite.userToken,
				)
			},
			eventType: "privilege_escalation_attempt",
			shouldLog: true,
		},
		{
			name: "Unauthorized access attempt",
			action: func() (*httptest.ResponseRecorder, error) {
				return suite.apiSuite.MakeAuthenticatedRequestWithToken(
					"GET", "/api/v1/admin/users",
					nil,
					suite.userToken,
				)
			},
			eventType: "unauthorized_access",
			shouldLog: true,
		},
		{
			name: "Password change",
			action: func() (*httptest.ResponseRecorder, error) {
				return suite.apiSuite.MakeAuthenticatedRequestWithToken(
					"POST", "/api/v1/auth/password/change",
					map[string]interface{}{
						"current_password": "test",
						"new_password":     "newtest",
					},
					suite.userToken,
				)
			},
			eventType: "password_changed",
			shouldLog: true,
		},
		{
			name: "Data export",
			action: func() (*httptest.ResponseRecorder, error) {
				return suite.apiSuite.MakeAuthenticatedRequestWithToken(
					"POST", "/api/v1/export/user-data",
					nil,
					suite.userToken,
				)
			},
			eventType: "data_export",
			shouldLog: true,
		},
		{
			name: "API key creation",
			action: func() (*httptest.ResponseRecorder, error) {
				return suite.apiSuite.MakeAuthenticatedRequestWithToken(
					"POST", "/api/v1/api-keys",
					map[string]string{"name": "test-key"},
					suite.userToken,
				)
			},
			eventType: "api_key_created",
			shouldLog: true,
		},
	}

	// Execute security events
	for _, event := range securityEvents {
		t.Run(event.name, func(t *testing.T) {
			// Perform the action
			_, err := event.action()
			require.NoError(t, err)

			// Give time for async logging
			time.Sleep(100 * time.Millisecond)

			// Check if event was logged (if audit endpoint exists)
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"GET", "/api/v1/audit/logs?type="+event.eventType,
				nil,
				suite.adminToken,
			)
			require.NoError(t, err)

			if rec.Code == http.StatusOK {
				var logs []map[string]interface{}
				json.Unmarshal(rec.Body.Bytes(), &logs)

				if event.shouldLog {
					require.NotEmpty(t, logs,
						"Security event should be logged: %s", event.name)
				}
			}
		})
	}
}

// TestLogInjectionPrevention tests that logs cannot be injected/forged
func (suite *A09SecurityLoggingMonitoringTestSuite) TestLogInjectionPrevention() {
	t := suite.T()

	logInjectionPayloads := []string{
		"test\nINFO: Fake log entry",
		"test\r\nERROR: System compromised",
		"test%0aWARN: Unauthorized access granted",
		"test%0d%0aINFO: User admin logged in",
		"test\x0aAUDIT: Permission escalated",
		"test\\nDEBUG: Password: secretpass",
	}

	for _, payload := range logInjectionPayloads {
		t.Run(fmt.Sprintf("Log injection: %s", strings.ReplaceAll(payload, "\n", "\\n")), func(t *testing.T) {
			// Try to inject into various logged fields
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"PATCH", "/api/v1/users/me",
				map[string]interface{}{
					"display_name": payload,
					"bio":          payload,
					"location":     payload,
				},
				suite.userToken,
			)
			require.NoError(t, err)

			// The application should sanitize log entries
			// We can't directly verify logs, but responses shouldn't reflect injection
			body := rec.Body.String()
			require.NotContains(t, body, "Fake log entry")
			require.NotContains(t, body, "System compromised")
			require.NotContains(t, body, "Password: secretpass")
		})
	}
}

// TestSensitiveDataInLogs tests that sensitive data is not logged
func (suite *A09SecurityLoggingMonitoringTestSuite) TestSensitiveDataInLogs() {
	t := suite.T()

	// Operations with sensitive data
	sensitiveOperations := []struct {
		name      string
		action    func() (*httptest.ResponseRecorder, error)
		sensitive []string
	}{
		{
			name: "Login with password",
			action: func() (*httptest.ResponseRecorder, error) {
				return suite.apiSuite.MakeUnauthenticatedRequest(
					"POST", "/api/v1/authn/login",
					map[string]string{
						"phone":    "+**********",
						"password": "SuperSecret123!",
					},
				)
			},
			sensitive: []string{"SuperSecret123!", "password"},
		},
		{
			name: "Update sensitive data",
			action: func() (*httptest.ResponseRecorder, error) {
				return suite.apiSuite.MakeAuthenticatedRequestWithToken(
					"PATCH", "/api/v1/users/me",
					map[string]interface{}{
						"ssn":         "***********",
						"credit_card": "****************",
						"api_secret":  "sk_test_**********",
					},
					suite.userToken,
				)
			},
			sensitive: []string{"***********", "****************", "sk_test_**********"},
		},
		{
			name: "Create API key",
			action: func() (*httptest.ResponseRecorder, error) {
				return suite.apiSuite.MakeAuthenticatedRequestWithToken(
					"POST", "/api/v1/api-keys",
					map[string]string{
						"name": "production-key",
					},
					suite.userToken,
				)
			},
			sensitive: []string{"key_", "secret_", "token_"},
		},
	}

	for _, op := range sensitiveOperations {
		t.Run(op.name, func(t *testing.T) {
			// Perform operation
			rec, err := op.action()
			require.NoError(t, err)

			// Check audit logs don't contain sensitive data
			if suite.adminToken != "" {
				auditRec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
					"GET", "/api/v1/audit/logs?limit=10",
					nil,
					suite.adminToken,
				)
				require.NoError(t, err)

				if auditRec.Code == http.StatusOK {
					body := auditRec.Body.String()

					// Sensitive data should not appear in logs
					for _, sensitive := range op.sensitive {
						require.NotContains(t, body, sensitive,
							"Sensitive data should not be in logs: %s", sensitive)
					}
				}
			}

			// Also check the response doesn't accidentally log sensitive data
			responseBody := rec.Body.String()
			for _, sensitive := range op.sensitive {
				if strings.Contains(responseBody, sensitive) {
					// If it's in the response, it should be masked
					require.True(t,
						strings.Contains(responseBody, "***") ||
							strings.Contains(responseBody, "REDACTED") ||
							strings.Contains(responseBody, "masked"),
						"Sensitive data should be masked in responses")
				}
			}
		})
	}
}

// TestMonitoringAlerts tests that monitoring alerts are triggered
func (suite *A09SecurityLoggingMonitoringTestSuite) TestMonitoringAlerts() {
	t := suite.T()

	// Actions that should trigger alerts
	alertScenarios := []struct {
		name          string
		action        func()
		expectedAlert string
	}{
		{
			name: "Multiple failed login attempts",
			action: func() {
				for i := 0; i < 10; i++ {
					suite.apiSuite.MakeUnauthenticatedRequest(
						"POST", "/api/v1/authn/login",
						map[string]string{
							"phone":    "+17777777777",
							"password": "wrong",
						},
					)
				}
			},
			expectedAlert: "brute_force_attempt",
		},
		{
			name: "Rapid API calls (potential DoS)",
			action: func() {
				var wg sync.WaitGroup
				for i := 0; i < 100; i++ {
					wg.Add(1)
					go func() {
						defer wg.Done()
						suite.apiSuite.MakeAuthenticatedRequestWithToken(
							"GET", "/api/v1/users/me",
							nil,
							suite.userToken,
						)
					}()
				}
				wg.Wait()
			},
			expectedAlert: "rate_limit_exceeded",
		},
		{
			name: "Access to multiple unauthorized resources",
			action: func() {
				endpoints := []string{
					"/api/v1/admin/users",
					"/api/v1/admin/settings",
					"/api/v1/admin/logs",
					"/api/v1/admin/database",
				}
				for _, endpoint := range endpoints {
					suite.apiSuite.MakeAuthenticatedRequestWithToken(
						"GET", endpoint, nil, suite.userToken,
					)
				}
			},
			expectedAlert: "unauthorized_access_pattern",
		},
		{
			name: "Data exfiltration attempt",
			action: func() {
				// Try to export large amounts of data
				for i := 0; i < 5; i++ {
					suite.apiSuite.MakeAuthenticatedRequestWithToken(
						"GET", fmt.Sprintf("/api/v1/export/all?page=%d&limit=1000", i),
						nil,
						suite.userToken,
					)
				}
			},
			expectedAlert: "potential_data_exfiltration",
		},
	}

	for _, scenario := range alertScenarios {
		t.Run(scenario.name, func(t *testing.T) {
			// Perform the suspicious action
			scenario.action()

			// Give time for alert generation
			time.Sleep(200 * time.Millisecond)

			// Check if alert was generated (if alerts endpoint exists)
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"GET", "/api/v1/alerts/recent",
				nil,
				suite.adminToken,
			)
			require.NoError(t, err)

			if rec.Code == http.StatusOK {
				var alerts []map[string]interface{}
				json.Unmarshal(rec.Body.Bytes(), &alerts)

				// Should have generated an alert
				alertFound := false
				for _, alert := range alerts {
					if alertType, ok := alert["type"].(string); ok {
						if alertType == scenario.expectedAlert {
							alertFound = true
							break
						}
					}
				}

				require.True(t, alertFound,
					"Should generate alert for: %s", scenario.name)
			}
		})
	}
}

// TestLogIntegrity tests that logs cannot be tampered with
func (suite *A09SecurityLoggingMonitoringTestSuite) TestLogIntegrity() {
	t := suite.T()

	// Test log tampering prevention
	t.Run("Log tampering prevention", func(t *testing.T) {
		// Try to modify audit logs
		tampering := []struct {
			method string
			path   string
			body   interface{}
		}{
			{
				method: "DELETE",
				path:   "/api/v1/audit/logs/12345",
				body:   nil,
			},
			{
				method: "PUT",
				path:   "/api/v1/audit/logs/12345",
				body:   map[string]string{"action": "modified"},
			},
			{
				method: "POST",
				path:   "/api/v1/audit/logs",
				body:   map[string]string{"action": "fake_entry"},
			},
		}

		for _, attempt := range tampering {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				attempt.method, attempt.path, attempt.body, suite.adminToken,
			)
			require.NoError(t, err)

			// Should not allow log modification
			require.NotEqual(t, http.StatusOK, rec.Code,
				"Should not allow log tampering: %s %s", attempt.method, attempt.path)
		}
	})

	// Test log integrity verification
	t.Run("Log integrity verification", func(t *testing.T) {
		// Check if logs have integrity protection
		rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"GET", "/api/v1/audit/logs?limit=10",
			nil,
			suite.adminToken,
		)
		require.NoError(t, err)

		if rec.Code == http.StatusOK {
			var logs []map[string]interface{}
			json.Unmarshal(rec.Body.Bytes(), &logs)

			for _, log := range logs {
				// Logs should have integrity fields
				hasIntegrity := log["hash"] != nil ||
					log["signature"] != nil ||
					log["checksum"] != nil ||
					log["sequence_number"] != nil

				require.True(t, hasIntegrity,
					"Logs should have integrity protection")
			}
		}
	})
}

// TestLogRetention tests proper log retention
func (suite *A09SecurityLoggingMonitoringTestSuite) TestLogRetention() {
	t := suite.T()

	// Test that logs are retained appropriately
	t.Run("Log retention policy", func(t *testing.T) {
		// Check log retention configuration
		rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"GET", "/api/v1/admin/config/logging",
			nil,
			suite.adminToken,
		)
		require.NoError(t, err)

		if rec.Code == http.StatusOK {
			var config map[string]interface{}
			json.Unmarshal(rec.Body.Bytes(), &config)

			// Check retention period
			if retention, ok := config["retention_days"].(float64); ok {
				// Should retain logs for reasonable period
				require.GreaterOrEqual(t, retention, float64(30),
					"Logs should be retained for at least 30 days")
				require.LessOrEqual(t, retention, float64(730),
					"Log retention should not exceed 2 years")
			}

			// Check if different log types have different retention
			if retentionPolicy, ok := config["retention_policy"].(map[string]interface{}); ok {
				// Security logs should have longer retention
				if securityRetention, ok := retentionPolicy["security"].(float64); ok {
					require.GreaterOrEqual(t, securityRetention, float64(90),
						"Security logs should be retained for at least 90 days")
				}
			}
		}
	})
}

// TestLogAccessControl tests access control for logs
func (suite *A09SecurityLoggingMonitoringTestSuite) TestLogAccessControl() {
	t := suite.T()

	// Test that only authorized users can access logs
	t.Run("Log access control", func(t *testing.T) {
		logEndpoints := []string{
			"/api/v1/audit/logs",
			"/api/v1/security/logs",
			"/api/v1/admin/logs",
			"/api/v1/logs/application",
			"/api/v1/logs/access",
		}

		for _, endpoint := range logEndpoints {
			// Regular user should not access logs
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"GET", endpoint, nil, suite.userToken,
			)
			require.NoError(t, err)

			require.True(t,
				rec.Code == http.StatusForbidden || rec.Code == http.StatusNotFound,
				"Regular user should not access logs: %s", endpoint)
		}
	})

	// Test log filtering
	t.Run("Log filtering and sanitization", func(t *testing.T) {
		// Even admins should see sanitized logs
		rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"GET", "/api/v1/audit/logs",
			nil,
			suite.adminToken,
		)
		require.NoError(t, err)

		if rec.Code == http.StatusOK {
			body := rec.Body.String()

			// Should not contain raw sensitive data
			sensitivePatterns := []string{
				"password:",
				"secret:",
				"token:",
				"api_key:",
				"\\d{3}-\\d{2}-\\d{4}", // SSN pattern
				"\\d{16}",              // Credit card pattern
			}

			for _, pattern := range sensitivePatterns {
				require.NotRegexp(t, pattern, body,
					"Logs should not contain pattern: %s", pattern)
			}
		}
	})
}

// TestRealTimeMonitoring tests real-time monitoring capabilities
func (suite *A09SecurityLoggingMonitoringTestSuite) TestRealTimeMonitoring() {
	t := suite.T()

	// Test that critical events are monitored in real-time
	t.Run("Real-time security monitoring", func(t *testing.T) {
		// Check monitoring endpoints
		rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"GET", "/api/v1/monitoring/status",
			nil,
			suite.adminToken,
		)
		require.NoError(t, err)

		if rec.Code == http.StatusOK {
			var status map[string]interface{}
			json.Unmarshal(rec.Body.Bytes(), &status)

			// Should have active monitoring
			if active, ok := status["active"].(bool); ok {
				require.True(t, active,
					"Real-time monitoring should be active")
			}

			// Check monitored metrics
			if metrics, ok := status["metrics"].(map[string]interface{}); ok {
				requiredMetrics := []string{
					"failed_logins",
					"unauthorized_access",
					"rate_limit_violations",
					"error_rate",
				}

				for _, metric := range requiredMetrics {
					require.NotNil(t, metrics[metric],
						"Should monitor metric: %s", metric)
				}
			}
		}
	})
}

// TestIncidentResponse tests incident response capabilities
func (suite *A09SecurityLoggingMonitoringTestSuite) TestIncidentResponse() {
	t := suite.T()

	// Test automated incident response
	t.Run("Automated incident response", func(t *testing.T) {
		// Trigger a security incident
		for i := 0; i < 20; i++ {
			suite.apiSuite.MakeUnauthenticatedRequest(
				"POST", "/api/v1/authn/login",
				map[string]string{
					"phone":    "+16666666666",
					"password": "hack",
				},
			)
		}

		// Check if incident response was triggered
		rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"GET", "/api/v1/incidents/recent",
			nil,
			suite.adminToken,
		)
		require.NoError(t, err)

		if rec.Code == http.StatusOK {
			var incidents []map[string]interface{}
			json.Unmarshal(rec.Body.Bytes(), &incidents)

			// Should have created an incident
			require.NotEmpty(t, incidents,
				"Should create incident for security event")

			// Check incident details
			if len(incidents) > 0 {
				incident := incidents[0]

				// Should have proper incident metadata
				require.NotNil(t, incident["id"])
				require.NotNil(t, incident["type"])
				require.NotNil(t, incident["severity"])
				require.NotNil(t, incident["timestamp"])
				require.NotNil(t, incident["status"])

				// Should have response actions
				if actions, ok := incident["response_actions"].([]interface{}); ok {
					require.NotEmpty(t, actions,
						"Incident should have response actions")
				}
			}
		}
	})
}

// TestComplianceLogging tests compliance-specific logging
func (suite *A09SecurityLoggingMonitoringTestSuite) TestComplianceLogging() {
	t := suite.T()

	// Test GDPR compliance logging
	t.Run("GDPR compliance logging", func(t *testing.T) {
		// Data access request
		rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"GET", "/api/v1/users/me/data",
			nil,
			suite.userToken,
		)
		require.NoError(t, err)

		// Data deletion request
		rec, err = suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"POST", "/api/v1/users/me/delete",
			map[string]string{"reason": "GDPR request"},
			suite.userToken,
		)
		require.NoError(t, err)

		// Check compliance logs
		rec, err = suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"GET", "/api/v1/compliance/logs?type=gdpr",
			nil,
			suite.adminToken,
		)
		require.NoError(t, err)

		if rec.Code == http.StatusOK {
			var logs []map[string]interface{}
			json.Unmarshal(rec.Body.Bytes(), &logs)

			// Should log GDPR-related activities
			require.NotEmpty(t, logs,
				"GDPR activities should be logged")
		}
	})

	// Test PCI compliance logging
	t.Run("PCI compliance logging", func(t *testing.T) {
		// Payment-related activities
		rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"POST", "/api/v1/payments/process",
			map[string]interface{}{
				"amount":     100,
				"card_token": "tok_test_visa",
			},
			suite.userToken,
		)
		require.NoError(t, err)

		// Check PCI compliance logs
		rec, err = suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"GET", "/api/v1/compliance/logs?type=pci",
			nil,
			suite.adminToken,
		)
		require.NoError(t, err)

		if rec.Code == http.StatusOK {
			var logs []map[string]interface{}
			json.Unmarshal(rec.Body.Bytes(), &logs)

			// Payment activities should be logged
			for _, log := range logs {
				// Should not log card details
				require.NotContains(t, fmt.Sprintf("%v", log), "4111")
				require.NotContains(t, fmt.Sprintf("%v", log), "card_number")
			}
		}
	})
}

func TestA09SecurityLoggingMonitoringTestSuite(t *testing.T) {
	suite.Run(t, new(A09SecurityLoggingMonitoringTestSuite))
}
