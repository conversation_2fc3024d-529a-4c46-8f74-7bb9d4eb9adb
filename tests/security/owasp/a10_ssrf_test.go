package owasp

import (
	"encoding/json"
	"fmt"
	"net"
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	"Membership-SAAS-System-Backend/tests/integration"
)

// A10SSRFTestSuite tests OWASP A10:2024 - Server-Side Request Forgery (SSRF)
type A10SSRFTestSuite struct {
	suite.Suite
	apiSuite  *integration.APITestSuite
	userToken string
	orgID     string
}

func (suite *A10SSRFTestSuite) SetupSuite() {
	suite.apiSuite = integration.SetupAPITest(suite.T())

	// Create test user and org
	userResp, err := suite.apiSuite.CreateTestUser("ssrf-test", "+1234567890")
	require.NoError(suite.T(), err)
	suite.userToken = userResp.AccessToken

	org, err := suite.apiSuite.CreateTestOrganization("test-ssrf-org", suite.userToken)
	require.NoError(suite.T(), err)
	suite.orgID = org.ID
}

func (suite *A10SSRFTestSuite) TearDownSuite() {
	suite.apiSuite.TeardownAPITest(suite.T())
}

// TestInternalNetworkAccess tests SSRF to internal networks
func (suite *A10SSRFTestSuite) TestInternalNetworkAccess() {
	t := suite.T()

	// Internal network addresses that should be blocked
	internalURLs := []string{
		// Localhost variations
		"http://localhost:8080/admin",
		"http://127.0.0.1:8080/admin",
		"http://0.0.0.0:8080/admin",
		"http://[::1]:8080/admin",
		"http://127.1:8080/admin",
		"http://2130706433:8080/admin", // 127.0.0.1 as decimal
		"http://0x7f000001:8080/admin", // 127.0.0.1 as hex
		"http://0177.0.0.1:8080/admin", // 127.0.0.1 as octal

		// Private networks
		"http://********/internal",
		"http://**********/internal",
		"http://***********/internal",
		"http://***********:22",
		"http://***************/latest/meta-data/", // AWS metadata

		// Cloud metadata endpoints
		"http://metadata.google.internal/computeMetadata/v1/",
		"http://***************/metadata/instance",
		"http://***************/latest/meta-data/", // Alibaba Cloud

		// Internal services
		"http://elasticsearch:9200/_cat/indices",
		"http://redis:6379/",
		"http://postgres:5432/",
		"http://mongodb:27017/",

		// File protocols
		"file:///etc/passwd",
		"file://C:/Windows/System32/drivers/etc/hosts",
		"gopher://localhost:70/",
		"dict://localhost:11211/",
		"sftp://localhost:22/",
		"tftp://localhost:69/",
		"ldap://localhost:389/",

		// DNS rebinding attempts
		"http://1ynrnhl.xip.io/admin",
		"http://spoofed.burpcollaborator.net/",
	}

	endpoints := []struct {
		path  string
		param string
	}{
		{"/api/v1/webhooks/callback", "url"},
		{"/api/v1/import/remote", "source"},
		{"/api/v1/proxy", "target"},
		{"/api/v1/fetch", "url"},
		{"/api/v1/organizations/%s/logo", "image_url"},
		{"/api/v1/users/avatar", "avatar_url"},
		{"/api/v1/validate/url", "check"},
	}

	for _, endpoint := range endpoints {
		for _, targetURL := range internalURLs {
			t.Run(fmt.Sprintf("%s to %s", endpoint.path, targetURL), func(t *testing.T) {
				path := endpoint.path
				if strings.Contains(path, "%s") {
					path = fmt.Sprintf(path, suite.orgID)
				}

				rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
					"POST", path,
					map[string]interface{}{
						endpoint.param: targetURL,
					},
					suite.userToken,
				)
				require.NoError(t, err)

				// Should block internal network access
				require.NotEqual(t, http.StatusOK, rec.Code,
					"Should block SSRF to: %s", targetURL)

				// Check response doesn't leak internal data
				body := rec.Body.String()
				require.NotContains(t, body, "root:")
				require.NotContains(t, body, "ami-id")
				require.NotContains(t, body, "instance-id")
				require.NotContains(t, body, "elasticsearch")
			})
		}
	}
}

// TestDNSRebinding tests DNS rebinding attacks
func (suite *A10SSRFTestSuite) TestDNSRebinding() {
	t := suite.T()

	// DNS rebinding domains
	rebindingDomains := []string{
		"7f000001.example.com", // Resolves to 127.0.0.1
		"localtest.me",         // Resolves to 127.0.0.1
		"vcap.me",              // Resolves to 127.0.0.1
		"spoofed.example.com",  // Could resolve differently
		"********.xip.io",      // xip.io wildcard DNS
		"192-168-1-1.nip.io",   // nip.io wildcard DNS
	}

	for _, domain := range rebindingDomains {
		t.Run(fmt.Sprintf("DNS rebinding: %s", domain), func(t *testing.T) {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"POST", "/api/v1/webhooks/register",
				map[string]interface{}{
					"url": fmt.Sprintf("http://%s/callback", domain),
				},
				suite.userToken,
			)
			require.NoError(t, err)

			// Should detect and block DNS rebinding
			require.NotEqual(t, http.StatusOK, rec.Code,
				"Should block DNS rebinding domain: %s", domain)
		})
	}
}

// TestURLRedirection tests open redirect via SSRF
func (suite *A10SSRFTestSuite) TestURLRedirection() {
	t := suite.T()

	// Redirect chains that lead to internal resources
	redirectTests := []struct {
		name        string
		url         string
		description string
	}{
		{
			name:        "HTTP redirect to internal",
			url:         "http://example.com/redirect?url=http://localhost:8080",
			description: "External URL redirecting to internal",
		},
		{
			name:        "Multiple redirects",
			url:         "http://bit.ly/2QJ9Pz9", // Shortened URL
			description: "URL shortener hiding internal target",
		},
		{
			name:        "Protocol downgrade",
			url:         "https://example.com/redirect?url=http://metadata.google.internal",
			description: "HTTPS to HTTP redirect",
		},
		{
			name:        "URL encoding bypass",
			url:         "http://example.com/redirect?url=http%3A%2F%2F127.0.0.1",
			description: "URL encoded internal address",
		},
		{
			name:        "Double encoding",
			url:         "http://example.com/redirect?url=http%253A%252F%252F127.0.0.1",
			description: "Double URL encoded",
		},
	}

	for _, test := range redirectTests {
		t.Run(test.name, func(t *testing.T) {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"POST", "/api/v1/fetch/content",
				map[string]interface{}{
					"url":              test.url,
					"follow_redirects": true,
				},
				suite.userToken,
			)
			require.NoError(t, err)

			// Should validate final destination
			require.NotEqual(t, http.StatusOK, rec.Code,
				"Should validate redirect destination: %s", test.description)
		})
	}
}

// TestProtocolSmuggling tests protocol smuggling attacks
func (suite *A10SSRFTestSuite) TestProtocolSmuggling() {
	t := suite.T()

	// Various protocol smuggling attempts
	smugglingTests := []struct {
		protocol string
		payload  string
	}{
		// Gopher protocol for SMTP
		{
			"gopher",
			"gopher://localhost:25/_HELO%20attacker%0d%0aMAIL%20FROM%3A%3Cattacker%40example.com%3E%0d%0a",
		},
		// Dict protocol for Redis
		{
			"dict",
			"dict://localhost:6379/SET%20key%20value",
		},
		// FTP for file access
		{
			"ftp",
			"ftp://anonymous:pass@localhost/etc/passwd",
		},
		// SFTP for SSH
		{
			"sftp",
			"sftp://root:password@localhost:22/",
		},
		// LDAP injection
		{
			"ldap",
			"ldap://localhost:389/cn=admin,dc=example,dc=com",
		},
		// Jar protocol (Java)
		{
			"jar",
			"jar:http://localhost:8080/evil.jar!/",
		},
	}

	for _, test := range smugglingTests {
		t.Run(fmt.Sprintf("Protocol smuggling: %s", test.protocol), func(t *testing.T) {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"POST", "/api/v1/import/url",
				map[string]interface{}{
					"source": test.payload,
				},
				suite.userToken,
			)
			require.NoError(t, err)

			// Should block non-HTTP(S) protocols
			require.NotEqual(t, http.StatusOK, rec.Code,
				"Should block protocol: %s", test.protocol)
		})
	}
}

// TestIPAddressValidation tests IP address validation bypasses
func (suite *A10SSRFTestSuite) TestIPAddressValidation() {
	t := suite.T()

	// Various IP representations
	ipBypassTests := []struct {
		representation string
		actualIP       string
		description    string
	}{
		{"2130706433", "127.0.0.1", "Decimal IP"},
		{"0x7f000001", "127.0.0.1", "Hexadecimal IP"},
		{"0177.0.0.1", "127.0.0.1", "Octal IP"},
		{"127.1", "127.0.0.1", "Short form IP"},
		{"127.0.1", "127.0.0.1", "Short form IP"},
		{"[::ffff:127.0.0.1]", "127.0.0.1", "IPv6 mapped IPv4"},
		{"[0:0:0:0:0:ffff:7f00:0001]", "127.0.0.1", "IPv6 hex"},
		{"localhost.example.com", "127.0.0.1", "Subdomain trick"},
		{"127.0.0.1.example.com", "127.0.0.1", "IP in subdomain"},
		{"www.⑩.⓪.⓪.①", "********", "Unicode numbers"},
	}

	for _, test := range ipBypassTests {
		t.Run(test.description, func(t *testing.T) {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"POST", "/api/v1/url/validate",
				map[string]interface{}{
					"url": fmt.Sprintf("http://%s:8080/admin", test.representation),
				},
				suite.userToken,
			)
			require.NoError(t, err)

			// Should detect and block all representations
			require.NotEqual(t, http.StatusOK, rec.Code,
				"Should block IP representation: %s (%s)", test.representation, test.description)
		})
	}
}

// TestTimeOfCheckTimeOfUse tests TOCTOU vulnerabilities
func (suite *A10SSRFTestSuite) TestTimeOfCheckTimeOfUse() {
	t := suite.T()

	// Test race condition in URL validation
	t.Run("TOCTOU URL validation", func(t *testing.T) {
		// This tests if the application properly validates URLs at the time of use
		// not just at the time of check

		// First, register a webhook with a valid external URL
		rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"POST", "/api/v1/webhooks/register",
			map[string]interface{}{
				"url":    "https://example.com/webhook",
				"events": []string{"user.created"},
			},
			suite.userToken,
		)
		require.NoError(t, err)

		if rec.Code == http.StatusOK {
			var resp map[string]interface{}
			json.Unmarshal(rec.Body.Bytes(), &resp)

			if webhookID, ok := resp["id"].(string); ok {
				// Try to update the webhook to an internal URL
				rec, err = suite.apiSuite.MakeAuthenticatedRequestWithToken(
					"PATCH", fmt.Sprintf("/api/v1/webhooks/%s", webhookID),
					map[string]interface{}{
						"url": "http://localhost:8080/internal",
					},
					suite.userToken,
				)
				require.NoError(t, err)

				// Should validate on update as well
				require.NotEqual(t, http.StatusOK, rec.Code,
					"Should validate URL on update")
			}
		}
	})
}

// TestRequestSplitting tests HTTP request splitting via SSRF
func (suite *A10SSRFTestSuite) TestRequestSplitting() {
	t := suite.T()

	// HTTP request splitting payloads
	splittingPayloads := []string{
		"http://example.com/path%0d%0aHost:%20localhost",
		"http://example.com/path%0d%0a%0d%0aGET%20/admin%20HTTP/1.1%0d%0aHost:%20localhost",
		"http://example.com/path\r\nX-Injected: header",
		"http://example.com/path\nContent-Length: 0\r\n\r\nGET /internal HTTP/1.1",
		"http://example.com/path%20HTTP/1.1%0d%0aHost:%20localhost%0d%0a",
	}

	for i, payload := range splittingPayloads {
		t.Run(fmt.Sprintf("Request splitting %d", i), func(t *testing.T) {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"POST", "/api/v1/proxy/request",
				map[string]interface{}{
					"url": payload,
				},
				suite.userToken,
			)
			require.NoError(t, err)

			// Should detect and block request splitting
			require.NotEqual(t, http.StatusOK, rec.Code,
				"Should block request splitting attempt")
		})
	}
}

// TestPartialURLValidation tests partial/incomplete URL validation
func (suite *A10SSRFTestSuite) TestPartialURLValidation() {
	t := suite.T()

	// Test various URL parsing edge cases
	edgeCaseURLs := []struct {
		url         string
		description string
	}{
		{"//localhost/path", "Protocol-relative URL"},
		{"///localhost/path", "Triple slash"},
		{"http:///localhost/path", "Empty host with local path"},
		{"http://[::1]:8080/", "IPv6 localhost"},
		{"http://0.0.0.0:8080/", "All interfaces"},
		{"http://0:8080/", "Zero IP"},
		{"http://example.com@localhost:8080/", "Credentials trick"},
		{"http://localhost#@example.com/", "Fragment trick"},
		{"http://foo@127.0.0.1:<EMAIL>/", "Double @ trick"},
		{"http://127.0.0.1:<EMAIL>/", "Encoded slash"},
	}

	for _, test := range edgeCaseURLs {
		t.Run(test.description, func(t *testing.T) {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"POST", "/api/v1/fetch",
				map[string]interface{}{
					"url": test.url,
				},
				suite.userToken,
			)
			require.NoError(t, err)

			// Should properly validate all URL forms
			require.NotEqual(t, http.StatusOK, rec.Code,
				"Should validate URL: %s (%s)", test.url, test.description)
		})
	}
}

// TestWhitelistBypass tests whitelist bypass techniques
func (suite *A10SSRFTestSuite) TestWhitelistBypass() {
	t := suite.T()

	// Assuming example.com is whitelisted
	whitelistBypassTests := []struct {
		url         string
		description string
	}{
		{"http://example.com.attacker.com/", "Subdomain of attacker"},
		{"http://attacker.com/example.com", "Path contains whitelist"},
		{"http://<EMAIL>/", "Credentials bypass"},
		{"http://example.com#.attacker.com/", "Fragment bypass"},
		{"http://example.com?.attacker.com/", "Query bypass"},
		{"http://example.com%2fattacker.com/", "Encoded slash"},
		{"http://ⓔⓧⓐⓜⓟⓛⓔ.ⓒⓞⓜ/", "Unicode lookalike"},
		{"http://еxamplе.com/", "Cyrillic lookalike"},
	}

	for _, test := range whitelistBypassTests {
		t.Run(test.description, func(t *testing.T) {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"POST", "/api/v1/webhooks/validate",
				map[string]interface{}{
					"url": test.url,
				},
				suite.userToken,
			)
			require.NoError(t, err)

			// Should detect whitelist bypass attempts
			if rec.Code == http.StatusOK {
				var resp map[string]interface{}
				json.Unmarshal(rec.Body.Bytes(), &resp)

				// If validation passed, check it's actually safe
				if valid, ok := resp["valid"].(bool); ok && valid {
					if host, ok := resp["host"].(string); ok {
						require.Equal(t, "example.com", host,
							"Should not bypass whitelist: %s", test.description)
					}
				}
			}
		})
	}
}

// TestSSRFChaining tests chaining SSRF with other vulnerabilities
func (suite *A10SSRFTestSuite) TestSSRFChaining() {
	t := suite.T()

	// Test SSRF to XXE
	t.Run("SSRF to XXE chain", func(t *testing.T) {
		// Try to fetch XML with external entities
		rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"POST", "/api/v1/import/xml",
			map[string]interface{}{
				"url": "http://attacker.com/xxe.xml",
			},
			suite.userToken,
		)
		require.NoError(t, err)

		// Should validate content even from external sources
		require.NotEqual(t, http.StatusOK, rec.Code,
			"Should validate external XML content")
	})

	// Test SSRF to SQL injection
	t.Run("SSRF to SQLi chain", func(t *testing.T) {
		// Try to make internal request with SQL injection
		rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
			"POST", "/api/v1/proxy",
			map[string]interface{}{
				"url": "http://localhost:8080/api/users?id=1' OR '1'='1",
			},
			suite.userToken,
		)
		require.NoError(t, err)

		// Should block internal requests regardless of payload
		require.NotEqual(t, http.StatusOK, rec.Code,
			"Should block SSRF even with SQLi payload")
	})
}

// TestPortScanning tests using SSRF for port scanning
func (suite *A10SSRFTestSuite) TestPortScanning() {
	t := suite.T()

	// Common ports that might be scanned
	scanPorts := []int{
		21,    // FTP
		22,    // SSH
		23,    // Telnet
		25,    // SMTP
		80,    // HTTP
		443,   // HTTPS
		3306,  // MySQL
		5432,  // PostgreSQL
		6379,  // Redis
		9200,  // Elasticsearch
		27017, // MongoDB
	}

	for _, port := range scanPorts {
		t.Run(fmt.Sprintf("Port scan prevention: %d", port), func(t *testing.T) {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"POST", "/api/v1/url/check",
				map[string]interface{}{
					"url": fmt.Sprintf("http://localhost:%d/", port),
				},
				suite.userToken,
			)
			require.NoError(t, err)

			// Should not allow port scanning
			require.NotEqual(t, http.StatusOK, rec.Code,
				"Should block port scanning on port: %d", port)

			// Response shouldn't indicate port status
			body := rec.Body.String()
			require.NotContains(t, body, "refused")
			require.NotContains(t, body, "timeout")
			require.NotContains(t, body, "unreachable")
		})
	}
}

// TestBlindSSRF tests blind SSRF detection
func (suite *A10SSRFTestSuite) TestBlindSSRF() {
	t := suite.T()

	// Test out-of-band detection methods
	t.Run("Blind SSRF detection", func(t *testing.T) {
		// URLs that might not return content but still make requests
		blindTargets := []string{
			"http://webhook.site/unique-id",
			"http://burpcollaborator.net/test",
			"http://localhost:8080/slow-endpoint",
			"http://example.com/404",
		}

		for _, target := range blindTargets {
			rec, err := suite.apiSuite.MakeAuthenticatedRequestWithToken(
				"POST", "/api/v1/background/fetch",
				map[string]interface{}{
					"url":   target,
					"async": true,
				},
				suite.userToken,
			)
			require.NoError(t, err)

			// Even async/background requests should be validated
			if strings.Contains(target, "localhost") {
				require.NotEqual(t, http.StatusAccepted, rec.Code,
					"Should validate async requests: %s", target)
			}
		}
	})
}

// Helper function to check if IP is private
func isPrivateIP(ip string) bool {
	privateRanges := []string{
		"10.0.0.0/8",
		"**********/12",
		"***********/16",
		"*********/8",
		"***********/16",
		"::1/128",
		"fc00::/7",
		"fe80::/10",
	}

	parsedIP := net.ParseIP(ip)
	if parsedIP == nil {
		return false
	}

	for _, cidr := range privateRanges {
		_, network, err := net.ParseCIDR(cidr)
		if err != nil {
			continue
		}
		if network.Contains(parsedIP) {
			return true
		}
	}

	return parsedIP.IsLoopback() || parsedIP.IsPrivate()
}

func TestA10SSRFTestSuite(t *testing.T) {
	suite.Run(t, new(A10SSRFTestSuite))
}
