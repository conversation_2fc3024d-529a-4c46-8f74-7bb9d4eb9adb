package owasp

import (
	"testing"

	"github.com/stretchr/testify/suite"
)

// OWASPTestSuite runs all OWASP Top 10 2024 security tests
type OWASPTestSuite struct {
	suite.Suite
}

// TestOWASPTop10Suite runs the complete OWASP Top 10 test suite
func TestOWASPTop10Suite(t *testing.T) {
	// Run each OWASP category test suite
	t.Run("A01_BrokenAccessControl", func(t *testing.T) {
		suite.Run(t, new(A01BrokenAccessControlTestSuite))
	})

	t.Run("A02_CryptographicFailures", func(t *testing.T) {
		suite.Run(t, new(A02CryptographicFailuresTestSuite))
	})

	t.Run("A03_Injection", func(t *testing.T) {
		suite.Run(t, new(A03InjectionTestSuite))
	})

	t.Run("A04_InsecureDesign", func(t *testing.T) {
		suite.Run(t, new(A04InsecureDesignTestSuite))
	})

	t.Run("A05_SecurityMisconfiguration", func(t *testing.T) {
		suite.Run(t, new(A05SecurityMisconfigurationTestSuite))
	})

	// A06: Vulnerable and Outdated Components is already covered in the existing test file
	// Skip here to avoid duplication

	t.Run("A07_IdentificationAuthentication", func(t *testing.T) {
		suite.Run(t, new(A07IdentificationAuthenticationTestSuite))
	})

	t.Run("A08_SoftwareDataIntegrity", func(t *testing.T) {
		suite.Run(t, new(A08SoftwareDataIntegrityTestSuite))
	})

	t.Run("A09_SecurityLoggingMonitoring", func(t *testing.T) {
		suite.Run(t, new(A09SecurityLoggingMonitoringTestSuite))
	})

	t.Run("A10_SSRF", func(t *testing.T) {
		suite.Run(t, new(A10SSRFTestSuite))
	})
}

// TestOWASPCritical runs only critical security tests for CI/CD
func TestOWASPCritical(t *testing.T) {
	// Run only the most critical tests for faster CI/CD
	criticalTests := []struct {
		name  string
		suite suite.TestingSuite
	}{
		{"A01_BrokenAccessControl", new(A01BrokenAccessControlTestSuite)},
		{"A03_Injection", new(A03InjectionTestSuite)},
		{"A07_Authentication", new(A07IdentificationAuthenticationTestSuite)},
		{"A10_SSRF", new(A10SSRFTestSuite)},
	}

	for _, test := range criticalTests {
		t.Run(test.name, func(t *testing.T) {
			suite.Run(t, test.suite)
		})
	}
}
