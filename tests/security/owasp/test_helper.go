package owasp

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/db"
	"Membership-SAAS-System-Backend/internal/config"
	"Membership-SAAS-System-Backend/internal/server"
	"Membership-SAAS-System-Backend/internal/token"
	"Membership-SAAS-System-Backend/internal/utils"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/stretchr/testify/require"
)

// OWASPTestUser represents a test user with authentication details
type OWASPTestUser struct {
	User         db.User
	Token        string
	RefreshToken string
	Organization *db.Organization
}

// SetupOWASPTestUser creates a test user with organization creation permissions
func SetupOWASPTestUser(t *testing.T, store db.Store, platformRole string) (*OWASPTestUser, error) {
	ctx := context.Background()

	// Create a test user with specified platform role
	phoneNumber := fmt.Sprintf("+1%010d", time.Now().UnixNano()%10000000000)

	var user db.User
	var err error

	if platformRole == "super_admin" || platformRole == "staff" {
		// Create staff user
		createParams := db.CreateStaffUserWithEmailPasswordParams{
			DisplayName:                 "OWASP Test Staff User",
			Email:                       utils.StringPtr(fmt.Sprintf("<EMAIL>", time.Now().UnixNano())),
			EmailVerifiedAt:             utils.TimePtr(time.Now()),
			HashedPassword:              utils.StringPtr("$2a$10$dummy.hashed.password"),
			InterfaceLanguage:           "en",
			CommunicationLanguage:       "en",
			EnableAppNotifications:      true,
			EnableWhatsappNotifications: false,
			EnableSmsNotifications:      false,
			EnableEmailNotifications:    true,
			PhoneOtpChannel:             "sms",
		}
		user, err = store.CreateStaffUserWithEmailPassword(ctx, createParams)
	} else {
		// Create regular user
		createParams := db.CreateUserWithPhoneParams{
			DisplayName:                 "OWASP Test User",
			Phone:                       &phoneNumber,
			PhoneVerifiedAt:             utils.TimePtr(time.Now()),
			InterfaceLanguage:           "en",
			CommunicationLanguage:       "en",
			EnableAppNotifications:      true,
			EnableWhatsappNotifications: false,
			EnableSmsNotifications:      true,
			EnableEmailNotifications:    false,
			PhoneOtpChannel:             "sms",
		}
		user, err = store.CreateUserWithPhone(ctx, createParams)
	}
	if err != nil {
		return nil, fmt.Errorf("failed to create test user: %w", err)
	}

	// Generate JWT token
	tokenString, refreshToken, err := generateOWASPTestToken(user)
	if err != nil {
		return nil, fmt.Errorf("failed to generate token: %w", err)
	}

	// Create a test organization for the user
	org, err := createTestOrganization(ctx, store, user.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to create test organization: %w", err)
	}

	return &OWASPTestUser{
		User:         user,
		Token:        tokenString,
		RefreshToken: refreshToken,
		Organization: org,
	}, nil
}

// generateOWASPTestToken generates a JWT token for testing
func generateOWASPTestToken(user db.User) (string, string, error) {
	// Use the enhanced claims structure
	claims := &token.EnhancedClaims{
		UserID:       user.ID,
		PlatformRole: string(user.PlatformRole),
		ActiveOrg:    nil, // Will be set when user switches to org
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "the-moment-test",
			Subject:   user.ID.String(),
		},
	}

	// Create access token
	jwtToken := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	// Use test secret that matches test environment setup
	tokenString, err := jwtToken.SignedString([]byte("test-secret-key-for-testing"))
	if err != nil {
		return "", "", err
	}

	// Create refresh token with longer expiry
	refreshClaims := *claims
	refreshClaims.ExpiresAt = jwt.NewNumericDate(time.Now().Add(7 * 24 * time.Hour))
	refreshJWT := jwt.NewWithClaims(jwt.SigningMethodHS256, &refreshClaims)
	refreshToken, err := refreshJWT.SignedString([]byte("test-refresh-secret-key-for-testing"))
	if err != nil {
		return "", "", err
	}

	return tokenString, refreshToken, nil
}

// createTestOrganization creates a test organization for the user
func createTestOrganization(ctx context.Context, store db.Store, userID uuid.UUID) (*db.Organization, error) {
	createOrgParams := db.CreateOrganizationParams{
		Name:        fmt.Sprintf("OWASP Test Org %s", uuid.New().String()[:8]),
		Description: utils.StringPtr("Organization for OWASP security testing"),
		OwnerUserID: userID,
		Status:      "active",
	}

	// Use transaction to create org and add user as owner
	var org db.Organization
	err := store.ExecTx(ctx, func(q db.Querier) error {
		// Create organization
		createdOrg, err := q.CreateOrganization(ctx, createOrgParams)
		if err != nil {
			return fmt.Errorf("failed to create organization: %w", err)
		}
		org = createdOrg

		// Add user as owner
		memberParams := db.AddUserToOrganizationParams{
			UserID:               userID,
			OrganizationID:       createdOrg.ID,
			Role:                 "owner",
			IsActive:             true,
			NotificationsEnabled: true,
		}
		_, err = q.AddUserToOrganization(ctx, memberParams)
		if err != nil {
			return fmt.Errorf("failed to add user to organization: %w", err)
		}

		return nil
	})
	if err != nil {
		return nil, err
	}

	return &org, nil
}

// SetupOWASPTestUserWithRole creates a test user with a specific organization role
func SetupOWASPTestUserWithRole(t *testing.T, store db.Store, platformRole string, orgRole string) (*OWASPTestUser, error) {
	// First create the user with platform role
	testUser, err := SetupOWASPTestUser(t, store, platformRole)
	if err != nil {
		return nil, err
	}

	// Update the user's role in the organization if different from owner
	if orgRole != "owner" {
		ctx := context.Background()
		updateParams := db.UpdateOrganizationMemberRoleParams{
			UserID:         testUser.User.ID,
			OrganizationID: testUser.Organization.ID,
			Role:           orgRole,
		}
		err = store.UpdateOrganizationMemberRole(ctx, updateParams)
		if err != nil {
			return nil, fmt.Errorf("failed to update organization role: %w", err)
		}
	}

	// Update the token to include organization context
	claims := &token.EnhancedClaims{
		UserID:       testUser.User.ID,
		PlatformRole: string(testUser.User.PlatformRole),
		ActiveOrg: &token.OrgContext{
			ID:   testUser.Organization.ID.String(),
			Role: orgRole,
		},
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "the-moment-test",
			Subject:   testUser.User.ID.String(),
		},
	}

	jwtToken := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := jwtToken.SignedString([]byte("test-secret-key-for-testing"))
	if err != nil {
		return nil, fmt.Errorf("failed to generate token with org context: %w", err)
	}

	testUser.Token = tokenString
	return testUser, nil
}

// CreateTestRequest creates an HTTP request with authentication
func CreateTestRequest(method, url string, body interface{}, token string) (*http.Request, error) {
	var reqBody []byte
	var err error

	if body != nil {
		reqBody, err = json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request body: %w", err)
		}
	}

	req := httptest.NewRequest(method, url, bytes.NewReader(reqBody))
	req.Header.Set("Content-Type", "application/json")
	if token != "" {
		req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token))
	}

	return req, nil
}

// PerformTestRequest executes a test request and returns the response
func PerformTestRequest(e *echo.Echo, req *http.Request) *httptest.ResponseRecorder {
	rec := httptest.NewRecorder()
	e.ServeHTTP(rec, req)
	return rec
}

// SetupOWASPTestServer creates a test server instance
func SetupOWASPTestServer(t *testing.T, cfg *config.Config) *echo.Echo {
	// Create server instance
	srv, err := server.New(cfg)
	require.NoError(t, err)

	// Get the echo instance
	e := srv.Echo()

	return e
}

// CleanupTestUser removes test user and associated data
func CleanupTestUser(t *testing.T, store db.Store, userID uuid.UUID) {
	// Note: For integration tests, we typically rely on:
	// 1. Transaction rollback for test isolation
	// 2. Test database cleanup between test runs
	// 3. Unique test data prefixes to avoid conflicts

	// If explicit cleanup is needed, it would require custom SQL queries
	// as SQLC doesn't generate delete methods for users
	t.Logf("Test user %s cleanup deferred to transaction rollback", userID)
}
