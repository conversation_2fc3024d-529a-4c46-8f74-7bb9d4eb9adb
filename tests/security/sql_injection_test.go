package security

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"

	"Membership-SAAS-System-Backend/tests/integration"
)

// SQLInjectionTestSuite tests SQL injection prevention across all database endpoints
type SQLInjectionTestSuite struct {
	suite.Suite
	apiSuite *integration.APITestSuite
}

func (suite *SQLInjectionTestSuite) SetupSuite() {
	suite.apiSuite = integration.SetupAPITest(suite.T())
}

func (suite *SQLInjectionTestSuite) TearDownSuite() {
	suite.apiSuite.TeardownAPITest(suite.T())
}

func (suite *SQLInjectionTestSuite) SetupTest() {
	suite.apiSuite.CleanupTestData(suite.T())
}

// Common SQL injection payloads
var sqlInjectionPayloads = []string{
	"'; DROP TABLE users; --",
	"' OR '1'='1",
	"' OR 1=1 --",
	"'; SELECT * FROM users WHERE 'x'='x",
	"' UNION SELECT password FROM users --",
	"'; INSERT INTO users (email) VALUES ('<EMAIL>'); --",
	"' OR EXISTS(SELECT * FROM users) --",
	"'; UPDATE users SET email='<EMAIL>' WHERE id='1'; --",
	"' AND (SELECT COUNT(*) FROM users) > 0 --",
	"\"; DROP DATABASE testdb; --",
	"'; EXEC xp_cmdshell('dir'); --",
	"' OR 'x'='x' /*",
	"' OR 1=1#",
	"'; WAITFOR DELAY '00:00:05'; --",
	"' OR SLEEP(5) --",
}

// TestUserEndpointsAgainstSQLInjection tests user-related endpoints
func (suite *SQLInjectionTestSuite) TestUserEndpointsAgainstSQLInjection() {
	t := suite.T()

	for _, payload := range sqlInjectionPayloads {
		t.Run(fmt.Sprintf("User endpoints with payload: %s", strings.ReplaceAll(payload, "'", "\\'")), func(t *testing.T) {
			// Test user profile endpoint with malicious user ID in URL
			rec, err := suite.apiSuite.MakeAuthenticatedRequest("GET", fmt.Sprintf("/api/v1/users/%s", payload), nil)
			require.NoError(t, err)

			// Should return 400/404, not 500 (which could indicate SQL error)
			require.True(t, rec.Code == http.StatusBadRequest || rec.Code == http.StatusNotFound || rec.Code == http.StatusUnauthorized,
				"Expected 400/404/401, got %d for payload %s. Response: %s", rec.Code, payload, rec.Body.String())

			// Test user update with malicious email
			updateReq := map[string]interface{}{
				"email":        payload,
				"display_name": "Test User",
			}
			rec, err = suite.apiSuite.MakeAuthenticatedRequest("PATCH", "/api/v1/users/me", updateReq)
			require.NoError(t, err)

			// Should return validation error, not SQL error
			require.True(t, rec.Code == http.StatusBadRequest || rec.Code == http.StatusUnprocessableEntity,
				"Expected 400/422 for malicious email, got %d. Response: %s", rec.Code, rec.Body.String())

			// Ensure response doesn't contain SQL error messages
			body := rec.Body.String()
			require.NotContains(t, strings.ToLower(body), "sql")
			require.NotContains(t, strings.ToLower(body), "syntax error")
			require.NotContains(t, strings.ToLower(body), "postgresql")
			require.NotContains(t, strings.ToLower(body), "relation")
		})
	}
}

// TestAuthEndpointsAgainstSQLInjection tests authentication endpoints
func (suite *SQLInjectionTestSuite) TestAuthEndpointsAgainstSQLInjection() {
	t := suite.T()

	for _, payload := range sqlInjectionPayloads {
		t.Run(fmt.Sprintf("Auth endpoints with payload: %s", strings.ReplaceAll(payload, "'", "\\'")), func(t *testing.T) {
			// Test phone check with malicious phone number
			checkReq := map[string]interface{}{
				"phone": payload,
			}
			rec, err := suite.apiSuite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/phone/check", checkReq)
			require.NoError(t, err)

			// Should handle gracefully, not return SQL errors
			body := rec.Body.String()
			require.NotContains(t, strings.ToLower(body), "sql")
			require.NotContains(t, strings.ToLower(body), "syntax error")
			require.NotContains(t, strings.ToLower(body), "postgresql")

			// Test staff login with malicious email
			staffReq := map[string]interface{}{
				"email":                 payload,
				"client_id":             "test-client",
				"code_challenge":        "test-challenge",
				"code_challenge_method": "S256",
				"state":                 "test-state",
			}
			rec, err = suite.apiSuite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/staff/login/initiate", staffReq)
			require.NoError(t, err)

			// Should return validation error or not found, not SQL error
			body = rec.Body.String()
			require.NotContains(t, strings.ToLower(body), "sql")
			require.NotContains(t, strings.ToLower(body), "syntax error")
			require.NotContains(t, strings.ToLower(body), "postgresql")
		})
	}
}

// TestOrganizationEndpointsAgainstSQLInjection tests organization endpoints
func (suite *SQLInjectionTestSuite) TestOrganizationEndpointsAgainstSQLInjection() {
	t := suite.T()

	for _, payload := range sqlInjectionPayloads {
		t.Run(fmt.Sprintf("Organization endpoints with payload: %s", strings.ReplaceAll(payload, "'", "\\'")), func(t *testing.T) {
			// Test get organization with malicious ID
			rec, err := suite.apiSuite.MakeAuthenticatedRequest("GET", fmt.Sprintf("/api/v1/organizations/%s", payload), nil)
			require.NoError(t, err)

			// Should return 400/404, not 500
			require.True(t, rec.Code == http.StatusBadRequest || rec.Code == http.StatusNotFound || rec.Code == http.StatusUnauthorized,
				"Expected 400/404/401, got %d for organization get. Response: %s", rec.Code, rec.Body.String())

			// Test organization update with malicious data
			updateReq := map[string]interface{}{
				"name":        payload,
				"description": "Test organization",
			}
			rec, err = suite.apiSuite.MakeAuthenticatedRequest("PATCH", fmt.Sprintf("/api/v1/organizations/%s", suite.apiSuite.GetOrgID()), updateReq)
			require.NoError(t, err)

			// Should handle validation gracefully
			body := rec.Body.String()
			require.NotContains(t, strings.ToLower(body), "sql")
			require.NotContains(t, strings.ToLower(body), "syntax error")
		})
	}
}

// TestEventEndpointsAgainstSQLInjection tests event-related endpoints
func (suite *SQLInjectionTestSuite) TestEventEndpointsAgainstSQLInjection() {
	t := suite.T()

	for _, payload := range sqlInjectionPayloads {
		t.Run(fmt.Sprintf("Event endpoints with payload: %s", strings.ReplaceAll(payload, "'", "\\'")), func(t *testing.T) {
			// Test get event with malicious ID
			rec, err := suite.apiSuite.MakeAuthenticatedRequest("GET", fmt.Sprintf("/api/v1/events/%s", payload), nil)
			require.NoError(t, err)

			// Should return 400/404, not 500
			require.True(t, rec.Code == http.StatusBadRequest || rec.Code == http.StatusNotFound || rec.Code == http.StatusUnauthorized,
				"Expected 400/404/401 for event get, got %d. Response: %s", rec.Code, rec.Body.String())

			// Test event creation with malicious data
			eventReq := map[string]interface{}{
				"title":       payload,
				"description": "Test event",
				"start_date":  "2024-12-31T10:00:00Z",
				"end_date":    "2024-12-31T12:00:00Z",
				"location":    "Test Location",
			}
			rec, err = suite.apiSuite.MakeAuthenticatedRequest("POST", fmt.Sprintf("/api/v1/organizations/%s/events", suite.apiSuite.GetOrgID()), eventReq)
			require.NoError(t, err)

			// Should handle validation gracefully
			body := rec.Body.String()
			require.NotContains(t, strings.ToLower(body), "sql")
			require.NotContains(t, strings.ToLower(body), "syntax error")
		})
	}
}

// TestSearchEndpointsAgainstSQLInjection tests search and filter endpoints
func (suite *SQLInjectionTestSuite) TestSearchEndpointsAgainstSQLInjection() {
	t := suite.T()

	for _, payload := range sqlInjectionPayloads {
		t.Run(fmt.Sprintf("Search endpoints with payload: %s", strings.ReplaceAll(payload, "'", "\\'")), func(t *testing.T) {
			// Test events search with malicious query
			rec, err := suite.apiSuite.MakeAuthenticatedRequest("GET", fmt.Sprintf("/api/v1/events?search=%s", payload), nil)
			require.NoError(t, err)

			// Should handle search gracefully
			body := rec.Body.String()
			require.NotContains(t, strings.ToLower(body), "sql")
			require.NotContains(t, strings.ToLower(body), "syntax error")
			require.NotContains(t, strings.ToLower(body), "postgresql")

			// Test with malicious filter parameters
			rec, err = suite.apiSuite.MakeAuthenticatedRequest("GET", fmt.Sprintf("/api/v1/events?title=%s&location=%s", payload, payload), nil)
			require.NoError(t, err)

			body = rec.Body.String()
			require.NotContains(t, strings.ToLower(body), "sql")
			require.NotContains(t, strings.ToLower(body), "syntax error")
		})
	}
}

// TestParameterizedQueriesProtection verifies SQLC generated queries are safe
func (suite *SQLInjectionTestSuite) TestParameterizedQueriesProtection() {
	t := suite.T()

	// Test with a known safe endpoint that uses parameterized queries
	// This should work normally with valid data
	validReq := map[string]interface{}{
		"phone": "+1234567890",
	}
	rec, err := suite.apiSuite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/phone/check", validReq)
	require.NoError(t, err)
	require.Equal(t, http.StatusOK, rec.Code)

	// Parse response to ensure it's valid JSON
	var response map[string]interface{}
	err = json.Unmarshal(rec.Body.Bytes(), &response)
	require.NoError(t, err)
	require.Contains(t, response, "exists")

	// Now test with SQL injection attempt - should be safely handled
	maliciousReq := map[string]interface{}{
		"phone": "'; DROP TABLE users; --",
	}
	rec, err = suite.apiSuite.MakeUnauthenticatedRequest("POST", "/api/v1/authn/phone/check", maliciousReq)
	require.NoError(t, err)

	// Should still return structured response, not SQL error
	body := rec.Body.String()
	require.NotContains(t, strings.ToLower(body), "sql")
	require.NotContains(t, strings.ToLower(body), "syntax error")
}

// TestBulkOperationsAgainstSQLInjection tests batch/bulk operations
func (suite *SQLInjectionTestSuite) TestBulkOperationsAgainstSQLInjection() {
	t := suite.T()

	// Test bulk operations with malicious data in arrays
	maliciousPayload := "'; DROP TABLE events; --"

	// Test bulk event creation (if such endpoint exists)
	bulkEventReq := map[string]interface{}{
		"events": []map[string]interface{}{
			{
				"title":       maliciousPayload,
				"description": "Test event 1",
				"start_date":  "2024-12-31T10:00:00Z",
				"end_date":    "2024-12-31T12:00:00Z",
				"location":    "Test Location",
			},
			{
				"title":       "Valid Event",
				"description": maliciousPayload,
				"start_date":  "2024-12-31T14:00:00Z",
				"end_date":    "2024-12-31T16:00:00Z",
				"location":    "Test Location",
			},
		},
	}

	// Try bulk operation endpoint (may not exist, but test gracefully)
	rec, err := suite.apiSuite.MakeAuthenticatedRequest("POST", fmt.Sprintf("/api/v1/organizations/%s/events/bulk", suite.apiSuite.GetOrgID()), bulkEventReq)
	require.NoError(t, err)

	// Should handle gracefully regardless of whether endpoint exists
	body := rec.Body.String()
	require.NotContains(t, strings.ToLower(body), "sql")
	require.NotContains(t, strings.ToLower(body), "syntax error")
}

// TestNumericParameterInjection tests injection through numeric parameters
func (suite *SQLInjectionTestSuite) TestNumericParameterInjection() {
	t := suite.T()

	numericPayloads := []string{
		"1; DROP TABLE users; --",
		"1 OR 1=1",
		"1 UNION SELECT * FROM users",
		"1'; DROP TABLE events; --",
		"-1 OR 1=1",
		"0x31",
		"1e10",
		"1.1 OR 1=1",
	}

	for _, payload := range numericPayloads {
		t.Run(fmt.Sprintf("Numeric injection with: %s", payload), func(t *testing.T) {
			// Test pagination parameters with malicious values
			rec, err := suite.apiSuite.MakeAuthenticatedRequest("GET", fmt.Sprintf("/api/v1/events?page=%s&limit=%s", payload, payload), nil)
			require.NoError(t, err)

			// Should handle gracefully
			body := rec.Body.String()
			require.NotContains(t, strings.ToLower(body), "sql")
			require.NotContains(t, strings.ToLower(body), "syntax error")
		})
	}
}

func TestSQLInjectionTestSuite(t *testing.T) {
	suite.Run(t, new(SQLInjectionTestSuite))
}
