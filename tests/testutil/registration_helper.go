package testutil

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/require"
)

// RegistrationHelper provides utilities for test user registration with dynamic OTP
type RegistrationHelper struct {
	baseURL string
}

// NewRegistrationHelper creates a new registration helper
func NewRegistrationHelper(baseURL string) *RegistrationHelper {
	return &RegistrationHelper{
		baseURL: baseURL,
	}
}

// RegisterUserWithDynamicOTP registers a user and retrieves the dynamic OTP for verification
func (h *RegistrationHelper) RegisterUserWithDynamicOTP(t *testing.T, phone, email, firstName, lastName string) (otpSid, otp string) {
	// Initiate registration
	regInitiateReq := map[string]interface{}{
		"phone_number":   phone,
		"email":          email,
		"first_name":     firstName,
		"last_name":      lastName,
		"code_challenge": fmt.Sprintf("test-challenge-%s", phone),
		"state":          fmt.Sprintf("test-state-%s", phone),
		"preferred_lang": "en",
	}

	// Make registration initiate request
	resp, err := makeJSONRequest("POST", h.baseURL+"/api/v1/authn/register/phone/initiate", regInitiateReq)
	require.NoError(t, err)
	require.Equal(t, http.StatusOK, resp.StatusCode)

	// Parse response to get flow ID
	var initiateResp map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&initiateResp)
	require.NoError(t, err)
	resp.Body.Close()

	// Extract OTP SID from the response (included in test mode)
	otpSid, ok := initiateResp["otp_sid"].(string)
	require.True(t, ok, "otp_sid not found in response - ensure the backend is running in test mode")

	// Retrieve the dynamic OTP from the test endpoint
	otp = h.GetTestOTP(t, otpSid)

	return otpSid, otp
}

// GetTestOTP retrieves a test OTP for the given SID
func (h *RegistrationHelper) GetTestOTP(t *testing.T, otpSid string) string {
	resp, err := http.Get(fmt.Sprintf("%s/api/v1/test/otp/%s", h.baseURL, otpSid))
	require.NoError(t, err)
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNotFound {
		t.Fatalf("OTP not found for SID: %s", otpSid)
	}
	require.Equal(t, http.StatusOK, resp.StatusCode)

	var otpResp map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&otpResp)
	require.NoError(t, err)

	otp, ok := otpResp["otp"].(string)
	require.True(t, ok, "otp not found in response")

	return otp
}

// Helper function to make JSON requests
func makeJSONRequest(method, url string, body interface{}) (*http.Response, error) {
	jsonBody, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest(method, url, bytes.NewReader(jsonBody))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	client := &http.Client{}
	return client.Do(req)
}
