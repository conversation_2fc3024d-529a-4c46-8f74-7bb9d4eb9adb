package unit

import (
	"testing"

	"Membership-SAAS-System-Backend/internal/authorization"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestCan_PlatformRoles(t *testing.T) {
	userID := uuid.New()
	resourceID := uuid.New()

	tests := []struct {
		name         string
		params       authorization.CanParams
		expectedAuth bool
	}{
		// Platform SuperAdmin tests
		{
			name: "superadmin_can_do_anything",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleSuperAdmin,
				Resource:     authorization.ResourceUser,
				Action:       authorization.ActionDelete,
			},
			expectedAuth: true,
		},
		{
			name: "superadmin_can_delete_organizations",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleSuperAdmin,
				Resource:     authorization.ResourceOrganization,
				Action:       authorization.ActionDelete,
			},
			expectedAuth: true,
		},
		{
			name: "superadmin_can_manage_all_resources",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleSuperAdmin,
				Resource:     authorization.ResourceAuditLog,
				Action:       authorization.ActionRead,
			},
			expectedAuth: true,
		},

		// Platform Staff tests
		{
			name: "platform_staff_can_read_users",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleStaff,
				Resource:     authorization.ResourceUser,
				Action:       authorization.ActionRead,
				ResourceID:   &resourceID,
			},
			expectedAuth: true,
		},
		{
			name: "platform_staff_can_update_users",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleStaff,
				Resource:     authorization.ResourceUser,
				Action:       authorization.ActionUpdate,
				ResourceID:   &resourceID,
			},
			expectedAuth: true,
		},
		{
			name: "platform_staff_can_delete_users",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleStaff,
				Resource:     authorization.ResourceUser,
				Action:       authorization.ActionDelete,
			},
			expectedAuth: true,
		},

		// Platform User tests
		{
			name: "platform_user_can_read_own_profile",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				Resource:     authorization.ResourceUser,
				Action:       authorization.ActionRead,
				ResourceID:   &userID, // Same as UserID
			},
			expectedAuth: true,
		},
		{
			name: "platform_member_cannot_read_other_profile",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				Resource:     authorization.ResourceUser,
				Action:       authorization.ActionRead,
				ResourceID:   &resourceID, // Different from UserID
			},
			expectedAuth: false,
		},
		{
			name: "platform_member_can_update_own_profile",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				Resource:     authorization.ResourceUser,
				Action:       authorization.ActionUpdate,
				ResourceID:   &userID,
			},
			expectedAuth: true,
		},
		{
			name: "platform_member_cannot_delete_users",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				Resource:     authorization.ResourceUser,
				Action:       authorization.ActionDelete,
			},
			expectedAuth: false,
		},
		{
			name: "platform_member_can_create_organization",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				Resource:     authorization.ResourceOrganization,
				Action:       authorization.ActionCreate,
			},
			expectedAuth: true,
		},
		{
			name: "platform_member_can_read_public_organizations",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				Resource:     authorization.ResourceOrganization,
				Action:       authorization.ActionRead,
			},
			expectedAuth: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := authorization.TestCanWithContext(t, tt.params)
			assert.Equal(t, tt.expectedAuth, result, "Expected authorization result to be %v", tt.expectedAuth)
		})
	}
}

func TestCan_OrganizationRoles(t *testing.T) {
	userID := uuid.New()
	orgID := uuid.New()
	resourceID := uuid.New()

	tests := []struct {
		name         string
		params       authorization.CanParams
		expectedAuth bool
	}{
		// Organization Owner tests
		{
			name: "org_owner_can_delete_organization",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				OrgID:        &orgID,
				OrgRole:      authorization.OrgRoleOwner,
				Resource:     authorization.ResourceOrganization,
				Action:       authorization.ActionDelete,
			},
			expectedAuth: true,
		},
		{
			name: "org_owner_can_manage_organization",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				OrgID:        &orgID,
				OrgRole:      authorization.OrgRoleOwner,
				Resource:     authorization.ResourceOrganization,
				Action:       authorization.ActionManage,
			},
			expectedAuth: true,
		},

		// Organization Admin tests
		{
			name: "org_admin_can_update_organization",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				OrgID:        &orgID,
				OrgRole:      authorization.OrgRoleAdmin,
				Resource:     authorization.ResourceOrganization,
				Action:       authorization.ActionUpdate,
			},
			expectedAuth: true,
		},
		{
			name: "org_admin_cannot_delete_organization",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				OrgID:        &orgID,
				OrgRole:      authorization.OrgRoleAdmin,
				Resource:     authorization.ResourceOrganization,
				Action:       authorization.ActionDelete,
			},
			expectedAuth: false,
		},
		{
			name: "org_admin_can_read_audit_logs",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				OrgID:        &orgID,
				OrgRole:      authorization.OrgRoleAdmin,
				Resource:     authorization.ResourceAuditLog,
				Action:       authorization.ActionRead,
			},
			expectedAuth: true,
		},

		// Organization Staff tests
		{
			name: "org_staff_can_create_events",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				OrgID:        &orgID,
				OrgRole:      authorization.OrgRoleStaff,
				Resource:     authorization.ResourceEvent,
				Action:       authorization.ActionCreate,
			},
			expectedAuth: true,
		},
		{
			name: "org_staff_can_update_events",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				OrgID:        &orgID,
				OrgRole:      authorization.OrgRoleStaff,
				Resource:     authorization.ResourceEvent,
				Action:       authorization.ActionUpdate,
			},
			expectedAuth: true,
		},
		{
			name: "org_staff_can_manage_content",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				OrgID:        &orgID,
				OrgRole:      authorization.OrgRoleStaff,
				Resource:     authorization.ResourceContent,
				Action:       authorization.ActionCreate,
			},
			expectedAuth: true,
		},
		{
			name: "org_staff_can_view_volunteers",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				OrgID:        &orgID,
				OrgRole:      authorization.OrgRoleStaff,
				Resource:     authorization.ResourceVolunteer,
				Action:       authorization.ActionRead,
			},
			expectedAuth: true,
		},
		{
			name: "org_staff_can_manage_volunteers",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				OrgID:        &orgID,
				OrgRole:      authorization.OrgRoleStaff,
				Resource:     authorization.ResourceVolunteer,
				Action:       authorization.ActionManage,
			},
			expectedAuth: true,
		},
		{
			name: "org_staff_can_send_notifications",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				OrgID:        &orgID,
				OrgRole:      authorization.OrgRoleStaff,
				Resource:     authorization.ResourceNotification,
				Action:       authorization.ActionCreate,
			},
			expectedAuth: true,
		},
		{
			name: "org_staff_cannot_read_audit_logs",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				OrgID:        &orgID,
				OrgRole:      authorization.OrgRoleStaff,
				Resource:     authorization.ResourceAuditLog,
				Action:       authorization.ActionRead,
			},
			expectedAuth: false,
		},

		// Organization Volunteer tests
		{
			name: "org_volunteer_can_update_own_volunteer_profile",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				OrgID:        &orgID,
				OrgRole:      authorization.OrgRoleVolunteer,
				Resource:     authorization.ResourceVolunteer,
				Action:       authorization.ActionUpdate,
				ResourceID:   &userID, // Own volunteer profile
				OwnerID:      &userID,
			},
			expectedAuth: true,
		},
		{
			name: "org_volunteer_cannot_update_other_volunteer_profile",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				OrgID:        &orgID,
				OrgRole:      authorization.OrgRoleVolunteer,
				Resource:     authorization.ResourceVolunteer,
				Action:       authorization.ActionUpdate,
				ResourceID:   &resourceID,
				OwnerID:      &resourceID, // Different user
			},
			expectedAuth: false,
		},
		{
			name: "org_volunteer_cannot_create_events",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				OrgID:        &orgID,
				OrgRole:      authorization.OrgRoleVolunteer,
				Resource:     authorization.ResourceEvent,
				Action:       authorization.ActionCreate,
			},
			expectedAuth: false,
		},

		// Organization Member tests
		{
			name: "org_member_can_read_organization",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				OrgID:        &orgID,
				OrgRole:      authorization.OrgRoleMember,
				Resource:     authorization.ResourceOrganization,
				Action:       authorization.ActionRead,
			},
			expectedAuth: true,
		},
		{
			name: "org_member_can_read_events",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				OrgID:        &orgID,
				OrgRole:      authorization.OrgRoleMember,
				Resource:     authorization.ResourceEvent,
				Action:       authorization.ActionRead,
			},
			expectedAuth: true,
		},
		{
			name: "org_member_can_create_event_registration",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				OrgID:        &orgID,
				OrgRole:      authorization.OrgRoleMember,
				Resource:     authorization.ResourceRegistration,
				Action:       authorization.ActionCreate,
			},
			expectedAuth: true,
		},
		{
			name: "org_member_can_read_own_registrations",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				OrgID:        &orgID,
				OrgRole:      authorization.OrgRoleMember,
				Resource:     authorization.ResourceRegistration,
				Action:       authorization.ActionRead,
				OwnerID:      &userID,
			},
			expectedAuth: true,
		},
		{
			name: "org_member_cannot_read_other_registrations",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				OrgID:        &orgID,
				OrgRole:      authorization.OrgRoleMember,
				Resource:     authorization.ResourceRegistration,
				Action:       authorization.ActionRead,
				OwnerID:      &resourceID, // Different user
			},
			expectedAuth: false,
		},
		{
			name: "org_member_can_update_own_registrations",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				OrgID:        &orgID,
				OrgRole:      authorization.OrgRoleMember,
				Resource:     authorization.ResourceRegistration,
				Action:       authorization.ActionUpdate,
				OwnerID:      &userID,
			},
			expectedAuth: true,
		},
		{
			name: "org_member_can_read_own_notifications",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				OrgID:        &orgID,
				OrgRole:      authorization.OrgRoleMember,
				Resource:     authorization.ResourceNotification,
				Action:       authorization.ActionRead,
				OwnerID:      &userID,
			},
			expectedAuth: true,
		},
		{
			name: "org_member_cannot_create_content",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				OrgID:        &orgID,
				OrgRole:      authorization.OrgRoleMember,
				Resource:     authorization.ResourceContent,
				Action:       authorization.ActionCreate,
			},
			expectedAuth: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := authorization.TestCanWithContext(t, tt.params)
			assert.Equal(t, tt.expectedAuth, result, "Expected authorization result to be %v", tt.expectedAuth)
		})
	}
}

func TestCan_EdgeCases(t *testing.T) {
	userID := uuid.New()
	orgID := uuid.New()

	tests := []struct {
		name         string
		params       authorization.CanParams
		expectedAuth bool
	}{
		{
			name: "no_org_context_no_platform_role",
			params: authorization.CanParams{
				UserID:   userID,
				Resource: authorization.ResourceEvent,
				Action:   authorization.ActionCreate,
			},
			expectedAuth: false,
		},
		{
			name: "invalid_resource",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				Resource:     "invalid_resource",
				Action:       authorization.ActionRead,
			},
			expectedAuth: false,
		},
		{
			name: "invalid_action",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				Resource:     authorization.ResourceUser,
				Action:       "invalid_action",
			},
			expectedAuth: false,
		},
		{
			name: "empty_org_role_with_org_id",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				OrgID:        &orgID,
				OrgRole:      "", // Empty org role
				Resource:     authorization.ResourceEvent,
				Action:       authorization.ActionCreate,
			},
			expectedAuth: false,
		},
		{
			name: "platform_admin_overrides_org_member",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleStaff,
				OrgID:        &orgID,
				OrgRole:      authorization.OrgRoleMember, // Low org role
				Resource:     authorization.ResourceEvent,
				Action:       authorization.ActionDelete,
			},
			expectedAuth: true, // Platform admin overrides
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := authorization.TestCanWithContext(t, tt.params)
			assert.Equal(t, tt.expectedAuth, result, "Expected authorization result to be %v", tt.expectedAuth)
		})
	}
}

func TestCan_ResourceSpecificPermissions(t *testing.T) {
	userID := uuid.New()
	otherUserID := uuid.New()

	tests := []struct {
		name         string
		params       authorization.CanParams
		expectedAuth bool
	}{
		// User resource without org context
		{
			name: "user_can_read_own_profile_without_org",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				Resource:     authorization.ResourceUser,
				Action:       authorization.ActionRead,
				ResourceID:   &userID,
			},
			expectedAuth: true,
		},
		{
			name: "user_cannot_read_other_profile_without_org",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				Resource:     authorization.ResourceUser,
				Action:       authorization.ActionRead,
				ResourceID:   &otherUserID,
			},
			expectedAuth: false,
		},
		{
			name: "user_can_update_own_profile_without_org",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				Resource:     authorization.ResourceUser,
				Action:       authorization.ActionUpdate,
				ResourceID:   &userID,
			},
			expectedAuth: true,
		},
		{
			name: "user_cannot_delete_self",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				Resource:     authorization.ResourceUser,
				Action:       authorization.ActionDelete,
				ResourceID:   &userID,
			},
			expectedAuth: false,
		},
		// Organization resource without org context
		{
			name: "any_user_can_create_organization",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				Resource:     authorization.ResourceOrganization,
				Action:       authorization.ActionCreate,
			},
			expectedAuth: true,
		},
		{
			name: "any_user_can_read_public_organizations",
			params: authorization.CanParams{
				UserID:       userID,
				PlatformRole: authorization.PlatformRoleUser,
				Resource:     authorization.ResourceOrganization,
				Action:       authorization.ActionRead,
			},
			expectedAuth: true, // TODO: Should check if org is public
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := authorization.TestCanWithContext(t, tt.params)
			assert.Equal(t, tt.expectedAuth, result, "Expected authorization result to be %v", tt.expectedAuth)
		})
	}
}

func TestCan_NilParameters(t *testing.T) {
	userID := uuid.New()

	// Test with nil OrgID
	t.Run("nil_org_id", func(t *testing.T) {
		params := authorization.CanParams{
			UserID:       userID,
			PlatformRole: authorization.PlatformRoleUser,
			OrgID:        nil,
			OrgRole:      authorization.OrgRoleStaff,
			Resource:     authorization.ResourceEvent,
			Action:       authorization.ActionCreate,
		}
		result := authorization.TestCanWithContext(t, params)
		assert.False(t, result, "Should fail with nil OrgID but OrgRole present")
	})

	// Test with nil ResourceID
	t.Run("nil_resource_id", func(t *testing.T) {
		params := authorization.CanParams{
			UserID:       userID,
			PlatformRole: authorization.PlatformRoleUser,
			Resource:     authorization.ResourceUser,
			Action:       authorization.ActionRead,
			ResourceID:   nil,
		}
		result := authorization.TestCanWithContext(t, params)
		assert.False(t, result, "Should fail when trying to read user without ResourceID")
	})

	// Test with nil OwnerID
	t.Run("nil_owner_id", func(t *testing.T) {
		orgID := uuid.New()
		params := authorization.CanParams{
			UserID:       userID,
			PlatformRole: authorization.PlatformRoleUser,
			OrgID:        &orgID,
			OrgRole:      authorization.OrgRoleMember,
			Resource:     authorization.ResourceRegistration,
			Action:       authorization.ActionRead,
			OwnerID:      nil,
		}
		result := authorization.TestCanWithContext(t, params)
		assert.False(t, result, "Should fail when checking own resource without OwnerID")
	})
}

func TestCan_CombinedScenarios(t *testing.T) {
	userID := uuid.New()
	orgID := uuid.New()
	otherOrgID := uuid.New()

	// Test platform admin with org context
	t.Run("platform_admin_with_org_context", func(t *testing.T) {
		params := authorization.CanParams{
			UserID:       userID,
			PlatformRole: authorization.PlatformRoleStaff,
			OrgID:        &orgID,
			OrgRole:      authorization.OrgRoleMember, // Low org role
			Resource:     authorization.ResourceAuditLog,
			Action:       authorization.ActionRead,
		}
		result := authorization.TestCanWithContext(t, params)
		assert.True(t, result, "Platform admin should override org role")
	})

	// Test user with multiple org contexts (simulated)
	t.Run("user_switching_org_context", func(t *testing.T) {
		// User is admin in org1
		params1 := authorization.CanParams{
			UserID:       userID,
			PlatformRole: authorization.PlatformRoleUser,
			OrgID:        &orgID,
			OrgRole:      authorization.OrgRoleAdmin,
			Resource:     authorization.ResourceEvent,
			Action:       authorization.ActionDelete,
		}
		result1 := authorization.TestCanWithContext(t, params1)
		assert.True(t, result1, "Should have permission as org admin")

		// Same user is member in org2
		params2 := authorization.CanParams{
			UserID:       userID,
			PlatformRole: authorization.PlatformRoleUser,
			OrgID:        &otherOrgID,
			OrgRole:      authorization.OrgRoleMember,
			Resource:     authorization.ResourceEvent,
			Action:       authorization.ActionDelete,
		}
		result2 := authorization.TestCanWithContext(t, params2)
		assert.False(t, result2, "Should not have permission as org member")
	})
}

// TestCanParams_Validation ensures params are validated correctly
func TestCanParams_Validation(t *testing.T) {
	t.Run("zero_user_id", func(t *testing.T) {
		params := authorization.CanParams{
			UserID:       uuid.Nil,
			PlatformRole: authorization.PlatformRoleUser,
			Resource:     authorization.ResourceUser,
			Action:       authorization.ActionRead,
		}
		// The function should still work but likely return false
		result := authorization.TestCanWithContext(t, params)
		assert.False(t, result, "Should handle zero UUID gracefully")
	})
}

// TestCan_RealWorldScenarios tests common real-world permission scenarios
func TestCan_RealWorldScenarios(t *testing.T) {
	// Scenario: Event management workflow
	t.Run("event_management_workflow", func(t *testing.T) {
		userID := uuid.New()
		orgID := uuid.New()

		// Staff creates event
		canCreate := authorization.TestCanWithContext(t, authorization.CanParams{
			UserID:       userID,
			PlatformRole: authorization.PlatformRoleUser,
			OrgID:        &orgID,
			OrgRole:      authorization.OrgRoleStaff,
			Resource:     authorization.ResourceEvent,
			Action:       authorization.ActionCreate,
		})
		require.True(t, canCreate, "Staff should create events")

		// Member reads event
		canRead := authorization.TestCanWithContext(t, authorization.CanParams{
			UserID:       uuid.New(),
			PlatformRole: authorization.PlatformRoleUser,
			OrgID:        &orgID,
			OrgRole:      authorization.OrgRoleMember,
			Resource:     authorization.ResourceEvent,
			Action:       authorization.ActionRead,
		})
		require.True(t, canRead, "Member should read events")

		// Member cannot update event
		canUpdate := authorization.TestCanWithContext(t, authorization.CanParams{
			UserID:       uuid.New(),
			PlatformRole: authorization.PlatformRoleUser,
			OrgID:        &orgID,
			OrgRole:      authorization.OrgRoleMember,
			Resource:     authorization.ResourceEvent,
			Action:       authorization.ActionUpdate,
		})
		require.False(t, canUpdate, "Member should not update events")
	})

	// Scenario: Volunteer management
	t.Run("volunteer_management_workflow", func(t *testing.T) {
		volunteerID := uuid.New()
		staffID := uuid.New()
		orgID := uuid.New()

		// Volunteer updates own profile
		canUpdateOwn := authorization.TestCanWithContext(t, authorization.CanParams{
			UserID:       volunteerID,
			PlatformRole: authorization.PlatformRoleUser,
			OrgID:        &orgID,
			OrgRole:      authorization.OrgRoleVolunteer,
			Resource:     authorization.ResourceVolunteer,
			Action:       authorization.ActionUpdate,
			ResourceID:   &volunteerID,
			OwnerID:      &volunteerID,
		})
		require.True(t, canUpdateOwn, "Volunteer should update own profile")

		// Staff manages volunteers
		canManage := authorization.TestCanWithContext(t, authorization.CanParams{
			UserID:       staffID,
			PlatformRole: authorization.PlatformRoleUser,
			OrgID:        &orgID,
			OrgRole:      authorization.OrgRoleStaff,
			Resource:     authorization.ResourceVolunteer,
			Action:       authorization.ActionManage,
		})
		require.True(t, canManage, "Staff should manage volunteers")
	})
}
