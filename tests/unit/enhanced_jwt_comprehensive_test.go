package unit

import (
	"context"
	"os"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/db"
	dbmocks "Membership-SAAS-System-Backend/internal/mocks/db"
	"Membership-SAAS-System-Backend/internal/token"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// Setup test environment for comprehensive JWT tests
func setupComprehensiveJWTTestEnv(t *testing.T) func() {
	// Store original values
	originalAccessSecret := os.Getenv("ACCESS_TOKEN_SECRET")
	originalRefreshSecret := os.Getenv("REFRESH_TOKEN_SECRET")
	originalAccessDuration := os.Getenv("ACCESS_TOKEN_DURATION")
	originalRefreshDuration := os.Getenv("REFRESH_TOKEN_DURATION")
	originalRefreshSessionDuration := os.Getenv("REFRESH_SESSION_DURATION")

	// Set test values
	os.Setenv("ACCESS_TOKEN_SECRET", "test-access-secret-key-32-characters-long")
	os.Setenv("REFRESH_TOKEN_SECRET", "test-refresh-secret-key-32-characters-long")
	os.Setenv("ACCESS_TOKEN_DURATION", "15m")
	os.Setenv("REFRESH_TOKEN_DURATION", "168h") // 7 days = 168 hours
	os.Setenv("REFRESH_SESSION_DURATION", "2160h")

	// Reset the singleton
	token.LoadJWTConfig()

	// Return cleanup function
	return func() {
		os.Setenv("ACCESS_TOKEN_SECRET", originalAccessSecret)
		os.Setenv("REFRESH_TOKEN_SECRET", originalRefreshSecret)
		os.Setenv("ACCESS_TOKEN_DURATION", originalAccessDuration)
		os.Setenv("REFRESH_TOKEN_DURATION", originalRefreshDuration)
		os.Setenv("REFRESH_SESSION_DURATION", originalRefreshSessionDuration)
		token.LoadJWTConfig()
	}
}

func TestGenerateEnhancedAccessTokenComprehensive(t *testing.T) {
	cleanup := setupComprehensiveJWTTestEnv(t)
	defer cleanup()

	ctx := context.Background()
	userID := uuid.New()
	orgID1 := uuid.New()
	orgID2 := uuid.New()

	tests := []struct {
		name           string
		setupMock      func(*dbmocks.MockStore)
		expectedError  bool
		validateClaims func(*testing.T, string)
	}{
		{
			name: "successful_token_generation_with_platform_super_admin",
			setupMock: func(m *dbmocks.MockStore) {
				m.On("GetUserByID", ctx, userID).Return(db.User{
					ID:           userID,
					PlatformRole: "super_admin",
				}, nil)
				m.On("GetUserOrganizationMemberships", ctx, userID).Return([]db.GetUserOrganizationMembershipsRow{
					{OrganizationID: orgID1, Role: "owner"},
					{OrganizationID: orgID2, Role: "admin"},
				}, nil)
			},
			expectedError: false,
			validateClaims: func(t *testing.T, tokenString string) {
				config, _ := token.GetJWTConfig()
				claims, err := token.ParseTokenWithBackwardCompatibility(tokenString, config.AccessTokenSecret)
				require.NoError(t, err)

				assert.Equal(t, userID, claims.UserID)
				assert.Equal(t, "super_admin", claims.PlatformRole)
				assert.Len(t, claims.OrgRoles, 2)
				assert.Equal(t, "owner", claims.OrgRoles[orgID1.String()])
				assert.Equal(t, "admin", claims.OrgRoles[orgID2.String()])
			},
		},
		{
			name: "successful_token_generation_with_platform_staff",
			setupMock: func(m *dbmocks.MockStore) {
				m.On("GetUserByID", ctx, userID).Return(db.User{
					ID:           userID,
					PlatformRole: "staff",
				}, nil)
				m.On("GetUserOrganizationMemberships", ctx, userID).Return([]db.GetUserOrganizationMembershipsRow{
					{OrganizationID: orgID1, Role: "member"},
				}, nil)
			},
			expectedError: false,
			validateClaims: func(t *testing.T, tokenString string) {
				config, _ := token.GetJWTConfig()
				claims, err := token.ParseTokenWithBackwardCompatibility(tokenString, config.AccessTokenSecret)
				require.NoError(t, err)

				assert.Equal(t, userID, claims.UserID)
				assert.Equal(t, "staff", claims.PlatformRole)
				assert.Len(t, claims.OrgRoles, 1)
				assert.Equal(t, "member", claims.OrgRoles[orgID1.String()])
			},
		},
		{
			name: "successful_token_generation_regular_user",
			setupMock: func(m *dbmocks.MockStore) {
				m.On("GetUserByID", ctx, userID).Return(db.User{
					ID:           userID,
					PlatformRole: "user",
				}, nil)
				m.On("GetUserOrganizationMemberships", ctx, userID).Return([]db.GetUserOrganizationMembershipsRow{
					{OrganizationID: orgID1, Role: "member"},
				}, nil)
			},
			expectedError: false,
			validateClaims: func(t *testing.T, tokenString string) {
				config, _ := token.GetJWTConfig()
				claims, err := token.ParseTokenWithBackwardCompatibility(tokenString, config.AccessTokenSecret)
				require.NoError(t, err)

				assert.Equal(t, userID, claims.UserID)
				assert.Equal(t, "user", claims.PlatformRole)
				assert.Len(t, claims.OrgRoles, 1)
				assert.Equal(t, "member", claims.OrgRoles[orgID1.String()])
			},
		},
		{
			name: "user_not_found",
			setupMock: func(m *dbmocks.MockStore) {
				m.On("GetUserByID", ctx, userID).Return(db.User{}, assert.AnError)
			},
			expectedError: true,
		},
		{
			name: "org_memberships_error_does_not_fail",
			setupMock: func(m *dbmocks.MockStore) {
				m.On("GetUserByID", ctx, userID).Return(db.User{
					ID:           userID,
					PlatformRole: "user",
				}, nil)
				m.On("GetUserOrganizationMemberships", ctx, userID).Return([]db.GetUserOrganizationMembershipsRow{}, assert.AnError)
			},
			expectedError: false,
			validateClaims: func(t *testing.T, tokenString string) {
				config, _ := token.GetJWTConfig()
				claims, err := token.ParseTokenWithBackwardCompatibility(tokenString, config.AccessTokenSecret)
				require.NoError(t, err)

				assert.Equal(t, userID, claims.UserID)
				assert.Empty(t, claims.OrgRoles) // Empty on error
			},
		},
		{
			name: "empty_platform_role",
			setupMock: func(m *dbmocks.MockStore) {
				m.On("GetUserByID", ctx, userID).Return(db.User{
					ID:           userID,
					PlatformRole: "", // Empty platform role
				}, nil)
				m.On("GetUserOrganizationMemberships", ctx, userID).Return([]db.GetUserOrganizationMembershipsRow{}, nil)
			},
			expectedError: false,
			validateClaims: func(t *testing.T, tokenString string) {
				config, _ := token.GetJWTConfig()
				claims, err := token.ParseTokenWithBackwardCompatibility(tokenString, config.AccessTokenSecret)
				require.NoError(t, err)

				assert.Equal(t, userID, claims.UserID)
				// Note: Empty platform_role gets converted to "user" by backward compatibility logic
				// This is expected behavior when parsing tokens with empty platform_role
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockStore := dbmocks.NewMockStore(t)
			tt.setupMock(mockStore)

			tokenString, err := token.GenerateEnhancedAccessToken(ctx, mockStore, userID)

			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, tokenString)

				if tt.validateClaims != nil {
					tt.validateClaims(t, tokenString)
				}
			}

			mockStore.AssertExpectations(t)
		})
	}
}

func TestParseTokenWithBackwardCompatibilityEdgeCases(t *testing.T) {
	cleanup := setupComprehensiveJWTTestEnv(t)
	defer cleanup()

	config, err := token.GetJWTConfig()
	require.NoError(t, err)

	userID := uuid.New()

	tests := []struct {
		name           string
		createToken    func() (string, error)
		expectedError  bool
		validateClaims func(*testing.T, *token.EnhancedClaims)
	}{
		{
			name: "parse_enhanced_token_with_org_roles",
			createToken: func() (string, error) {
				orgID := uuid.New()
				claims := token.EnhancedClaims{
					UserID:       userID,
					PlatformRole: "staff",
					OrgRoles: map[string]string{
						orgID.String(): "admin",
					},
					RegisteredClaims: jwt.RegisteredClaims{
						ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
						IssuedAt:  jwt.NewNumericDate(time.Now()),
						NotBefore: jwt.NewNumericDate(time.Now()),
						Issuer:    "MembershipSaas",
						Subject:   userID.String(),
					},
				}
				jwtToken := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
				return jwtToken.SignedString(config.AccessTokenSecret)
			},
			expectedError: false,
			validateClaims: func(t *testing.T, claims *token.EnhancedClaims) {
				assert.Equal(t, userID, claims.UserID)
				assert.Equal(t, "staff", claims.PlatformRole)
				assert.Len(t, claims.OrgRoles, 1)
			},
		},
		{
			name: "parse_legacy_token_staff",
			createToken: func() (string, error) {
				claims := token.AppClaims{
					UserID:       userID,
					PlatformRole: "staff",
					RegisteredClaims: jwt.RegisteredClaims{
						ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
						IssuedAt:  jwt.NewNumericDate(time.Now()),
						NotBefore: jwt.NewNumericDate(time.Now()),
						Issuer:    "MembershipSaas",
						Subject:   userID.String(),
					},
				}
				jwtToken := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
				return jwtToken.SignedString(config.AccessTokenSecret)
			},
			expectedError: false,
			validateClaims: func(t *testing.T, claims *token.EnhancedClaims) {
				assert.Equal(t, userID, claims.UserID)
				assert.Equal(t, "staff", claims.PlatformRole) // Backward compatibility
				assert.Empty(t, claims.OrgRoles)
			},
		},
		{
			name: "parse_legacy_token_non_staff",
			createToken: func() (string, error) {
				claims := token.AppClaims{
					UserID:       userID,
					PlatformRole: "user",
					RegisteredClaims: jwt.RegisteredClaims{
						ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
						IssuedAt:  jwt.NewNumericDate(time.Now()),
						NotBefore: jwt.NewNumericDate(time.Now()),
						Issuer:    "MembershipSaas",
						Subject:   userID.String(),
					},
				}
				jwtToken := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
				return jwtToken.SignedString(config.AccessTokenSecret)
			},
			expectedError: false,
			validateClaims: func(t *testing.T, claims *token.EnhancedClaims) {
				assert.Equal(t, userID, claims.UserID)
				assert.Equal(t, "user", claims.PlatformRole) // Backward compatibility
				assert.Empty(t, claims.OrgRoles)
			},
		},
		{
			name: "malformed_token_missing_fields",
			createToken: func() (string, error) {
				// Create a token with missing required fields
				claims := jwt.MapClaims{
					"exp": time.Now().Add(time.Hour).Unix(),
					"iat": time.Now().Unix(),
					"nbf": time.Now().Unix(),
					"iss": "MembershipSaas",
					// Missing user_id and other essential fields
				}
				jwtToken := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
				return jwtToken.SignedString(config.AccessTokenSecret)
			},
			expectedError: false, // Our parser is lenient and can parse this as enhanced token with zero values
		},
		{
			name: "expired_token",
			createToken: func() (string, error) {
				claims := token.EnhancedClaims{
					UserID:       userID,
					PlatformRole: "user",
					RegisteredClaims: jwt.RegisteredClaims{
						ExpiresAt: jwt.NewNumericDate(time.Now().Add(-time.Hour)), // Expired
						IssuedAt:  jwt.NewNumericDate(time.Now().Add(-2 * time.Hour)),
						NotBefore: jwt.NewNumericDate(time.Now().Add(-2 * time.Hour)),
						Issuer:    "MembershipSaas",
						Subject:   userID.String(),
					},
				}
				jwtToken := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
				return jwtToken.SignedString(config.AccessTokenSecret)
			},
			expectedError: true,
		},
		{
			name: "invalid_signature",
			createToken: func() (string, error) {
				claims := token.EnhancedClaims{
					UserID:       userID,
					PlatformRole: "user",
					RegisteredClaims: jwt.RegisteredClaims{
						ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
						IssuedAt:  jwt.NewNumericDate(time.Now()),
						NotBefore: jwt.NewNumericDate(time.Now()),
						Issuer:    "MembershipSaas",
						Subject:   userID.String(),
					},
				}
				jwtToken := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
				return jwtToken.SignedString([]byte("wrong-secret"))
			},
			expectedError: true,
		},
		{
			name: "token_with_null_platform_role",
			createToken: func() (string, error) {
				// Create a token with null platform_role (using map claims)
				claims := jwt.MapClaims{
					"user_id":       userID.String(),
					"platform_role": nil, // Null platform role
					"exp":           time.Now().Add(time.Hour).Unix(),
					"iat":           time.Now().Unix(),
					"nbf":           time.Now().Unix(),
					"iss":           "MembershipSaas",
					"sub":           userID.String(),
				}
				jwtToken := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
				return jwtToken.SignedString(config.AccessTokenSecret)
			},
			expectedError: false, // Parser will parse this and convert to enhanced format
		},
		{
			name: "token_with_missing_active_org",
			createToken: func() (string, error) {
				claims := token.EnhancedClaims{
					UserID:       userID,
					PlatformRole: "user",
					OrgRoles:     make(map[string]string),
					ActiveOrg:    nil, // Missing active org is OK
					RegisteredClaims: jwt.RegisteredClaims{
						ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
						IssuedAt:  jwt.NewNumericDate(time.Now()),
						NotBefore: jwt.NewNumericDate(time.Now()),
						Issuer:    "MembershipSaas",
						Subject:   userID.String(),
					},
				}
				jwtToken := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
				return jwtToken.SignedString(config.AccessTokenSecret)
			},
			expectedError: false,
			validateClaims: func(t *testing.T, claims *token.EnhancedClaims) {
				assert.Equal(t, userID, claims.UserID)
				assert.Equal(t, "user", claims.PlatformRole)
				assert.Nil(t, claims.ActiveOrg)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tokenString, err := tt.createToken()
			if err != nil {
				t.Fatalf("Failed to create token: %v", err)
			}

			claims, err := token.ParseTokenWithBackwardCompatibility(tokenString, config.AccessTokenSecret)

			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, claims)

				if tt.validateClaims != nil {
					tt.validateClaims(t, claims)
				}
			}
		})
	}
}

func TestGetValidatedEnhancedClaims(t *testing.T) {
	cleanup := setupComprehensiveJWTTestEnv(t)
	defer cleanup()

	config, err := token.GetJWTConfig()
	require.NoError(t, err)

	userID := uuid.New()

	tests := []struct {
		name          string
		createToken   func() (string, error)
		expectedError bool
	}{
		{
			name: "valid_token",
			createToken: func() (string, error) {
				claims := token.EnhancedClaims{
					UserID:       userID,
					PlatformRole: "user",
					OrgRoles:     make(map[string]string),
					RegisteredClaims: jwt.RegisteredClaims{
						ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
						IssuedAt:  jwt.NewNumericDate(time.Now()),
						NotBefore: jwt.NewNumericDate(time.Now()),
						Issuer:    "MembershipSaas",
						Subject:   userID.String(),
					},
				}
				jwtToken := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
				return jwtToken.SignedString(config.AccessTokenSecret)
			},
			expectedError: false,
		},
		{
			name: "token_with_invalid_user_id",
			createToken: func() (string, error) {
				claims := token.EnhancedClaims{
					UserID:       uuid.Nil, // Invalid user ID
					PlatformRole: "user",
					RegisteredClaims: jwt.RegisteredClaims{
						ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
						IssuedAt:  jwt.NewNumericDate(time.Now()),
						NotBefore: jwt.NewNumericDate(time.Now()),
						Issuer:    "MembershipSaas",
						Subject:   uuid.Nil.String(),
					},
				}
				jwtToken := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
				return jwtToken.SignedString(config.AccessTokenSecret)
			},
			expectedError: true,
		},
		{
			name: "token_with_empty_platform_role",
			createToken: func() (string, error) {
				claims := token.EnhancedClaims{
					UserID:       userID,
					PlatformRole: "", // Empty platform role
					RegisteredClaims: jwt.RegisteredClaims{
						ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
						IssuedAt:  jwt.NewNumericDate(time.Now()),
						NotBefore: jwt.NewNumericDate(time.Now()),
						Issuer:    "MembershipSaas",
						Subject:   userID.String(),
					},
				}
				jwtToken := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
				return jwtToken.SignedString(config.AccessTokenSecret)
			},
			expectedError: false, // Allow empty platform role for backward compatibility
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tokenString, err := tt.createToken()
			require.NoError(t, err)

			claims, err := token.GetValidatedEnhancedClaims(tokenString, config.AccessTokenSecret)

			if tt.expectedError {
				assert.Error(t, err)
				assert.Nil(t, claims)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, claims)
			}
		})
	}
}

// TestTokenSizeLimits ensures tokens don't exceed reasonable size limits
func TestTokenSizeLimits(t *testing.T) {
	cleanup := setupComprehensiveJWTTestEnv(t)
	defer cleanup()

	ctx := context.Background()
	userID := uuid.New()

	mockStore := dbmocks.NewMockStore(t)

	// Create user with many organization memberships to test token size
	var memberships []db.GetUserOrganizationMembershipsRow
	for i := 0; i < 100; i++ { // 100 org memberships
		memberships = append(memberships, db.GetUserOrganizationMembershipsRow{
			OrganizationID: uuid.New(),
			Role:           "member",
		})
	}

	mockStore.On("GetUserByID", ctx, userID).Return(db.User{
		ID:           userID,
		PlatformRole: "user",
	}, nil)
	mockStore.On("GetUserOrganizationMemberships", ctx, userID).Return(memberships, nil)

	tokenString, err := token.GenerateEnhancedAccessToken(ctx, mockStore, userID)
	assert.NoError(t, err)

	// Token should be reasonable size (less than 8KB for practical HTTP header limits)
	assert.Less(t, len(tokenString), 8*1024, "Token size should be less than 8KB")

	// Token should still be parseable
	config, _ := token.GetJWTConfig()
	claims, err := token.ParseTokenWithBackwardCompatibility(tokenString, config.AccessTokenSecret)
	assert.NoError(t, err)
	assert.Equal(t, userID, claims.UserID)
	assert.Equal(t, 100, len(claims.OrgRoles))

	mockStore.AssertExpectations(t)
}

// TestConcurrentTokenValidation tests token validation under concurrent load
func TestConcurrentTokenValidation(t *testing.T) {
	cleanup := setupComprehensiveJWTTestEnv(t)
	defer cleanup()

	config, err := token.GetJWTConfig()
	require.NoError(t, err)

	userID := uuid.New()

	// Create a valid token
	claims := token.EnhancedClaims{
		UserID:       userID,
		PlatformRole: "user",
		OrgRoles:     make(map[string]string),
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "MembershipSaas",
			Subject:   userID.String(),
		},
	}
	jwtToken := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := jwtToken.SignedString(config.AccessTokenSecret)
	require.NoError(t, err)

	// Test concurrent access
	const numGoroutines = 50
	const numIterations = 10

	errChan := make(chan error, numGoroutines*numIterations)

	for i := 0; i < numGoroutines; i++ {
		go func() {
			for j := 0; j < numIterations; j++ {
				parsedClaims, err := token.ParseTokenWithBackwardCompatibility(tokenString, config.AccessTokenSecret)
				if err != nil {
					errChan <- err
					continue
				}
				if parsedClaims.UserID != userID {
					errChan <- assert.AnError
					continue
				}
				errChan <- nil
			}
		}()
	}

	// Check all results
	for i := 0; i < numGoroutines*numIterations; i++ {
		err := <-errChan
		assert.NoError(t, err, "Concurrent token validation should not fail")
	}
}
