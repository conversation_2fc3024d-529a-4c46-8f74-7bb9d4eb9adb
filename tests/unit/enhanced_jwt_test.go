package unit

import (
	"context"
	"os"
	"testing"
	"time"

	"Membership-SAAS-System-Backend/db"
	dbmocks "Membership-SAAS-System-Backend/internal/mocks/db"
	"Membership-SAAS-System-Backend/internal/token"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

// Setup test environment
func setupJWTTestEnv(t *testing.T) func() {
	// Store original values
	originalAccessSecret := os.Getenv("ACCESS_TOKEN_SECRET")
	originalRefreshSecret := os.Getenv("REFRESH_TOKEN_SECRET")
	originalAccessDuration := os.Getenv("ACCESS_TOKEN_DURATION")
	originalRefreshDuration := os.Getenv("REFRESH_TOKEN_DURATION")
	originalRefreshSessionDuration := os.Getenv("REFRESH_SESSION_DURATION")

	// Set test values
	os.Setenv("ACCESS_TOKEN_SECRET", "test-access-secret-key-32-characters-long")
	os.Setenv("REFRESH_TOKEN_SECRET", "test-refresh-secret-key-32-characters-long")
	os.Setenv("ACCESS_TOKEN_DURATION", "15m")
	os.Setenv("REFRESH_TOKEN_DURATION", "168h") // 7 days = 168 hours
	os.Setenv("REFRESH_SESSION_DURATION", "2160h")

	// Reset the singleton
	token.LoadJWTConfig()

	// Return cleanup function
	return func() {
		os.Setenv("ACCESS_TOKEN_SECRET", originalAccessSecret)
		os.Setenv("REFRESH_TOKEN_SECRET", originalRefreshSecret)
		os.Setenv("ACCESS_TOKEN_DURATION", originalAccessDuration)
		os.Setenv("REFRESH_TOKEN_DURATION", originalRefreshDuration)
		os.Setenv("REFRESH_SESSION_DURATION", originalRefreshSessionDuration)
	}
}

func TestGenerateEnhancedAccessToken(t *testing.T) {
	cleanup := setupJWTTestEnv(t)
	defer cleanup()

	ctx := context.Background()
	userID := uuid.New()
	orgID1 := uuid.New()
	orgID2 := uuid.New()

	tests := []struct {
		name           string
		setupMock      func(*dbmocks.MockStore)
		expectedError  bool
		validateClaims func(*testing.T, string)
	}{
		{
			name: "successful_token_generation_with_platform_admin",
			setupMock: func(m *dbmocks.MockStore) {
				m.On("GetUserByID", ctx, userID).Return(db.User{
					ID:           userID,
					PlatformRole: "admin",
				}, nil)
				m.On("GetUserOrganizationMemberships", ctx, userID).Return([]db.GetUserOrganizationMembershipsRow{
					{OrganizationID: orgID1, Role: "owner"},
					{OrganizationID: orgID2, Role: "staff"},
				}, nil)
			},
			expectedError: false,
			validateClaims: func(t *testing.T, tokenString string) {
				config, _ := token.GetJWTConfig()
				claims, err := token.ParseTokenWithBackwardCompatibility(tokenString, config.AccessTokenSecret)
				require.NoError(t, err)

				assert.Equal(t, userID, claims.UserID)
				assert.Equal(t, "admin", claims.PlatformRole)
				assert.Len(t, claims.OrgRoles, 2)
				assert.Equal(t, "owner", claims.OrgRoles[orgID1.String()])
				assert.Equal(t, "staff", claims.OrgRoles[orgID2.String()])
			},
		},
		{
			name: "successful_token_generation_with_backward_compatibility",
			setupMock: func(m *dbmocks.MockStore) {
				m.On("GetUserByID", ctx, userID).Return(db.User{
					ID:           userID,
					PlatformRole: "staff", // Legacy staff role
				}, nil)
				m.On("GetUserOrganizationMemberships", ctx, userID).Return([]db.GetUserOrganizationMembershipsRow{}, nil)
			},
			expectedError: false,
			validateClaims: func(t *testing.T, tokenString string) {
				config, _ := token.GetJWTConfig()
				claims, err := token.ParseTokenWithBackwardCompatibility(tokenString, config.AccessTokenSecret)
				require.NoError(t, err)

				assert.Equal(t, userID, claims.UserID)
				assert.Equal(t, "staff", claims.PlatformRole) // Uses staff role
				assert.Empty(t, claims.OrgRoles)
			},
		},
		{
			name: "successful_token_generation_regular_member",
			setupMock: func(m *dbmocks.MockStore) {
				m.On("GetUserByID", ctx, userID).Return(db.User{
					ID:           userID,
					PlatformRole: "user",
				}, nil)
				m.On("GetUserOrganizationMemberships", ctx, userID).Return([]db.GetUserOrganizationMembershipsRow{
					{OrganizationID: orgID1, Role: "member"},
				}, nil)
			},
			expectedError: false,
			validateClaims: func(t *testing.T, tokenString string) {
				config, _ := token.GetJWTConfig()
				claims, err := token.ParseTokenWithBackwardCompatibility(tokenString, config.AccessTokenSecret)
				require.NoError(t, err)

				assert.Equal(t, userID, claims.UserID)
				assert.Equal(t, "user", claims.PlatformRole)
				assert.Len(t, claims.OrgRoles, 1)
				assert.Equal(t, "member", claims.OrgRoles[orgID1.String()])
			},
		},
		{
			name: "user_not_found",
			setupMock: func(m *dbmocks.MockStore) {
				m.On("GetUserByID", ctx, userID).Return(db.User{}, assert.AnError)
			},
			expectedError: true,
		},
		{
			name: "org_memberships_error_does_not_fail",
			setupMock: func(m *dbmocks.MockStore) {
				m.On("GetUserByID", ctx, userID).Return(db.User{
					ID:           userID,
					PlatformRole: "user",
				}, nil)
				m.On("GetUserOrganizationMemberships", ctx, userID).Return([]db.GetUserOrganizationMembershipsRow{}, assert.AnError)
			},
			expectedError: false,
			validateClaims: func(t *testing.T, tokenString string) {
				config, _ := token.GetJWTConfig()
				claims, err := token.ParseTokenWithBackwardCompatibility(tokenString, config.AccessTokenSecret)
				require.NoError(t, err)

				assert.Equal(t, userID, claims.UserID)
				assert.Empty(t, claims.OrgRoles) // Empty on error
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockStore := dbmocks.NewMockStore(t)
			tt.setupMock(mockStore)

			tokenString, err := token.GenerateEnhancedAccessToken(ctx, mockStore, userID)

			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, tokenString)

				if tt.validateClaims != nil {
					tt.validateClaims(t, tokenString)
				}
			}

			mockStore.AssertExpectations(t)
		})
	}
}

func TestParseTokenWithBackwardCompatibility(t *testing.T) {
	cleanup := setupJWTTestEnv(t)
	defer cleanup()

	config, err := token.GetJWTConfig()
	require.NoError(t, err)

	userID := uuid.New()

	tests := []struct {
		name           string
		createToken    func() (string, error)
		expectedError  bool
		validateClaims func(*testing.T, *token.EnhancedClaims)
	}{
		{
			name: "parse_enhanced_token",
			createToken: func() (string, error) {
				claims := token.EnhancedClaims{
					UserID:       userID,
					PlatformRole: "admin",
					OrgRoles: map[string]string{
						uuid.New().String(): "owner",
					},
					RegisteredClaims: jwt.RegisteredClaims{
						ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
						IssuedAt:  jwt.NewNumericDate(time.Now()),
						NotBefore: jwt.NewNumericDate(time.Now()),
						Issuer:    "MembershipSaas",
						Subject:   userID.String(),
					},
				}
				jwtToken := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
				return jwtToken.SignedString(config.AccessTokenSecret)
			},
			expectedError: false,
			validateClaims: func(t *testing.T, claims *token.EnhancedClaims) {
				assert.Equal(t, userID, claims.UserID)
				assert.Equal(t, "admin", claims.PlatformRole)
				assert.Len(t, claims.OrgRoles, 1)
			},
		},
		{
			name: "parse_legacy_token_staff",
			createToken: func() (string, error) {
				claims := token.AppClaims{
					UserID:       userID,
					PlatformRole: "staff",
					RegisteredClaims: jwt.RegisteredClaims{
						ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
						IssuedAt:  jwt.NewNumericDate(time.Now()),
						NotBefore: jwt.NewNumericDate(time.Now()),
						Issuer:    "MembershipSaas",
						Subject:   userID.String(),
					},
				}
				jwtToken := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
				return jwtToken.SignedString(config.AccessTokenSecret)
			},
			expectedError: false,
			validateClaims: func(t *testing.T, claims *token.EnhancedClaims) {
				t.Logf("Claims: UserID=%v, PlatformRole=%q", claims.UserID, claims.PlatformRole)
				assert.Equal(t, userID, claims.UserID)
				assert.Equal(t, "staff", claims.PlatformRole) // Backward compatibility
				assert.Empty(t, claims.OrgRoles)
			},
		},
		{
			name: "parse_legacy_token_non_staff",
			createToken: func() (string, error) {
				claims := token.AppClaims{
					UserID:       userID,
					PlatformRole: "user",
					RegisteredClaims: jwt.RegisteredClaims{
						ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
						IssuedAt:  jwt.NewNumericDate(time.Now()),
						NotBefore: jwt.NewNumericDate(time.Now()),
						Issuer:    "MembershipSaas",
						Subject:   userID.String(),
					},
				}
				jwtToken := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
				return jwtToken.SignedString(config.AccessTokenSecret)
			},
			expectedError: false,
			validateClaims: func(t *testing.T, claims *token.EnhancedClaims) {
				assert.Equal(t, userID, claims.UserID)
				assert.Equal(t, "user", claims.PlatformRole) // Backward compatibility
				assert.Empty(t, claims.OrgRoles)
			},
		},
		{
			name: "expired_token",
			createToken: func() (string, error) {
				claims := token.EnhancedClaims{
					UserID:       userID,
					PlatformRole: "member",
					RegisteredClaims: jwt.RegisteredClaims{
						ExpiresAt: jwt.NewNumericDate(time.Now().Add(-time.Hour)), // Expired
						IssuedAt:  jwt.NewNumericDate(time.Now().Add(-2 * time.Hour)),
						NotBefore: jwt.NewNumericDate(time.Now().Add(-2 * time.Hour)),
						Issuer:    "MembershipSaas",
						Subject:   userID.String(),
					},
				}
				jwtToken := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
				return jwtToken.SignedString(config.AccessTokenSecret)
			},
			expectedError: true,
		},
		{
			name: "invalid_signature",
			createToken: func() (string, error) {
				claims := token.EnhancedClaims{
					UserID:       userID,
					PlatformRole: "member",
					RegisteredClaims: jwt.RegisteredClaims{
						ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
						IssuedAt:  jwt.NewNumericDate(time.Now()),
						NotBefore: jwt.NewNumericDate(time.Now()),
						Issuer:    "MembershipSaas",
						Subject:   userID.String(),
					},
				}
				jwtToken := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
				return jwtToken.SignedString([]byte("wrong-secret"))
			},
			expectedError: true,
		},
		{
			name: "invalid_algorithm",
			createToken: func() (string, error) {
				claims := token.EnhancedClaims{
					UserID:       userID,
					PlatformRole: "member",
					RegisteredClaims: jwt.RegisteredClaims{
						ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
						IssuedAt:  jwt.NewNumericDate(time.Now()),
						NotBefore: jwt.NewNumericDate(time.Now()),
						Issuer:    "MembershipSaas",
						Subject:   userID.String(),
					},
				}
				// Use RS256 instead of HS256
				_ = jwt.NewWithClaims(jwt.SigningMethodRS256, claims)
				// This will fail because we don't have RSA keys
				return "", assert.AnError
			},
			expectedError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tokenString, err := tt.createToken()
			if err != nil && tt.name != "invalid_algorithm" {
				t.Fatalf("Failed to create token: %v", err)
			}

			if tt.name == "invalid_algorithm" {
				// Skip parsing for this test
				return
			}

			claims, err := token.ParseTokenWithBackwardCompatibility(tokenString, config.AccessTokenSecret)

			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, claims)

				if tt.validateClaims != nil {
					tt.validateClaims(t, claims)
				}
			}
		})
	}
}

func TestJWTConfig(t *testing.T) {
	// Save original env vars
	originalAccessSecret := os.Getenv("ACCESS_TOKEN_SECRET")
	originalRefreshSecret := os.Getenv("REFRESH_TOKEN_SECRET")
	originalAccessDuration := os.Getenv("ACCESS_TOKEN_DURATION")
	originalRefreshDuration := os.Getenv("REFRESH_TOKEN_DURATION")
	originalRefreshSessionDuration := os.Getenv("REFRESH_SESSION_DURATION")

	// Restore env vars after all tests
	defer func() {
		os.Setenv("ACCESS_TOKEN_SECRET", originalAccessSecret)
		os.Setenv("REFRESH_TOKEN_SECRET", originalRefreshSecret)
		os.Setenv("ACCESS_TOKEN_DURATION", originalAccessDuration)
		os.Setenv("REFRESH_TOKEN_DURATION", originalRefreshDuration)
		os.Setenv("REFRESH_SESSION_DURATION", originalRefreshSessionDuration)
		token.LoadJWTConfig()
	}()

	// Test with missing environment variables
	t.Run("missing_access_token_secret", func(t *testing.T) {
		os.Unsetenv("ACCESS_TOKEN_SECRET")
		os.Setenv("REFRESH_TOKEN_SECRET", "test-refresh-secret")

		token.LoadJWTConfig()
		_, err := token.GetJWTConfig()
		assert.Error(t, err)
		if err != nil {
			assert.Contains(t, err.Error(), "access token secret")
		}
	})

	t.Run("missing_refresh_token_secret", func(t *testing.T) {
		os.Setenv("ACCESS_TOKEN_SECRET", "test-access-secret")
		os.Unsetenv("REFRESH_TOKEN_SECRET")

		token.LoadJWTConfig()
		_, err := token.GetJWTConfig()
		assert.Error(t, err)
		if err != nil {
			assert.Contains(t, err.Error(), "refresh token secret")
		}
	})

	t.Run("invalid_duration_format", func(t *testing.T) {
		os.Setenv("ACCESS_TOKEN_SECRET", "test-access-secret-key-32-characters-long")
		os.Setenv("REFRESH_TOKEN_SECRET", "test-refresh-secret-key-32-characters-long")
		os.Setenv("ACCESS_TOKEN_DURATION", "invalid-duration")

		token.LoadJWTConfig()
		_, err := token.GetJWTConfig()
		assert.Error(t, err)
		if err != nil {
			assert.Contains(t, err.Error(), "ACCESS_TOKEN_DURATION")
		}
	})

	t.Run("default_durations", func(t *testing.T) {
		os.Setenv("ACCESS_TOKEN_SECRET", "test-access-secret-key-32-characters-long")
		os.Setenv("REFRESH_TOKEN_SECRET", "test-refresh-secret-key-32-characters-long")
		os.Unsetenv("ACCESS_TOKEN_DURATION")
		os.Unsetenv("REFRESH_TOKEN_DURATION")
		os.Unsetenv("REFRESH_SESSION_DURATION")

		token.LoadJWTConfig()
		config, err := token.GetJWTConfig()
		assert.NoError(t, err)
		assert.Equal(t, 15*time.Minute, config.AccessTokenDuration)
		assert.Equal(t, 7*24*time.Hour, config.RefreshTokenDuration)
		assert.Equal(t, 2160*time.Hour, config.RefreshSessionDuration)
	})
}

func TestGenerateAccessToken(t *testing.T) {
	cleanup := setupJWTTestEnv(t)
	defer cleanup()

	ctx := context.Background()
	userID := uuid.New()

	tests := []struct {
		name          string
		setupMock     func(*dbmocks.MockStore)
		expectedError bool
	}{
		{
			name: "successful_generation",
			setupMock: func(m *dbmocks.MockStore) {
				m.On("GetUserByID", ctx, userID).Return(db.User{
					ID:           userID,
					PlatformRole: "staff",
				}, nil)
			},
			expectedError: false,
		},
		{
			name: "user_not_found",
			setupMock: func(m *dbmocks.MockStore) {
				m.On("GetUserByID", ctx, userID).Return(db.User{}, assert.AnError)
			},
			expectedError: true,
		},
		{
			name:          "nil_store",
			setupMock:     func(m *dbmocks.MockStore) {},
			expectedError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.name == "nil_store" {
				_, err := token.GenerateAccessToken(ctx, nil, userID)
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "nil")
				return
			}

			mockStore := dbmocks.NewMockStore(t)
			tt.setupMock(mockStore)

			tokenString, err := token.GenerateAccessToken(ctx, mockStore, userID)

			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, tokenString)

				// Verify token can be parsed
				config, _ := token.GetJWTConfig()
				claims, err := token.ParseToken(tokenString, config.AccessTokenSecret)
				assert.NoError(t, err)
				assert.Equal(t, userID, claims.UserID)
				assert.Equal(t, "staff", claims.PlatformRole)
			}

			mockStore.AssertExpectations(t)
		})
	}
}

func TestGenerateRefreshToken(t *testing.T) {
	cleanup := setupJWTTestEnv(t)
	defer cleanup()

	ctx := context.Background()
	userID := uuid.New()

	tests := []struct {
		name          string
		setupMock     func(*dbmocks.MockStore)
		expectedError bool
	}{
		{
			name: "successful_generation",
			setupMock: func(m *dbmocks.MockStore) {
				m.On("GetUserByID", ctx, userID).Return(db.User{
					ID:           userID,
					PlatformRole: "user",
				}, nil)
				m.On("CreateRefreshToken", ctx, mock.AnythingOfType("db.CreateRefreshTokenParams")).Return(db.RefreshToken{
					ID:        uuid.New(),
					UserID:    userID,
					TokenHash: "hash",
					ExpiresAt: time.Now().Add(7 * 24 * time.Hour),
				}, nil)
			},
			expectedError: false,
		},
		{
			name: "user_not_found",
			setupMock: func(m *dbmocks.MockStore) {
				m.On("GetUserByID", ctx, userID).Return(db.User{}, assert.AnError)
			},
			expectedError: true,
		},
		{
			name: "create_refresh_token_fails",
			setupMock: func(m *dbmocks.MockStore) {
				m.On("GetUserByID", ctx, userID).Return(db.User{
					ID:           userID,
					PlatformRole: "user",
				}, nil)
				m.On("CreateRefreshToken", ctx, mock.AnythingOfType("db.CreateRefreshTokenParams")).Return(db.RefreshToken{}, assert.AnError)
			},
			expectedError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockStore := dbmocks.NewMockStore(t)
			tt.setupMock(mockStore)

			tokenString, err := token.GenerateRefreshToken(ctx, mockStore, userID)

			if tt.expectedError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.NotEmpty(t, tokenString)

				// Verify token can be parsed
				config, _ := token.GetJWTConfig()
				claims, err := token.ParseToken(tokenString, config.RefreshTokenSecret)
				assert.NoError(t, err)
				assert.Equal(t, userID, claims.UserID)
			}

			mockStore.AssertExpectations(t)
		})
	}
}

func TestHashToken(t *testing.T) {
	tests := []struct {
		name     string
		token    string
		expected string
	}{
		{
			name:     "empty_token",
			token:    "",
			expected: "e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855",
		},
		{
			name:     "simple_token",
			token:    "test-token",
			expected: "4c5dc9b7708905f77f5e5d16316b5dfb425e68cb326dcd55a860e90a7707031e",
		},
		{
			name:     "jwt_like_token",
			token:    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
			expected: "7f75367e7881255134e1375e723d1dea8ad5f6a4fdb79d938df1f1754a830606",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := token.HashToken(tt.token)
			assert.Equal(t, tt.expected, result)

			// Hash should be consistent
			result2 := token.HashToken(tt.token)
			assert.Equal(t, result, result2)
		})
	}
}

func TestJWTMaker(t *testing.T) {
	t.Run("create_maker_with_short_key", func(t *testing.T) {
		_, err := token.NewJWTMaker("short-key")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "32 characters")
	})

	t.Run("create_maker_with_valid_key", func(t *testing.T) {
		maker, err := token.NewJWTMaker("this-is-a-valid-secret-key-32-characters-long")
		assert.NoError(t, err)
		assert.NotNil(t, maker)
	})

	t.Run("create_and_verify_token", func(t *testing.T) {
		secretKey := "this-is-a-valid-secret-key-32-characters-long"
		maker, err := token.NewJWTMaker(secretKey)
		require.NoError(t, err)

		userID := uuid.New()
		duration := time.Hour

		// Create token
		tokenString, err := maker.CreateToken(userID, duration)
		assert.NoError(t, err)
		assert.NotEmpty(t, tokenString)

		// Verify token
		claims, err := maker.VerifyToken(tokenString)
		assert.NoError(t, err)
		assert.Equal(t, userID, claims.UserID)
		assert.Equal(t, "user", claims.PlatformRole) // Default value
		assert.WithinDuration(t, time.Now().Add(duration), claims.ExpiresAt.Time, 5*time.Second)
	})

	t.Run("verify_expired_token", func(t *testing.T) {
		secretKey := "this-is-a-valid-secret-key-32-characters-long"
		maker, err := token.NewJWTMaker(secretKey)
		require.NoError(t, err)

		userID := uuid.New()

		// Create expired token
		tokenString, err := maker.CreateToken(userID, -time.Hour)
		assert.NoError(t, err)

		// Verify should fail
		_, err = maker.VerifyToken(tokenString)
		assert.Error(t, err)
	})

	t.Run("verify_invalid_token", func(t *testing.T) {
		secretKey := "this-is-a-valid-secret-key-32-characters-long"
		maker, err := token.NewJWTMaker(secretKey)
		require.NoError(t, err)

		// Try to verify invalid token
		_, err = maker.VerifyToken("invalid.token.string")
		assert.Error(t, err)
	})
}
