package unit

import (
	"testing"

	"Membership-SAAS-System-Backend/internal/authorization"

	"github.com/stretchr/testify/assert"
)

func TestOrgRoleHierarchy(t *testing.T) {
	tests := []struct {
		name           string
		role1          string
		role2          string
		expectedResult bool
	}{
		// Owner role tests
		{
			name:           "owner_higher_than_admin",
			role1:          authorization.OrgRoleOwner,
			role2:          authorization.OrgRoleAdmin,
			expectedResult: true,
		},
		{
			name:           "owner_higher_than_staff",
			role1:          authorization.OrgRoleOwner,
			role2:          authorization.OrgRoleStaff,
			expectedResult: true,
		},
		{
			name:           "owner_higher_than_volunteer",
			role1:          authorization.OrgRoleOwner,
			role2:          authorization.OrgRoleVolunteer,
			expectedResult: true,
		},
		{
			name:           "owner_higher_than_member",
			role1:          authorization.OrgRoleOwner,
			role2:          authorization.OrgRoleMember,
			expectedResult: true,
		},
		{
			name:           "owner_equal_to_owner",
			role1:          authorization.OrgRoleOwner,
			role2:          authorization.OrgRoleOwner,
			expectedResult: true,
		},

		// Admin role tests
		{
			name:           "admin_lower_than_owner",
			role1:          authorization.OrgRoleAdmin,
			role2:          authorization.OrgRoleOwner,
			expectedResult: false,
		},
		{
			name:           "admin_higher_than_staff",
			role1:          authorization.OrgRoleAdmin,
			role2:          authorization.OrgRoleStaff,
			expectedResult: true,
		},
		{
			name:           "admin_higher_than_volunteer",
			role1:          authorization.OrgRoleAdmin,
			role2:          authorization.OrgRoleVolunteer,
			expectedResult: true,
		},
		{
			name:           "admin_higher_than_member",
			role1:          authorization.OrgRoleAdmin,
			role2:          authorization.OrgRoleMember,
			expectedResult: true,
		},
		{
			name:           "admin_equal_to_admin",
			role1:          authorization.OrgRoleAdmin,
			role2:          authorization.OrgRoleAdmin,
			expectedResult: true,
		},

		// Staff role tests
		{
			name:           "staff_lower_than_admin",
			role1:          authorization.OrgRoleStaff,
			role2:          authorization.OrgRoleAdmin,
			expectedResult: false,
		},
		{
			name:           "staff_higher_than_volunteer",
			role1:          authorization.OrgRoleStaff,
			role2:          authorization.OrgRoleVolunteer,
			expectedResult: true,
		},
		{
			name:           "staff_higher_than_member",
			role1:          authorization.OrgRoleStaff,
			role2:          authorization.OrgRoleMember,
			expectedResult: true,
		},
		{
			name:           "staff_equal_to_staff",
			role1:          authorization.OrgRoleStaff,
			role2:          authorization.OrgRoleStaff,
			expectedResult: true,
		},

		// Volunteer role tests
		{
			name:           "volunteer_lower_than_staff",
			role1:          authorization.OrgRoleVolunteer,
			role2:          authorization.OrgRoleStaff,
			expectedResult: false,
		},
		{
			name:           "volunteer_higher_than_member",
			role1:          authorization.OrgRoleVolunteer,
			role2:          authorization.OrgRoleMember,
			expectedResult: true,
		},
		{
			name:           "volunteer_equal_to_volunteer",
			role1:          authorization.OrgRoleVolunteer,
			role2:          authorization.OrgRoleVolunteer,
			expectedResult: true,
		},

		// Member role tests
		{
			name:           "member_lower_than_volunteer",
			role1:          authorization.OrgRoleMember,
			role2:          authorization.OrgRoleVolunteer,
			expectedResult: false,
		},
		{
			name:           "member_lower_than_all_other_roles",
			role1:          authorization.OrgRoleMember,
			role2:          authorization.OrgRoleOwner,
			expectedResult: false,
		},
		{
			name:           "member_equal_to_member",
			role1:          authorization.OrgRoleMember,
			role2:          authorization.OrgRoleMember,
			expectedResult: true,
		},

		// Invalid role tests
		{
			name:           "invalid_role1",
			role1:          "invalid_role",
			role2:          authorization.OrgRoleMember,
			expectedResult: false,
		},
		{
			name:           "invalid_role2",
			role1:          authorization.OrgRoleAdmin,
			role2:          "invalid_role",
			expectedResult: false,
		},
		{
			name:           "both_roles_invalid",
			role1:          "invalid_role1",
			role2:          "invalid_role2",
			expectedResult: false,
		},
		{
			name:           "empty_role1",
			role1:          "",
			role2:          authorization.OrgRoleMember,
			expectedResult: false,
		},
		{
			name:           "empty_role2",
			role1:          authorization.OrgRoleAdmin,
			role2:          "",
			expectedResult: false,
		},
		{
			name:           "both_roles_empty",
			role1:          "",
			role2:          "",
			expectedResult: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := authorization.IsOrgRoleHigherOrEqual(tt.role1, tt.role2)
			assert.Equal(t, tt.expectedResult, result,
				"IsOrgRoleHigherOrEqual(%s, %s) should return %v",
				tt.role1, tt.role2, tt.expectedResult)
		})
	}
}

func TestPlatformRoleHierarchy(t *testing.T) {
	tests := []struct {
		name           string
		role1          string
		role2          string
		expectedResult bool
	}{
		// SuperAdmin role tests
		{
			name:           "superadmin_higher_than_staff",
			role1:          authorization.PlatformRoleSuperAdmin,
			role2:          authorization.PlatformRoleStaff,
			expectedResult: true,
		},
		{
			name:           "superadmin_higher_than_user",
			role1:          authorization.PlatformRoleSuperAdmin,
			role2:          authorization.PlatformRoleUser,
			expectedResult: true,
		},
		{
			name:           "superadmin_equal_to_superadmin",
			role1:          authorization.PlatformRoleSuperAdmin,
			role2:          authorization.PlatformRoleSuperAdmin,
			expectedResult: true,
		},

		// Staff role tests
		{
			name:           "staff_lower_than_superadmin",
			role1:          authorization.PlatformRoleStaff,
			role2:          authorization.PlatformRoleSuperAdmin,
			expectedResult: false,
		},
		{
			name:           "staff_higher_than_user",
			role1:          authorization.PlatformRoleStaff,
			role2:          authorization.PlatformRoleUser,
			expectedResult: true,
		},
		{
			name:           "staff_equal_to_staff",
			role1:          authorization.PlatformRoleStaff,
			role2:          authorization.PlatformRoleStaff,
			expectedResult: true,
		},

		// User role tests
		{
			name:           "user_lower_than_staff",
			role1:          authorization.PlatformRoleUser,
			role2:          authorization.PlatformRoleStaff,
			expectedResult: false,
		},
		{
			name:           "user_lower_than_superadmin",
			role1:          authorization.PlatformRoleUser,
			role2:          authorization.PlatformRoleSuperAdmin,
			expectedResult: false,
		},
		{
			name:           "user_equal_to_user",
			role1:          authorization.PlatformRoleUser,
			role2:          authorization.PlatformRoleUser,
			expectedResult: true,
		},

		// Invalid role tests
		{
			name:           "invalid_role1",
			role1:          "invalid_role",
			role2:          authorization.PlatformRoleUser,
			expectedResult: false,
		},
		{
			name:           "invalid_role2",
			role1:          authorization.PlatformRoleStaff,
			role2:          "invalid_role",
			expectedResult: false,
		},
		{
			name:           "both_roles_invalid",
			role1:          "invalid_role1",
			role2:          "invalid_role2",
			expectedResult: false,
		},
		{
			name:           "empty_role1",
			role1:          "",
			role2:          authorization.PlatformRoleUser,
			expectedResult: false,
		},
		{
			name:           "empty_role2",
			role1:          authorization.PlatformRoleStaff,
			role2:          "",
			expectedResult: false,
		},
		{
			name:           "both_roles_empty",
			role1:          "",
			role2:          "",
			expectedResult: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := authorization.IsPlatformRoleHigherOrEqual(tt.role1, tt.role2)
			assert.Equal(t, tt.expectedResult, result,
				"IsPlatformRoleHigherOrEqual(%s, %s) should return %v",
				tt.role1, tt.role2, tt.expectedResult)
		})
	}
}

func TestRoleConstants(t *testing.T) {
	// Ensure all role constants are properly defined
	t.Run("org_role_constants", func(t *testing.T) {
		assert.Equal(t, "member", authorization.OrgRoleMember)
		assert.Equal(t, "volunteer", authorization.OrgRoleVolunteer)
		assert.Equal(t, "staff", authorization.OrgRoleStaff)
		assert.Equal(t, "admin", authorization.OrgRoleAdmin)
		assert.Equal(t, "owner", authorization.OrgRoleOwner)
	})

	t.Run("platform_role_constants", func(t *testing.T) {
		assert.Equal(t, "user", authorization.PlatformRoleUser)
		assert.Equal(t, "staff", authorization.PlatformRoleStaff)
		assert.Equal(t, "super_admin", authorization.PlatformRoleSuperAdmin)
	})

	t.Run("action_constants", func(t *testing.T) {
		assert.Equal(t, "create", authorization.ActionCreate)
		assert.Equal(t, "read", authorization.ActionRead)
		assert.Equal(t, "update", authorization.ActionUpdate)
		assert.Equal(t, "delete", authorization.ActionDelete)
		assert.Equal(t, "manage", authorization.ActionManage)
	})

	t.Run("resource_constants", func(t *testing.T) {
		assert.Equal(t, "user", authorization.ResourceUser)
		assert.Equal(t, "organization", authorization.ResourceOrganization)
		assert.Equal(t, "event", authorization.ResourceEvent)
		assert.Equal(t, "content", authorization.ResourceContent)
		assert.Equal(t, "volunteer", authorization.ResourceVolunteer)
		assert.Equal(t, "registration", authorization.ResourceRegistration)
		assert.Equal(t, "notification", authorization.ResourceNotification)
		assert.Equal(t, "audit_log", authorization.ResourceAuditLog)
	})
}

func TestRoleHierarchyValues(t *testing.T) {
	// Test that hierarchy values are correctly ordered
	t.Run("org_hierarchy_values", func(t *testing.T) {
		assert.Equal(t, 1, authorization.OrgRoleHierarchy[authorization.OrgRoleMember])
		assert.Equal(t, 2, authorization.OrgRoleHierarchy[authorization.OrgRoleVolunteer])
		assert.Equal(t, 3, authorization.OrgRoleHierarchy[authorization.OrgRoleStaff])
		assert.Equal(t, 4, authorization.OrgRoleHierarchy[authorization.OrgRoleAdmin])
		assert.Equal(t, 5, authorization.OrgRoleHierarchy[authorization.OrgRoleOwner])

		// Ensure hierarchy is properly ordered
		assert.Less(t, authorization.OrgRoleHierarchy[authorization.OrgRoleMember],
			authorization.OrgRoleHierarchy[authorization.OrgRoleVolunteer])
		assert.Less(t, authorization.OrgRoleHierarchy[authorization.OrgRoleVolunteer],
			authorization.OrgRoleHierarchy[authorization.OrgRoleStaff])
		assert.Less(t, authorization.OrgRoleHierarchy[authorization.OrgRoleStaff],
			authorization.OrgRoleHierarchy[authorization.OrgRoleAdmin])
		assert.Less(t, authorization.OrgRoleHierarchy[authorization.OrgRoleAdmin],
			authorization.OrgRoleHierarchy[authorization.OrgRoleOwner])
	})

	t.Run("platform_hierarchy_values", func(t *testing.T) {
		assert.Equal(t, 1, authorization.PlatformRoleHierarchy[authorization.PlatformRoleUser])
		assert.Equal(t, 2, authorization.PlatformRoleHierarchy[authorization.PlatformRoleStaff])
		assert.Equal(t, 3, authorization.PlatformRoleHierarchy[authorization.PlatformRoleSuperAdmin])

		// Ensure hierarchy is properly ordered
		assert.Less(t, authorization.PlatformRoleHierarchy[authorization.PlatformRoleUser],
			authorization.PlatformRoleHierarchy[authorization.PlatformRoleStaff])
		assert.Less(t, authorization.PlatformRoleHierarchy[authorization.PlatformRoleStaff],
			authorization.PlatformRoleHierarchy[authorization.PlatformRoleSuperAdmin])
	})
}

func TestRoleComparisonEdgeCases(t *testing.T) {
	// Test case sensitivity
	t.Run("case_sensitivity", func(t *testing.T) {
		// Roles should be case-sensitive
		assert.False(t, authorization.IsOrgRoleHigherOrEqual("ADMIN", authorization.OrgRoleAdmin))
		assert.False(t, authorization.IsOrgRoleHigherOrEqual(authorization.OrgRoleAdmin, "ADMIN"))
		assert.False(t, authorization.IsPlatformRoleHigherOrEqual("STAFF", authorization.PlatformRoleStaff))
		assert.False(t, authorization.IsPlatformRoleHigherOrEqual(authorization.PlatformRoleStaff, "STAFF"))
	})

	// Test with whitespace
	t.Run("whitespace_handling", func(t *testing.T) {
		assert.False(t, authorization.IsOrgRoleHigherOrEqual(" admin", authorization.OrgRoleAdmin))
		assert.False(t, authorization.IsOrgRoleHigherOrEqual("admin ", authorization.OrgRoleAdmin))
		assert.False(t, authorization.IsOrgRoleHigherOrEqual(" admin ", authorization.OrgRoleAdmin))
		assert.False(t, authorization.IsPlatformRoleHigherOrEqual(" staff", authorization.PlatformRoleStaff))
		assert.False(t, authorization.IsPlatformRoleHigherOrEqual("staff ", authorization.PlatformRoleStaff))
		assert.False(t, authorization.IsPlatformRoleHigherOrEqual(" staff ", authorization.PlatformRoleStaff))
	})

	// Test with special characters
	t.Run("special_characters", func(t *testing.T) {
		assert.False(t, authorization.IsOrgRoleHigherOrEqual("admin!", authorization.OrgRoleAdmin))
		assert.False(t, authorization.IsOrgRoleHigherOrEqual("admin@", authorization.OrgRoleAdmin))
		assert.False(t, authorization.IsOrgRoleHigherOrEqual("admin#", authorization.OrgRoleAdmin))
		assert.False(t, authorization.IsPlatformRoleHigherOrEqual("staff!", authorization.PlatformRoleStaff))
		assert.False(t, authorization.IsPlatformRoleHigherOrEqual("staff@", authorization.PlatformRoleStaff))
		assert.False(t, authorization.IsPlatformRoleHigherOrEqual("staff#", authorization.PlatformRoleStaff))
	})
}

// TestRoleTransitions tests common role transition scenarios
func TestRoleTransitions(t *testing.T) {
	// Test promoting users
	t.Run("org_role_promotions", func(t *testing.T) {
		roles := []string{
			authorization.OrgRoleMember,
			authorization.OrgRoleVolunteer,
			authorization.OrgRoleStaff,
			authorization.OrgRoleAdmin,
			authorization.OrgRoleOwner,
		}

		// Each role should be promotable to the next
		for i := 0; i < len(roles)-1; i++ {
			currentRole := roles[i]
			nextRole := roles[i+1]

			assert.False(t, authorization.IsOrgRoleHigherOrEqual(currentRole, nextRole),
				"%s should be lower than %s", currentRole, nextRole)
			assert.True(t, authorization.IsOrgRoleHigherOrEqual(nextRole, currentRole),
				"%s should be higher than %s", nextRole, currentRole)
		}
	})

	t.Run("platform_role_promotions", func(t *testing.T) {
		roles := []string{
			authorization.PlatformRoleUser,
			authorization.PlatformRoleStaff,
			authorization.PlatformRoleSuperAdmin,
		}

		// Each role should be promotable to the next
		for i := 0; i < len(roles)-1; i++ {
			currentRole := roles[i]
			nextRole := roles[i+1]

			assert.False(t, authorization.IsPlatformRoleHigherOrEqual(currentRole, nextRole),
				"%s should be lower than %s", currentRole, nextRole)
			assert.True(t, authorization.IsPlatformRoleHigherOrEqual(nextRole, currentRole),
				"%s should be higher than %s", nextRole, currentRole)
		}
	})
}
