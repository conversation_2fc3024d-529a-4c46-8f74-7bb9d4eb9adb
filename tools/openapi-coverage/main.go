package main

import (
	"context"
	"fmt"
	"log"
	"path/filepath"
	"sort"
	"strings"

	"Membership-SAAS-System-Backend/internal/config"
	"Membership-SAAS-System-Backend/internal/server"

	"github.com/getkin/kin-openapi/openapi3"
)

func main() {
	spec := loadOpenAPISpec()
	srv := createServer()

	documentedEndpoints := collectDocumentedEndpoints(spec)
	actualEndpoints := collectActualEndpoints(srv)

	printCoverageReport(documentedEndpoints, actualEndpoints)
}

func loadOpenAPISpec() *openapi3.T {
	specPath := filepath.Join("docs", "openapi.yaml")
	loader := openapi3.NewLoader()
	spec, err := loader.LoadFromFile(specPath)
	if err != nil {
		log.Fatalf("Failed to load OpenAPI specification: %v", err)
	}

	// Validate spec
	ctx := context.Background()
	if err := spec.Validate(ctx); err != nil {
		log.Fatalf("Invalid OpenAPI specification: %v", err)
	}

	return spec
}

func createServer() *server.Server {
	cfg := config.Load()
	srv, err := server.New(cfg)
	if err != nil {
		log.Fatalf("Failed to create server: %v", err)
	}
	return srv
}

func collectDocumentedEndpoints(spec *openapi3.T) map[string]bool {
	documentedEndpoints := make(map[string]bool)

	for path, pathItem := range spec.Paths.Map() {
		methods := []struct {
			name string
			op   *openapi3.Operation
		}{
			{"GET", pathItem.Get},
			{"POST", pathItem.Post},
			{"PUT", pathItem.Put},
			{"PATCH", pathItem.Patch},
			{"DELETE", pathItem.Delete},
		}

		for _, method := range methods {
			if method.op != nil {
				documentedEndpoints[fmt.Sprintf("%s %s", method.name, path)] = true
			}
		}
	}

	return documentedEndpoints
}

func collectActualEndpoints(srv *server.Server) map[string]bool {
	actualEndpoints := make(map[string]bool)
	echoRoutes := srv.Echo().Routes()

	for _, route := range echoRoutes {
		// Skip internal Echo routes
		if strings.HasPrefix(route.Path, "/echo/") {
			continue
		}

		// Skip non-API routes
		if !strings.HasPrefix(route.Path, "/api/") {
			continue
		}

		// Normalize path by removing :param syntax
		normalizedPath := normalizePath(route.Path)
		actualEndpoints[fmt.Sprintf("%s %s", route.Method, normalizedPath)] = true
	}

	return actualEndpoints
}

func normalizePath(path string) string {
	// Convert Echo's :param to OpenAPI's {param}
	parts := strings.Split(path, "/")
	for i, part := range parts {
		if strings.HasPrefix(part, ":") {
			parts[i] = "{" + part[1:] + "}"
		}
	}
	return strings.Join(parts, "/")
}

func printCoverageReport(documentedEndpoints, actualEndpoints map[string]bool) {
	fmt.Println("=== OpenAPI Documentation Coverage Report ===")
	fmt.Println()

	// Find undocumented endpoints
	var undocumented []string
	for endpoint := range actualEndpoints {
		if !documentedEndpoints[endpoint] {
			undocumented = append(undocumented, endpoint)
		}
	}

	// Find documented but not implemented endpoints
	var notImplemented []string
	for endpoint := range documentedEndpoints {
		if !actualEndpoints[endpoint] {
			notImplemented = append(notImplemented, endpoint)
		}
	}

	// Sort for consistent output
	sort.Strings(undocumented)
	sort.Strings(notImplemented)

	// Print statistics
	totalActual := len(actualEndpoints)
	totalDocumented := len(documentedEndpoints)
	coverage := float64(totalDocumented-len(undocumented)) / float64(totalActual) * 100

	fmt.Printf("Total API endpoints: %d\n", totalActual)
	fmt.Printf("Documented endpoints: %d\n", totalDocumented)
	fmt.Printf("Coverage: %.1f%%\n", coverage)
	fmt.Println()

	// Print undocumented endpoints
	if len(undocumented) > 0 {
		fmt.Printf("Undocumented endpoints (%d):\n", len(undocumented))
		for _, endpoint := range undocumented {
			fmt.Printf("  - %s\n", endpoint)
		}
		fmt.Println()
	}

	// Print not implemented endpoints
	if len(notImplemented) > 0 {
		fmt.Printf("Documented but not implemented (%d):\n", len(notImplemented))
		for _, endpoint := range notImplemented {
			fmt.Printf("  - %s\n", endpoint)
		}
		fmt.Println()
	}

	if len(undocumented) == 0 && len(notImplemented) == 0 {
		fmt.Println("✅ Perfect coverage! All endpoints are documented and all documented endpoints are implemented.")
	}
}
